import React, { useCallback, useState } from 'react';

import { useModalCaller } from '@/mixins/modal';
import DMModal from '@/components/Common/Modal/DMModal';
import { Radio, Space } from 'antd';
import HelpLink from '@/components/HelpLink';

type Props = {
  onFinish: () => void;
  onClose: () => void;
};

/**
 *
 * @param props
 * @constructor
 */
const CreateShopEntryModal: React.FC<Props> = (props) => {
  const { onFinish, onClose } = props;
  const [v, setV] = useState<'browser' | 'extention'>('browser');

  return (
    <DMModal
      open
      width={600}
      title="添加TikTok店铺"
      onCancel={onClose}
      onOk={() => {
        if (v === 'browser') {
          onFinish();
        }
        onClose();
      }}
    >
      <Radio.Group
        value={v}
        style={{ fontSize: 14 }}
        onChange={(e) => {
          setV(e.target.value);
        }}
      >
        <Radio value="browser">通过花漾浏览器管理您的 TikTok 店铺</Radio>
        <div style={{ paddingLeft: 24, color: '#999', marginTop: 8, marginBottom: 40 }}>
          无论是跨境店还是本土店，都可以通过花漾浏览器来管理您的 TikTok
          店铺，既解决了多店铺防关联的场景，并拥有花漾 TK 完整的功能
        </div>
        <Radio value="extention">使用其它浏览器管理您的 TikTok 店铺</Radio>
        <div style={{ paddingLeft: 24, color: '#999', marginTop: 8, marginBottom: 12 }}>
          如果您已经使用其它浏览器管理 TikTok
          店铺，且不希望迁移至花漾浏览器，您可以通过给浏览器安装插件，使用花漾 TK 大部分的功能
        </div>
        <Space direction="vertical" size={12} style={{ paddingLeft: 24 }}>
          <HelpLink href="/help/tkshop2/plugin#chrome">Chrome 浏览器安装说明</HelpLink>
          <HelpLink href="/help/tkshop2/plugin#edge">Edge 浏览器安装说明</HelpLink>
          <HelpLink href="/help/tkshop2/plugin#ziniao">紫鸟浏览器安装说明</HelpLink>
        </Space>
      </Radio.Group>
    </DMModal>
  );
};

export default CreateShopEntryModal;

export function useCreateShopEntryModal() {
  const modalCaller = useModalCaller(true);
  return useCallback(
    (props: Omit<Props, 'onClose'>) => {
      modalCaller({
        component: ({ close }) => <CreateShopEntryModal {...props} onClose={close} />,
      });
    },
    [modalCaller],
  );
}
