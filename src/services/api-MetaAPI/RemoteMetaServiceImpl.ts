// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** getLocation GET /api/remote/meta/ip/location */
export async function remoteMetaIpLocationGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteMetaIpLocationGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultIpLocationResultVo>('/api/remote/meta/ip/location', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** getIpLocationProviderVersion GET /api/remote/meta/ip/locationProviderVersion */
export async function remoteMetaIpLocationProviderVersionGet(options?: { [key: string]: any }) {
  return request<API.WebResultIpLocationResultVo>('/api/remote/meta/ip/locationProviderVersion', {
    method: 'GET',
    ...(options || {}),
  });
}

/** reloadIpLocationProvider GET /api/remote/meta/ip/reloadLocationProvider */
export async function remoteMetaIpReloadLocationProviderGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteMetaIpReloadLocationProviderGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultIpLocationResultVo>('/api/remote/meta/ip/reloadLocationProvider', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
