// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 创建快捷方式 POST /api/shop/${param0}/shortcut */
export async function shopByIdShortcutPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopByIdShortcutPostParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultShopShortcutDto>(`/api/shop/${param0}/shortcut`, {
    method: 'POST',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取所有快捷方式 GET /api/shop/${param0}/shortcuts */
export async function shopByIdShortcutsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopByIdShortcutsGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultListShopShortcutDto>(`/api/shop/${param0}/shortcuts`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 删除快捷方式 DELETE /api/shop/shortcut/${param0} */
export async function shopShortcutByIdDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopShortcutByIdDeleteParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/shop/shortcut/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取快捷方式详情 GET /api/shop/shortcut/${param0} */
export async function shopShortcutByTokenGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopShortcutByTokenGetParams,
  options?: { [key: string]: any },
) {
  const { token: param0, ...queryParams } = params;
  return request<API.WebResultShopShortcutDetailVo>(`/api/shop/shortcut/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 以token的方式登录 GET /api/shop/shortcut/login/${param0} */
export async function shopShortcutLoginByTokenGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopShortcutLoginByTokenGetParams,
  options?: { [key: string]: any },
) {
  const { token: param0, ...queryParams } = params;
  return request<API.WebResultLoginResultVo>(`/api/shop/shortcut/login/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}
