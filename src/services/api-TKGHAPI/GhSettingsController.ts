// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 删除一个用于执行rpa的设备 DELETE /api/gh/settings */
export async function ghSettingsDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSettingsDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/settings', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 添加一个用于执行rpa的设备 PUT /api/gh/settings/bindRpaDevice */
export async function ghSettingsBindRpaDevicePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSettingsBindRpaDevicePutParams,
  body: number[],
  options?: { [key: string]: any },
) {
  return request<API.WebResultListGhJobDeviceVo>('/api/gh/settings/bindRpaDevice', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取团队的工会注册地 GET /api/gh/settings/countries */
export async function ghSettingsCountriesGet(options?: { [key: string]: any }) {
  return request<API.WebResultListstring>('/api/gh/settings/countries', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 设置团队的工会注册地 PUT /api/gh/settings/countries */
export async function ghSettingsCountriesPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSettingsCountriesPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/settings/countries', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 达人自动化标签配置 GET /api/gh/settings/creatorExpiredAutoTags */
export async function ghSettingsCreatorExpiredAutoTagsGet(options?: { [key: string]: any }) {
  return request<API.WebResultGhCreatorExpiredAutoTags>('/api/gh/settings/creatorExpiredAutoTags', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 配置达人自动化标签 PUT /api/gh/settings/creatorExpiredAutoTags */
export async function ghSettingsCreatorExpiredAutoTagsPut(
  body: API.GhCreatorExpiredAutoTags,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/settings/creatorExpiredAutoTags', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改主播类型 PUT /api/gh/settings/creatorTypes */
export async function ghSettingsCreatorTypesPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSettingsCreatorTypesPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/settings/creatorTypes', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修改屏蔽原因 PUT /api/gh/settings/deleteRemarks */
export async function ghSettingsDeleteRemarksPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSettingsDeleteRemarksPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/settings/deleteRemarks', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取公会平台分身统计信息 GET /api/gh/settings/getShopStatistics */
export async function ghSettingsGetShopStatisticsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSettingsGetShopStatisticsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultGhShopStatistics>('/api/gh/settings/getShopStatistics', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 抓tk数据域名配置 GET /api/gh/settings/grabApiDomains */
export async function ghSettingsGrabApiDomainsGet(options?: { [key: string]: any }) {
  return request<API.WebResultMap>('/api/gh/settings/grabApiDomains', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 缓存时长（天） PUT /api/gh/settings/keepDays */
export async function ghSettingsKeepDaysPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSettingsKeepDaysPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/settings/keepDays', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修改日期类标签保留天数 0表示一直保留 PUT /api/gh/settings/keepTagDays */
export async function ghSettingsKeepTagDaysPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSettingsKeepTagDaysPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/settings/keepTagDays', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** KOL配置 GET /api/gh/settings/kolConfig */
export async function ghSettingsKolConfigGet(options?: { [key: string]: any }) {
  return request<API.WebResultTeamKolConfig>('/api/gh/settings/kolConfig', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 设置最近开播时间 PUT /api/gh/settings/liveHours */
export async function ghSettingsLiveHoursPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSettingsLiveHoursPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/settings/liveHours', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取blockTags GET /api/gh/settings/loadBlockTags */
export async function ghSettingsLoadBlockTagsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSettingsLoadBlockTagsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMapstring>('/api/gh/settings/loadBlockTags', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取公会执行任务的通用配置 GET /api/gh/settings/loadSettings */
export async function ghSettingsLoadSettingsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSettingsLoadSettingsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultGhGeneralSettingsVo>('/api/gh/settings/loadSettings', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 设置最低流水 PUT /api/gh/settings/minRevenue */
export async function ghSettingsMinRevenuePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSettingsMinRevenuePutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/settings/minRevenue', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取一个手机详情 GET /api/gh/settings/mobileDetail */
export async function ghSettingsMobileDetailGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSettingsMobileDetailGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTeamMobileVo>('/api/gh/settings/mobileDetail', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取通知配置 GET /api/gh/settings/notifySchedule */
export async function ghSettingsNotifyScheduleGet(options?: { [key: string]: any }) {
  return request<API.WebResultNotifyScheduleVo>('/api/gh/settings/notifySchedule', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 更新通知配置 PUT /api/gh/settings/notifySchedule */
export async function ghSettingsNotifySchedulePut(
  body: API.NotifyScheduleVo,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/settings/notifySchedule', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** updateFailPolicy PUT /api/gh/settings/updateFailPolicy */
export async function ghSettingsUpdateFailPolicyPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSettingsUpdateFailPolicyPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/settings/updateFailPolicy', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 更改一个设备的迸发数 PUT /api/gh/settings/updateRpaDeviceConcurrent */
export async function ghSettingsUpdateRpaDeviceConcurrentPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSettingsUpdateRpaDeviceConcurrentPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultGhJobDeviceVo>('/api/gh/settings/updateRpaDeviceConcurrent', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 更新TikTok私信账号调度配置 PUT /api/gh/settings/updateScheduleConfig */
export async function ghSettingsUpdateScheduleConfigPut(
  body: API.UpdateScheduleConfigRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/settings/updateScheduleConfig', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** updateSchedulePriority PUT /api/gh/settings/updateSchedulePriority */
export async function ghSettingsUpdateSchedulePriorityPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSettingsUpdateSchedulePriorityPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/settings/updateSchedulePriority', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 更新主播信息更新调度配置 PUT /api/gh/settings/updateSyncCreatorConfig */
export async function ghSettingsUpdateSyncCreatorConfigPut(
  body: API.UpdateSyncCreatorConfigRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/settings/updateSyncCreatorConfig', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
