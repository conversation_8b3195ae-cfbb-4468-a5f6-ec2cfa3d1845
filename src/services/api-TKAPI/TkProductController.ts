// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 设置商品的备注 PUT /api/tk/product/${param0}/remark */
export async function tkProductByIdRemarkPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkProductByIdRemarkPutParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/tk/product/${param0}/remark`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 分页查询TK商品 GET /api/tk/product/page */
export async function tkProductPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkProductPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkProductDto>('/api/tk/product/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 同步TK商品 POST /api/tk/product/sync */
export async function tkProductSyncPost(
  body: API.SyncProductRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/tk/product/sync', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
