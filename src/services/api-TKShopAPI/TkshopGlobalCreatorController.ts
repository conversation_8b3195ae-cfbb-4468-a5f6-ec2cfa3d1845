// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 查询店铺达人总数 POST /api/tkshop/global/creator/count */
export async function tkshopGlobalCreatorCountPost(
  body: API.FindTkshopGlobalCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultlong>('/api/tkshop/global/creator/count', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 按区域分组查询达人 POST /api/tkshop/global/creator/groupByRegion */
export async function tkshopGlobalCreatorGroupByRegionPost(
  body: API.PageTkshopGlobalCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListRegionBatchVo>('/api/tkshop/global/creator/groupByRegion', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 按查询批量导入到团队 POST /api/tkshop/global/creator/importToTeam */
export async function tkshopGlobalCreatorImportToTeamPost(
  body: API.ImportTkshopGlobalCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/global/creator/importToTeam', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 若干天内同步过的达人导入团队 返回未同步的达人列表 POST /api/tkshop/global/creator/importToTeamByHandles */
export async function tkshopGlobalCreatorImportToTeamByHandlesPost(
  body: API.FilterBasicSyncedRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListstring>('/api/tkshop/global/creator/importToTeamByHandles', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 按查询批量导入到团队 POST /api/tkshop/global/creator/importToTeamByIds */
export async function tkshopGlobalCreatorImportToTeamByIdsPost(
  body: API.ImportGlobalCreatorByIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/global/creator/importToTeamByIds', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询达人（不返回总数） POST /api/tkshop/global/creator/page */
export async function tkshopGlobalCreatorPagePost(
  body: API.PageTkshopGlobalCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkshopGlobalCreatorDetailVo>(
    '/api/tkshop/global/creator/page',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 达人可导出的字段元信息 GET /api/tkshop/global/fields */
export async function tkshopGlobalFieldsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTkshopCreatorFiledVo>('/api/tkshop/global/fields', {
    method: 'GET',
    ...(options || {}),
  });
}
