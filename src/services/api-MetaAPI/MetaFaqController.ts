// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 分页查询FAQ GET /api/meta/faq/page */
export async function metaFaqPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.metaFaqPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultKfFaqDto>('/api/meta/faq/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
