// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 追加抽屉任务（返回跳过的记录） POST /api/tkshop/taskDrawer */
export async function tkshopTaskDrawerPost(
  body: API.AddTaskDrawerRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTkshopTaskDrawerDto>('/api/tkshop/taskDrawer', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** deleteTaskDrawer DELETE /api/tkshop/taskDrawer/${param0} */
export async function tkshopTaskDrawerByIdDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopTaskDrawerByIdDeleteParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/tkshop/taskDrawer/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 批量删除抽屉任务 POST /api/tkshop/taskDrawer/batchDelete */
export async function tkshopTaskDrawerBatchDeletePost(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/tkshop/taskDrawer/batchDelete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询用户的任务抽屉 GET /api/tkshop/taskDrawers */
export async function tkshopTaskDrawersGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopTaskDrawersGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkshopTaskDrawerDto>('/api/tkshop/taskDrawers', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
