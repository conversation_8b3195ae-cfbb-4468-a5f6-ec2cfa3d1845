// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 删除视图 DELETE /api/view/${param0} */
export async function viewByViewIdDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.viewByViewIdDeleteParams,
  options?: { [key: string]: any },
) {
  const { viewId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/view/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 判断视图是否存在 GET /api/view/${param0}/existsByName */
export async function viewByResourceTypeExistsByNameGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.viewByResourceTypeExistsByNameGetParams,
  options?: { [key: string]: any },
) {
  const { resourceType: param0, ...queryParams } = params;
  return request<API.WebResultboolean>(`/api/view/${param0}/existsByName`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 修改静态视图资源 PUT /api/view/${param0}/resources */
export async function viewByViewIdResourcesPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.viewByViewIdResourcesPutParams,
  body: API.ChangeViewResourceRequest,
  options?: { [key: string]: any },
) {
  const { viewId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/view/${param0}/resources`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 创建IP视图 POST /api/view/ip */
export async function viewIpPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.viewIpPostParams,
  body: API.IpViewFilterVo,
  options?: { [key: string]: any },
) {
  return request<API.WebResultViewDto>('/api/view/ip', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改IP视图 PUT /api/view/ip/${param0} */
export async function viewIpByViewIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.viewIpByViewIdPutParams,
  body: API.IpViewFilterVo,
  options?: { [key: string]: any },
) {
  const { viewId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/view/ip/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...queryParams,
    },
    data: body,
    ...(options || {}),
  });
}

/** 调整视图的顺序 PUT /api/view/order */
export async function viewOrderPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.viewOrderPutParams,
  options?: { [key: string]: any },
) {
  const { resourceType: param0, ...queryParams } = params;
  return request<API.WebResult>('/api/view/order', {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 创建店铺视图 POST /api/view/shop */
export async function viewShopPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.viewShopPostParams,
  body: API.ShopViewFilterVo,
  options?: { [key: string]: any },
) {
  return request<API.WebResultViewDto>('/api/view/shop', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改店铺视图 PUT /api/view/shop/${param0} */
export async function viewShopByViewIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.viewShopByViewIdPutParams,
  body: API.ShopViewFilterVo,
  options?: { [key: string]: any },
) {
  const { viewId: param0, ...queryParams } = params;
  return request<API.WebResultViewDto>(`/api/view/shop/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...queryParams,
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询当前用户的所有视图（包括分享的） GET /api/views/byType/${param0} */
export async function viewsByTypeByResourceTypeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.viewsByTypeByResourceTypeGetParams,
  options?: { [key: string]: any },
) {
  const { resourceType: param0, ...queryParams } = params;
  return request<API.WebResultListViewVo>(`/api/views/byType/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}
