// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 查询任务的排队顺序 如果为null，则表示任务已经开始了 GET /api/queueTask/rank */
export async function queueTaskRankGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.queueTaskRankGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultRankVo>('/api/queueTask/rank', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
