import I18N from '@/i18n';
import { useVT } from 'virtualizedtableforantd4';
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import type { ActionType } from '@ant-design/pro-table';
import { ProTable } from '@ant-design/pro-table';
import { scrollProTableOptionFn } from '@/mixins/table';
import { Button, ConfigProvider, Space, Tooltip, Typography } from 'antd';
import EmptyView from '@/components/Common/EmptyView';
import { GhostModalCaller } from '@/mixins/modal';
import MiddleSpin from '@/components/Common/MiddleSpin';
import { StyledTableWrapper } from '@/style/styled';
import { getTaskShelfJobIcon } from '@/pages/TikTok/Live/components/TaskShelfModal';
import { useSyncVideoSelectedModal } from '@/pages/VideoManage/components/SyncVideoSelectedModal';
import RemarkModal from '@/components/Common/RemarkModal';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { useRequest } from '@@/plugin-request/request';
import { versionAlert } from '@/pages/VideoManage/components/utils';
import useVideoHeader from '@/pages/VideoManage/components/useVideoHeader';
import {
  tkshopVideoPagePost,
  tkshopVideoRemarkPut,
} from '@/services/api-TKShopAPI/TkshopVideoController';
import DMFormItem from '@/components/Common/DMFormItem';
import VideoGroupActionsModal from '@/pages/VideoManage/components/VideoGroupActionsModal';
import { getVersionValid } from '@/hooks/useVersionValid';
import CollectOperation from '@/components/Common/CollectOperation';
import { useVideoAutoSyncCallback } from '@/pages/VideoManage/components/useVideoColumns';
import buttonStyles from '@/style/button.less';
import { StyledOverflow } from '@/components/Common/MoreDropdown';
import BatchVideoOption from '@/pages/VideoManage/components/BatchVideoOption';
import AboutVideoAutoSyncModal from '@/pages/VideoManage/components/AboutVideoAutoSyncModal';
import VideoADCodeGroupModal from '@/pages/VideoManage/components/VideoADCodeGroupModal';

const VideoTable = forwardRef((props, ref) => {
  const actionRef = useRef<ActionType>();
  const [selectedVos, changeSelectedVos] = useState<API.TkshopVideoVo[]>([]);
  const [vt] = useVT(() => ({ scroll: { y: '100%' } }), []);
  const loaded = useRef(false);
  const [total, setTotal] = useState(0);
  const [tableLoading, setTableLoading] = useState(true);
  const onReset = useCallback((reset?: boolean) => {
    if (reset) {
      changeSelectedVos([]);
      actionRef.current?.reloadAndRest?.();
    } else {
      actionRef.current?.reload?.();
    }
  }, []);

  const {
    header,
    getSearchParams,
    getSortParams,
    columns,
    tableHeader,
    tableWidth,
    form,
    setSearchBody,
    isInitialized,
  } = useVideoHeader({
    onChange: onReset,
  });

  useImperativeHandle(
    ref,
    () => {
      return {
        refresh: (reset?: boolean) => {
          return onReset(reset);
        },
      };
    },
    [onReset],
  );
  useEffect(() => {
    onReset(true);
  }, [onReset]);
  const selectedIds = useMemo(() => {
    if (selectedVos?.length) {
      return selectedVos.map((item) => item.id!);
    }
    return [];
  }, [selectedVos]);

  const { run: openSyncVideoModal, loading: syncing } = useSyncVideoSelectedModal();
  const { run: openVideoRemarkModal, loading: remarking } = useRequest(
    async (selected: any[]) => {
      if (remarking) {
        return;
      }

      const remark = selected?.[0].remark || '';
      GhostModalCaller(
        <RemarkModal
          placeholder={'通过对视频添加备注方便您日后查询（最多1000字）'}
          title={selected.length > 1 ? I18N.t('批量编辑视频备注') : I18N.t('编辑视频备注')}
          prefix={
            selected.length > 1 ? (
              <DMFormItem label={I18N.t('选择的视频')}>
                {I18N.t('{{count}}个', {
                  count: selected.length.toLocaleString(),
                })}
              </DMFormItem>
            ) : undefined
          }
          value={remark}
          onSubmit={async (val) => {
            await tkshopVideoRemarkPut({
              ids: selected.map((item) => item.id),
              remark: val,
            });
            onReset(false);
          }}
        />,
        'RemarkModal',
      );
    },
    {
      manual: true,
    },
  );
  const { run: groupCreator, loading: grouping } = useRequest(
    async () => {
      if (grouping) {
        return;
      }
      if (!(await getVersionValid())) {
        versionAlert();
        return;
      }
      GhostModalCaller(
        <VideoGroupActionsModal
          videos={selectedVos}
          onUpdate={() => {
            onReset(false);
          }}
        />,
      );
    },
    {
      manual: true,
    },
  );
  const onVideoAutoSyncSearch = useCallback(() => {
    setSearchBody({
      enabled: false,
      data: {},
    });
    form.setFieldValue('autoSync', 'autoSync');
  }, [form, setSearchBody]);
  const _addSyncCallback = useVideoAutoSyncCallback(
    onReset.bind(null, true),
    onVideoAutoSyncSearch,
  );
  const { run: addSyncRequest, loading } = useRequest(
    async (ids: number[], checked: boolean) => {
      if (loading) {
        return;
      }
      await _addSyncCallback(ids, checked);
    },
    {
      manual: true,
    },
  );

  const getParams = useCallback(() => {
    const payload: API.PageVideoRequest = {
      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      ...getSortParams(),
      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      ...getSearchParams(),
    };
    if (payload.shopId) {
      payload.shopIds = [payload.shopId];
    }
    return payload;
  }, [getSearchParams, getSortParams]);
  const table = useMemo(() => {
    if (!columns.length) {
      return <MiddleSpin />;
    }
    return (
      <ProTable<API.TkshopVideoVo>
        components={{ ...vt, header: tableHeader }}
        request={async (_params) => {
          setTableLoading(true);
          try {
            const { current, pageSize } = _params;
            const payload: API.PageVideoRequest = {
              pageNum: current,
              pageSize,
              ...getParams(),
            };
            const res = await tkshopVideoPagePost(payload);
            loaded.current = true;
            setTotal(res.data?.total || 0);
            return {
              data: res.data?.list,
              total: res.data?.total,
            };
          } finally {
            setTableLoading(false);
          }
        }}
        key={'table'}
        actionRef={actionRef}
        rowSelection={{
          selectedRowKeys: selectedIds,
          onChange(keys, videos) {
            changeSelectedVos(videos);
          },
        }}
        columns={columns}
        {...scrollProTableOptionFn({
          pageId: 'tkshop_video_manage',
          scroll: {
            x: tableWidth,
          },
          alwaysShowFooter: true,
          footer: () => {
            const disabled = selectedVos?.length === 0;

            return (
              <div
                style={{
                  display: 'flex',
                  flex: 1,
                  gap: 8,
                  flexWrap: 'nowrap',
                  alignItems: 'center',
                }}
              >
                <Tooltip title={I18N.t('针对当前搜索条件下的所有带货视频进行操作')}>
                  <span>
                    <Button
                      disabled={!total}
                      className={buttonStyles.successBtn}
                      onClick={async () => {
                        GhostModalCaller(
                          <BatchVideoOption
                            onSearch={() => {
                              setSearchBody({
                                enabled: false,
                                data: {},
                              });
                              form.setFieldValue('autoSync', 'autoSync');
                            }}
                            total={total!}
                            getParams={() => {
                              return {
                                ...getParams(),
                              };
                            }}
                            onUpdate={() => {
                              onReset(true);
                            }}
                          />,
                        );
                      }}
                    >
                      {I18N.t('全量操作')}
                    </Button>
                  </span>
                </Tooltip>
                <Tooltip placement={'topLeft'} title={disabled ? '请选择视频后操作' : false}>
                  <StyledOverflow
                    style={{ marginRight: 0 }}
                    disabled={disabled}
                    data={[
                      {
                        node(_props) {
                          return (
                            <Button
                              icon={getTaskShelfJobIcon('TS_SyncVideos', {
                                iconType: 'iconfont',
                              })}
                              loading={syncing}
                              onClick={() => {
                                if (disabled) {
                                  return;
                                }
                                openSyncVideoModal(
                                  selectedVos.map((item) => {
                                    return {
                                      id: item.id!,
                                      mediaId: item.mediaId!,
                                      creatorId: item.creatorId!,
                                      handle: item.creator!.handle!,
                                    };
                                  }),
                                  () => {
                                    if (selectedVos.length === 1) {
                                      return selectedVos[0].mediaName || '未命名';
                                    }
                                    return `选中的 ${selectedVos.length.toLocaleString()} 个带货视频`;
                                  },
                                );
                              }}
                              {..._props}
                            >
                              <span>视频信息更新</span>
                            </Button>
                          );
                        },
                        key: 'sync',
                      },
                      {
                        node(_props) {
                          return (
                            <Button
                              loading={remarking}
                              icon={<IconFontIcon iconName={'edit_24'} />}
                              onClick={() => {
                                if (disabled) {
                                  return;
                                }
                                openVideoRemarkModal(selectedVos);
                              }}
                              {..._props}
                            >
                              <span>编辑视频备注</span>
                            </Button>
                          );
                        },
                        key: 'remark',
                      },
                      {
                        node(_props) {
                          return (
                            <Button
                              icon={<IconFontIcon iconName={'AD_24'} />}
                              onClick={() => {
                                if (disabled) {
                                  return;
                                }
                                GhostModalCaller(<VideoADCodeGroupModal videos={selectedVos} />);
                              }}
                              {..._props}
                            >
                              <span>投流码</span>
                            </Button>
                          );
                        },
                        key: 'remark',
                      },
                      {
                        node(_props) {
                          return (
                            <Button
                              icon={<IconFontIcon size={16} iconName={'daren_24'} />}
                              onClick={async () => {
                                if (disabled) {
                                  return;
                                }
                                groupCreator();
                              }}
                              {..._props}
                            >
                              <span>达人批量操作</span>
                            </Button>
                          );
                        },
                        key: 'select_creators',
                      },
                      {
                        node(_props) {
                          return (
                            <Button
                              loading={loading}
                              icon={<IconFontIcon iconName="shoucang_24" />}
                              onClick={async () => {
                                if (disabled) {
                                  return;
                                }
                                const ids = selectedVos.map((item) => item.id!);

                                GhostModalCaller(
                                  <CollectOperation
                                    showLoading={false}
                                    onDislike={async () => {
                                      await addSyncRequest(ids, false);
                                    }}
                                    onLike={async () => {
                                      await addSyncRequest(ids, true);
                                    }}
                                    resourceType={'Video'}
                                  />,
                                );
                              }}
                              {..._props}
                            >
                              <span>{I18N.t('关注/取消关注')}</span>
                            </Button>
                          );
                        },
                        key: 'autoSync',
                      },
                    ]}
                  />
                </Tooltip>
                <Typography.Link
                  onClick={() => {
                    GhostModalCaller(<AboutVideoAutoSyncModal />);
                  }}
                >
                  <Space align={'center'}>
                    <IconFontIcon size={'inherit'} iconName={'info_24'} />
                    <span>{I18N.t('关于视频更新频率')}</span>
                  </Space>
                </Typography.Link>
              </div>
            );
          },
        })}
        style={
          !isInitialized
            ? {
                pointerEvents: 'none',
                opacity: 0,
              }
            : {}
        }
      />
    );
  }, [
    addSyncRequest,
    columns,
    form,
    getParams,
    groupCreator,
    isInitialized,
    loading,
    onReset,
    openSyncVideoModal,
    openVideoRemarkModal,
    remarking,
    selectedIds,
    selectedVos,
    setSearchBody,
    syncing,
    tableHeader,
    tableWidth,
    total,
    vt,
  ]);

  return (
    <>
      <div
        className="header"
        style={{ pointerEvents: tableLoading ? 'none' : 'auto', overflow: 'auto', gap: 8 }}
      >
        {header}
      </div>
      <main className="main">
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: 8,
            overflow: 'hidden',
            height: '100%',
          }}
        >
          <ConfigProvider
            renderEmpty={() => {
              return <EmptyView description={I18N.t('暂无数据')} />;
            }}
          >
            <StyledTableWrapper style={{ flex: 1 }}>{table}</StyledTableWrapper>
          </ConfigProvider>
        </div>
      </main>
    </>
  );
});
export default VideoTable;
