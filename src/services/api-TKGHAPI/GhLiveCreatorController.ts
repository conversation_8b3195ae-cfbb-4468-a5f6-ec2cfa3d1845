// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 当前是不正在直播间打赏守候。 GET /api/gh/liveCreator/isWatchingLiveRewards */
export async function ghLiveCreatorIsWatchingLiveRewardsGet(options?: { [key: string]: any }) {
  return request<API.WebResultboolean>('/api/gh/liveCreator/isWatchingLiveRewards', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 标记所有达人下播 POST /api/gh/liveCreator/markAllOffline */
export async function ghLiveCreatorMarkAllOfflinePost(options?: { [key: string]: any }) {
  return request<API.WebResult>('/api/gh/liveCreator/markAllOffline', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 标记某个达人下播 POST /api/gh/liveCreator/markOffline */
export async function ghLiveCreatorMarkOfflinePost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghLiveCreatorMarkOfflinePostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/liveCreator/markOffline', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询在线主播 GET /api/gh/liveCreator/page */
export async function ghLiveCreatorPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghLiveCreatorPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultGhLiveCreatorDetailVo>('/api/gh/liveCreator/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 开始直播间打赏守候 POST /api/gh/liveCreator/startWatchLiveRewards */
export async function ghLiveCreatorStartWatchLiveRewardsPost(
  body: API.StartWatchLiveRewardsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/liveCreator/startWatchLiveRewards', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 停止直播间打赏守候 DELETE /api/gh/liveCreator/stopWatchLiveRewards */
export async function ghLiveCreatorStopWatchLiveRewardsDelete(options?: { [key: string]: any }) {
  return request<API.WebResult>('/api/gh/liveCreator/stopWatchLiveRewards', {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** 同步直播主播 POST /api/gh/liveCreator/syncBatch */
export async function ghLiveCreatorSyncBatchPost(
  body: API.GhLiveCreatorDocumentBatch,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/liveCreator/syncBatch', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
