// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 用AI生成相似的描述 POST /api/tk/chat/generate */
export async function tkChatGeneratePost(
  body: API.OpenaiChatGenerateRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultlong>('/api/tk/chat/generate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 用AI生成相似的描述 POST /api/tk/chat/translate */
export async function tkChatTranslatePost(
  body: API.OpenaiChatTranslateRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultlong>('/api/tk/chat/translate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
