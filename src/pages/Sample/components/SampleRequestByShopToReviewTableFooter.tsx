import I18N from '@/i18n';
import { But<PERSON>, message, Tooltip } from 'antd';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { useRequest } from '@@/plugin-request/request';
import DMConfirm from '@/components/Common/DMConfirm';
import { useMemo } from 'react';
import buttonStyles from '@/style/button.less';
import { StyledOverflow } from '@/components/Common/MoreDropdown';
import { triggerUpdate, useAddTask } from '@/pages/TikTok/Live/components/TaskShelfModal';
import { tkshopSampleRequestBatchDeletePost } from '@/services/api-TKShopAPI/TkshopSampleRequestController';
import { GhostModalCaller } from '@/mixins/modal';
import SampleGroupActionsModal from '@/pages/Sample/components/SampleGroupActionsModal';
import { tkshopTaskDrawerBatchDeletePost } from '@/services/api-TKShopAPI/TkshopTaskDrawerController';

function SampleRequestByShopToReviewTableFooter(props: {
  selected: API.TkshopSampleRequestAuditVo[];
  onChange: (reset?: boolean) => void;
}) {
  const { selected, onChange } = props;
  const disabled = useMemo(() => selected.length === 0, [selected]);
  const { run: add } = useAddTask();
  const { run: batchReject, loading: batchRejectLoading } = useRequest(
    async (rows: API.TkshopSampleRequestAuditVo[]) => {
      return new Promise<void>((resolve, reject) => {
        const _dialog = DMConfirm({
          title: I18N.t('确定拒绝选中的') + rows.length + I18N.t('个索样吗？'),
          onOk(e) {
            add(
              {
                items: rows.map((item) => {
                  return {
                    taskType: 'TS_SampleApprove',
                    accountId: item.shopId,
                    parameter: JSON.stringify({
                      reject_apply_ids: [item.applyId!],
                      applyId: item.applyId,
                      handle: item.creator?.handle,
                    }),
                    creatorId: item.creator?.id,
                  };
                }),
              },
              e,
            )
              .then((res) => {
                _dialog?.destroy?.();
                const errorCount = res.data?.length || 0;
                if (errorCount) {
                  DMConfirm({
                    type: 'info',
                    width: 520,
                    title: I18N.t('在任务抽屉中创建了 {{count}} 笔索样审批任务', {
                      count: rows.length - errorCount,
                    }),
                    content:
                      errorCount > 0
                        ? I18N.t('有 {{count}} 笔索样审批，已由他人审批', {
                            count: errorCount,
                          })
                        : false,
                  });
                }
                resolve();
              })
              .catch(reject);
          },
          onCancel: () => {
            resolve();
          },
        });
      });
    },
    {
      manual: true,
    },
  );
  const { run: batchDelete, loading: batchDeleteLoading } = useRequest(
    async (rows: API.TkshopSampleRequestAuditVo[]) => {
      return new Promise<void>((resolve, reject) => {
        const _dialog = DMConfirm({
          title: I18N.t('确定删除选中的') + rows.length + I18N.t('个索样吗？'),
          onOk() {
            tkshopSampleRequestBatchDeletePost({
              ids: rows.map((item) => item.id!),
            })
              .then(() => {
                _dialog?.destroy?.();
                message.success(I18N.t('删除成功'));
                onChange(true);
                resolve();
              })
              .catch(reject);
          },
          onCancel: () => {
            resolve();
          },
        });
      });
    },
    {
      manual: true,
    },
  );
  const { run: batchAgree, loading: batchAgreeLoading } = useRequest(
    async (rows: API.TkshopSampleRequestAuditVo[]) => {
      return new Promise<void>((resolve, reject) => {
        const _dialog = DMConfirm({
          title: I18N.t('确定同意选中的') + rows.length + I18N.t('个索样吗？'),
          onOk(e: any) {
            add(
              {
                items: rows.map((item) => {
                  return {
                    taskType: 'TS_SampleApprove',
                    accountId: item.shopId,
                    parameter: JSON.stringify({
                      approve_apply_ids: [item.applyId!],
                      applyId: item.applyId,
                      handle: item.creator?.handle,
                    }),
                    creatorId: item.creator?.id,
                  };
                }),
              },
              e,
            )
              .then((res) => {
                _dialog?.destroy?.();
                const errorCount = res.data?.length || 0;
                if (errorCount) {
                  DMConfirm({
                    type: 'info',
                    width: 520,
                    title: I18N.t('在任务抽屉中创建了 {{count}} 笔索样审批任务', {
                      count: rows.length - errorCount,
                    }),
                    content:
                      errorCount > 0
                        ? I18N.t('有 {{count}} 笔索样审批，已由他人审批', {
                            count: errorCount,
                          })
                        : false,
                  });
                }
                resolve();
              })
              .catch(reject);
          },
          onCancel: () => {
            resolve();
          },
        });
      });
    },
    {
      manual: true,
    },
  );

  const nodes = useMemo(() => {
    return [
      {
        key: 'agree',
        node: (_props) => {
          return (
            <Button
              className={buttonStyles.successBtnGhost}
              loading={batchAgreeLoading}
              onClick={() => {
                if (disabled) {
                  return;
                }
                batchAgree(selected);
              }}
              icon={<IconFontIcon iconName={'Check-Circle_24'} />}
              {..._props}
            >
              {I18N.t('批量同意')}
            </Button>
          );
        },
      },
      {
        node: (_props) => {
          return (
            <Button
              danger
              loading={batchRejectLoading}
              onClick={() => {
                if (disabled) {
                  return;
                }
                batchReject(selected);
              }}
              icon={<IconFontIcon iconName={'Close-Circle_24'} />}
              {..._props}
            >
              {I18N.t('批量拒绝')}
            </Button>
          );
        },
        key: 'refuse',
      },
      {
        node: (_props) => {
          return (
            <Button
              onClick={() => {
                if (disabled) {
                  return;
                }
                const ids: number[] = [];
                selected.forEach((item) => {
                  if (item.pendingDrawer?.id) {
                    ids.push(item.pendingDrawer?.id);
                  }
                });
                DMConfirm({
                  width: 520,
                  iconType: 'confirm',
                  title: I18N.t('确定要撤销选中的所有的审批吗？'),
                  content: I18N.t('无论该审批是由您本人还是他人做出的，都会被撤销审批'),
                  onOk: async () => {
                    await tkshopTaskDrawerBatchDeletePost({
                      ids,
                    });
                    triggerUpdate();
                  },
                });
              }}
              icon={<IconFontIcon iconName={'Undo_24'} />}
              {..._props}
            >
              {I18N.t('批量撤销')}
            </Button>
          );
        },
        key: 'cancel',
      },
      {
        node: (_props) => {
          return (
            <Button
              loading={batchDeleteLoading}
              onClick={() => {
                if (disabled) {
                  return;
                }
                batchDelete(selected);
              }}
              icon={<IconFontIcon iconName={'Trash_24'} />}
              {..._props}
            >
              {I18N.t('批量删除')}
            </Button>
          );
        },
        key: 'delete',
      },
      {
        node: (_props) => {
          return (
            <Button
              disabled={disabled}
              icon={<IconFontIcon size={16} iconName={'daren_24'} />}
              onClick={() => {
                if (disabled) {
                  return;
                }
                GhostModalCaller(
                  <SampleGroupActionsModal
                    data={selected}
                    shopId={selected[0].shopId!}
                    onUpdate={() => {
                      onChange(false);
                    }}
                  />,
                );
              }}
              {..._props}
            >
              <span>达人批量操作</span>
            </Button>
          );
        },
        key: 'creators',
      },
    ];
  }, [
    batchAgreeLoading,
    disabled,
    batchAgree,
    selected,
    batchRejectLoading,
    batchReject,
    batchDeleteLoading,
    batchDelete,
    onChange,
  ]);

  return (
    <>
      <Tooltip placement={'topLeft'} title={disabled ? I18N.t('请至少选择一笔记录') : undefined}>
        <StyledOverflow disabled={disabled} itemWidth={110} data={nodes} />
      </Tooltip>
    </>
  );
}
export default SampleRequestByShopToReviewTableFooter;
