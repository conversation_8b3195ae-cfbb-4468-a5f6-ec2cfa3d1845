import { useEffect, useMemo, useState } from 'react';
import { Descriptions, InputNumber, Table, Typography } from 'antd';
import MiddleSpin from '@/components/Common/MiddleSpin';
import { useRequest } from 'umi';
import I18N from '@/i18n';
import constants from '@/constants';
import { GhostModalCaller } from '@/mixins/modal';
import BuyCombo from '@/pages/NewCombo/Order/BuyCombo';
import {
  tkshopCalcBuyTkshopPost,
  tkshopSystemConfigGet,
} from '@/services/api-TKShopAPI/TkshopSystemController';
import DescriptionsItem from 'antd/es/descriptions/Item';
import styled, { ThemeProvider } from 'styled-components';
import IconFontIcon from '@/components/Common/IconFontIcon';

const StyledCard = styled.div`
  width: 380px;
  height: 580px;
  background: white;
  position: relative;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: visible;
  transition: all 0.3s linear;
  &:hover {
    box-shadow: 0 0 16px 0 rgba(0, 0, 0, 0.08);
  }
  .title-section {
    display: flex;
    flex-direction: column;
    gap: 4px;
    justify-content: center;
    height: 100px;
    padding: 24px 32px;
    color: white;
    background-image: url(${(props) => props.theme.bg});
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .title {
      font-size: 20px;
    }
    .sub-title {
      color: rgba(255, 255, 255, 0.8);
      font-size: 16px;
    }
  }
  .profile {
    padding: 8px 12px 16px 12px;
    text-align: right;
    .sup {
      margin: 0 4px;
      color: ${(props) => props.theme.color};
      font-weight: bold;
      font-size: 30px;
      vertical-align: baseline;
    }
  }
  .section-divider {
    height: 30px;
    line-height: 30px;
    text-align: center;
    background: #f2f2f2;
  }
  .shop-price {
    padding: 16px 12px;
    color: #666666;
  }
  footer {
    position: absolute;
    right: 0;
    bottom: -52px;
    left: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;
    justify-content: center;
    button {
      width: 245px;
      height: 46px;
      color: white;
      font-size: 16px;
      background: ${(props) => props.theme.color};
      border: none;
      border-radius: 23px;
      outline: none;
      cursor: pointer;
    }
  }
  .form-section {
    position: relative;
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 16px;
    padding: 16px 12px;
    color: #666666;
    .ant-input-number {
      margin: 0 4px;
      input {
        font-weight: bold;
        font-size: 18px;
      }
    }
    .total-price {
      position: absolute;
      right: 12px;
      bottom: 36px;
      text-align: right;
      .sup {
        margin: 0 4px;
        color: ${(props) => props.theme.color};
        font-weight: bold;
        font-size: 46px;
        vertical-align: baseline;
      }
    }
  }
`;
const VersionCard = (props: {
  version: API.CreateBuyTkshopRequest['version'];
  data: API.TkshopVersionConfig;
}) => {
  const [shopCount, setShopCount] = useState(1);
  const { version, data } = props;
  const {
    run: calculatePrice,
    data: priceRes,
    loading: calculating,
  } = useRequest(
    () => {
      return tkshopCalcBuyTkshopPost({
        immediatePay: false,
        shopCount,
        version,
        periodUnit: '月',
        duration: 1,
        payType: 'BankPay',
      });
    },
    {
      formatResult(res) {
        return res.data?.payablePrice || 0;
      },
    },
  );
  useEffect(() => {
    if (shopCount) {
      calculatePrice();
    }
  }, [shopCount, calculatePrice]);
  return (
    <StyledCard className={'card'}>
      <section className={'title-section'}>
        <div className={'title'}>
          {version === 'TkshopStandard' ? I18N.t('标准版') : I18N.t('企业版')}
        </div>
        <div className={'sub-title'}>{I18N.t('计费标准')}</div>
      </section>
      <section className={'profile'}>
        <div>
          ￥<span className={'sup'}>{data?.basePrice}</span>
          {I18N.t('元/月')}
          <span style={{ color: '#999' }}>
            {version === 'TkshopStandard'
              ? I18N.t('（基础功能免费）')
              : I18N.t('（基础功能免费 + 企业级功能）')}
          </span>
        </div>
        <div>
          <span className={'sup'}>+</span>
          {I18N.t('按店铺数量计费')}
        </div>
      </section>
      <div className={'section-divider'}>{I18N.t('店铺数量计费标准')}</div>
      <section className={'shop-price'}>
        <Descriptions
          size="small"
          colon
          labelStyle={{
            width: 120,
            color: '#666',
          }}
          contentStyle={{
            color: '#666',
          }}
          bordered={false}
          column={1}
        >
          {data?.ladderPrices?.map((item, index) => {
            const { quantity, price } = item;
            const label =
              index === data?.ladderPrices?.length - 1
                ? I18N.t('店铺数量>{{count}}', {
                    count: quantity!,
                  })
                : I18N.t('店铺数量<={{count}}', {
                    count: quantity!,
                  });
            return (
              <DescriptionsItem key={quantity} label={label}>
                {I18N.t('每店铺 {{price}} 元/月', { price })}
              </DescriptionsItem>
            );
          })}
        </Descriptions>
      </section>
      <div className={'section-divider'}>
        {version === 'TkshopStandard' ? I18N.t('我的标准版价格') : I18N.t('我的企业版价格')}
      </div>
      <section className={'form-section'}>
        <div>
          {I18N.t('我有 {{input}} 家店铺', {
            input: (
              <InputNumber
                onBlur={(e) => {
                  if (!e.target.value) {
                    setShopCount(1);
                  }
                }}
                value={shopCount}
                onChange={setShopCount}
              />
            ),
          })}
        </div>
        <div>{I18N.t('我的价格：')}</div>
        <span className={'total-price'}>
          ￥<span className={'sup'}>{priceRes}</span>
          {I18N.t('元/月')}
        </span>
      </section>
      <footer>
        <button
          onClick={() => {
            GhostModalCaller(<BuyCombo version={version} shopCount={shopCount} />, 'BuyCombo');
          }}
        >
          {I18N.t('立即购买')}
        </button>
        <Typography.Link
          onClick={() => {
            window.open(constants.productSite + '/service/about');
          }}
        >
          {I18N.t('联系客服')}
        </Typography.Link>
      </footer>
    </StyledCard>
  );
};
export default function () {
  const { data } = useRequest(() => tkshopSystemConfigGet());

  const content = useMemo(() => {
    if (!data) {
      return <MiddleSpin />;
    }
    return (
      <div style={{ width: '815px', margin: '0 auto' }}>
        <div
          style={{
            textAlign: 'center',
            fontSize: 18,
            fontWeight: 'bold',
            marginBottom: 16,
          }}
        >
          {I18N.t('花漾TK达人营销决策与管理系统套餐')}
        </div>
        <header style={{ display: 'flex', gap: 24, marginBottom: 24 }}>
          <ThemeProvider
            theme={{
              color: '#92d050',
              bg: '/version_1.svg',
            }}
          >
            <VersionCard data={data.standard!} version={'TkshopStandard'} />
          </ThemeProvider>
          <ThemeProvider
            theme={{
              color: '#00b0f0',
              bg: '/version_4.svg',
            }}
          >
            <VersionCard data={data.enterprise!} version={'TkshopEnterprise'} />
          </ThemeProvider>
        </header>
        <div
          style={{
            textAlign: 'center',
            fontSize: 18,
            fontWeight: 'bold',
            marginTop: 96,
            marginBottom: 16,
          }}
        >
          {I18N.t('套餐权益')}
        </div>
        <Table
          columns={[
            {
              dataIndex: 'version',
              title: I18N.t('占位'),
              align: 'center',
              colSpan: 0,
              width: 60,
              onCell() {
                return {
                  style: {
                    writingMode: 'vertical-rl',
                    textOrientation: 'mixed',
                    letterSpacing: '0.3em',
                  },
                };
              },
              render(dom, record) {
                const { version, rowSpan } = record;
                if (version === 'TkshopStandard') {
                  return {
                    children: I18N.t('基础功能'),
                    props: {
                      rowSpan: rowSpan || 0,
                    },
                  };
                }
                return {
                  children: I18N.t('企业级功能'),
                  props: {
                    rowSpan: rowSpan || 0,
                  },
                };
              },
            },
            {
              dataIndex: 'label',
              title: I18N.t('功能'),
              align: 'center',
              colSpan: 2,
            },
            {
              dataIndex: 'TkshopStandard',
              title: I18N.t('标准版'),
              onHeaderCell() {
                return {
                  style: {
                    background: '#e9f3df',
                  },
                };
              },
              onCell() {
                return {
                  style: {
                    background: '#f6f8f5',
                  },
                };
              },
              align: 'center',
              render: (dom, record) => {
                const { TkshopStandard } = record;
                if (TkshopStandard === true) {
                  return (
                    <Typography.Text style={{ fontWeight: 'bold' }} type={'success'}>
                      <IconFontIcon iconName={'check_24'} />
                    </Typography.Text>
                  );
                }
                if (TkshopStandard === false) {
                  return (
                    <Typography.Text style={{ fontWeight: 'bold' }} type={'danger'}>
                      <IconFontIcon iconName={'guanbi_24'} />
                    </Typography.Text>
                  );
                }
                return TkshopStandard;
              },
            },
            {
              dataIndex: 'TkshopEnterprise',
              title: I18N.t('企业版'),
              align: 'center',
              onHeaderCell() {
                return {
                  style: {
                    background: '#bee8f8',
                  },
                };
              },
              onCell() {
                return {
                  style: {
                    background: '#f4f7fb',
                  },
                };
              },
              render: (dom, record) => {
                const { TkshopEnterprise } = record;
                if (TkshopEnterprise === true) {
                  return (
                    <Typography.Text style={{ fontWeight: 'bold' }} type={'success'}>
                      <IconFontIcon iconName={'check_24'} />
                    </Typography.Text>
                  );
                }
                if (TkshopEnterprise === false) {
                  return (
                    <Typography.Text style={{ fontWeight: 'bold' }} type={'danger'}>
                      <IconFontIcon iconName={'guanbi_24'} />
                    </Typography.Text>
                  );
                }
                return TkshopEnterprise;
              },
            },
          ]}
          pagination={false}
          bordered
          size={'small'}
          dataSource={[
            {
              label: '支持的站点',
              version: 'TkshopStandard',
              rowSpan: 21,
              TkshopStandard: I18N.t('全站点(本土/跨境)'),
              TkshopEnterprise: I18N.t('全站点(本土/跨境)'),
            },
            {
              label: '批量邀请达人数量',
              version: 'TkshopStandard',
              TkshopStandard:
                data?.standard?.targetPlanQuota > -1
                  ? I18N.t('{{count}}人/天/店铺', {
                      count: data?.standard?.targetPlanQuota,
                    })
                  : I18N.t('不限'),
              TkshopEnterprise:
                data?.enterprise?.targetPlanQuota > -1
                  ? I18N.t('{{count}}人/天/店铺', {
                      count: data?.enterprise?.targetPlanQuota,
                    })
                  : I18N.t('不限'),
            },
            {
              label: '批量私信达人数量',
              version: 'TkshopStandard',
              TkshopStandard:
                data?.standard?.imChatQuota > -1
                  ? I18N.t('{{count}}人/天/店铺', {
                      count: data?.standard?.imChatQuota,
                    })
                  : I18N.t('不限'),
              TkshopEnterprise:
                data?.enterprise?.imChatQuota > -1
                  ? I18N.t('{{count}}人/天/店铺', {
                      count: data?.enterprise?.imChatQuota,
                    })
                  : I18N.t('不限'),
            },
            {
              label: '批量私信买家数量',
              version: 'TkshopStandard',
              TkshopStandard:
                data?.standard?.buyerChatQuota > -1
                  ? I18N.t('{{count}}人/天/店铺', {
                      count: data?.standard?.buyerChatQuota,
                    })
                  : I18N.t('不限'),
              TkshopEnterprise:
                data?.enterprise?.buyerChatQuota > -1
                  ? I18N.t('{{count}}人/天/店铺', {
                      count: data?.enterprise?.buyerChatQuota,
                    })
                  : I18N.t('不限'),
            },
            {
              label: '特别关注的带货视频数量',
              version: 'TkshopStandard',
              TkshopStandard:
                data?.standard?.syncVideoQuota > -1
                  ? data?.standard?.syncVideoQuota
                  : I18N.t('不限'),
              TkshopEnterprise:
                data?.enterprise?.syncVideoQuota > -1
                  ? data?.enterprise?.syncVideoQuota
                  : I18N.t('不限'),
            },
            {
              label: '团队成员(子账号)数量',
              version: 'TkshopStandard',
              TkshopStandard:
                data?.standard?.baseQuota?.['TeamMemberQuota'] > -1
                  ? data?.standard?.baseQuota?.['TeamMemberQuota']
                  : I18N.t('不限'),
              TkshopEnterprise:
                data?.enterprise?.baseQuota?.['TeamMemberQuota'] > -1
                  ? data?.enterprise?.baseQuota?.['TeamMemberQuota']
                  : I18N.t('不限'),
            },
            {
              label: '邮件批量发送',
              version: 'TkshopStandard',
              TkshopStandard: I18N.t('不限'),
              TkshopEnterprise: I18N.t('不限'),
            },
            {
              label: '达人管理系统',
              version: 'TkshopStandard',
              TkshopStandard: true,
              TkshopEnterprise: true,
            },
            {
              label: '买家管理系统',
              version: 'TkshopStandard',
              TkshopStandard: true,
              TkshopEnterprise: true,
            },
            {
              label: '达人联系方式抓取',
              version: 'TkshopStandard',
              TkshopStandard: true,
              TkshopEnterprise: true,
            },
            {
              label: '百万海外达人库',
              version: 'TkshopStandard',
              TkshopStandard: true,
              TkshopEnterprise: true,
            },
            {
              label: '达人带货视频直播记录',
              version: 'TkshopStandard',
              TkshopStandard: true,
              TkshopEnterprise: true,
            },
            {
              label: '抓取榜单达人',
              version: 'TkshopStandard',
              TkshopStandard: true,
              TkshopEnterprise: true,
            },
            {
              label: '多店铺同时邀约',
              version: 'TkshopStandard',
              TkshopStandard: true,
              TkshopEnterprise: true,
            },
            {
              label: '定时自动邀约(流程编排)',
              version: 'TkshopStandard',
              TkshopStandard: true,
              TkshopEnterprise: true,
            },
            {
              label: '达人导入与导出',
              version: 'TkshopStandard',
              TkshopStandard: true,
              TkshopEnterprise: true,
            },
            {
              label: '过滤已邀约达人',
              version: 'TkshopStandard',
              TkshopStandard: true,
              TkshopEnterprise: true,
            },
            {
              label: '清理无效计划',
              version: 'TkshopStandard',
              TkshopStandard: true,
              TkshopEnterprise: true,
            },
            {
              label: '批量私信买家',
              version: 'TkshopStandard',
              TkshopStandard: true,
              TkshopEnterprise: true,
            },
            {
              label: '买家管理系统',
              version: 'TkshopStandard',
              TkshopStandard: true,
              TkshopEnterprise: true,
            },
            {
              label: '买家手机号码抓取',
              version: 'TkshopStandard',
              TkshopStandard: true,
              TkshopEnterprise: true,
            },
            {
              label: '公海达人库联系方式获取与导入',
              rowSpan: 7,
              version: 'TkshopEnterprise',
              TkshopStandard: false,
              TkshopEnterprise: true,
            },
            {
              label: '手机群控管理',
              version: 'TkshopEnterprise',
              TkshopStandard: false,
              TkshopEnterprise: true,
            },
            {
              label: 'Whatsapp批量建联与群发消息',
              version: 'TkshopEnterprise',
              TkshopStandard: false,
              TkshopEnterprise: true,
            },
            {
              label: 'Line批量建联与群发消息',
              version: 'TkshopEnterprise',
              TkshopStandard: false,
              TkshopEnterprise: true,
            },
            {
              label: 'Facebook Messenger 批量建联与群发消息',
              version: 'TkshopEnterprise',
              TkshopStandard: false,
              TkshopEnterprise: true,
            },
            {
              label: '买家手机号建联',
              version: 'TkshopEnterprise',
              TkshopStandard: false,
              TkshopEnterprise: true,
            },
            {
              label: '达人的带货视频分析',
              version: 'TkshopEnterprise',
              TkshopStandard: false,
              TkshopEnterprise: true,
            },
          ]}
        />
      </div>
    );
  }, [data]);

  return (
    <div
      style={{
        background: '#f8f8f8',
        width: '100%',
        minHeight: '100%',
        overflow: 'auto',
        padding: 32,
      }}
    >
      {content}
    </div>
  );
}
