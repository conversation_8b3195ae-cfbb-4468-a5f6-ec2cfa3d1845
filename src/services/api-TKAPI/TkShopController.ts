// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 导入TK店铺 POST /api/tk/shop/ */
export async function tkShopPost(body: API.TkShopImportParamVo, options?: { [key: string]: any }) {
  return request<API.WebResultListShopDto>('/api/tk/shop/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据协调ID查询所有店铺的区域列表 GET /api/tk/shop/areaListByCoordinateId/${param0} */
export async function tkShopAreaListByCoordinateIdByCoordinateIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkShopAreaListByCoordinateIdByCoordinateIdGetParams,
  options?: { [key: string]: any },
) {
  const { coordinateId: param0, ...queryParams } = params;
  return request<API.WebResultListstring>(`/api/tk/shop/areaListByCoordinateId/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 根据协调ID查询所有店铺列表 GET /api/tk/shop/byCoordinateId/${param0} */
export async function tkShopByCoordinateIdByCoordinateIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkShopByCoordinateIdByCoordinateIdGetParams,
  options?: { [key: string]: any },
) {
  const { coordinateId: param0, ...queryParams } = params;
  return request<API.WebResultListShopDto>(`/api/tk/shop/byCoordinateId/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 扩展店铺的区域（新增其他区域的店铺） POST /api/tk/shop/byPlatforms */
export async function tkShopByPlatformsPost(
  body: API.TkShopAddRegionRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListShopDto>('/api/tk/shop/byPlatforms', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建赠送IP POST /api/tk/shop/presentIp */
export async function tkShopPresentIpPost(
  body: API.CreatePresentIpRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tk/shop/presentIp', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
