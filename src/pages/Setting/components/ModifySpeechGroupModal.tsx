import DMModal from '@/components/Common/Modal/DMModal';
import type { GhostModalWrapperComponentProps } from '@/mixins/modal';
import type { InputRef } from 'antd';
import { Button, Form, Input, message, Space } from 'antd';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useRequest } from 'umi';
import _ from 'lodash';
import {
  ghSpeechGroupByGroupIdDelete,
  ghSpeechGroupByIdNamePut,
  ghSpeechGroupCreatePost,
} from '@/services/api-TKGHAPI/GhSpeechController';
import DMFormItem, { DMFormItemContext } from '@/components/Common/DMFormItem';
import { trimValues } from '@/utils/utils';
import DMConfirm from '@/components/Common/DMConfirm';
import buttonStyles from '@/style/button.less';

const ModifySpeechGroupModal = (
  props: GhostModalWrapperComponentProps & {
    group: Pick<API.GhSpeechGroupDto, 'id' | 'name' | 'bizScene' | 'speechType'>;
    onUpdate: (groupId?: number) => void;
    showDelete?: boolean;
  },
) => {
  const { group, modalProps, onUpdate, showDelete } = props;
  const { bizScene, speechType, id } = group;
  const [open, setOpen] = useState(true);
  const [form] = Form.useForm();
  const inputRef = useRef<InputRef>(null);
  const { run: submit, loading } = useRequest(
    async () => {
      const values = await form.validateFields();
      const { name } = trimValues(values);
      if (id) {
        await ghSpeechGroupByIdNamePut({
          id,
          name,
        });
        message.success('修改成功');
        onUpdate();
      } else {
        const res = await ghSpeechGroupCreatePost({
          name,
          bizScene,
          speechType,
        });
        onUpdate(res.data?.id);
        message.success('创建成功');
      }
      setOpen(false);
    },
    {
      manual: true,
    },
  );

  useEffect(() => {
    setTimeout(() => {
      inputRef.current?.focus();
    }, 300);
  }, [open]);
  const deleteGroup = useCallback(() => {
    DMConfirm({
      width: 460,
      title: '确定要删除此话术分组吗？',
      content: '删除话术分组会同步删除此分组下的所有话术',
      onOk() {
        setOpen(false);
        ghSpeechGroupByGroupIdDelete({
          groupId: id!,
          confirm: true,
        }).then(() => {
          onUpdate();
        });
      },
    });
  }, [id, onUpdate]);
  const footer = useMemo(() => {
    return (
      <Space>
        {showDelete && (
          <Button className={buttonStyles.dangerBtn} onClick={deleteGroup}>
            删除
          </Button>
        )}
        <Button type={'primary'} onClick={submit} loading={loading}>
          确定
        </Button>
        <Button
          onClick={() => {
            setOpen(false);
          }}
        >
          取消
        </Button>
      </Space>
    );
  }, [deleteGroup, loading, showDelete, submit]);

  return (
    <DMModal
      onOk={submit}
      open={open}
      onEnter={submit}
      footer={footer}
      bodyStyle={{
        paddingBottom: 0,
      }}
      onCancel={() => setOpen(false)}
      title={group?.id ? '修改话术分组' : '创建话术分组'}
      {...modalProps}
    >
      <Form form={form} initialValues={group}>
        <DMFormItemContext.Provider value={{ labelWidth: 80 }}>
          <DMFormItem
            name={'name'}
            label={'分组名称'}
            rules={[
              {
                validator(rule, value) {
                  const _value = _.trim(value);
                  if (!_value) {
                    return Promise.reject('请输入话术分组名称');
                  }
                  if (_value.length > 64) {
                    return Promise.reject('话术分组名称不能超过64个字符');
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Input ref={inputRef} />
          </DMFormItem>
        </DMFormItemContext.Provider>
      </Form>
    </DMModal>
  );
};

export default ModifySpeechGroupModal;
