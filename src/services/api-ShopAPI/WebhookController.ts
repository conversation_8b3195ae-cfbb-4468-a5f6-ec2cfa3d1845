// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 发起一个长时间的请求 GET /api/webhook/longRequest */
export async function webhookLongRequestGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.webhookLongRequestGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/webhook/longRequest', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 记录访问页面 POST /api/webhook/tg/access */
export async function webhookTgAccessPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.webhookTgAccessPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/webhook/tg/access', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 添加下载记录 POST /api/webhook/tg/download */
export async function webhookTgDownloadPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.webhookTgDownloadPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/webhook/tg/download', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
