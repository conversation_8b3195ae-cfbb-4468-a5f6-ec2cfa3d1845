declare namespace API {
  type CreateFileResult = {
    exists?: boolean;
    fileId?: number;
    filePath?: string;
    userHeaders?: Record<string, any>;
  };

  type DepartmentDto = {
    createTime?: string;
    hidden?: boolean;
    id?: number;
    invitingAuditEnabled?: boolean;
    invitingEnabled?: boolean;
    name?: string;
    parentId?: number;
    sortNumber?: number;
    teamId?: number;
  };

  type diskCheckPathGrantedGetParams = {
    /** path */
    path: string;
  };

  type diskCollectPostParams = {
    /** diskType */
    diskType: 'LocalDisk' | 'TeamDisk' | 'UserDisk';
    /** name */
    name: string;
  };

  type diskCopyPostParams = {
    /** sourceDiskType */
    sourceDiskType: 'LocalDisk' | 'TeamDisk' | 'UserDisk';
    /** targetDiskType */
    targetDiskType: 'LocalDisk' | 'TeamDisk' | 'UserDisk';
    /** srcName */
    srcName: string;
    /** targetName */
    targetName: string;
  };

  type diskDirectoryPostParams = {
    /** 相对网盘根目录的目录名 */
    name: string;
    /** diskType */
    diskType: 'LocalDisk' | 'TeamDisk' | 'UserDisk';
  };

  type diskFavoritesPageGetParams = {
    /** collectTimeFrom */
    collectTimeFrom?: string;
    /** collectTimeTo */
    collectTimeTo?: string;
    /** query */
    query?: string;
    /** sortField */
    sortField?: 'collectTime' | 'id' | 'name' | 'size';
    /** sortOrder */
    sortOrder?: 'asc' | 'desc';
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type diskFileByFileIdFinishedPutParams = {
    /** fileId */
    fileId: number;
    /** status */
    status: 'Abort' | 'Fail' | 'Pending' | 'Running' | 'Success' | 'Timeout';
    /** remarks */
    remarks?: string;
  };

  type DiskFileItem = {
    category?: 'RecordSlice' | 'RpaFile' | 'ShopData' | 'TeamDisk' | 'UserDisk';
    filePath?: string;
    id?: number;
    lastModified?: string;
    name?: string;
    size?: number;
  };

  type diskFileOrDirectoryDeleteParams = {
    /** 相对网盘根目录的目录名或文件名 */
    name: string;
    /** diskType */
    diskType: 'LocalDisk' | 'TeamDisk' | 'UserDisk';
  };

  type diskFilePostParams = {
    /** 相对网盘根目录的文件名 */
    name: string;
    /** mineType */
    mineType?: string;
    /** diskType */
    diskType: 'LocalDisk' | 'TeamDisk' | 'UserDisk';
    /** size */
    size: number;
    /** confirm */
    confirm?: boolean;
  };

  type diskMovePostParams = {
    /** sourceDiskType */
    sourceDiskType: 'LocalDisk' | 'TeamDisk' | 'UserDisk';
    /** targetDiskType */
    targetDiskType: 'LocalDisk' | 'TeamDisk' | 'UserDisk';
    /** srcName */
    srcName: string;
    /** targetName */
    targetName: string;
  };

  type diskPrepareNameGetParams = {
    /** diskType */
    diskType: 'LocalDisk' | 'TeamDisk' | 'UserDisk';
    /** name */
    name: string;
  };

  type diskPreSignUrlGetParams = {
    /** diskType */
    diskType: 'LocalDisk' | 'TeamDisk' | 'UserDisk';
    /** name */
    name: string;
  };

  type DiskRootGrant2UsersRequest = {
    /** 清除网盘文件已有的用户与组织单元授权关系 */
    cleanFirst?: boolean;
    confirm?: boolean;
    departmentIdList?: number[];
    files?: string[];
    userIdList?: number[];
  };

  type DiskSpacePriceVo = {
    /** 昨日消耗花瓣 */
    creditYesterday?: number;
    /** 单价,花瓣/GB*天 */
    price?: number;
    /** 免费配额，GB */
    quotaTraffic?: number;
    storageAlertCredit?: number;
  };

  type DiskStatVo = {
    myUserDiskSize?: number;
    otherSize?: number;
    otherUserDiskSize?: number;
    recordFileSize?: number;
    rpaFileSize?: number;
    shopDataSize?: number;
    teamDiskSize?: number;
  };

  type DiskStsVo = {
    baseDir?: string;
    downloadEndpoint?: string;
    stsToken?: StsPostSignature;
  };

  type diskTeamListGetParams = {
    /** nextMarker */
    nextMarker?: string;
    /** path */
    path?: string;
    /** limit */
    limit?: number;
  };

  type diskTeamRootGetParams = {
    /** path */
    path: string;
  };

  type DiskTreeNode = {
    fullPath?: string;
    lastModified?: string;
    name?: string;
    size?: number;
    subNodes?: any[];
  };

  type diskTriggerChangedGetParams = {
    /** path */
    path: string;
    /** eventType */
    eventType: 'Created' | 'Deleted' | 'Modified';
  };

  type diskUpdateFileKeepDaysPutParams = {
    /** recordKeepDays */
    recordKeepDays: number;
    /** rpaKeepDays */
    rpaKeepDays: number;
  };

  type diskUpdateStorageAlertPutParams = {
    /** storageAlertCredit */
    storageAlertCredit: number;
  };

  type FavoriteDto = {
    collectTime?: string;
    collector?: number;
    id?: number;
    name?: string;
    resourceId?: number;
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    size?: number;
    teamId?: number;
    url?: string;
  };

  type FileKeepDaysVo = {
    recordKeepDays?: number;
    rpaKeepDays?: number;
    teamId?: number;
  };

  type MemberVo = {
    /** 账号 */
    account?: string;
    avatar?: string;
    createTime?: string;
    /** 邮箱 */
    email?: string;
    gender?: 'FEMALE' | 'MALE' | 'UNSPECIFIC';
    id?: number;
    nickname?: string;
    partnerId?: number;
    /** 手机 */
    phone?: string;
    residentCity?: string;
    roleCode?: 'boss' | 'manager' | 'staff' | 'superadmin';
    signature?: string;
    status?: 'ACTIVE' | 'BLOCK' | 'DELETED' | 'INACTIVE';
    teamNickname?: string;
    tenant?: number;
    userType?: 'NORMAL' | 'PARTNER' | 'SHADOW';
    weixin?: string;
  };

  type OssListResult = {
    files?: StorageItemMeta[];
    nextMarker?: string;
    truncated?: boolean;
  };

  type PageResultFavoriteDto = {
    current?: number;
    list?: FavoriteDto[];
    pageSize?: number;
    total?: number;
  };

  type StorageItemMeta = {
    broken?: boolean;
    contentLength?: number;
    createTime?: string;
    lastModified?: string;
    md5sum?: string;
    path?: string;
    userMetadata?: Record<string, any>;
  };

  type StsPostSignature = {
    accessKeyId?: string;
    accessKeySecret?: string;
    bucketName?: string;
    expiration?: string;
    fileVal?: string;
    policy?: string;
    provider?: string;
    region?: string;
    securityToken?: string;
    serverTime?: string;
    url?: string;
  };

  type TeamDiskRootVo = {
    bucketId?: number;
    createTime?: string;
    creatorId?: number;
    filePath?: string;
    folder?: boolean;
    /** 授权的部门 */
    grantDepartmentList?: DepartmentDto[];
    /** 授权给的用户 */
    grantUserVoList?: MemberVo[];
    id?: number;
    mineType?: string;
    name?: string;
    remarks?: string;
    size?: number;
    teamId?: number;
  };

  type WebResult = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultboolean = {
    code?: number;
    data?: boolean;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCreateFileResult = {
    code?: number;
    data?: CreateFileResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultDiskSpacePriceVo = {
    code?: number;
    data?: DiskSpacePriceVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultDiskStatVo = {
    code?: number;
    data?: DiskStatVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultDiskStsVo = {
    code?: number;
    data?: DiskStsVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultFileKeepDaysVo = {
    code?: number;
    data?: FileKeepDaysVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListDiskFileItem = {
    code?: number;
    data?: DiskFileItem[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListDiskTreeNode = {
    code?: number;
    data?: DiskTreeNode[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultOssListResult = {
    code?: number;
    data?: OssListResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultFavoriteDto = {
    code?: number;
    data?: PageResultFavoriteDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultstring = {
    code?: number;
    data?: string;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamDiskRootVo = {
    code?: number;
    data?: TeamDiskRootVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };
}
