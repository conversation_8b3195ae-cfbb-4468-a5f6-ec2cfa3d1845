import I18N from '@/i18n';
import { useVT } from 'virtualizedtableforantd4';
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import type { ActionType } from '@ant-design/pro-table';
import { ProTable } from '@ant-design/pro-table';
import { scrollProTableOptionFn } from '@/mixins/table';
import { Button, ConfigProvider, Space, Tooltip } from 'antd';
import EmptyView from '@/components/Common/EmptyView';
import { GhostModalCaller } from '@/mixins/modal';
import MiddleSpin from '@/components/Common/MiddleSpin';
import { StyledTableWrapper } from '@/style/styled';
import RemarkModal from '@/components/Common/RemarkModal';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { useRequest } from '@@/plugin-request/request';
import { versionAlert } from '@/pages/VideoManage/components/utils';
import DMFormItem from '@/components/Common/DMFormItem';
import {
  tkshopLivePagePost,
  tkshopLiveRemarkPut,
} from '@/services/api-TKShopAPI/TkshopLiveController';
import LiveGroupActionsModal from '@/pages/LiveManage/components/LiveGroupActionsModal';
import useLiveHeader from '@/pages/LiveManage/components/useLiveHeader';
import { getVersionValid } from '@/hooks/useVersionValid';

const LiveTable = forwardRef((props, ref) => {
  const actionRef = useRef<ActionType>();
  const [selectedVos, changeSelectedVos] = useState<API.TkshopVideoVo[]>([]);
  const [vt] = useVT(() => ({ scroll: { y: '100%' } }), []);
  const loaded = useRef(false);
  const [tableLoading, setTableLoading] = useState(true);
  const onReset = useCallback((reset?: boolean) => {
    if (reset) {
      changeSelectedVos([]);
      actionRef.current?.reloadAndRest?.();
    } else {
      actionRef.current?.reload?.();
    }
  }, []);

  const {
    header,
    getSearchParams,
    getSortParams,
    columns,
    tableHeader,
    tableWidth,
    isInitialized,
  } = useLiveHeader({
    onChange: onReset,
  });

  useImperativeHandle(
    ref,
    () => {
      return {
        refresh: (reset?: boolean) => {
          return onReset(reset);
        },
      };
    },
    [onReset],
  );
  useEffect(() => {
    onReset(true);
  }, [onReset]);
  const selectedIds = useMemo(() => {
    if (selectedVos?.length) {
      return selectedVos.map((item) => item.id!);
    }
    return [];
  }, [selectedVos]);

  const { run: openLiveRemarkModal, loading: remarking } = useRequest(
    async (selected: any[]) => {
      if (remarking) {
        return;
      }
      const valid = await getVersionValid();
      if (!valid) {
        versionAlert();
      } else {
        const remark = selected?.[0].remark || '';
        GhostModalCaller(
          <RemarkModal
            placeholder={'通过对直播添加备注方便您日后查询（最多1000字）'}
            title={selected.length > 1 ? I18N.t('批量编辑直播备注') : I18N.t('编辑直播备注')}
            prefix={
              selected.length > 1 ? (
                <DMFormItem label={I18N.t('选择的直播')}>
                  {I18N.t('{{count}}个', {
                    count: selected.length.toLocaleString(),
                  })}
                </DMFormItem>
              ) : undefined
            }
            value={remark}
            onSubmit={async (val) => {
              await tkshopLiveRemarkPut({
                ids: selected.map((item) => item.id),
                remark: val,
              });
              onReset(false);
            }}
          />,
          'RemarkModal',
        );
      }
    },
    {
      manual: true,
    },
  );
  const { run: groupCreator, loading: grouping } = useRequest(
    async () => {
      if (grouping) {
        return;
      }
      if (!(await getVersionValid())) {
        versionAlert();
        return;
      }
      GhostModalCaller(
        <LiveGroupActionsModal
          lives={selectedVos}
          onUpdate={() => {
            onReset(false);
          }}
        />,
      );
    },
    {
      manual: true,
    },
  );
  const table = useMemo(() => {
    if (!columns.length) {
      return <MiddleSpin />;
    }
    return (
      <ProTable<API.TkshopVideoVo>
        components={{ ...vt, header: tableHeader }}
        request={async (_params) => {
          setTableLoading(true);
          try {
            const { current, pageSize } = _params;
            const payload: API.PageLiveRequest = {
              pageNum: current,
              pageSize,
              // eslint-disable-next-line @typescript-eslint/no-use-before-define
              ...getSortParams(),
              // eslint-disable-next-line @typescript-eslint/no-use-before-define
              ...getSearchParams(),
            };
            if (payload.shopId) {
              payload.shopIds = [payload.shopId];
            }
            const res = await tkshopLivePagePost(payload);
            loaded.current = true;
            return {
              data: res.data?.list,
              total: res.data?.total,
            };
          } finally {
            setTableLoading(false);
          }
        }}
        key={'table'}
        actionRef={actionRef}
        rowSelection={{
          selectedRowKeys: selectedIds,
          onChange(keys, creators) {
            changeSelectedVos(creators);
          },
        }}
        columns={columns}
        style={
          !isInitialized
            ? {
                pointerEvents: 'none',
                opacity: 0,
              }
            : {}
        }
        {...scrollProTableOptionFn({
          pageId: 'tkshop_live_manage',
          scroll: {
            x: tableWidth,
          },
          alwaysShowFooter: true,
          footer: () => {
            const disabled = selectedVos?.length === 0;

            return (
              <div
                style={{
                  display: 'flex',
                  flex: 1,
                  gap: 8,
                  flexWrap: 'nowrap',
                  alignItems: 'center',
                }}
              >
                <Tooltip placement={'topLeft'} title={disabled ? '请选择直播后操作' : false}>
                  <Space>
                    <Button
                      ghost
                      type={'primary'}
                      disabled={disabled}
                      loading={remarking}
                      icon={<IconFontIcon iconName={'edit_24'} />}
                      onClick={() => {
                        if (disabled) {
                          return;
                        }
                        openLiveRemarkModal(selectedVos);
                      }}
                    >
                      <span>编辑直播备注</span>
                    </Button>
                    <Button
                      disabled={disabled}
                      type={'primary'}
                      ghost
                      icon={<IconFontIcon size={16} iconName={'daren_24'} />}
                      onClick={async () => {
                        if (disabled) {
                          return;
                        }
                        groupCreator();
                      }}
                    >
                      <span>达人批量操作</span>
                    </Button>
                  </Space>
                </Tooltip>
              </div>
            );
          },
        })}
      />
    );
  }, [
    columns,
    getSearchParams,
    getSortParams,
    groupCreator,
    isInitialized,
    openLiveRemarkModal,
    remarking,
    selectedIds,
    selectedVos,
    tableHeader,
    tableWidth,
    vt,
  ]);

  return (
    <>
      <div className="header" style={tableLoading ? { pointerEvents: 'none' } : {}}>
        {header}
      </div>
      <main className="main">
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: 8,
            overflow: 'hidden',
            height: '100%',
          }}
        >
          <ConfigProvider
            renderEmpty={() => {
              return <EmptyView description={I18N.t('暂无数据')} />;
            }}
          >
            <StyledTableWrapper style={{ flex: 1 }}>{table}</StyledTableWrapper>
          </ConfigProvider>
        </div>
      </main>
    </>
  );
});
export default LiveTable;
