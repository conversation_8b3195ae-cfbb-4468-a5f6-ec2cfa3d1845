// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取某个插件信息 GET /api/extensions/${param0} */
export async function extensionsByExtensionIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.extensionsByExtensionIdGetParams,
  options?: { [key: string]: any },
) {
  const { extensionId: param0, ...queryParams } = params;
  return request<API.WebResultExtensionsVo>(`/api/extensions/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取插件的图片 GET /api/extensions/${param0}/images */
export async function extensionsByExtensionIdImagesGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.extensionsByExtensionIdImagesGetParams,
  options?: { [key: string]: any },
) {
  const { extensionId: param0, ...queryParams } = params;
  return request<API.WebResultListExtensionImageVo>(`/api/extensions/${param0}/images`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 重新导入一个chrome市场上的插件 PUT /api/extensions/${param0}/refresh */
export async function extensionsByExtensionIdRefreshPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.extensionsByExtensionIdRefreshPutParams,
  options?: { [key: string]: any },
) {
  const { extensionId: param0, ...queryParams } = params;
  return request<API.WebResultExtensionsVo>(`/api/extensions/${param0}/refresh`, {
    method: 'PUT',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 批量将插件添加团队 PUT /api/extensions/addExtensionsToTeam */
export async function extensionsAddExtensionsToTeamPut(
  body: API.AddExtensionsToTeamRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/extensions/addExtensionsToTeam', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 将插件批量添加到一些账户 PUT /api/extensions/addExtensionToShops */
export async function extensionsAddExtensionToShopsPut(
  body: API.AddExtensionToShopsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/extensions/addExtensionToShops', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 申请将某个插件上架到系统中，返回申请id POST /api/extensions/applyExtension */
export async function extensionsApplyExtensionPost(
  body: API.ApplyExtensionRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultlong>('/api/extensions/applyExtension', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 导入一个chrome市场上的插件 PUT /api/extensions/importExtension */
export async function extensionsImportExtensionPut(body: string, options?: { [key: string]: any }) {
  return request<API.WebResultExtensionsVo>('/api/extensions/importExtension', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页获取插件列表 GET /api/extensions/list */
export async function extensionsListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.extensionsListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultExtensionsVo>('/api/extensions/list', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取插件统计信息 GET /api/extensions/statList */
export async function extensionsStatListGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTeamExtensionStatVo>('/api/extensions/statList', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 根据插件市场的id获取插件列表（同一个插件允许导入两次？） GET /api/extensions/store/${param0} */
export async function extensionsStoreByStoreIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.extensionsStoreByStoreIdGetParams,
  options?: { [key: string]: any },
) {
  const { storeId: param0, ...queryParams } = params;
  return request<API.WebResultListExtensionsVo>(`/api/extensions/store/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 同步的插件ID列表 GET /api/extensions/syncExtensionIds */
export async function extensionsSyncExtensionIdsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListlong>('/api/extensions/syncExtensionIds', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 设置同步插件ID列表 PUT /api/extensions/syncExtensionIds */
export async function extensionsSyncExtensionIdsPut(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/extensions/syncExtensionIds', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
