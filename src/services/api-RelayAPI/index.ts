// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：
import * as IpgoController from './IpgoController';
import * as ProxyController from './ProxyController';
import * as ProxyRemoteController from './ProxyRemoteController';
import * as RemoteApiWorkerServiceImpl from './RemoteApiWorkerServiceImpl';
import * as RemoteTunnelServiceImpl from './RemoteTunnelServiceImpl';
import * as TunnelTransitController from './TunnelTransitController';
export default {
  IpgoController,
  ProxyController,
  ProxyRemoteController,
  RemoteApiWorkerServiceImpl,
  RemoteTunnelServiceImpl,
  TunnelTransitController,
};
