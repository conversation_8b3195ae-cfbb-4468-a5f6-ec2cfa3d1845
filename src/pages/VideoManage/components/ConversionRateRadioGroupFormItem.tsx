import DMFormItem from '@/components/Common/DMFormItem';
import type { SystemConfig } from '@/hooks/useSystemConfig';
import I18N from '@/i18n';
import { Checkbox, Col, Form, InputNumber, Radio, Row } from 'antd';
import _ from 'lodash';

const ConversionRateRadioGroupFormItem = (props: { config: SystemConfig }) => {
  const { config } = props;
  return (
    <Form.Item shouldUpdate noStyle>
      {(f) => {
        const { _searchByOrdersPm, _orderPmType } = f.getFieldsValue();
        const checked = _searchByOrdersPm;
        return (
          <DMFormItem
            tooltip={I18N.t('优秀、正常、较差的划分区间可在系统设置中查看')}
            label={
              <Form.Item
                noStyle
                name={'_searchByOrdersPm'}
                valuePropName={'checked'}
                initialValue={false}
              >
                <Checkbox style={{ color: 'inherit' }}>千次播放转化率</Checkbox>
              </Form.Item>
            }
          >
            <Form.Item style={{ marginBottom: 0 }}>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'stretch',
                  gap: 12,
                }}
              >
                <Form.Item noStyle name={'_orderPmType'} initialValue="good">
                  <Radio.Group
                    style={{ display: 'flex', flexDirection: 'column', gap: 8 }}
                    onChange={(e) => {
                      f.setFieldValue('_searchByOrdersPm', true);
                      // 根据选中的值，先清空,再设置orderPmFrom，orderPmTo的值
                      const { value } = e.target;
                      if (value !== 'Range') {
                        f.setFieldValue('ordersPmFrom', undefined);
                        f.setFieldValue('ordersPmTo', undefined);
                      }
                      if (value === 'good') {
                        f.setFieldValue('ordersPmFrom', config?.ordersPmQueryVo?.goodRate);
                      } else if (value === 'normal') {
                        f.setFieldValue('ordersPmFrom', config?.ordersPmQueryVo?.badRate);
                        f.setFieldValue('ordersPmTo', config?.ordersPmQueryVo?.goodRate);
                      } else if (value === 'bad') {
                        f.setFieldValue('ordersPmTo', config?.ordersPmQueryVo?.badRate);
                      }
                    }}
                  >
                    <Row gutter={[16, 8]}>
                      <Col span={8}>
                        <Radio
                          style={{ lineHeight: '32px' }}
                          className={checked ? '' : 'ant-radio-disabled'}
                          value={'good'}
                        >
                          优秀
                        </Radio>
                      </Col>
                      <Col span={8}>
                        <Radio
                          style={{ lineHeight: '32px' }}
                          className={checked ? '' : 'ant-radio-disabled'}
                          value={'normal'}
                        >
                          正常
                        </Radio>
                      </Col>
                      <Col span={8}>
                        <Radio
                          style={{ lineHeight: '32px' }}
                          className={checked ? '' : 'ant-radio-disabled'}
                          value={'bad'}
                        >
                          较差
                        </Radio>
                      </Col>
                      <Col
                        span={24}
                        style={{ display: 'flex', alignItems: 'flex-start', flexWrap: 'nowrap' }}
                      >
                        <Radio
                          style={{ lineHeight: '32px' }}
                          className={checked ? '' : 'ant-radio-disabled'}
                          value={'Range'}
                        >
                          {I18N.t('指定区间')}
                        </Radio>
                        <div style={{ flex: 1, overflow: 'hidden' }}>
                          <Row gutter={[16, 16]}>
                            <Col span={12}>
                              <DMFormItem
                                initialValue={config.ordersPmQueryVo?.goodRate}
                                name={'ordersPmFrom'}
                                rules={[
                                  {
                                    validator: (rule, value) => {
                                      // 如果没有选中，不用校验
                                      if (_orderPmType !== 'Range' || !checked) {
                                        return Promise.resolve();
                                      }
                                      if (_.isNil(value) && f.getFieldValue('ordersPmTo')) {
                                        return Promise.reject(new Error('请输入'));
                                      }

                                      // 取值范围
                                      if (value < 0 || value > 1000) {
                                        return Promise.reject(new Error('取值范围为0.001 - 1000'));
                                      }
                                      return Promise.resolve();
                                    },
                                  },
                                ]}
                              >
                                <InputNumber
                                  disabled={_orderPmType !== 'Range'}
                                  placeholder={'最小值（可为空）'}
                                  step={0.001}
                                />
                              </DMFormItem>
                            </Col>
                            <Col span={12}>
                              <DMFormItem
                                name={'ordersPmTo'}
                                rules={[
                                  {
                                    validator: (rule, value) => {
                                      // 如果没有选中，不用校验
                                      if (_orderPmType !== 'Range' || !checked) {
                                        return Promise.resolve();
                                      }
                                      if (_.isNil(value) && f.getFieldValue('ordersPmFrom')) {
                                        return Promise.reject(new Error('请输入'));
                                      }
                                      // 取值范围
                                      if (value > 1000 || value < 0) {
                                        return Promise.reject(new Error('取值范围为0.001 - 1000'));
                                      }
                                      return Promise.resolve();
                                    },
                                  },
                                ]}
                              >
                                <InputNumber
                                  disabled={_orderPmType !== 'Range'}
                                  placeholder={'最大值（可为空）'}
                                  step={0.001}
                                />
                              </DMFormItem>
                            </Col>
                          </Row>
                        </div>
                      </Col>
                    </Row>
                  </Radio.Group>
                </Form.Item>
              </div>
            </Form.Item>
          </DMFormItem>
        );
      }}
    </Form.Item>
  );
};

export default ConversionRateRadioGroupFormItem;
