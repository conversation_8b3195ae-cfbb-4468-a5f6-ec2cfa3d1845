// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 手工更新视频的投流码 GET /api/tkshop/video/${param0}/adCode */
export async function tkshopVideoByIdAdCodeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopVideoByIdAdCodeGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/tkshop/video/${param0}/adCode`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** scrapAvatar GET /api/tkshop/video/${param0}/scrapAvatar */
export async function tkshopVideoByIdScrapAvatarGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopVideoByIdScrapAvatarGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultstring>(`/api/tkshop/video/${param0}/scrapAvatar`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** scrapVideo GET /api/tkshop/video/${param0}/scrapTest1 */
export async function tkshopVideoByIdScrapTest1Get(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopVideoByIdScrapTest1GetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultTKVideo>(`/api/tkshop/video/${param0}/scrapTest1`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 视频的热力趋势数据 GET /api/tkshop/video/${param0}/statList */
export async function tkshopVideoByIdStatListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopVideoByIdStatListGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultListTkshopVideoStatDto>(`/api/tkshop/video/${param0}/statList`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 查询视频的封面URL GET /api/tkshop/video/avatar */
export async function tkshopVideoAvatarGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopVideoAvatarGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/tkshop/video/avatar', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 取消同步视频 POST /api/tkshop/video/cancelSync */
export async function tkshopVideoCancelSyncPost(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/video/cancelSync', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询带视频的达人 POST /api/tkshop/video/creator/page */
export async function tkshopVideoCreatorPagePost(
  body: API.PageVideoCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkshopCreatorProductVo>('/api/tkshop/video/creator/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 返回视频的详情 GET /api/tkshop/video/detail */
export async function tkshopVideoDetailGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopVideoDetailGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkshopVideoVo>('/api/tkshop/video/detail', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 全量查询视频简介 POST /api/tkshop/video/listBrief */
export async function tkshopVideoListBriefPost(
  body: API.PageVideoRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListVideoBriefVo>('/api/tkshop/video/listBrief', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 标记同步视频 POST /api/tkshop/video/markSync */
export async function tkshopVideoMarkSyncPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopVideoMarkSyncPostParams,
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/video/markSync', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询视频 POST /api/tkshop/video/page */
export async function tkshopVideoPagePost(
  body: API.PageVideoRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkshopVideoVo>('/api/tkshop/video/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取视频预签名直传oss的URL GET /api/tkshop/video/putOssUrl */
export async function tkshopVideoPutOssUrlGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopVideoPutOssUrlGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/tkshop/video/putOssUrl', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 更新备注 PUT /api/tkshop/video/remark */
export async function tkshopVideoRemarkPut(
  body: API.UpdateRemarkRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/video/remark', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** scrapVideo2 GET /api/tkshop/video/scrapTest2 */
export async function tkshopVideoScrapTest2Get(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopVideoScrapTest2GetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/video/scrapTest2', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询带视频的店铺 GET /api/tkshop/video/shops */
export async function tkshopVideoShopsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopVideoShopsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListlong>('/api/tkshop/video/shops', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 同步视频投流码 POST /api/tkshop/video/syncAdCode */
export async function tkshopVideoSyncAdCodePost(
  body: API.SyncVideoAdCodeRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/video/syncAdCode', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量同步视频 POST /api/tkshop/video/syncBatch */
export async function tkshopVideoSyncBatchPost(
  body: API.SyncCreatorVideoRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/video/syncBatch', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取tk视频的播放地址V2 GET /api/tkshop/video/url */
export async function tkshopVideoUrlGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopVideoUrlGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/tkshop/video/url', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
