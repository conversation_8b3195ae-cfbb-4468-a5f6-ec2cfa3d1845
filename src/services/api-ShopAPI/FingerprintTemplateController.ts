// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取某个指纹模板的概览 GET /api/finger/template/${param0} */
export async function fingerTemplateByFingerTemplateIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerTemplateByFingerTemplateIdGetParams,
  options?: { [key: string]: any },
) {
  const { fingerTemplateId: param0, ...queryParams } = params;
  return request<API.WebResultFingerprintTemplateVo>(`/api/finger/template/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除一个实例模板，会级联删除所有的指纹实例 DELETE /api/finger/template/${param0} */
export async function fingerTemplateByFingerTemplateIdDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerTemplateByFingerTemplateIdDeleteParams,
  options?: { [key: string]: any },
) {
  const { fingerTemplateId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/finger/template/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 绑定指纹模板到动态店铺 PUT /api/finger/template/${param0}/bind/${param1} */
export async function fingerTemplateByFingerprintTemplateIdBindByShopIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerTemplateByFingerprintTemplateIdBindByShopIdPutParams,
  options?: { [key: string]: any },
) {
  const { fingerprintTemplateId: param0, shopId: param1, ...queryParams } = params;
  return request<API.WebResult>(`/api/finger/template/${param0}/bind/${param1}`, {
    method: 'PUT',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 更新一个指纹模板名称和描述 PUT /api/finger/template/${param0}/updateName */
export async function fingerTemplateByFingerTemplateIdUpdateNamePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerTemplateByFingerTemplateIdUpdateNamePutParams,
  options?: { [key: string]: any },
) {
  const { fingerTemplateId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/finger/template/${param0}/updateName`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 返回空闲指纹模板个数 GET /api/finger/template/countIdle */
export async function fingerTemplateCountIdleGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerTemplateCountIdleGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/finger/template/countIdle', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查看指纹模板配置详情 GET /api/finger/template/detail/${param0} */
export async function fingerTemplateDetailByFingerTemplateIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerTemplateDetailByFingerTemplateIdGetParams,
  options?: { [key: string]: any },
) {
  const { fingerTemplateId: param0, ...queryParams } = params;
  return request<API.WebResultFingerprintTemplateConfigVo>(
    `/api/finger/template/detail/${param0}`,
    {
      method: 'GET',
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}

/** 获取团队的免费指纹模板配额 GET /api/finger/template/fetchTeamFreeTemplateQuota */
export async function fingerTemplateFetchTeamFreeTemplateQuotaGet(options?: {
  [key: string]: any;
}) {
  return request<API.WebResultFreeTemplateQuota>(
    '/api/finger/template/fetchTeamFreeTemplateQuota',
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/** 获取一个抓取指纹模板的token GET /api/finger/template/fetchToken */
export async function fingerTemplateFetchTokenGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerTemplateFetchTokenGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/finger/template/fetchToken', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取团队指纹模板列表 GET /api/finger/template/list */
export async function fingerTemplateListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerTemplateListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListFingerprintTemplateVo>('/api/finger/template/list', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取团队最大模板数量 GET /api/finger/template/maxCount */
export async function fingerTemplateMaxCountGet(options?: { [key: string]: any }) {
  return request<API.WebResultint>('/api/finger/template/maxCount', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 汇报指纹模板 POST /api/finger/template/report */
export async function fingerTemplateReportPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerTemplateReportPostParams,
  body: API.FingerprintTemplateCreateParamVo,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/finger/template/report', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 保存一个已汇报的指纹模板并为其指定名称 PUT /api/finger/template/saveReportedTemplate */
export async function fingerTemplateSaveReportedTemplatePut(
  body: API.FingerprintTemplateCache,
  options?: { [key: string]: any },
) {
  return request<API.WebResultFingerprintTemplateVo>('/api/finger/template/saveReportedTemplate', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取一个抓取浏览器指纹的token的详情 GET /api/finger/template/token/${param0} */
export async function fingerTemplateTokenByTokenGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerTemplateTokenByTokenGetParams,
  options?: { [key: string]: any },
) {
  const { token: param0, ...queryParams } = params;
  return request<API.WebResultFingerTemplateFetchTokenVo>(`/api/finger/template/token/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 解绑动态店铺所绑定的指纹模板 PUT /api/finger/template/unbind/${param0} */
export async function fingerTemplateUnbindByShopIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerTemplateUnbindByShopIdPutParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/finger/template/unbind/${param0}`, {
    method: 'PUT',
    params: { ...queryParams },
    ...(options || {}),
  });
}
