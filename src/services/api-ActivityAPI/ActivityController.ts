// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 检查(团队-用户)是否已经完成了某个活动 GET /api/activity/isActivityFinished */
export async function activityIsActivityFinishedGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.activityIsActivityFinishedGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultboolean>('/api/activity/isActivityFinished', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
