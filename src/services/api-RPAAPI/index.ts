// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：
import * as RpaTaskAiController from './RpaTaskAiController';
import * as RapTaskCaptchaController from './RapTaskCaptchaController';
import * as RpaCloudInstanceController from './RpaCloudInstanceController';
import * as RpaController from './RpaController';
import * as RpaFeesController from './RpaFeesController';
import * as RpaMarketController from './RpaMarketController';
import * as RpaPaymentController from './RpaPaymentController';
import * as RpaPlanController from './RpaPlanController';
import * as RpaPlanGroupController from './RpaPlanGroupController';
import * as RpaRemoteServiceController from './RpaRemoteServiceController';
import * as RpaTaskController from './RpaTaskController';
import * as RpaTaskDbController from './RpaTaskDbController';
import * as RpaTriggerController from './RpaTriggerController';
export default {
  RpaTaskAiController,
  RapTaskCaptchaController,
  RpaCloudInstanceController,
  RpaController,
  RpaFeesController,
  RpaMarketController,
  RpaPaymentController,
  RpaPlanController,
  RpaPlanGroupController,
  RpaRemoteServiceController,
  RpaTaskController,
  RpaTaskDbController,
  RpaTriggerController,
};
