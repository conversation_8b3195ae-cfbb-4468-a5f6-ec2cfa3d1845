// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 绑定指纹到店铺 PUT /api/finger/${param0}/bind/${param1} */
export async function fingerByFingerprintIdBindByShopIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerByFingerprintIdBindByShopIdPutParams,
  options?: { [key: string]: any },
) {
  const { fingerprintId: param0, shopId: param1, ...queryParams } = params;
  return request<API.WebResult>(`/api/finger/${param0}/bind/${param1}`, {
    method: 'PUT',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 更新一个指纹配置详情 POST /api/finger/${param0}/update */
export async function fingerByFingerprintIdUpdatePost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerByFingerprintIdUpdatePostParams,
  body: API.FingerprintCreateParamVo,
  options?: { [key: string]: any },
) {
  const { fingerprintId: param0, ...queryParams } = params;
  return request<API.WebResultFingerprintConfigVo>(`/api/finger/${param0}/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 批量更新指纹实例配置详情 POST /api/finger/batchUpdate */
export async function fingerBatchUpdatePost(
  body: API.BatchUpdateFingerprintConfig,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/finger/batchUpdate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 检查某个指纹配置是否会和系统已有指纹的特征码重复 POST /api/finger/checkDuplicate */
export async function fingerCheckDuplicatePost(
  body: API.FingerprintConfigVo,
  options?: { [key: string]: any },
) {
  return request<API.WebResultFingerprintDuplicateVo>('/api/finger/checkDuplicate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 检查团队指纹是否需要升级 GET /api/finger/checkWhetherNeedUpgrade */
export async function fingerCheckWhetherNeedUpgradeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerCheckWhetherNeedUpgradeGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultFingerUpgradeCheckResult>('/api/finger/checkWhetherNeedUpgrade', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询团队指纹数量 GET /api/finger/countByTeam */
export async function fingerCountByTeamGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerCountByTeamGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/finger/countByTeam', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页获取全部指纹列表 GET /api/finger/countFinger */
export async function fingerCountFingerGet(options?: { [key: string]: any }) {
  return request<API.WebResultFingerCountResult>('/api/finger/countFinger', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 返回空闲指纹实例个数 GET /api/finger/countIdle */
export async function fingerCountIdleGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerCountIdleGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/finger/countIdle', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取指纹缺省配置 GET /api/finger/defaultConfig */
export async function fingerDefaultConfigGet(options?: { [key: string]: any }) {
  return request<API.WebResultTeamFingerprintDefaultConfig>('/api/finger/defaultConfig', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 设置指纹缺省配置 PUT /api/finger/defaultConfig */
export async function fingerDefaultConfigPut(
  body: API.TeamFingerprintDefaultConfig,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTeamFingerprintDefaultConfig>('/api/finger/defaultConfig', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除指纹 DELETE /api/finger/delete */
export async function fingerDeleteDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerDeleteDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/finger/delete', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查看指纹配置详情 GET /api/finger/detail/${param0} */
export async function fingerDetailByFingerprintIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerDetailByFingerprintIdGetParams,
  options?: { [key: string]: any },
) {
  const { fingerprintId: param0, ...queryParams } = params;
  return request<API.WebResultFingerprintConfigVo>(`/api/finger/detail/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取一个抓取浏览器指纹的token GET /api/finger/fetchToken/${param0} */
export async function fingerFetchTokenByShopIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerFetchTokenByShopIdGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultstring>(`/api/finger/fetchToken/${param0}`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 根据id获取一个指纹的基本信息 GET /api/finger/get/${param0} */
export async function fingerGetByFingerprintIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerGetByFingerprintIdGetParams,
  options?: { [key: string]: any },
) {
  const { fingerprintId: param0, ...queryParams } = params;
  return request<API.WebResultFingerprintVo>(`/api/finger/get/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 分页获取全部指纹列表 GET /api/finger/list/all */
export async function fingerListAllGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerListAllGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultFingerprintDetailVo>('/api/finger/list/all', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页获取单独的指纹列表 GET /api/finger/list/free */
export async function fingerListFreeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerListFreeGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultFingerprintDetailVo>('/api/finger/list/free', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页获取模板生成的指纹列表 GET /api/finger/list/template */
export async function fingerListTemplateGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerListTemplateGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultFingerprintDetailVo>('/api/finger/list/template', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 抓取汇报浏览器指纹 POST /api/finger/report */
export async function fingerReportPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerReportPostParams,
  body: API.FingerprintCreateParamVo,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/finger/report', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 给店铺创建一个新指纹（不删除旧的指纹实例） POST /api/finger/set/shop/${param0} */
export async function fingerSetShopByShopIdPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerSetShopByShopIdPostParams,
  body: API.FingerprintCreateParamVo,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/finger/set/shop/${param0}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 获取某个店铺的指纹绑定历史 GET /api/finger/shop/${param0}/bindHis */
export async function fingerShopByShopIdBindHisGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerShopByShopIdBindHisGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultListFingerBindHisVo>(`/api/finger/shop/${param0}/bindHis`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取一个抓取浏览器指纹的token的详情 GET /api/finger/token/${param0} */
export async function fingerTokenByTokenGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerTokenByTokenGetParams,
  options?: { [key: string]: any },
) {
  const { token: param0, ...queryParams } = params;
  return request<API.WebResultFingerFetchTokenVo>(`/api/finger/token/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 解绑店铺的指纹 PUT /api/finger/unbind/${param0} */
export async function fingerUnbindByShopIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerUnbindByShopIdPutParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/finger/unbind/${param0}`, {
    method: 'PUT',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 手动升级指纹实例的版本号, 只改动user-agent PUT /api/finger/upgradeFingerUseragent */
export async function fingerUpgradeFingerUseragentPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fingerUpgradeFingerUseragentPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/finger/upgradeFingerUseragent', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
