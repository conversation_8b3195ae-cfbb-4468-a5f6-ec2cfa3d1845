// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 请求远程支持 返回支持码 POST /api/support/request */
export async function supportRequestPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.supportRequestPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/support/request', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
