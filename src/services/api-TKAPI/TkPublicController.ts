// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 分页查询团队达人（高级查询） POST /api/tk/public/creator-view/page-complex */
export async function tkPublicCreatorViewPageComplexPost(
  body: API.PageCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkCreatorDetailVo>(
    '/api/tk/public/creator-view/page-complex',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 分页查询公海达人（简洁版）V2 POST /api/tk/public/creator-view/page-simple-v2 */
export async function tkPublicCreatorViewPageSimpleV2Post(
  body: API.PageCreatorSimpleRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkCreatorDetailVo>(
    '/api/tk/public/creator-view/page-simple-v2',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 查询公海达人标签列表 GET /api/tk/public/tags */
export async function tkPublicTagsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTagVo>('/api/tk/public/tags', {
    method: 'GET',
    ...(options || {}),
  });
}
