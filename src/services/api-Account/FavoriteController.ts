// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 查询我的所有收藏 GET /api/favorites */
export async function favoritesGet(options?: { [key: string]: any }) {
  return request<API.WebResultListFavoriteDto>('/api/favorites', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 添加收藏 POST /api/favorites */
export async function favoritesPost(
  body: API.AddFavorateRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/favorites', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 按类型查询收藏列表 GET /api/favorites/byType/${param0} */
export async function favoritesByTypeByResourceTypeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.favoritesByTypeByResourceTypeGetParams,
  options?: { [key: string]: any },
) {
  const { resourceType: param0, ...queryParams } = params;
  return request<API.WebResultListFavoriteDto>(`/api/favorites/byType/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 取消收藏 POST /api/favorites/delete */
export async function favoritesDeletePost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.favoritesDeletePostParams,
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/favorites/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 按类型查询收藏列表 GET /api/favorites/detailByType/${param0} */
export async function favoritesDetailByTypeByResourceTypeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.favoritesDetailByTypeByResourceTypeGetParams,
  options?: { [key: string]: any },
) {
  const { resourceType: param0, ...queryParams } = params;
  return request<API.WebResultPageResultFavoriteDetailVo>(`/api/favorites/detailByType/${param0}`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 分页查询收藏列表 GET /api/favorites/page */
export async function favoritesPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.favoritesPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultFavoriteDto>('/api/favorites/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
