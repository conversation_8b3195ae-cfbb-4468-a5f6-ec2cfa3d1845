// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 查询达人详情 GET /api/gh/gifter/${param0} */
export async function ghGifterByCreatorIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghGifterByCreatorIdGetParams,
  options?: { [key: string]: any },
) {
  const { creatorId: param0, ...queryParams } = params;
  return request<API.WebResultGhGifterDetailVo>(`/api/gh/gifter/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 查询直播打赏记录 GET /api/gh/gifter/${param0}/live-gift/list */
export async function ghGifterByGifterIdLiveGiftListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghGifterByGifterIdLiveGiftListGetParams,
  options?: { [key: string]: any },
) {
  const { gifterId: param0, ...queryParams } = params;
  return request<API.WebResultListGhLiveGiftDto>(`/api/gh/gifter/${param0}/live-gift/list`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 认领达人 PUT /api/gh/gifter/acquire */
export async function ghGifterAcquirePut(
  body: API.GhCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/gifter/acquire', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分配达人 PUT /api/gh/gifter/allocate */
export async function ghGifterAllocatePut(
  body: API.GhGifterAllocateRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/gifter/allocate', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取TK达人头像 GET /api/gh/gifter/avatar */
export async function ghGifterAvatarGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghGifterAvatarGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/gh/gifter/avatar', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 取消分配 PUT /api/gh/gifter/cancelAllocate */
export async function ghGifterCancelAllocatePut(
  body: API.GhCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/gifter/cancelAllocate', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 统计团队达人的数量 GET /api/gh/gifter/count */
export async function ghGifterCountGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghGifterCountGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultlong>('/api/gh/gifter/count', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 删除达人 POST /api/gh/gifter/delete */
export async function ghGifterDeletePost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghGifterDeletePostParams,
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/gifter/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除达人（按查询） POST /api/gh/gifter/deleteByQuery */
export async function ghGifterDeleteByQueryPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghGifterDeleteByQueryPostParams,
  body: API.FindGhGifterRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/gifter/deleteByQuery', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 达人可导出的字段元信息 GET /api/gh/gifter/fields */
export async function ghGifterFieldsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTkCreatorFiledVo>('/api/gh/gifter/fields', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 同步直播打赏记录 POST /api/gh/gifter/live-gift/sync */
export async function ghGifterLiveGiftSyncPost(
  body: API.SyncGhLiveGiftRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/gifter/live-gift/sync', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询团队达人V2（不返回总数） GET /api/gh/gifter/page */
export async function ghGifterPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghGifterPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultGhGifterDetailVo>('/api/gh/gifter/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询我认领的达人 GET /api/gh/gifter/pageResponsible */
export async function ghGifterPageResponsibleGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghGifterPageResponsibleGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultGhGifterDetailVo>('/api/gh/gifter/pageResponsible', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 恢复达人 POST /api/gh/gifter/recover */
export async function ghGifterRecoverPost(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/gifter/recover', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取TK达人区域 GET /api/gh/gifter/regions */
export async function ghGifterRegionsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListstring>('/api/gh/gifter/regions', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 取消认领达人 PUT /api/gh/gifter/release */
export async function ghGifterReleasePut(
  body: API.GhCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/gifter/release', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** getResponsibleUserList GET /api/gh/gifter/responsibleUserList */
export async function ghGifterResponsibleUserListGet(options?: { [key: string]: any }) {
  return request<API.WebResultListlong>('/api/gh/gifter/responsibleUserList', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 同步TK达人头像 POST /api/gh/gifter/syncAvatar */
export async function ghGifterSyncAvatarPost(
  body: API.SyncCreatorAvatarRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/gifter/syncAvatar', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量同步（创建）TK达人 POST /api/gh/gifter/syncBatch */
export async function ghGifterSyncBatchPost(
  body: API.GhUserDocumentBatch,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListGhGifterDto>('/api/gh/gifter/syncBatch', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量更新达人是否关注后私信 PUT /api/gh/gifter/updateMessageStatus */
export async function ghGifterUpdateMessageStatusPut(
  body: API.GhCreatorUpdateMessageStatusRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/gifter/updateMessageStatus', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量更新达人状态 PUT /api/gh/gifter/updateStatus */
export async function ghGifterUpdateStatusPut(
  body: API.GhCreatorUpdateStatusRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListGhCreatorDto>('/api/gh/gifter/updateStatus', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
