// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 达人复制 返回异步任务ID POST /api/tk/operation/copy */
export async function tkOperationCopyPost(
  body: API.TkCopyToClientRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultlong>('/api/tk/operation/copy', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询标签列表 GET /api/tk/tags */
export async function tkTagsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkTagsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTagVo>('/api/tk/tags', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
