import I18N from '@/i18n';
import { useVT } from 'virtualizedtableforantd4';
import { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import type { ActionType } from '@ant-design/pro-table';
import { ProTable } from '@ant-design/pro-table';
import { scrollProTableOptionFn } from '@/mixins/table';
import { ConfigProvider, Typography } from 'antd';
import EmptyView from '@/components/Common/EmptyView';
import useSampleRequestToReviewHeader from '@/pages/Sample/components/useSampleRequestToReviewHeader';
import SampleRequestByShopToReviewTableFooter from '@/pages/Sample/components/SampleRequestByShopToReviewTableFooter';
import { tkshopSampleRequestPageV2Post } from '@/services/api-TKShopAPI/TkshopSampleRequestController';
import { useShopBriefById } from '@/components/Common/ShopNode';
import MiddleSpin from '@/components/Common/MiddleSpin';
import { triggerShopTask } from '@/pages/Shop/components/utils';
import ColoursIcon from '@/components/Common/ColoursIcon';
import HelpLink from '@/components/HelpLink';
import _ from 'lodash';
import { StyledLayout } from '@/style/styled';
import { SampleBreadCrumb } from '@/pages/Sample/components/utils';

// 店铺 待审批的索样记录
const SampleToViewByShopTable = forwardRef(
  (props: { shopId: number; style?: React.CSSProperties }, ref) => {
    const { shopId, style } = props;
    const actionRef = useRef<ActionType>();
    const [selectedVos, changeSelectedVos] = useState<API.TkshopSampleRequestAuditVo[]>([]);
    const [vt] = useVT(() => ({ scroll: { y: '100%' } }), []);
    const shopDetail = useShopBriefById(shopId);
    const timer = useRef<any>();
    const onReset = useRef((reset?: boolean) => {
      if (reset) {
        changeSelectedVos([]);
        actionRef.current?.reloadAndRest?.();
      } else {
        actionRef.current?.reload?.();
      }
      clearTimeout(timer.current);
      if (shopId) {
        timer.current = setTimeout(() => {
          onReset.current();
        }, 10 * 1000);
      }
    });
    useImperativeHandle(ref, () => {
      return actionRef.current;
    });

    const {
      header,
      getSearchParams,
      getSortParams,
      columns,
      tableHeader,
      tableWidth,
      isInitialized,
    } = useSampleRequestToReviewHeader({
      onChange: onReset.current,
      shopId,
    });
    const loaded = useRef(false);
    useEffect(() => {
      onReset.current(true);
    }, [onReset]);
    const selected = useMemo(() => {
      if (selectedVos?.length) {
        return selectedVos.map((item) => item.id!);
      }
      return [];
    }, [selectedVos]);
    useEffect(() => {
      onReset.current(true);
    }, [shopId]);
    const table = useMemo(() => {
      if (!columns.length) {
        return <MiddleSpin />;
      }
      return (
        <ConfigProvider
          renderEmpty={() => {
            if (loaded.current) {
              return (
                <div>
                  <div style={{ display: 'inline-flex', gap: 16, width: 600 }}>
                    <span style={{ flex: '0 0 72px' }}>
                      <ColoursIcon size={72} className={'duzhanshifangwen_24'} />
                    </span>
                    <div
                      style={{
                        display: 'inline-flex',
                        flexDirection: 'column',
                        gap: 16,
                        textAlign: 'left',
                      }}
                    >
                      <Typography.Text style={{ fontSize: 16 }}>
                        {I18N.t('索样审批')}
                      </Typography.Text>
                      <Typography.Text type={'secondary'}>
                        {I18N.t(
                          '通过执行“索样记录同步”流程，将索样达人抓取到花漾私域系统，能够方便您在花漾私域系统中完整的查看待审批达人的所有信息，辅助您快速做出决策，提升审批效率',
                        )}
                        <HelpLink style={{ marginLeft: 16 }} href={'/tkshop2/sample'} />
                      </Typography.Text>
                      <Typography.Text type={'secondary'}>
                        {I18N.t(
                          '当前店铺未查询到有待审批的索样请求，建议您重新执行一次“索样记录同步”流程',
                        )}
                        <Typography.Link
                          style={{ marginLeft: 16 }}
                          onClick={() => {
                            if (shopDetail) {
                              triggerShopTask([shopDetail], 'TS_SyncSampleCreator');
                            }
                          }}
                        >
                          {I18N.t('立即执行')}
                        </Typography.Link>
                      </Typography.Text>
                    </div>
                  </div>
                </div>
              );
            }
            return false;
          }}
        >
          <ProTable<API.TkshopSampleRequestAuditVo>
            components={{ ...vt, header: tableHeader }}
            request={(_params) => {
              const { current, pageSize } = _params;
              const payload: API.PageSampleRequestRequest = {
                pageNum: current,
                pageSize,
                status: 'ToReview',
                // eslint-disable-next-line @typescript-eslint/no-use-before-define
                ...getSortParams(),
                // eslint-disable-next-line @typescript-eslint/no-use-before-define
                ...getSearchParams(),
                shopId,
              };
              return tkshopSampleRequestPageV2Post(payload).then((res) => {
                loaded.current = true;
                const list = res.data?.list;
                changeSelectedVos((prev) => {
                  // 更新已选中的状态
                  return prev
                    .map((item) => {
                      const target = _.find(list, (_item) => {
                        return _item.id === item.id;
                      });
                      if (target) {
                        return target;
                      }
                      return false;
                    })
                    .filter(Boolean);
                });
                return {
                  data: list,
                  total: res.data?.total,
                };
              });
            }}
            key={'table'}
            actionRef={actionRef}
            rowSelection={{
              selectedRowKeys: selected,
              onChange(keys, rows) {
                changeSelectedVos(rows);
              },
            }}
            columns={columns}
            {...scrollProTableOptionFn({
              scroll: {
                x: tableWidth,
              },
              alwaysShowFooter: true,
              footer: () => {
                return (
                  <SampleRequestByShopToReviewTableFooter
                    onChange={onReset.current}
                    selected={selectedVos}
                  />
                );
              },
            })}
            style={
              !isInitialized
                ? {
                    pointerEvents: 'none',
                    opacity: 0,
                  }
                : {}
            }
          />
        </ConfigProvider>
      );
    }, [
      columns,
      getSearchParams,
      getSortParams,
      isInitialized,
      selected,
      selectedVos,
      shopDetail,
      shopId,
      tableHeader,
      tableWidth,
      vt,
    ]);

    return (
      <StyledLayout style={style}>
        <div style={{ paddingLeft: 0 }} className={'header'}>
          <SampleBreadCrumb shopId={shopId} />
          {header}
        </div>
        <div className={'main'}>
          <ConfigProvider
            renderEmpty={() => {
              return <EmptyView description={I18N.t('暂无数据')} />;
            }}
          >
            {table}
          </ConfigProvider>
        </div>
      </StyledLayout>
    );
  },
);
export default SampleToViewByShopTable;
