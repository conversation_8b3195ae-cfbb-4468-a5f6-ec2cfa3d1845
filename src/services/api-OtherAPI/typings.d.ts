declare namespace API {
  type ajaxEventBatchListenerPutParams = {
    /** pageId */
    pageId: string;
    /** resIds */
    resIds: string;
    /** eventName */
    eventName: string;
  };

  type ajaxEventBatchUnListenerPutParams = {
    /** pageId */
    pageId: string;
    /** resIds */
    resIds: string;
    /** eventName */
    eventName: string;
  };

  type ajaxEventListenerPutParams = {
    /** pageId */
    pageId: string;
    /** resId */
    resId: string;
    /** eventName */
    eventName: string;
  };

  type ajaxEventQueryGetParams = {
    /** pageId */
    pageId: string;
  };

  type ajaxEventUnListenerPutParams = {
    /** pageId */
    pageId: string;
    /** resId */
    resId: string;
    /** eventName */
    eventName: string;
  };

  type AjaxEventVo = {
    data?: Record<string, any>;
    name?: string;
    resId?: string;
  };

  type CaptchaBase64Vo = {
    /** 图片的base64 */
    base64?: string;
  };

  type captchaCheckGetParams = {
    /** captcha */
    captcha: string;
  };

  type captchaGetBase64GetParams = {
    /** captcha token */
    token: string;
    /** 图片宽度 */
    width?: number;
    /** 图片高度 */
    height?: number;
  };

  type captchaGetGetParams = {
    /** captcha token */
    token: string;
    /** 图片宽度 */
    width?: number;
    /** 图片高度 */
    height?: number;
  };

  type CaptchaTokenVo = {
    /** 用于验证captcha的token */
    token?: string;
  };

  type WebResult = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    success?: boolean;
  };

  type WebResultCaptchaBase64Vo = {
    code?: number;
    data?: CaptchaBase64Vo;
    message?: string;
    success?: boolean;
  };

  type WebResultCaptchaTokenVo = {
    code?: number;
    data?: CaptchaTokenVo;
    message?: string;
    success?: boolean;
  };

  type WebResultListAjaxEventVo = {
    code?: number;
    data?: AjaxEventVo[];
    message?: string;
    success?: boolean;
  };
}
