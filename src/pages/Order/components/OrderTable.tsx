import I18N from '@/i18n';
import { useCallback, useMemo, useRef, useState } from 'react';
import type { ActionType } from '@ant-design/pro-table';
import { ProTable } from '@ant-design/pro-table';
import { scrollProTableOptionFn } from '@/mixins/table';
import { Button, ConfigProvider, Layout, Space, Tooltip, Typography } from 'antd';
import EmptyView from '@/components/Common/EmptyView';
import { GhostModalCaller } from '@/mixins/modal';
import MiddleSpin from '@/components/Common/MiddleSpin';
import useOrderHeader from '@/pages/Order/components/useOrderHeader';
import TriggerSyncOrderModal from '@/pages/Order/components/TriggerSyncOrderModal';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { StyledTableWrapper } from '@/style/styled';
import { useOrderGroupActionsModal } from '@/pages/Order/components/OrderGroupActionsModal';
import { useLocalStorageState } from 'ahooks';
import { GlobalHeaderAction } from '@/utils/pageUtils';
import DmPagination from '@/components/DmPagination';
import { useVT } from 'virtualizedtableforantd4';
import _ from 'lodash';
import { useRequest } from '@@/plugin-request/request';
import { tkshopOrderPagePost } from '@/services/api-TKShopAPI/TkshopOrderController';

const EXPAND_ICON_COLOR = '#888';

const OrderTable = () => {
  const actionRef = useRef<ActionType>();
  const [current, setCurrent] = useState<number>(1);
  const [pageSize, setPageSize] = useLocalStorageState<number>('tkshop_order_list', 15);
  const [vt] = useVT(() => ({ scroll: { y: '100%' } }), []);
  const [selectedVos, changeSelectedVos] = useState<API.TkshopOrderVo[]>([]);
  const [total, setTotal] = useState(0);
  const [expandKeys, setExpandKeys] = useState<number[]>([]);
  const [tableLoading, setTableLoading] = useState(true); // 新增，默认true
  const {
    data: dataSource,
    loading,
    run: fetchTable,
  } = useRequest(
    async (_current: number = current, _pageSize: number = pageSize) => {
      setTableLoading(true); // 请求开始时设置 loading
      setPageSize(_pageSize);
      if (_current !== current) {
        // eslint-disable-next-line @typescript-eslint/no-use-before-define
        setExpandKeys([]);
      }
      setCurrent(_current);
      const payload: API.PageTkshopOrderRequest = {
        pageNum: _current,
        pageSize: _pageSize,
        // eslint-disable-next-line @typescript-eslint/no-use-before-define
        ...getSortParams(),
        // eslint-disable-next-line @typescript-eslint/no-use-before-define
        ...getSearchParams(),
      };
      // @ts-ignore
      if (payload.shopId) {
        // @ts-ignore
        payload.shopIds = [payload.shopId];
      }
      const data = await tkshopOrderPagePost(payload).then((res) => {
        const list: (API.TkshopOrderVo & { level: 'parent' | 'child'; expandKey?: any })[] = [];
        res.data?.list?.forEach((item) => {
          const { items, ...rest } = item;
          list.push({
            ...rest,
            level: 'parent',
            items,
          });
          items?.forEach((_item) => {
            list.push({
              ..._item,
              level: 'child',
              shopId: item.shopId!,
              expandKey: rest.id!,
            });
          });
        });
        setTotal(res.data?.total || 0);
        return list;
      });
      return { data };
    },
    {
      debounceInterval: 300,
      defaultLoading: true,
      onSuccess() {
        setTableLoading(false); // 数据加载完毕后关闭 loading
      },
      onError() {
        setTableLoading(false); // 数据加载失败后也关闭 loading
      },
    },
  );
  const onSyncClick = useCallback(() => {
    GhostModalCaller(<TriggerSyncOrderModal />);
  }, []);
  const onReset = useCallback(
    (reset?: boolean) => {
      if (reset) {
        changeSelectedVos([]);
        // eslint-disable-next-line @typescript-eslint/no-use-before-define
        setExpandKeys([]);
        fetchTable(1);
      } else {
        fetchTable();
      }
    },
    [fetchTable],
  );
  const {
    header,
    getSearchParams,
    getSortParams,
    columns,
    tableHeader,
    tableWidth,
    isInitialized,
  } = useOrderHeader({
    onChange: onReset,
    getParams() {
      if (selectedVos.length) {
        return {
          orderIds: selectedVos.map((item) => item.id!),
        };
      }
      const payload: API.PageTkshopOrderRequest = {
        // eslint-disable-next-line @typescript-eslint/no-use-before-define
        ...getSortParams(),
        // eslint-disable-next-line @typescript-eslint/no-use-before-define
        ...getSearchParams(),
      };
      // @ts-ignore
      if (payload.shopId) {
        // @ts-ignore
        payload.shopIds = [payload.shopId];
      }
      return payload;
    },
  });
  const selected = useMemo(() => {
    if (selectedVos?.length) {
      return selectedVos.map((item) => item.id!);
    }
    return [];
  }, [selectedVos]);

  const openOrderGroupModal = useOrderGroupActionsModal(onReset.bind(null, false));
  const expanded_data = useMemo(() => {
    if (!dataSource) {
      return [];
    }
    if (!expandKeys.length) {
      return dataSource.filter((item) => item.level === 'parent');
    }
    return dataSource.filter((item) => {
      if (item.level === 'child') {
        return expandKeys.includes(item.expandKey);
      }
      return true;
    });
  }, [dataSource, expandKeys]);
  const expandAll = useCallback(() => {
    setExpandKeys(
      expanded_data
        ?.filter((item) => {
          return !!item?.items?.length;
        })
        ?.map((item) => item.id!) || [],
    );
  }, [expanded_data]);
  const expandButton = useMemo(() => {
    if (expanded_data.length === 0) {
      return false;
    }
    const expandable_rows = expanded_data.filter((item) => {
      return !!item?.items?.length;
    });
    if (!expandable_rows.length) {
      return false;
    }
    if (expandable_rows.length === expandKeys.length) {
      return (
        <div style={{ textAlign: 'center' }}>
          <Tooltip title={I18N.t('全部收起')}>
            <Typography.Text
              style={{ cursor: 'pointer', color: EXPAND_ICON_COLOR }}
              type={'secondary'}
              onClick={() => {
                setExpandKeys([]);
              }}
            >
              <IconFontIcon iconName="jianhaoshouqi_24" />
            </Typography.Text>
          </Tooltip>
        </div>
      );
    }
    return (
      <div style={{ textAlign: 'center' }}>
        <Tooltip title={I18N.t('全部展开')}>
          <Typography.Text
            style={{ cursor: 'pointer', color: EXPAND_ICON_COLOR }}
            type={'secondary'}
            onClick={() => {
              expandAll();
            }}
          >
            <IconFontIcon iconName="jiahaozhankai_24" />
          </Typography.Text>
        </Tooltip>
      </div>
    );
  }, [expandAll, expandKeys.length, expanded_data]);

  const table = useMemo(() => {
    if (!columns.length) {
      return <MiddleSpin />;
    }
    const disabled = !selectedVos.length;
    return (
      <>
        <div style={{ flex: 1, overflow: 'hidden' }}>
          <ProTable<API.TkshopOrderVo & { level: 'parent' | 'child' }>
            components={{ ...vt, header: tableHeader }}
            dataSource={expanded_data}
            loading={loading}
            key={'table'}
            actionRef={actionRef}
            onRow={(record) => {
              return {
                style: {
                  backgroundColor: record.level === 'child' ? '#fafafa' : '',
                  cursor: record.level === 'parent' ? 'pointer' : 'initial',
                },
                onClick() {
                  if (record.level === 'parent') {
                    setExpandKeys((prev) => {
                      const newState = [...(prev || [])];
                      const index = newState.indexOf(record.id!);
                      if (index !== -1) {
                        newState.splice(index, 1);
                      } else {
                        newState.push(record.id!);
                      }
                      return _.uniq(newState);
                    });
                  }
                },
              };
            }}
            rowSelection={{
              selectedRowKeys: selected,
              onChange(keys, creators) {
                changeSelectedVos(creators);
              },
              renderCell(value, record, index, originNode) {
                if (record.level === 'parent') {
                  return originNode;
                }
                return false;
              },
            }}
            columns={[
              {
                title: expandButton,
                onHeaderCell() {
                  return {
                    style: {
                      padding: '0px !important',
                    },
                  };
                },
                onCell() {
                  return {
                    style: {
                      padding: '0px !important',
                    },
                  };
                },
                align: 'center',
                width: 30,
                dataIndex: 'expandable',
                render(dom, record) {
                  if (!record?.items?.length) {
                    return false;
                  }
                  if (expandKeys.includes(record.id!)) {
                    return (
                      <Typography.Text
                        type={'secondary'}
                        style={{ cursor: 'pointer', color: EXPAND_ICON_COLOR }}
                        onClick={(e) => {
                          e.stopPropagation();
                          setExpandKeys(expandKeys.filter((key) => key !== record.id));
                        }}
                      >
                        <IconFontIcon iconName="jianhaoshouqi_24" size={16} />
                      </Typography.Text>
                    );
                  }
                  return (
                    <Typography.Text
                      type={'secondary'}
                      style={{ cursor: 'pointer', color: EXPAND_ICON_COLOR }}
                      onClick={(e) => {
                        e.stopPropagation();
                        setExpandKeys([...expandKeys, record.id!]);
                      }}
                    >
                      <IconFontIcon iconName="jiahaozhankai_24" size={16} />
                    </Typography.Text>
                  );
                },
              },
              ...columns,
            ]}
            style={
              !isInitialized
                ? {
                    opacity: 0,
                    pointerEvents: 'none',
                  }
                : {}
            }
            {...scrollProTableOptionFn({
              pageId: 'tkshop_order_list',
              scroll: {
                x: tableWidth,
              },
              pagination: false,
            })}
          />
        </div>
        <div
          style={{
            height: 48,
            flex: '0 0 48px',
            borderTop: '1px solid #ddd',
            display: 'flex',
            justifyContent: 'space-between',
            flexWrap: 'nowrap',
            alignItems: 'center',
            padding: '0 16px',
          }}
        >
          <Tooltip placement={'topLeft'} title={disabled ? '请选择订单后操作' : false}>
            <Space>
              <Button
                disabled={disabled}
                type={'primary'}
                ghost
                icon={<IconFontIcon size={16} iconName={'tuandui_24'} />}
                onClick={() => {
                  if (disabled) {
                    return;
                  }
                  openOrderGroupModal(
                    selectedVos.filter((item) => {
                      return !!item?.items?.length;
                    }),
                    'buyer',
                  );
                }}
              >
                <span>买家批量操作</span>
              </Button>
              <Button
                disabled={disabled}
                type={'primary'}
                ghost
                icon={<IconFontIcon size={16} iconName={'daren_24'} />}
                onClick={() => {
                  if (disabled) {
                    return;
                  }
                  openOrderGroupModal(
                    selectedVos.filter((item) => {
                      return !!item?.items?.length;
                    }),
                    'creator',
                  );
                }}
              >
                <span>带货达人批量操作</span>
              </Button>
            </Space>
          </Tooltip>
          <DmPagination
            total={total}
            current={current}
            pageSize={pageSize}
            onChange={(p, s) => {
              fetchTable(p, s);
            }}
            pageId={'tkshop_order_list'}
          />
        </div>
      </>
    );
  }, [
    columns,
    selectedVos,
    vt,
    tableHeader,
    expanded_data,
    loading,
    selected,
    expandButton,
    isInitialized,
    tableWidth,
    total,
    current,
    pageSize,
    expandKeys,
    openOrderGroupModal,
    fetchTable,
  ]);

  return (
    <Layout.Content>
      <GlobalHeaderAction.Emit>
        <Space size={32}>
          <Button
            ghost
            style={{ background: 'none' }}
            icon={<IconFontIcon iconName={'shuaxin_24'} />}
            onClick={() => {
              onReset(false);
            }}
          >
            {I18N.t('刷新')}
          </Button>
          <Button
            ghost
            style={{ background: 'none' }}
            icon={<IconFontIcon iconName={'wuliufuwu_24'} />}
            onClick={onSyncClick}
          >
            {I18N.t('订单同步')}
          </Button>
          <Button
            ghost
            style={{ background: 'none' }}
            icon={<IconFontIcon iconName={'jiahaozhankai_24'} />}
            onClick={expandAll}
          >
            {I18N.t('全部展开')}
          </Button>
          <Button
            ghost
            style={{ background: 'none' }}
            icon={<IconFontIcon iconName={'jianhaoshouqi_24'} />}
            onClick={() => {
              setExpandKeys([]);
            }}
          >
            {I18N.t('全部收起')}
          </Button>
        </Space>
      </GlobalHeaderAction.Emit>
      <div
        className="header"
        style={{ pointerEvents: tableLoading ? 'none' : 'auto', overflow: 'auto', gap: 8 }}
      >
        {header}
      </div>
      <main className="main">
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: 8,
            overflow: 'hidden',
            height: '100%',
          }}
        >
          <ConfigProvider
            renderEmpty={() => {
              return <EmptyView description={I18N.t('暂无数据')} />;
            }}
          >
            <StyledTableWrapper
              style={{ flex: 1, display: 'flex', flexDirection: 'column', overflow: 'hidden' }}
            >
              {table}
            </StyledTableWrapper>
          </ConfigProvider>
        </div>
      </main>
    </Layout.Content>
  );
};
export default OrderTable;
