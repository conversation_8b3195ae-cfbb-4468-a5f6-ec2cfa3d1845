// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 当前服务tunnel地址 GET /api/remote/tunnel/address */
export async function remoteTunnelAddressGet(options?: { [key: string]: any }) {
  return request<API.WebResultTunnelAddress>('/api/remote/tunnel/address', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 阻塞Proxy事件线程（用于测试） GET /api/remote/tunnel/blockProxy */
export async function remoteTunnelBlockProxyGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelBlockProxyGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/remote/tunnel/blockProxy', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取目标端的已经连接的端 GET /api/remote/tunnel/connectedPeers */
export async function remoteTunnelConnectedPeersGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelConnectedPeersGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultRpcConnectedPeers>('/api/remote/tunnel/connectedPeers', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取一个等待启动的IPGO的Token GET /api/remote/tunnel/createProxyPendingToken */
export async function remoteTunnelCreateProxyPendingTokenGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelCreateProxyPendingTokenGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultProxyTokenVo>('/api/remote/tunnel/createProxyPendingToken', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取目标端的线程信息 GET /api/remote/tunnel/dumpThread */
export async function remoteTunnelDumpThreadGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelDumpThreadGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/remote/tunnel/dumpThread', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 通知Proxy停止（用于测试） GET /api/remote/tunnel/exitProxy */
export async function remoteTunnelExitProxyGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelExitProxyGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/remote/tunnel/exitProxy', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取网关升级网址前缀 GET /api/remote/tunnel/gatewayEndpoint */
export async function remoteTunnelGatewayEndpointGet(options?: { [key: string]: any }) {
  return request<API.WebResultstring>('/api/remote/tunnel/gatewayEndpoint', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取目标端的GIT信息 GET /api/remote/tunnel/gitInfo */
export async function remoteTunnelGitInfoGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelGitInfoGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultGitInfoVo>('/api/remote/tunnel/gitInfo', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取目标端的Spy信息 GET /api/remote/tunnel/gitSpy */
export async function remoteTunnelGitSpyGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelGitSpyGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultSpyVo>('/api/remote/tunnel/gitSpy', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 探测remoteIp POST /api/remote/tunnel/probeRemoteIp */
export async function remoteTunnelProbeRemoteIpPost(
  body: API.RemoteProbeRemoteIpRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultProxyProbeWithRemoteIp>('/api/remote/tunnel/probeRemoteIp', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** IPGO修改特定用户的登录秘密 PUT /api/remote/tunnel/proxy/changePassword */
export async function remoteTunnelProxyChangePasswordPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelProxyChangePasswordPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/remote/tunnel/proxy/changePassword', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** IPGO修改Linux系统是否允许秘密登录 PUT /api/remote/tunnel/proxy/changePwdLogin */
export async function remoteTunnelProxyChangePwdLoginPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelProxyChangePwdLoginPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/remote/tunnel/proxy/changePwdLogin', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 触发Proxy连接最快中转2 从特定分组中连接，而不是所有 POST /api/remote/tunnel/proxy/connectTransit */
export async function remoteTunnelProxyConnectTransitPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelProxyConnectTransitPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/remote/tunnel/proxy/connectTransit', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 触发Proxy一次IP汇报 POST /api/remote/tunnel/proxy/forceReportIp */
export async function remoteTunnelProxyForceReportIpPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelProxyForceReportIpPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/remote/tunnel/proxy/forceReportIp', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 强制更新 POST /api/remote/tunnel/proxy/forceUpdate */
export async function remoteTunnelProxyForceUpdatePost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelProxyForceUpdatePostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/remote/tunnel/proxy/forceUpdate', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取Proxy时间戳 POST /api/remote/tunnel/proxy/getTimestamp */
export async function remoteTunnelProxyGetTimestampPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelProxyGetTimestampPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultlong>('/api/remote/tunnel/proxy/getTimestamp', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 触发Proxy连接最快中转 POST /api/remote/tunnel/proxy/pingFasttestAndConnectTransit */
export async function remoteTunnelProxyPingFasttestAndConnectTransitPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelProxyPingFasttestAndConnectTransitPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTransitConnectionInfo>(
    '/api/remote/tunnel/proxy/pingFasttestAndConnectTransit',
    {
      method: 'POST',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 触发Proxy连接最快中转2 从特定分组中连接，而不是所有 POST /api/remote/tunnel/proxy/pingFasttestAndConnectTransit2 */
export async function remoteTunnelProxyPingFasttestAndConnectTransit2Post(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelProxyPingFasttestAndConnectTransit2PostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTransitConnectionInfo>(
    '/api/remote/tunnel/proxy/pingFasttestAndConnectTransit2',
    {
      method: 'POST',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 触发Proxy连接最快中转 POST /api/remote/tunnel/proxy/pingFasttestAndConnectTransitById */
export async function remoteTunnelProxyPingFasttestAndConnectTransitByIdPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelProxyPingFasttestAndConnectTransitByIdPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTransitConnectionInfo>(
    '/api/remote/tunnel/proxy/pingFasttestAndConnectTransitById',
    {
      method: 'POST',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 通过中转（或门户）探测代理 POST /api/remote/tunnel/proxy/probeTcp */
export async function remoteTunnelProxyProbeTcpPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelProxyProbeTcpPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultProbeResult>('/api/remote/tunnel/proxy/probeTcp', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 通过Proxy探测中转连接性 POST /api/remote/tunnel/proxy/probeToTransit */
export async function remoteTunnelProxyProbeToTransitPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelProxyProbeToTransitPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultProxyProbeWithRemoteIp>('/api/remote/tunnel/proxy/probeToTransit', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取Proxy时间戳 POST /api/remote/tunnel/proxy/runCmd */
export async function remoteTunnelProxyRunCmdPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelProxyRunCmdPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultExecResult>('/api/remote/tunnel/proxy/runCmd', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 启动一个DF2协议的Server POST /api/remote/tunnel/proxy/startDf2Server */
export async function remoteTunnelProxyStartDf2ServerPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelProxyStartDf2ServerPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/remote/tunnel/proxy/startDf2Server', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 启动一个代理服务 POST /api/remote/tunnel/proxy/startProxyForwardServer */
export async function remoteTunnelProxyStartProxyForwardServerPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelProxyStartProxyForwardServerPostParams,
  body: API.StartProxyForwardVo,
  options?: { [key: string]: any },
) {
  return request<API.WebResultStartProxyForwardResult>(
    '/api/remote/tunnel/proxy/startProxyForwardServer',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      params: {
        ...params,
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 启动一个Raw Socks5 server POST /api/remote/tunnel/proxy/startSocks5Server */
export async function remoteTunnelProxyStartSocks5ServerPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelProxyStartSocks5ServerPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/remote/tunnel/proxy/startSocks5Server', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 停止一个Tcp Server监听 POST /api/remote/tunnel/proxy/stopBindServer */
export async function remoteTunnelProxyStopBindServerPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelProxyStopBindServerPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/remote/tunnel/proxy/stopBindServer', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 停止代理服务 POST /api/remote/tunnel/proxy/stopProxyForwardServers */
export async function remoteTunnelProxyStopProxyForwardServersPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelProxyStopProxyForwardServersPostParams,
  body: API.StopProxyForwardVo,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/remote/tunnel/proxy/stopProxyForwardServers', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** IPGO替换用户的登录ssh公钥 PUT /api/remote/tunnel/proxy/storeSshKey */
export async function remoteTunnelProxyStoreSshKeyPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelProxyStoreSshKeyPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/remote/tunnel/proxy/storeSshKey', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修改代理服务 POST /api/remote/tunnel/proxy/updateProxyForwardServerAuth */
export async function remoteTunnelProxyUpdateProxyForwardServerAuthPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelProxyUpdateProxyForwardServerAuthPostParams,
  body: API.UpdateProxyForwardAuth,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/remote/tunnel/proxy/updateProxyForwardServerAuth', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取Proxy主机启动脚本userdata GET /api/remote/tunnel/proxy/userData */
export async function remoteTunnelProxyUserDataGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelProxyUserDataGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/remote/tunnel/proxy/userData', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 重启proxy（用于测试） GET /api/remote/tunnel/restartProxy */
export async function remoteTunnelRestartProxyGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelRestartProxyGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/remote/tunnel/restartProxy', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 开启TunnelTrace GET /api/remote/tunnel/startTunnelTrace */
export async function remoteTunnelStartTunnelTraceGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelStartTunnelTraceGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultGitInfoVo>('/api/remote/tunnel/startTunnelTrace', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 通知Proxy停止（之后可以手工启动） GET /api/remote/tunnel/stopProxy */
export async function remoteTunnelStopProxyGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelStopProxyGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/remote/tunnel/stopProxy', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 关闭TunnelTrace GET /api/remote/tunnel/stopTunnelTrace */
export async function remoteTunnelStopTunnelTraceGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelStopTunnelTraceGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultGitInfoVo>('/api/remote/tunnel/stopTunnelTrace', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取目标端的系统信息 GET /api/remote/tunnel/systemInfo */
export async function remoteTunnelSystemInfoGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelSystemInfoGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMap>('/api/remote/tunnel/systemInfo', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建一个批量导入Key POST /api/remote/tunnel/template */
export async function remoteTunnelTemplatePost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelTemplatePostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultProxyTemplateVo>('/api/remote/tunnel/template', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取一个有效的批量导入Key（如果不存在则创建） GET /api/remote/tunnel/template/getOrCreate */
export async function remoteTunnelTemplateGetOrCreateGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelTemplateGetOrCreateGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultProxyTemplateVo>('/api/remote/tunnel/template/getOrCreate', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建一个批量导入Key模版 GET /api/remote/tunnel/templates */
export async function remoteTunnelTemplatesGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelTemplatesGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListGatewayTemplateDto>('/api/remote/tunnel/templates', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 通过中转（或门户）探测代理 POST /api/remote/tunnel/transit/probeProxy */
export async function remoteTunnelTransitProbeProxyPost(
  body: API.ProbeProxyRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/remote/tunnel/transit/probeProxy', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 通过中转（或门户）探测代理 POST /api/remote/tunnel/transit/probeTcp */
export async function remoteTunnelTransitProbeTcpPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelTransitProbeTcpPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultProbeResult>('/api/remote/tunnel/transit/probeTcp', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** refreshBlackList GET /api/remote/tunnel/transit/refreshBlackList */
export async function remoteTunnelTransitRefreshBlackListGet(options?: { [key: string]: any }) {
  return request<API.WebResultint>('/api/remote/tunnel/transit/refreshBlackList', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 通知tunnel端断开到特定目标的连接 POST /api/remote/tunnel/tunnel/disconnectToTarget */
export async function remoteTunnelTunnelDisconnectToTargetPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelTunnelDisconnectToTargetPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultForceDisconnectResult>(
    '/api/remote/tunnel/tunnel/disconnectToTarget',
    {
      method: 'POST',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 通知tunnel端断开到所有特定类型目标的连接 POST /api/remote/tunnel/tunnel/disconnectToTargetByType */
export async function remoteTunnelTunnelDisconnectToTargetByTypePost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelTunnelDisconnectToTargetByTypePostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultForceDisconnectResult>(
    '/api/remote/tunnel/tunnel/disconnectToTargetByType',
    {
      method: 'POST',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 获取Tunnel一级路由 GET /api/remote/tunnel/tunnel/routerL1 */
export async function remoteTunnelTunnelRouterL1Get(options?: { [key: string]: any }) {
  return request<API.WebResultMapstring>('/api/remote/tunnel/tunnel/routerL1', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取Tunnel二级路由 GET /api/remote/tunnel/tunnel/routerL2 */
export async function remoteTunnelTunnelRouterL2Get(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelTunnelRouterL2GetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTunnelAddress>('/api/remote/tunnel/tunnel/routerL2', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取目标端的连接统计信息 GET /api/remote/tunnel/tunnelConnStat */
export async function remoteTunnelTunnelConnStatGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelTunnelConnStatGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTunnelConnStat>('/api/remote/tunnel/tunnelConnStat', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 通知Proxy卸载（删除cred文件并停止） GET /api/remote/tunnel/uninstallProxy */
export async function remoteTunnelUninstallProxyGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteTunnelUninstallProxyGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/remote/tunnel/uninstallProxy', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
