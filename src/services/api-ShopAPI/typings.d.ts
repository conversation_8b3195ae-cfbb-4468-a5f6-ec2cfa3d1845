declare namespace API {
  type AbstractLogVo = {
    id?: Serializable;
  };

  type AccountCardConfig = {
    /** 单账号每日最多分享卡片条数 */
    maxDayCard?: number;
    /** 单账号每批次最多分享卡片条数 */
    maxTaskCard?: number;
  };

  type AccountInviteConfig = {
    /** 单账号每日最多私信条数 */
    maxDayPm?: number;
    /** 单账号每批次最多私信条数 */
    maxTaskPm?: number;
  };

  type AccountMaintenanceConfig = {
    /** 流程相关的参数全部放到高级设置里 */
    advanceSettings?: Record<string, any>;
    /** 是否启用 */
    enable?: boolean;
    /** 养号评论时要使用的话术列表，会随机从中选择发送 */
    wordsIds?: number[];
  };

  type ActiveShopSessionVo = {
    /** 创建时间 */
    createTime?: string;
    /** 创建人ID */
    creatorId?: number;
    /** 创建人昵称 */
    creatorName?: string;
    /** 会话所在设备ID */
    deviceId?: string;
    /** 会话ID */
    id?: number;
    /** 分身ID */
    shopId?: number;
    shopName?: string;
    /** 会话所在团队ID */
    teamId?: number;
    /** 会话所在团队名称 */
    teamName?: string;
  };

  type AddBatchShopShareRequest = {
    extensions?: boolean;
    favoriteSites?: boolean;
    ipVisible?: boolean;
    shareShopPolicyType?: 'copy' | 'create' | 'none';
    shopIds?: number[];
    targetTeamId?: number;
    targetUserId?: number;
  };

  type AddExtensionsToTeamRequest = {
    extensionIds?: number[];
  };

  type AddExtensionToShopsRequest = {
    extensionId?: number;
    shopIds?: number[];
  };

  type AddMobileShareRequest = {
    mobileIds?: number[];
    targetTeamId?: number;
    targetUserId?: number;
  };

  type AddMobileToGroupRequest = {
    groupId?: number;
    mobileIds?: number[];
  };

  type AppleDeveloperIosVo = {
    /** 提交时间 */
    createTime?: string;
    /** 花瓣扣费记录 */
    creditDetailId?: number;
    developerId?: string;
    id?: number;
    /** 如果已经导入了，相应的手机id */
    mobileId?: number;
    /** 如果已经导入过系统，对应的手机信息 */
    mobileInfo?: TeamMobileDto;
    /** 当前状态, Submitted - 正在等待审核，请耐心等待 ； Ready - 已就绪，可以调用installWDA了； */
    status?: 'Approved' | 'Created' | 'Expired' | 'Ready' | 'Submitted';
    teamId?: number;
    teamName?: string;
    udid?: string;
  };

  type ApplyExtensionRequest = {
    reason?: string;
    url?: string;
  };

  type AppVersionVo = {
    autoUpdate?: boolean;
    description?: string;
    downloadLink?: string;
    versionCode?: string;
    versionName?: string;
    versionNum?: number;
  };

  type AreaVo = {
    area?:
      | 'Argentina'
      | 'Australia'
      | 'Austria'
      | 'Belarus'
      | 'Belgium'
      | 'Bolivia'
      | 'Brazil'
      | 'Canada'
      | 'Chile'
      | 'China'
      | 'Colombia'
      | 'Costa_Rica'
      | 'Dominican'
      | 'Ecuador'
      | 'Egypt'
      | 'France'
      | 'Germany'
      | 'Global'
      | 'Guatemala'
      | 'Honduras'
      | 'HongKong'
      | 'India'
      | 'Indonesia'
      | 'Ireland'
      | 'Israel'
      | 'Italy'
      | 'Japan'
      | 'Kazakhstan'
      | 'Korea'
      | 'Malaysia'
      | 'Mexico'
      | 'Netherlands'
      | 'Nicaragua'
      | 'Panama'
      | 'Paraguay'
      | 'Peru'
      | 'Philippines'
      | 'Poland'
      | 'Portuguese'
      | 'Puerto_Rico'
      | 'Russia'
      | 'Salvador'
      | 'Saudi_Arabia'
      | 'Singapore'
      | 'Spain'
      | 'Sweden'
      | 'Switzerland'
      | 'Taiwan'
      | 'Thailand'
      | 'Turkey'
      | 'United_Arab_Emirates'
      | 'United_Kingdom'
      | 'United_States'
      | 'Uruguay'
      | 'Venezuela'
      | 'Vietnam';
    desc?: string;
  };

  type AuditDto = {
    applyUserId?: number;
    applyUserNickname?: string;
    auditTime?: string;
    bizId?: number;
    createTime?: string;
    handleUserId?: number;
    handleUserNickname?: string;
    id?: number;
    paramContent?: string;
    remark?: string;
    status?: 'APPROVED' | 'CANCEL' | 'NEW' | 'NOT_PASS';
    teamId?: number;
    teamName?: string;
    type?:
      | 'JOIN_TEAM'
      | 'NEW_DEVICE'
      | 'RECEIVE_SHOP'
      | 'RECEIVE_SHOPS'
      | 'SHARED_MOBILES'
      | 'SHARED_SHOP'
      | 'SHARED_SHOPS'
      | 'TRANSFER_SHOP';
  };

  type BackupMobileRequest = {
    mobileId?: number;
  };

  type BatchReGenFingerRequest = {
    /** 如何生成指纹 */
    params?: GenFingerParams;
    shopIds?: number[];
  };

  type BatchToggleCloudMobileNetworkRequest = {
    /** 只有显式的false才是禁用 */
    enabled?: boolean;
    mobileIds?: number[];
  };

  type BatchTransferShopPrepareResult = {
    creditFail?: boolean;
  };

  type BatchTransferShopRequest = {
    shopIds?: number[];
    targetTeamId?: number;
    targetUserId?: number;
    transferFingerprint?: boolean;
    transferIp?: boolean;
  };

  type BatchTransferShopRequestV2 = {
    cache?: boolean;
    cookie?: boolean;
    extensions?: boolean;
    favoriteSites?: boolean;
    fingerprint?: boolean;
    ip?: boolean;
    password?: boolean;
    shopIds?: number[];
    targetTeamId?: number;
    targetUserId?: number;
  };

  type BatchUpdateAccountHealthCheckRequest = {
    /** 其它的流程关心但引擎不关心的属性 */
    advanceSettings?: Record<string, any>;
    disabled?: boolean;
    /** 消息检查工作时间段，格式如 03:22-15:34 */
    duration?: string;
    /** first | random | assign  为空表示random */
    friendType?: string;
    /** 消息同步频率，单位分钟，为空表示不同步；最小10分钟，最大1000分钟 */
    interval?: number;
    mobileIds?: number[];
  };

  type BatchUpdateAccountMessageCheckConfigRequest = {
    disabled?: boolean;
    /** 消息检查工作时间段，格式如 03:22-15:34 */
    duration?: string;
    /** 消息同步频率，单位分钟，为空表示不同步；最小10分钟，最大1000分钟 */
    interval?: number;
    mobileIds?: number[];
    /** 以逗号分隔开的用户id列表，表示如果有新消息要通知哪些用户 */
    receiveIds?: string;
  };

  type BatchUpdateCardConfigRequest = {
    /** 单账号每日最多卡片条数 */
    maxDayCard?: number;
    /** 单账号每批次最多卡片条数 */
    maxTaskCard?: number;
    mobileAccountIds?: number[];
  };

  type BatchUpdateFingerprintConfig = {
    /** 修改指纹实例属性 */
    fingerprintIds?: number[];
    /** 修改模板属性 */
    fingerprintTemplateId?: number;
    lang?: string;
    location?: string;
    location_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    timezone?: string;
    timezone_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** 仅当修改模板属性时有意义，是否同时修改该模板的指纹实例 */
    updateFingerprints?: boolean;
    userAgent?: string;
  };

  type BatchUpdateIntervalConfigRequest = {
    mobileIds?: number[];
    /** 单账号每批发卡片间隔，单位分钟 */
    taskCardInterval?: number;
    /** 单账号每批私信间隔，单位分钟 */
    taskPmInterval?: number;
  };

  type BatchUpdateInviteConfigRequest = {
    /** 单账号每日最多私信条数 */
    maxDayPm?: number;
    /** 单账号每批次最多私信条数 */
    maxTaskPm?: number;
    mobileAccountIds?: number[];
    /** 单账号每日最多私信条数；邀约话术分组id */
    speedGroupId?: number;
  };

  type BatchUpdateMaintenanceConfigRequest = {
    /** 流程相关的参数全部放到高级设置里 */
    advanceSettings?: Record<string, any>;
    /** 是否启用 */
    enable?: boolean;
    mobileAccountIds?: number[];
    /** 养号评论时要使用的话术列表，会随机从中选择发送 */
    wordsIds?: number[];
  };

  type BatchUpdateShopDefaultRouterRequest = {
    /** 通道类型 */
    channelType?: 'Direct' | 'None' | 'Official' | 'Primary' | 'Secondary';
    shopIds?: number[];
  };

  type BatchUpdateShopFingerprintRequest = {
    /** 为true的时候shopIds无意义 */
    allShops?: boolean;
    cpu?: number;
    enableAudio?: boolean;
    enableCanvas?: boolean;
    enableRect?: boolean;
    enableWebgl?: boolean;
    /** 浏览器所使用的语言 */
    lang?: string;
    /** Assign表示指定，Auto表示跟随Ip，否则等价于Original */
    lang_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    location?: string;
    location_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    mem?: number;
    mobileScreenSize?: string;
    /** 如何生成userAgent，只有在updateUserAgent=true的时候有意义 */
    params?: GenFingerParams;
    shopIds?: number[];
    timezone?: string;
    timezone_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    updateHardware?: boolean;
    updateLang?: boolean;
    updateLocation?: boolean;
    updateMobileScreenSize?: boolean;
    updateTimezone?: boolean;
    updateUserAgent?: boolean;
    updateWebrtc?: boolean;
    /** webrtc内网ip */
    webrtcInnerIp?: string;
    /** webrtc内网ip指定方法，Assign表示指定，Auto表示跟随Ip，否则等价于Original */
    webrtcInnerIp_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** webrtc公网ip */
    webrtcPublicIp?: string;
    /** Assign表示指定，Auto表示跟随Ip，否则等价于Original */
    webrtcPublicIp_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
  };

  type BatchUpdateShopLanProxyVo = {
    all?: boolean;
    enabled?: boolean;
    host?: string;
    latitude?: number;
    locale?: string;
    locationId?: number;
    longitude?: number;
    networkType?: 'UseDirect' | 'UseProxy' | 'UseSystem';
    password?: string;
    port?: number;
    probeOnSession?: boolean;
    proxyType?: string;
    remoteIp?: string;
    shopIdList?: number[];
    sshKey?: string;
    timezone?: string;
    updateTime?: string;
    username?: string;
  };

  type BatchUpdateShopRouterRequest = {
    routers?: ShopRouterTypeItem[];
    shopIds?: number[];
  };

  type BatchUpdateWorkTimeConfigRequest = {
    FRI?: number[];
    MON?: number[];
    SAT?: number[];
    SUN?: number[];
    THU?: number[];
    TUE?: number[];
    WED?: number[];
    mobileIds?: number[];
  };

  type BindFingerprintOrTemplateRequest = {
    /** 是否绑定空闲指纹 */
    bindFingerprint?: boolean;
    /** Windows | Linux | macOS */
    clientPlatform?: string;
    /** 是否绑定指定模版 */
    fingerprintTemplateId?: number;
    shopIdList?: number[];
  };

  type BlockElementItem = {
    description?: string;
    element?: string;
    enabled?: boolean;
    /** 如果refId不为空表示引用团队配置，其它属性会被忽略 */
    refId?: number;
    url?: string;
    wholePage?: boolean;
  };

  type BlockElementVo = {
    description?: string;
    element?: string;
    enabled?: boolean;
    id?: number;
    refId?: number;
    shopId?: number;
    teamId?: number;
    url?: string;
    wholePage?: boolean;
  };

  type BrowserMedia = {
    deviceId?: string;
    groupId?: string;
    label?: string;
  };

  type BrowserPlugin = {
    description?: string;
    filename?: string;
    name?: string;
    version?: string;
  };

  type BuyOperatingCloudIpOrderRequest = {
    /** 已经勾选阅读和同意使用协议，没什么用 */
    agreement?: boolean;
    /** 自动绑定分身ID */
    autoBindShopIds?: number[];
    /** 到期是否自动续费 */
    autoRenew?: boolean;
    /** 余额抵扣金额 */
    balanceAmount?: number;
    /** 云账户id */
    cloudId?: number;
    /** 每个goods购买多少个，必须与goodsIds顺序一一对应 */
    counts?: number[];
    /** 购买时长，根据 periodUnit的值 有可能是月，周或天 */
    duration?: number;
    /** ip方案对应的商品id */
    goodsIds?: number[];
    /** 是否立即支付（点稍候支付该属性传false） */
    immediatePay?: boolean;
    payType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    /** 代金券抵扣金额，不得大于代金券余额 */
    voucherAmount?: number;
    /** 要使用的代金券id */
    voucherId?: number;
  };

  type CalcRenewIpResponse = {
    /** 打折减掉的金额(如果是打折的话) */
    discountAmount?: number;
    /** 记录按月续费的价格及折扣，<key=ipId || rpaVoucherId, value=PriceInfo> */
    monthItems?: Record<string, any>;
    /** 订单应付价(减掉了打折等信息) */
    payablePrice?: number;
    /** 订单总成本 */
    totalCost?: number;
    /** 订单总价(原价) */
    totalPrice?: number;
    /** 记录按周续费的价格及折扣，<key=ipId || rpaVoucherId, value=PriceInfo> */
    weekItems?: Record<string, any>;
  };

  type ChangeViewResourceRequest = {
    action?: 'add' | 'delete' | 'update';
    resourceIds?: number[];
  };

  type ChosenTransitGroupVo = {
    groupId?: number;
    transitIds?: number[];
  };

  type ClashProxyDto = {
    countryCode?: string;
    createTime?: string;
    currentClients?: number;
    enabled?: boolean;
    failover?: boolean;
    id?: number;
    jump?: boolean;
    lastUpdateTime?: string;
    location?: string;
    multiplier?: number;
    name?: string;
    originalJson?: string;
    paused?: boolean;
    profileId?: number;
    provider?: string;
    type?: string;
    url?: string;
  };

  type CloudAdbAddress = {
    adbAddress?: string;
    adbFrontends?: string;
  };

  type CloudMobileInfo = {
    /** 是否自动续费 */
    autoRenew?: boolean;
    cloudMobileInsId?: number;
    id?: number;
    networkEnabled?: boolean;
    padCode?: string;
    /** 计价周期单位 */
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    /** 手机代理 */
    proxy?: string;
    /** 续费价格 */
    renewPrice?: number;
    /** 过期时间 */
    validEndDate?: string;
  };

  type CloudMobileVo = {
    androidVersion?: string;
    connectType?: 'ARMCLOUD' | 'Baidu' | 'QCloud' | 'USB' | 'WIFI';
    createTime?: string;
    id?: number;
    /** adb shell getprop ro.product.model */
    mode?: string;
    name?: string;
    /** 实例编号 */
    padCode?: string;
    screenHeight?: number;
    screenWidth?: number;
  };

  type CommonCell = {
    c?: number;
    linkType?: 'DOCUMENT' | 'EMAIL' | 'FILE' | 'URL';
    val?: Record<string, any>;
  };

  type CommonExcel = {
    sheets?: CommonSheet[];
  };

  type CommonIdsRequest = {
    ids?: number[];
  };

  type CommonRow = {
    cells?: CommonCell[];
    r?: number;
  };

  type CommonSheet = {
    /** sheet名称 */
    name?: string;
    rows?: CommonRow[];
  };

  type ConfigVo = {
    configKey?: string;
    configValue?: string;
  };

  type ConnectQCloudMobileRequest = {
    /** 后台调用传递数据用，客户端不用传 */
    clientId?: string;
    clientSession?: string;
    mobileId?: number;
  };

  type CopyShopPropsRequest = {
    /** 迁移之前是否清空目标分身的Cookie数据 */
    clearTargetCookies?: boolean;
    /** Cookie冲突策略: override | skip，其它任何值认为是 override */
    cookieConflictPolicy?: string;
    enableCookies?: boolean;
    enableFingerprint?: boolean;
    enableIndexDb?: boolean;
    enableLocalstorage?: boolean;
    /** 迁移并替换目标分身的网站密码 */
    enablePasswords?: boolean;
    sourceShopId?: number;
    targetShopId?: number;
  };

  type CountryDto = {
    code?: string;
    continentCode?: string;
    continentName?: string;
    continentNameEn?: string;
    geonameId?: number;
    id?: number;
    inEu?: boolean;
    name?: string;
    nameEn?: string;
    show?: boolean;
  };

  type CreateBlockElementRequest = {
    description?: string;
    element?: string;
    enabled?: boolean;
    /** 如果为空表示操作团队的配置(但是团队配置和店铺配置使用的是不同的接口) */
    shopId?: number;
    url?: string;
    wholePage?: boolean;
  };

  type CreateBuyIpOrderRequest = {
    /** 已经勾选阅读和同意使用协议，没什么用 */
    agreement?: boolean;
    /** 自动绑定分身ID */
    autoBindShopIds?: number[];
    /** 到期是否自动续费 */
    autoRenew?: boolean;
    /** 余额抵扣金额 */
    balanceAmount?: number;
    /** 每个goods购买多少个，必须与goodsIds顺序一一对应 */
    counts?: number[];
    /** 购买时长，根据 periodUnit的值 有可能是月，周或天 */
    duration?: number;
    /** ip方案对应的商品id */
    goodsIds?: number[];
    /** 是否立即支付（点稍候支付该属性传false） */
    immediatePay?: boolean;
    payType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    /** 代金券抵扣金额，不得大于代金券余额 */
    voucherAmount?: number;
    /** 要使用的代金券id */
    voucherId?: number;
  };

  type CreateIppIpRequest = {
    ips?: CreateIppIpVo[];
  };

  type CreateIppIpVo = {
    host?: string;
    ip?: string;
    password?: string;
    port?: number;
    proxyType?: 'http' | 'httpTunnel' | 'ipgo' | 'luminati' | 'socks5' | 'ssh' | 'vps';
    status?: 'Available' | 'Pending' | 'Unavailable';
    testingTime?: number;
    username?: string;
  };

  type CreateIpRenewOrderRequest = {
    /** 已经勾选阅读和同意使用协议，没什么用 */
    agreement?: boolean;
    /** 余额抵扣金额 */
    balanceAmount?: number;
    /** 是否立即支付（点稍候支付该属性传false） */
    immediatePay?: boolean;
    /** 有哪些ip需要续费 */
    ipIds?: number[];
    /** 其中按月续费的续几月 */
    monthDuration?: number;
    payType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    /** 代金券抵扣金额，不得大于代金券余额 */
    voucherAmount?: number;
    /** 要使用的代金券id */
    voucherId?: number;
    /** 其中按周续费的续几周 */
    weekDuration?: number;
  };

  type CreateMobileAccountRequest = {
    accountType?: 'Business' | 'Regular';
    createTime?: string;
    creatorId?: number;
    description?: string;
    mobileId?: number;
    platformId?: number;
    tags?: string[];
    teamId?: number;
    /** 账号名称 */
    username?: string;
  };

  type CreateMobileGroupRequest = {
    name?: string;
    sortNumber?: number;
  };

  type CreateMobileRequest = {
    /** adb shell getprop ro.product.vndk.version */
    androidVersion?: string;
    /** adb devices -l 获取到的设备标识 */
    code?: string;
    /** 连接类型，默认值 USB，为空就是也是USB */
    connectType?: 'ARMCLOUD' | 'Baidu' | 'QCloud' | 'USB' | 'WIFI';
    description?: string;
    /** adb shell getprop ro.product.model */
    mode?: string;
    /** 手机设备名字，例如 MI 14 */
    name?: string;
    /** android | ios */
    platform?: 'Android' | 'IOS';
    screenHeight?: number;
    screenWidth?: number;
    status?: 'EXPIRED' | 'OFFLINE' | 'ONLINE' | 'RESETING';
  };

  type CreateOrderResponse = {
    bankAccount?: string;
    bankAccountName?: string;
    bankName?: string;
    bankRemark?: string;
    createTime?: string;
    /** 扣减金额 */
    deductedPrice?: number;
    orderId?: number;
    /** 支付输出的内容 */
    payOutContent?: string;
    /** 支付输出的内容类型 */
    payOutType?: string;
    /** 如果不需要现金支付，该订单状态会直接变成已支付 */
    payStatus?:
      | 'CANCELED'
      | 'Created'
      | 'Locked'
      | 'PAID'
      | 'PartialRefund'
      | 'REFUNDED'
      | 'WAIT_CONFIRM';
    /** 支付方式 */
    payType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    /** 需要现金支付的money */
    realPrice?: number;
    salesReduction?: number;
    serialNumber?: string;
  };

  type CreateProxyIpRequest = {
    ips?: CreateProxyIpVo[];
  };

  type CreateProxyIpVo = {
    advancedSet?: boolean;
    description?: string;
    dynamic?: boolean;
    dynamicIp?: string;
    /** 是否开启白名单 */
    enableWhitelist?: boolean;
    exclusive?: boolean;
    /** 允许的链接方式 */
    groups?: ChosenTransitGroupVo[];
    host?: string;
    ip?: string;
    ipVersion?: 'Auto' | 'IPv4' | 'IPv6';
    ipv6?: boolean;
    name?: string;
    password?: string;
    port?: number;
    provider?: string;
    proxyType?: string;
    refreshUrl?: string;
    sshKey?: string;
    status?: 'Available' | 'Pending' | 'Unavailable';
    teamId?: number;
    testingTime?: number;
    /** 切换策略 */
    transitType?: 'Auto' | 'Direct' | 'Transit';
    tunnelTypes?: ('clash' | 'direct' | 'jump' | 'localFrontend' | 'platform' | 'transit')[];
    username?: string;
  };

  type CreateShopSnapshotPlanRequest = {
    /** 快照有哪些数据，逗号隔开，允许值 : Cookies,LocalStorage,IndexedDB,ExtensionData,Histories,Fingerprint */
    contents?: string;
    /** 执行时间表达式的星期部分 */
    cronDay?: string;
    /** 执行时间表达式的时分秒部分 */
    cronTime?: string;
    description?: string;
    /** 保留个数 */
    keepCount?: number;
    name?: string;
  };

  type CreateShopSnapshotRequest = {
    contents?: string;
    description?: string;
    name?: string;
    /** 如果shopIds不为空该字段无意义 */
    shopId?: number;
    /** 如果不为空表示批量创建 */
    shopIds?: number[];
  };

  type CreateShopsRequest = {
    /** 会话是否允许监视 */
    allowMonitor?: boolean;
    /** 允许跳过敏感操作 */
    allowSkip?: boolean;
    /** 创建PC指纹分身数量 */
    count?: number;
    /** 备注 */
    description?: string;
    disableLongLatitude?: boolean;
    disableWebrtc?: boolean;
    /** 是否独占访问 */
    exclusive?: boolean;
    /** 浏览器首页类型 */
    homePageType?: 'checker' | 'defaults' | 'frontUrl' | 'loginUrl' | 'restoreSession';
    /** 绑定的IP */
    ipId?: number;
    /** 绑定的IP池 */
    ippId?: number;
    /** 创建手机指纹分身数量 */
    mobileCount?: number;
    /** 如何生成android指纹 */
    mobileParams?: GenFingerParams;
    /** 店铺名称前缀，创建数量为1时，代表分身名称 */
    namePrefix?: string;
    /** 经营品类 */
    operatingCategory?:
      | '医药保健'
      | '图书文具'
      | '宠物用品'
      | '家具建材'
      | '家电电器'
      | '工业用品'
      | '户外运动'
      | '手机数码'
      | '手表眼镜'
      | '护肤美妆'
      | '母婴玩具'
      | '汽车配件'
      | '生活家居'
      | '电商其他'
      | '电脑平板'
      | '艺术珠宝'
      | '花园聚会'
      | '计生情趣'
      | '软件程序'
      | '鞋服箱包'
      | '音乐影视'
      | '食品生鲜'
      | '鲜花绿植';
    /** 店铺账号密码 */
    password?: string;
    /** 如何生成pc指纹 */
    pcParams?: GenFingerParams;
    /** 平台ID */
    platformId?: number;
    /** 录像策略 */
    recordPolicy?: 'Chosen' | 'Disabled' | 'Forced';
    /** 本地代理 */
    shopLanProxy?: ShopLanProxyVo;
    /** 是否创建无状态分身 */
    stateless?: boolean;
    /** 店铺类型 */
    type?: 'Global' | 'Local' | 'None';
    /** 店铺账号 */
    username?: string;
  };

  type CreateSnapshotForAllShopsRequest = {
    contents?: string;
    description?: string;
    name?: string;
  };

  type CustomerIpInfo = {
    /** 自有云账户IP的商品 */
    goods?: GoodsDto;
    regions?: IpRegionVo[];
  };

  type dashboardActiveSessionsGetParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** sortFiled */
    sortFiled?: 'create_time' | 'creator_nickname' | 'shop_name';
    /** sortOrder */
    sortOrder?: 'asc' | 'desc';
  };

  type dashboardLastUserActivityGetParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** sortFiled */
    sortFiled?: 'last_login_time' | 'online';
    /** sortOrder */
    sortOrder?: 'asc' | 'desc';
  };

  type dashboardMyShopSessionLastAccessGetParams = {
    /** limit */
    limit?: number;
  };

  type dashboardPendingRpaPlansGetParams = {
    /** limit */
    limit?: number;
  };

  type dashboardRpaPlansGetParams = {
    /** limit */
    limit?: number;
  };

  type dashboardRpaTaskListGetParams = {
    /** limit */
    limit?: number;
  };

  type dashboardTeamShopSessionLastAccessGetParams = {
    /** limit */
    limit?: number;
  };

  type DeductionAndSubmitRequest = {
    udid?: string;
  };

  type DepartmentDto = {
    createTime?: string;
    hidden?: boolean;
    id?: number;
    invitingAuditEnabled?: boolean;
    invitingEnabled?: boolean;
    name?: string;
    parentId?: number;
    sortNumber?: number;
    teamId?: number;
  };

  type DirectDomainItem = {
    description?: string;
    domain?: string;
    enabled?: boolean;
  };

  type DirectDomainsDto = {
    description?: string;
    domain?: string;
    enabled?: boolean;
    id?: number;
    shopId?: number;
    teamId?: number;
  };

  type DiscountsVo = {
    /** 赠送数量或折扣百分比或阶梯折扣百分比 */
    amount?: number;
    /** 打折code */
    discountCode?: string;
    /** 打折还是赠送 */
    discountType?: 'Discount' | 'LadderPrice' | 'Present';
    /** 周期或数量单位 */
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    /** 备注 */
    remarks?: string;
    /** 期数或数量 */
    threshold?: number;
  };

  type DistributeFileToMobilesRequest = {
    /** 在手机上的保存路径，腾讯云手机要求只能上传到 /sdcard/ 目录或其子目录下，示例值：/sdcard/Download/ */
    destinationDirectory?: string;
    /** 文件的下载地址，要求不需要鉴权 */
    fileUrl?: string;
    mobileIds?: number[];
    /** 如果是通过 /api/shop/mobile/qcloud/tempFile/putOssUrl 拿到的路径，需要传递该参数，以便上传完成之后可以迅速删除oss的缓存以降低成本 */
    ossPath?: string;
  };

  type DistributionCodeVo = {
    amount?: number;
    code?: string;
    createTime?: string;
    description?: string;
    discount?: PartnerDiscountDto;
    discountId?: number;
    distributionType?: 'Deduction' | 'Discount' | 'Official';
    distributor?: number;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    limited?: boolean;
    limitedGoods?: GoodsDto[];
    name?: string;
    systemDefault?: boolean;
    usageCount?: number;
    usedCount?: number;
    valid?: boolean;
    validDays?: number;
  };

  type distributionCodeWithGoodsGetParams = {
    /** 商品ID */
    goodsType:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    /** 优惠码 */
    distributionCode: string;
  };

  type DomainCookieVo = {
    /** 该域名下cookie数量 */
    count?: number;
    domain?: string;
  };

  type DomainWhitelistItem = {
    domain?: string;
  };

  type DwlAuditDto = {
    auditRemark?: string;
    auditStatus?: 'APPROVED' | 'CANCEL' | 'NEW' | 'NOT_PASS';
    auditTime?: string;
    auditorId?: number;
    auditorName?: string;
    company?: string;
    createTime?: string;
    domain?: string;
    effectAll?: boolean;
    id?: number;
    reason?: string;
    teamId?: number;
    teamName?: string;
    userId?: number;
    userName?: string;
    userPhone?: string;
  };

  type DwlAuditVo = {
    auditRemark?: string;
    auditStatus?: 'APPROVED' | 'CANCEL' | 'NEW' | 'NOT_PASS';
    auditTime?: string;
    auditedDomain?: string;
    auditorId?: number;
    auditorName?: string;
    company?: string;
    createTime?: string;
    domain?: string;
    effectAll?: boolean;
    id?: number;
    reason?: string;
    teamId?: number;
    teamName?: string;
    userId?: number;
    userName?: string;
    userPhone?: string;
  };

  type EditBlockElementRequest = {
    description?: string;
    element?: string;
    enabled?: boolean;
    id?: number;
    /** 如果为空表示操作团队的配置(但是团队配置和店铺配置使用的是不同的接口) */
    shopId?: number;
    url?: string;
    wholePage?: boolean;
  };

  type EndpointVo = {
    bgpPro?: boolean;
    direct?: boolean;
    endpoint?: string;
    groups?: TransitGroupDto[];
    ipEndpoint?: string;
    ipEndpointEnabled?: boolean;
    load?: number;
    probeCode?: number;
    probeError?: string;
    specialTrafficPrice?: number;
    status?: 'Available' | 'Pending' | 'Unavailable';
    /** 接入点到IP测试时间，为-1表示未进行过测试或测试不可用 */
    testingTime?: number;
    trafficPrice?: number;
    transitId?: number;
    transitIpv6?: boolean;
    transitName?: string;
  };

  type ExcelAccountWithIpVo = {
    area?: string;
    connectiontype?: string;
    cookie?: string;
    dynamicIp?: string;
    forceRecord?: string;
    ip?: string;
    ipname?: string;
    ipv6?: string;
    name?: string;
    password?: string;
    platform?: string;
    platformId?: number;
    proxy?: string;
    proxytype?: string;
    tags?: string;
    username?: string;
  };

  type excelGenePostParams = {
    /** name */
    name?: string;
  };

  type ExcelIpImportVo = {
    connectiontype?: string;
    dynamicIp?: string;
    host?: string;
    ipv6?: string;
    name?: string;
    password?: string;
    port?: number;
    proxy?: string;
    proxytype?: string;
    transitType?: 'Auto' | 'Direct' | 'Transit';
    username?: string;
  };

  type ExcelIppIPImportVo = {
    proxy?: string;
    proxytype?: string;
  };

  type ExportShopRequest = {
    area?: boolean;
    cookie?: boolean;
    platform?: boolean;
    shopIds?: number[];
  };

  type ExtensionImageVo = {
    description?: string;
    id?: number;
    url?: string;
  };

  type ExtensionPolicyVo = {
    allowExtension?: boolean;
  };

  type extensionsByExtensionIdGetParams = {
    /** extensionId */
    extensionId: number;
  };

  type extensionsByExtensionIdImagesGetParams = {
    /** extensionId */
    extensionId: number;
  };

  type extensionsByExtensionIdRefreshPutParams = {
    /** extensionId */
    extensionId: number;
  };

  type extensionsListGetParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** 插件类别，为空表示全部类别 */
    category?: string;
    /** q */
    q?: string;
  };

  type extensionsStoreByStoreIdGetParams = {
    /** storeId */
    storeId: string;
  };

  type extensionsTeamByIdDeleteParams = {
    /** id */
    id: number;
    /** 如果当前插件正被分身引用，是否强制删除 */
    force?: boolean;
  };

  type extensionsTeamListGetParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** q */
    q?: string;
  };

  type extensionsTeamUpdatePostParams = {
    /** param */
    param: string;
  };

  type ExtensionsVo = {
    /** 插件类别 */
    category?: 'ChooseGoods' | 'General' | 'Marketing' | 'Productivity' | 'ShopOperator';
    /** 创建时间 */
    createTime?: string;
    /** crx文件路径 */
    crx?: string;
    /** 详细描述 */
    description?: string;
    /** 图标;图标地址 */
    icon?: string;
    id?: number;
    installed?: boolean;
    /** 提供商 */
    provider?: string;
    /** 提供商网站 */
    providerSite?: string;
    /** 上架时间 */
    publishTime?: string;
    /** 评分，0-100分 */
    score?: number;
    /** 简短描述 */
    shortDesc?: string;
    /** 当前状态;正上架，已下架等 */
    status?: 'Normal' | 'OffLine';
    /** 插件市场最后一次更新日期(可能为空) */
    storeUpdateDate?: string;
    /** 如果不为空且不为0，表明该插件是团队自有插件 */
    teamId?: number;
    /** 标题 */
    title?: string;
    /** 更新时间 */
    updateTime?: string;
    /** 用户数 */
    userCount?: number;
    /** 版本号 */
    version?: string;
  };

  type FavoriteSiteItem = {
    description?: string;
    /** 如果是编辑，该项会被忽略 */
    favoriteScope?: 'BM_Shop' | 'BM_User' | 'Shop' | 'Team' | 'User';
    name?: string;
    /** 技术/Java 表示该项位于目录  技术/Java 下，为空表示根目录 */
    path?: string;
    /** 排序号，后台只记录，不维护 */
    sortNumber?: number;
    url?: string;
  };

  type FavoriteSiteVo = {
    description?: string;
    favoriteScope?: 'BM_Shop' | 'BM_User' | 'Shop' | 'Team' | 'User';
    id?: number;
    name?: string;
    ownerId?: number;
    path?: string;
    sortNumber?: number;
    teamId?: number;
    url?: string;
  };

  type FeeSummaryVo = {
    /** 账户余额 */
    balance?: number;
    /** 3天内过期的IP余额不足以续费 */
    balanceInsufficient?: boolean;
    /** 花瓣余额 */
    creditBalance?: number;
    /** 花瓣余额还可以维持的天数 */
    creditRemainDays?: number;
    /** 临期的平台IP数量 */
    expiringIpCount?: number;
    /** 临期IP的续费总价 */
    expiringIpPrice?: number;
    /** 未开启自动续费的临期平台IP数量 */
    notAutoRenewExpiringIpCount?: number;
    /** 未开启自动续费的平台IP数量 */
    notAutoRenewPlatformIpCount?: number;
    /** 平台IP数量 */
    platformIpCount?: number;
    /** 小于当前花瓣时打开会话告警 */
    sessionAlertRemainCredit?: number;
    /** 昨日消费金额 */
    yesterdayCost?: number;
    /** 昨日消耗花瓣 */
    yesterdayCostCredit?: number;
  };

  type FetchTokenDetailVo = {
    expireTime?: string;
    platformArea?:
      | 'Argentina'
      | 'Australia'
      | 'Austria'
      | 'Belarus'
      | 'Belgium'
      | 'Bolivia'
      | 'Brazil'
      | 'Canada'
      | 'Chile'
      | 'China'
      | 'Colombia'
      | 'Costa_Rica'
      | 'Dominican'
      | 'Ecuador'
      | 'Egypt'
      | 'France'
      | 'Germany'
      | 'Global'
      | 'Guatemala'
      | 'Honduras'
      | 'HongKong'
      | 'India'
      | 'Indonesia'
      | 'Ireland'
      | 'Israel'
      | 'Italy'
      | 'Japan'
      | 'Kazakhstan'
      | 'Korea'
      | 'Malaysia'
      | 'Mexico'
      | 'Netherlands'
      | 'Nicaragua'
      | 'Panama'
      | 'Paraguay'
      | 'Peru'
      | 'Philippines'
      | 'Poland'
      | 'Portuguese'
      | 'Puerto_Rico'
      | 'Russia'
      | 'Salvador'
      | 'Saudi_Arabia'
      | 'Singapore'
      | 'Spain'
      | 'Sweden'
      | 'Switzerland'
      | 'Taiwan'
      | 'Thailand'
      | 'Turkey'
      | 'United_Arab_Emirates'
      | 'United_Kingdom'
      | 'United_States'
      | 'Uruguay'
      | 'Venezuela'
      | 'Vietnam';
    platformCategory?: 'IM' | 'Mail' | 'Other' | 'Payment' | 'Shop' | 'SocialMedia';
    platformId?: number;
    platformType?: string;
    shopId?: number;
    shopName?: string;
    teamId?: number;
    teamName?: string;
    token?: string;
  };

  type FetchTokenVo = {
    expire?: number;
    shopId?: number;
    token?: string;
    userId?: number;
  };

  type FingerBindHisVo = {
    bindTime?: string;
    computerName?: string;
    configId?: string;
    createType?: 'Fetch' | 'Gen' | 'HyGen' | 'MKLong' | 'Temp' | 'Template' | 'Transfer';
    fingerprintCreatorId?: number;
    fingerprintCreatorName?: string;
    fingerprintId?: number;
    id?: number;
    md5sum?: string;
    shopId?: number;
    teamId?: number;
    templateName?: string;
    unbindTime?: string;
  };

  type fingerByFingerprintIdBindByShopIdPutParams = {
    /** fingerprintId */
    fingerprintId: number;
    /** shopId */
    shopId: number;
  };

  type fingerByFingerprintIdUpdatePostParams = {
    /** fingerprintId */
    fingerprintId: number;
  };

  type fingerCheckWhetherNeedUpgradeGetParams = {
    /** clientMajorVersion */
    clientMajorVersion: number;
    /** fingerprintId */
    fingerprintId?: number;
  };

  type fingerCountByTeamGetParams = {
    /** teamId */
    teamId: number;
  };

  type fingerCountIdleGetParams = {
    /** 类型(大小写敏感)：pc|mobile ，为空表示查所有空闲实例个数 */
    type?: string;
  };

  type FingerCountResult = {
    fingerprintCount?: number;
    templateCount?: number;
  };

  type fingerDeleteDeleteParams = {
    /** fingerprintIds */
    fingerprintIds: number;
  };

  type fingerDetailByFingerprintIdGetParams = {
    /** fingerprintId */
    fingerprintId: number;
  };

  type fingerFetchTokenByShopIdGetParams = {
    /** shopId */
    shopId: number;
    /** token有效期，默认10分钟 */
    expireMinutes?: number;
  };

  type FingerFetchTokenVo = {
    platformName?: string;
    shopId?: number;
    shopName?: string;
    teamId?: number;
    teamName?: string;
    validSeconds?: number;
  };

  type fingerGetByFingerprintIdGetParams = {
    /** fingerprintId */
    fingerprintId: number;
  };

  type fingerListAllGetParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** 格式为[field_name asc|desc], id,create_type,platform,browser */
    orderBy?: string;
    /** md5sum */
    md5sum?: string;
    /** platform */
    platform?: 'Android' | 'IOS' | 'Linux' | 'Mac' | 'Windows';
    /** 状态: bound|notBound ,为空表示所有 */
    status?: string;
  };

  type fingerListFreeGetParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** 格式为[field_name asc|desc], id,create_type,platform,browser */
    orderBy?: string;
    /** md5sum */
    md5sum?: string;
    /** platform */
    platform?: 'Android' | 'IOS' | 'Linux' | 'Mac' | 'Windows';
    /** 状态: bound|notBound ,为空表示所有 */
    status?: string;
  };

  type fingerListTemplateGetParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** 格式为[field_name asc|desc], id,create_type,platform,browser */
    orderBy?: string;
    /** 指纹模板id，不为空表示只查某个具体的模板指纹 */
    templateId?: number;
    /** md5sum */
    md5sum?: string;
    /** platform */
    platform?: 'Android' | 'IOS' | 'Linux' | 'Mac' | 'Windows';
    /** 状态: bound|notBound ,为空表示所有 */
    status?: string;
  };

  type FingerprintConfig = {
    /** audio噪声，0表示不指定 */
    audio?: number;
    /** 指定有哪些麦克风 */
    audioInputs?: BrowserMedia[];
    /** 指定有哪些喇叭 */
    audioOutputs?: BrowserMedia[];
    /** 当前是不是接上了电源 */
    batteryCharging?: boolean;
    /** 电池还有多久能充满 */
    batteryChargingTime?: number;
    /** 电池还能用多久 */
    batteryDischargingTime?: number;
    /** 电量百分比 */
    batteryLevel?: number;
    /** Assign表示自定义电池信息，否则等价于Original */
    batteryType?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    brand?: string;
    /** 平台，需与userAgent保持相关联 */
    browser?: string;
    /** canvas噪声模式 */
    canvasMode?: 'fillText';
    /** fillText的x */
    canvasTX?: number;
    /** 屏幕颜色深度，不是 [16, 24, 32] 这个值表示不指定 */
    colorDepth?: number;
    /** 色彩模式，不为空表示指定，否则等价于Original。['srgb' | 'p3' | 'rec2020'] */
    colorGamut?: string;
    /** 电脑名称 */
    computerName?: string;
    /** cpu核数，0表示不指定 */
    cpu?: number;
    /** window.devicePixelRatio */
    dpr?: number;
    /** 定义允许哪些字体 */
    fonts?: string;
    /** 指定额外的http header */
    headers?: string[];
    /** id */
    id?: string;
    /** 浏览器所使用的语言 */
    lang?: string;
    /** 语言指定方法，Assign表示指定，Auto表示跟随Ip，否则等价于Original */
    lang_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** 浏览器所在位置 */
    location?: string;
    /** 位置指定方法，Disabled表示直接拒绝，Assign表示指定，Auto表示跟随Ip，否则等价于Original */
    location_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** MAC地址 */
    mac?: string;
    /** 媒体设备指定方法，Assign表示指定，否则等价于Original */
    mediaType?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** 内存数量，0表示不指定，单位G */
    mem?: number;
    /** 由哪个configId生成 */
    parentId?: string;
    /** 平台，需与userAgent保持相关联 */
    platform?: string;
    /** 浏览器插件指定方法，Assign表示指定，否则等价于Original */
    pluginType?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** 指定有哪些插件 */
    plugins?: BrowserPlugin[];
    /** 允许扫描端口。不过没多大意义 */
    ports?: number[];
    /** 允许扫描端口指定方法，Assign表示指定，否则等价于Original */
    ports_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** rect噪声，区间(-1, 1)，绝对值大于0.0001。为空表示不添加噪声 */
    rectDX?: number;
    /** 屏幕分辨率，为空表示不指定 */
    screenSize?: string;
    /** 屏幕分辨率类型 */
    screenSize_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** 所在时区 */
    timezone?: string;
    /** 时区指定方法，Assign表示指定，Auto表示跟随Ip，否则等价于Original */
    timezone_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** 是否支持触摸屏 */
    touchSupported?: boolean;
    /** Assign表示自定义，否则等价于Original */
    touchSupported_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** user-agent */
    userAgent: string;
    /** uuid */
    uuid?: string;
    /** 指定有哪些摄像头 */
    videoInputs?: BrowserMedia[];
    /** webgl噪声，区间(-1, 1)，绝对值大于0.0001 */
    webglDX?: number;
    /** webgl renderer */
    webglRenderer?: string;
    /** webgl vendor */
    webglVendor?: string;
    /** webgl指定方法，Assign表示指定，否则等价于Original */
    webgl_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** webrtc内网ip */
    webrtcInnerIp?: string;
    /** webrtc内网ip指定方法，Assign表示指定，Auto表示跟随Ip，否则等价于Original */
    webrtcInnerIp_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** webrtc公网ip */
    webrtcPublicIp?: string;
    /** webrtc公网ip指定方法，Assign表示指定，Auto表示跟随Ip，否则等价于Original */
    webrtcPublicIp_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
  };

  type FingerprintConfigVo = {
    /** audio噪声，0表示不指定 */
    audio?: number;
    /** 指定有哪些麦克风 */
    audioInputs?: BrowserMedia[];
    /** 指定有哪些喇叭 */
    audioOutputs?: BrowserMedia[];
    /** 当前是不是接上了电源 */
    batteryCharging?: boolean;
    /** 电池还有多久能充满 */
    batteryChargingTime?: number;
    /** 电池还能用多久 */
    batteryDischargingTime?: number;
    /** 电量百分比 */
    batteryLevel?: number;
    /** Assign表示自定义电池信息，否则等价于Original */
    batteryType?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    brand?: string;
    /** 平台，需与userAgent保持相关联 */
    browser?: string;
    /** canvas噪声模式 */
    canvasMode?: 'fillText';
    /** fillText的x */
    canvasTX?: number;
    /** 屏幕颜色深度，不是 [16, 24, 32] 这个值表示不指定 */
    colorDepth?: number;
    /** 色彩模式，不为空表示指定，否则等价于Original。['srgb' | 'p3' | 'rec2020'] */
    colorGamut?: string;
    /** 电脑名称 */
    computerName?: string;
    /** cpu核数，0表示不指定 */
    cpu?: number;
    /** window.devicePixelRatio */
    dpr?: number;
    /** 定义允许哪些字体 */
    fonts?: string;
    /** 指定额外的http header */
    headers?: string[];
    /** id */
    id?: string;
    /** 浏览器所使用的语言 */
    lang?: string;
    /** 语言指定方法，Assign表示指定，Auto表示跟随Ip，否则等价于Original */
    lang_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** 浏览器所在位置 */
    location?: string;
    /** 位置指定方法，Disabled表示直接拒绝，Assign表示指定，Auto表示跟随Ip，否则等价于Original */
    location_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** MAC地址 */
    mac?: string;
    /** 媒体设备指定方法，Assign表示指定，否则等价于Original */
    mediaType?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** 内存数量，0表示不指定，单位G */
    mem?: number;
    /** 由哪个configId生成 */
    parentId?: string;
    /** 平台，需与userAgent保持相关联 */
    platform?: string;
    /** 浏览器插件指定方法，Assign表示指定，否则等价于Original */
    pluginType?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** 指定有哪些插件 */
    plugins?: BrowserPlugin[];
    /** 允许扫描端口。不过没多大意义 */
    ports?: number[];
    /** 允许扫描端口指定方法，Assign表示指定，否则等价于Original */
    ports_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** rect噪声，区间(-1, 1)，绝对值大于0.0001。为空表示不添加噪声 */
    rectDX?: number;
    /** 屏幕分辨率，为空表示不指定 */
    screenSize?: string;
    /** 屏幕分辨率类型 */
    screenSize_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** 所在时区 */
    timezone?: string;
    /** 时区指定方法，Assign表示指定，Auto表示跟随Ip，否则等价于Original */
    timezone_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** 是否支持触摸屏 */
    touchSupported?: boolean;
    /** Assign表示自定义，否则等价于Original */
    touchSupported_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** user-agent */
    userAgent: string;
    /** uuid */
    uuid?: string;
    /** 指定有哪些摄像头 */
    videoInputs?: BrowserMedia[];
    /** webgl噪声，区间(-1, 1)，绝对值大于0.0001 */
    webglDX?: number;
    /** webgl renderer */
    webglRenderer?: string;
    /** webgl vendor */
    webglVendor?: string;
    /** webgl指定方法，Assign表示指定，否则等价于Original */
    webgl_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** webrtc内网ip */
    webrtcInnerIp?: string;
    /** webrtc内网ip指定方法，Assign表示指定，Auto表示跟随Ip，否则等价于Original */
    webrtcInnerIp_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** webrtc公网ip */
    webrtcPublicIp?: string;
    /** webrtc公网ip指定方法，Assign表示指定，Auto表示跟随Ip，否则等价于Original */
    webrtcPublicIp_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
  };

  type FingerprintCreateParamVo = {
    /** 相应的指纹详细配置 */
    config: FingerprintConfigVo;
    /** 指纹描述 */
    description?: string;
    /** 指纹名称，非必须 */
    name?: string;
  };

  type FingerprintDetailVo = {
    /** 指纹对应浏览器名称，如chrome */
    browser?: 'Chrome' | 'Edge' | 'Safari';
    /** 只有在按模板查询的时候会有值 */
    config?: FingerprintConfig;
    /** mongo里的id */
    configId?: string;
    /** 备用字段，指纹类型，抓取or生成 */
    createType?: 'Fetch' | 'Gen' | 'HyGen' | 'MKLong' | 'Temp' | 'Template' | 'Transfer';
    /** 创建者。有可能可能为空! */
    creatorId?: number;
    /** 指纹实例创建者id（如果有的话） */
    creatorName?: string;
    /**  指纹描述 */
    description?: string;
    /** 和哪个指纹的md5sum一样，为空表示唯一 */
    duplicateId?: number;
    /** id */
    id?: number;
    /** 浏览器主版本号，如 109 */
    majorVersion?: number;
    md5sum?: string;
    /** 指纹名称 */
    name?: string;
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    /** 指纹对应平台 */
    platform?: 'Android' | 'IOS' | 'Linux' | 'Mac' | 'Windows';
    /** 当前绑定到了哪些店铺上，列表为空表示未绑定店铺 */
    shops?: ShopWithPlatformVo[];
    stateless?: boolean;
    /** 团队ID */
    teamId?: number;
    /** 指纹所属模板id */
    templateId?: number;
    /** 模板名称（如果有的话） */
    templateName?: string;
    /** 最后更新时间 */
    updateTime?: string;
  };

  type FingerprintDto = {
    /** 指纹对应浏览器名称，如chrome */
    browser?: 'Chrome' | 'Edge' | 'Safari';
    /** mongo里的id */
    configId?: string;
    /** 备用字段，指纹类型，抓取or生成 */
    createType?: 'Fetch' | 'Gen' | 'HyGen' | 'MKLong' | 'Temp' | 'Template' | 'Transfer';
    /** 创建者。有可能可能为空! */
    creatorId?: number;
    /**  指纹描述 */
    description?: string;
    /** id */
    id?: number;
    /** 浏览器主版本号，如 109 */
    majorVersion?: number;
    md5sum?: string;
    /** 指纹名称 */
    name?: string;
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    /** 指纹对应平台 */
    platform?: 'Android' | 'IOS' | 'Linux' | 'Mac' | 'Windows';
    stateless?: boolean;
    /** 团队ID */
    teamId?: number;
    /** 指纹所属模板id */
    templateId?: number;
    /** 最后更新时间 */
    updateTime?: string;
  };

  type FingerprintDuplicateVo = {
    duplicateShops?: SimpleShopVo[];
    md5sum?: string;
  };

  type FingerprintTemplateCache = {
    name?: string;
    token?: string;
  };

  type FingerprintTemplateConfigVo = {
    /** 指定有哪些麦克风 */
    audioInputs?: BrowserMedia[];
    /** 指定有哪些喇叭 */
    audioOutputs?: BrowserMedia[];
    /** 当前是不是接上了电源 */
    batteryCharging?: boolean;
    /** 电池还有多久能充满 */
    batteryChargingTime?: number;
    /** 电池还能用多久 */
    batteryDischargingTime?: number;
    /** 电量百分比 */
    batteryLevel?: number;
    /** Assign表示自定义电池信息，否则等价于Original */
    batteryType?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    brand?: string;
    /** 平台，需与userAgent保持相关联 */
    browser?: string;
    /** 屏幕颜色深度，不是 [16, 24, 32] 这个值表示不指定 */
    colorDepth?: number;
    /** 色彩模式，不为空表示指定，否则等价于Original。['srgb' | 'p3' | 'rec2020'] */
    colorGamut?: string;
    /** 电脑名称 */
    computerName?: string;
    /** cpu核数，0表示不指定 */
    cpu?: number;
    /** window.devicePixelRatio */
    dpr?: number;
    /** 定义允许哪些字体 */
    fonts?: string;
    /** id */
    id?: string;
    /** 浏览器所使用的语言 */
    lang?: string;
    /** 语言指定方法，Assign表示指定，Auto表示跟随Ip，否则等价于Original */
    lang_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** 浏览器所在位置 */
    location?: string;
    /** 位置指定方法，Disabled表示直接拒绝，Assign表示指定，Auto表示跟随Ip，否则等价于Original */
    location_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** 媒体设备指定方法，Assign表示指定，否则等价于Original */
    mediaType?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** 内存数量，0表示不指定，单位G */
    mem?: number;
    /** 由哪个configId生成 */
    parentId?: string;
    /** 平台，需与userAgent保持相关联 */
    platform?: string;
    /** 浏览器插件指定方法，Assign表示指定，否则等价于Original */
    pluginType?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** 指定有哪些插件 */
    plugins?: BrowserPlugin[];
    /** 屏幕分辨率，为空表示不指定 */
    screenSize?: string;
    /** 屏幕分辨率类型 */
    screenSize_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** 所在时区 */
    timezone?: string;
    /** 时区指定方法，Assign表示指定，Auto表示跟随Ip，否则等价于Original */
    timezone_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** 是否支持触摸屏 */
    touchSupported?: boolean;
    /** Assign表示自定义，否则等价于Original */
    touchSupported_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** user-agent */
    userAgent: string;
    /** 指定有哪些摄像头 */
    videoInputs?: BrowserMedia[];
    /** webgl renderer */
    webglRenderer?: string;
    /** webgl vendor */
    webglVendor?: string;
    /** webgl指定方法，Assign表示指定，否则等价于Original */
    webgl_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** webrtc内网ip */
    webrtcInnerIp?: string;
    /** webrtc内网ip指定方法，Assign表示指定，Auto表示跟随Ip，否则等价于Original */
    webrtcInnerIp_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** webrtc公网ip */
    webrtcPublicIp?: string;
    /** webrtc公网ip指定方法，Assign表示指定，Auto表示跟随Ip，否则等价于Original */
    webrtcPublicIp_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
  };

  type FingerprintTemplateCreateParamVo = {
    /** 相应的指纹模板详细配置 */
    config: FingerTemplateConfig;
    /** 指纹模板描述 */
    description?: string;
  };

  type FingerprintTemplateDto = {
    browser?: 'Chrome' | 'Edge' | 'Safari';
    configId?: string;
    createTime?: string;
    createType?: 'Fetch' | 'Gen' | 'HyGen' | 'MKLong' | 'Temp' | 'Template' | 'Transfer';
    creatorId?: number;
    description?: string;
    id?: number;
    /** 如果不为空则说明该模板生成的实例固定使用该版本号，否则使用随机版本 */
    majorVersion?: number;
    maxIns?: number;
    md5sum?: string;
    name?: string;
    platform?: 'Android' | 'IOS' | 'Linux' | 'Mac' | 'Windows';
    teamId?: number;
    usedIns?: number;
  };

  type FingerprintTemplateVo = {
    browser?: 'Chrome' | 'Edge' | 'Safari';
    configId?: string;
    createTime?: string;
    createType?: 'Fetch' | 'Gen' | 'HyGen' | 'MKLong' | 'Temp' | 'Template' | 'Transfer';
    creatorId?: number;
    creatorName?: string;
    description?: string;
    id?: number;
    /** 已生成实例个数 */
    instanceCount?: number;
    /** 如果不为空则说明该模板生成的实例固定使用该版本号，否则使用随机版本 */
    majorVersion?: number;
    maxIns?: number;
    md5sum?: string;
    name?: string;
    platform?: 'Android' | 'IOS' | 'Linux' | 'Mac' | 'Windows';
    /** 当前绑定到了哪些分身上，列表为空表示未绑定店铺 */
    shops?: ShopWithPlatformVo[];
    teamId?: number;
    usedIns?: number;
  };

  type FingerprintVo = {
    /** 指纹对应浏览器名称，如chrome */
    browser?: 'Chrome' | 'Edge' | 'Safari';
    /** mongo里的id */
    configId?: string;
    /** 备用字段，指纹类型，抓取or生成 */
    createType?: 'Fetch' | 'Gen' | 'HyGen' | 'MKLong' | 'Temp' | 'Template' | 'Transfer';
    /** 创建者。有可能可能为空! */
    creatorId?: number;
    creatorName?: string;
    /**  指纹描述 */
    description?: string;
    /** id */
    id?: number;
    /** 浏览器主版本号，如 109 */
    majorVersion?: number;
    md5sum?: string;
    /** 指纹名称 */
    name?: string;
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    /** 指纹对应平台 */
    platform?: 'Android' | 'IOS' | 'Linux' | 'Mac' | 'Windows';
    stateless?: boolean;
    /** 团队ID */
    teamId?: number;
    /** 指纹所属模板id */
    templateId?: number;
    templateName?: string;
    /** 最后更新时间 */
    updateTime?: string;
  };

  type fingerReportPostParams = {
    /** token */
    token: string;
  };

  type fingerSetShopByShopIdPostParams = {
    /** shopId */
    shopId: number;
  };

  type fingerShopByShopIdBindHisGetParams = {
    /** shopId */
    shopId: number;
  };

  type fingerTemplateByFingerprintTemplateIdBindByShopIdPutParams = {
    /** fingerprintTemplateId */
    fingerprintTemplateId: number;
    /** shopId */
    shopId: number;
  };

  type fingerTemplateByFingerTemplateIdDeleteParams = {
    /** fingerTemplateId */
    fingerTemplateId: number;
  };

  type fingerTemplateByFingerTemplateIdGetParams = {
    /** fingerTemplateId */
    fingerTemplateId: number;
  };

  type fingerTemplateByFingerTemplateIdUpdateNamePutParams = {
    /** fingerTemplateId */
    fingerTemplateId: number;
    /** name */
    name: string;
    /** description */
    description: string;
  };

  type FingerTemplateConfig = {
    /** 指定有哪些麦克风 */
    audioInputs?: BrowserMedia[];
    /** 指定有哪些喇叭 */
    audioOutputs?: BrowserMedia[];
    /** 当前是不是接上了电源 */
    batteryCharging?: boolean;
    /** 电池还有多久能充满 */
    batteryChargingTime?: number;
    /** 电池还能用多久 */
    batteryDischargingTime?: number;
    /** 电量百分比 */
    batteryLevel?: number;
    /** Assign表示自定义电池信息，否则等价于Original */
    batteryType?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    brand?: string;
    /** 平台，需与userAgent保持相关联 */
    browser?: string;
    /** 屏幕颜色深度，不是 [16, 24, 32] 这个值表示不指定 */
    colorDepth?: number;
    /** 色彩模式，不为空表示指定，否则等价于Original。['srgb' | 'p3' | 'rec2020'] */
    colorGamut?: string;
    /** 电脑名称 */
    computerName?: string;
    /** cpu核数，0表示不指定 */
    cpu?: number;
    /** window.devicePixelRatio */
    dpr?: number;
    /** 定义允许哪些字体 */
    fonts?: string;
    /** id */
    id?: string;
    /** 浏览器所使用的语言 */
    lang?: string;
    /** 语言指定方法，Assign表示指定，Auto表示跟随Ip，否则等价于Original */
    lang_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** 浏览器所在位置 */
    location?: string;
    /** 位置指定方法，Disabled表示直接拒绝，Assign表示指定，Auto表示跟随Ip，否则等价于Original */
    location_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** 媒体设备指定方法，Assign表示指定，否则等价于Original */
    mediaType?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** 内存数量，0表示不指定，单位G */
    mem?: number;
    /** 由哪个configId生成 */
    parentId?: string;
    /** 平台，需与userAgent保持相关联 */
    platform?: string;
    /** 浏览器插件指定方法，Assign表示指定，否则等价于Original */
    pluginType?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** 指定有哪些插件 */
    plugins?: BrowserPlugin[];
    /** 屏幕分辨率，为空表示不指定 */
    screenSize?: string;
    /** 屏幕分辨率类型 */
    screenSize_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** 所在时区 */
    timezone?: string;
    /** 时区指定方法，Assign表示指定，Auto表示跟随Ip，否则等价于Original */
    timezone_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** 是否支持触摸屏 */
    touchSupported?: boolean;
    /** Assign表示自定义，否则等价于Original */
    touchSupported_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** user-agent */
    userAgent: string;
    /** 指定有哪些摄像头 */
    videoInputs?: BrowserMedia[];
    /** webgl renderer */
    webglRenderer?: string;
    /** webgl vendor */
    webglVendor?: string;
    /** webgl指定方法，Assign表示指定，否则等价于Original */
    webgl_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** webrtc内网ip */
    webrtcInnerIp?: string;
    /** webrtc内网ip指定方法，Assign表示指定，Auto表示跟随Ip，否则等价于Original */
    webrtcInnerIp_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
    /** webrtc公网ip */
    webrtcPublicIp?: string;
    /** webrtc公网ip指定方法，Assign表示指定，Auto表示跟随Ip，否则等价于Original */
    webrtcPublicIp_type?: 'Assign' | 'Auto' | 'Disabled' | 'Original';
  };

  type fingerTemplateCountIdleGetParams = {
    /** 类型(大小写敏感)：pc|mobile ，为空表示查所有空闲实例个数 */
    type?: string;
  };

  type fingerTemplateDetailByFingerTemplateIdGetParams = {
    /** fingerTemplateId */
    fingerTemplateId: number;
  };

  type fingerTemplateFetchTokenGetParams = {
    /** token有效期，默认10分钟 */
    expireMinutes?: number;
  };

  type FingerTemplateFetchTokenVo = {
    teamId?: number;
    teamName?: string;
    validSeconds?: number;
  };

  type fingerTemplateListGetParams = {
    /** 按名称模糊查询或按md5查询 */
    search?: string;
    /** 格式为[field_name asc|desc], id,create_type,platform,browser */
    orderBy?: string;
  };

  type fingerTemplateReportPostParams = {
    /** token */
    token: string;
  };

  type fingerTemplateTokenByTokenGetParams = {
    /** token */
    token: string;
  };

  type fingerTemplateUnbindByShopIdPutParams = {
    /** shopId */
    shopId: number;
  };

  type fingerTokenByTokenGetParams = {
    /** token */
    token: string;
  };

  type fingerUnbindByShopIdPutParams = {
    /** shopId */
    shopId: number;
  };

  type FingerUpgradeCheckResult = {
    upgradeClient?: boolean;
    upgradeFingers?: boolean;
  };

  type fingerUpgradeFingerUseragentPutParams = {
    /** 为空表示升级团队所有的，不为空表示只升级指定的 */
    fingerprintId?: number;
  };

  type FreeTemplateQuota = {
    quota?: number;
    used?: number;
  };

  type GatewayDto = {
    associatedId?: number;
    associatedType?: string;
    buildTime?: string;
    cloudProvider?:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    commitId?: string;
    cores?: number;
    createTime?: string;
    creator?: number;
    description?: string;
    dfPort?: number;
    domestic?: boolean;
    hostName?: string;
    id?: number;
    identifier?: string;
    innerIp?: string;
    instanceId?: string;
    ipVersion?: 'All' | 'Custom' | 'IPv4' | 'IPv6' | 'None';
    memory?: number;
    orderItemId?: number;
    osArch?: string;
    osName?: string;
    osVersion?: string;
    publicIp?: string;
    region?: string;
    remoteIp?: string;
    secretKey?: string;
    status?: 'DELETED' | 'READY' | 'STOPPED';
    teamId?: number;
    templateId?: number;
    updateEnabled?: boolean;
    updateTime?: string;
    uptime?: string;
    version?: string;
    vpsId?: number;
  };

  type GatewayProxyDto = {
    auth?: boolean;
    bindIp?: string;
    createTime?: string;
    downTraffic?: number;
    enabled?: boolean;
    forwardIp?: string;
    gatewayId?: number;
    id?: number;
    ipId?: number;
    lastServeTime?: string;
    password?: string;
    paused?: boolean;
    proxyIp?: string;
    proxyPort?: number;
    proxyType?: string;
    remarks?: string;
    teamId?: number;
    totalDownTraffic?: number;
    totalUpTraffic?: number;
    upTraffic?: number;
    username?: string;
    whitelist?: string;
    whitelistEnabled?: boolean;
  };

  type GenFingerParams = {
    browser?: 'Chrome' | 'Edge' | 'Safari';
    platform?: 'Android' | 'IOS' | 'Linux' | 'Mac' | 'Windows';
    /** 生成的ua版本，如果为空表示随机版本 */
    version?: number;
  };

  type GiftCardPackDetailVo = {
    cardCount?: number;
    createTime?: string;
    giftCardType?: 'Credit' | 'RpaVoucher';
    id?: number;
    name?: string;
    packItems?: GiftCardPackItemDetailVo[];
    partnerId?: number;
    /** 剩余可用礼品卡数量 */
    remainCount?: number;
    remarks?: string;
    serialNumber?: string;
    /** 是否在有效期内（根据createTime & validDays计算出来的） */
    valid?: boolean;
    validDays?: number;
  };

  type GiftCardPackItemDetailVo = {
    activatedTeamId?: number;
    activatedTeamName?: string;
    activeTime?: string;
    amount?: number;
    cardNumber?: string;
    cardPackId?: number;
    cardPassword?: string;
    createTime?: string;
    disabled?: boolean;
    /** 当为rpa礼品卡时，表示该卡有几个 periodUnit 月/周 */
    duration?: number;
    expireDate?: string;
    giftCardType?: 'Credit' | 'RpaVoucher';
    goodsId?: number;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    orderItemId?: number;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    remarks?: string;
    /** 是否在有效期内（根据createTime & validDays计算出来的） */
    valid?: boolean;
  };

  type GlobalViewVo = {
    view?: ViewVo;
    viewId?: string;
  };

  type GoodsDto = {
    arch?: string;
    bandwidth?: number;
    buyoutPrice?: number;
    city?: string;
    cost?: number;
    countryCode?: string;
    cpu?: number;
    currency?: 'CREDIT' | 'RMB' | 'USD';
    dayCost?: number;
    dayPrice?: number;
    dayTraffic?: number;
    description?: string;
    disk?: number;
    diskCategory?: string;
    dynamic?: boolean;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    initialQuantity?: number;
    instanceType?: string;
    ipv6?: boolean;
    listPrice?: number;
    mem?: number;
    name?: string;
    networkType?: 'cloudIdc' | 'mobile' | 'proxyIdc' | 'residential' | 'unknown' | 'unknownIdc';
    onSale?: 'Disabled' | 'Offline' | 'Online';
    perfLevel?:
      | 'CostEffective'
      | 'HighlyConcurrent'
      | 'LargeTraffic'
      | 'None'
      | 'RemoteLogin'
      | 'UnlimitedTraffic';
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    pipeType?: 'None' | 'Proxy' | 'Tunnel' | 'TunnelFailToProxy';
    platform?: 'android' | 'linux' | 'macos' | 'unknown' | 'web' | 'windows' | 'windows7';
    price?: number;
    provider?: string;
    region?: string;
    remoteLogin?: boolean;
    resourceId?: number;
    sortNo?: number;
    tcpfp?: boolean;
    traffic?: number;
    trafficCurrency?: 'CREDIT' | 'RMB' | 'USD';
    trafficPrice?: number;
    updateTime?: string;
    weekCost?: number;
    weekPrice?: number;
    weekTraffic?: number;
  };

  type GoodsUsabilityVo = {
    arch?: string;
    bandwidth?: number;
    buyoutPrice?: number;
    city?: string;
    cost?: number;
    countryCode?: string;
    cpu?: number;
    currency?: 'CREDIT' | 'RMB' | 'USD';
    dayCost?: number;
    dayPrice?: number;
    dayTraffic?: number;
    description?: string;
    disk?: number;
    diskCategory?: string;
    dynamic?: boolean;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    initialQuantity?: number;
    instanceType?: string;
    ipv6?: boolean;
    listPrice?: number;
    mem?: number;
    name?: string;
    networkType?: 'cloudIdc' | 'mobile' | 'proxyIdc' | 'residential' | 'unknown' | 'unknownIdc';
    onSale?: 'Disabled' | 'Offline' | 'Online';
    perfLevel?:
      | 'CostEffective'
      | 'HighlyConcurrent'
      | 'LargeTraffic'
      | 'None'
      | 'RemoteLogin'
      | 'UnlimitedTraffic';
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    pipeType?: 'None' | 'Proxy' | 'Tunnel' | 'TunnelFailToProxy';
    platform?: 'android' | 'linux' | 'macos' | 'unknown' | 'web' | 'windows' | 'windows7';
    platformUsabilityList?: PlatformUsabilityVo[];
    price?: number;
    provider?: string;
    region?: string;
    remoteLogin?: boolean;
    resourceId?: number;
    sortNo?: number;
    tcpfp?: boolean;
    traffic?: number;
    trafficCurrency?: 'CREDIT' | 'RMB' | 'USD';
    trafficPrice?: number;
    updateTime?: string;
    weekCost?: number;
    weekPrice?: number;
    weekTraffic?: number;
  };

  type GroupWeightVo = {
    deviceWeight?: number;
    forbidden?: boolean;
    groupId?: number;
    systemWeight?: number;
    tunnelName?: string;
    tunnelType?: 'clash' | 'direct' | 'jump' | 'localFrontend' | 'platform' | 'transit';
    userWeight?: number;
  };

  type HealthCheckConfig = {
    /** 其它的流程关心但引擎不关心的属性 */
    advanceSettings?: Record<string, any>;
    disabled?: boolean;
    /** 健康检查工作时间段，格式如 03:22-15:34 */
    duration?: string;
    /** first | random | assign  为空表示random */
    friendType?: string;
    /** 健康检查频率，为空表示不检查；最小10分钟，最大1000分钟 */
    interval?: number;
    /** 调度quartz id */
    scheduleId?: string;
  };

  type HuaYongCheckerConfig = {
    domestic?: boolean;
    endpoint?: string;
    ipv6?: boolean;
  };

  type id = {
    code?: number;
    data?: id[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type id = {
    createTime?: string;
    credit?: number;
    creditTraffic?: number;
    id?: Serializable;
    packDeductions?: Record<string, any>;
    primary?: boolean;
    sessionId?: number;
    shopId?: number;
    speedUpCredit?: number;
    speedupTraffic?: number;
    totalDownTraffic?: number;
    totalUpTraffic?: number;
    transitEndpoint?: string;
    transitId?: number;
    transitName?: string;
  };

  type IdNameVo = {
    id?: number;
    name?: string;
  };

  type ImportCloudMobileRequest = {
    /** 就是padCode */
    code?: string;
    connectType?: 'ARMCLOUD' | 'Baidu' | 'QCloud' | 'USB' | 'WIFI';
    description?: string;
    /** 手机设备名字，例如 MI 14 */
    name?: string;
  };

  type ImportTrafficSpecRequest = {
    overwrite?: boolean;
    shopIds?: number[];
    spec?: RouterSpecVo;
  };

  type InstallScriptVo = {
    batScript?: string;
    endpoint?: string;
    importKey?: string;
    linuxDownloadUrl?: string;
    psScript?: string;
    shScript?: string;
    windowsDownloadUrl?: string;
  };

  type ipAutoRenewPutParams = {
    /** autoRenew */
    autoRenew: boolean;
    /** ipIds */
    ipIds: number;
  };

  type ipBatchDeleteDeleteParams = {
    /** ipIds */
    ipIds: number;
  };

  type ipBatchDeleteV2DeleteParams = {
    /** ipIds */
    ipIds: number;
  };

  type IpBindDto = {
    accessCount?: number;
    bindTime?: string;
    id?: number;
    ip?: string;
    ipId?: number;
    platformType?: string;
    shopId?: number;
    shopName?: string;
    teamId?: number;
    unbindTime?: string;
  };

  type IpBindHistoryVo = {
    accessCount?: number;
    bindTime?: string;
    id?: number;
    ip?: string;
    ipId?: number;
    operatingCategory?:
      | '医药保健'
      | '图书文具'
      | '宠物用品'
      | '家具建材'
      | '家电电器'
      | '工业用品'
      | '户外运动'
      | '手机数码'
      | '手表眼镜'
      | '护肤美妆'
      | '母婴玩具'
      | '汽车配件'
      | '生活家居'
      | '电商其他'
      | '电脑平板'
      | '艺术珠宝'
      | '花园聚会'
      | '计生情趣'
      | '软件程序'
      | '鞋服箱包'
      | '音乐影视'
      | '食品生鲜'
      | '鲜花绿植';
    platformArea?:
      | 'Argentina'
      | 'Australia'
      | 'Austria'
      | 'Belarus'
      | 'Belgium'
      | 'Bolivia'
      | 'Brazil'
      | 'Canada'
      | 'Chile'
      | 'China'
      | 'Colombia'
      | 'Costa_Rica'
      | 'Dominican'
      | 'Ecuador'
      | 'Egypt'
      | 'France'
      | 'Germany'
      | 'Global'
      | 'Guatemala'
      | 'Honduras'
      | 'HongKong'
      | 'India'
      | 'Indonesia'
      | 'Ireland'
      | 'Israel'
      | 'Italy'
      | 'Japan'
      | 'Kazakhstan'
      | 'Korea'
      | 'Malaysia'
      | 'Mexico'
      | 'Netherlands'
      | 'Nicaragua'
      | 'Panama'
      | 'Paraguay'
      | 'Peru'
      | 'Philippines'
      | 'Poland'
      | 'Portuguese'
      | 'Puerto_Rico'
      | 'Russia'
      | 'Salvador'
      | 'Saudi_Arabia'
      | 'Singapore'
      | 'Spain'
      | 'Sweden'
      | 'Switzerland'
      | 'Taiwan'
      | 'Thailand'
      | 'Turkey'
      | 'United_Arab_Emirates'
      | 'United_Kingdom'
      | 'United_States'
      | 'Uruguay'
      | 'Venezuela'
      | 'Vietnam';
    platformType?: string;
    shopId?: number;
    shopName?: string;
    teamId?: number;
    unbindTime?: string;
  };

  type IpBindShopChangeRequest = {
    disableLongLatitude?: boolean;
    disableWebrtc?: boolean;
    dynamicStrategy?: 'Off' | 'Remain' | 'SwitchOnSession';
    level?: 'City' | 'Continent' | 'Country' | 'District' | 'None' | 'Province' | 'Unknown';
    locationId?: number;
    newIpId?: number;
    previousIpId?: number;
    shopId?: number;
  };

  type IpBindShopParamVo = {
    ipId?: number;
    primary?: boolean;
    shopIdList?: number[];
  };

  type IpBindShopParamVoV2 = {
    disableLongLatitude?: boolean;
    disableWebrtc?: boolean;
    dynamicStrategy?: 'Off' | 'Remain' | 'SwitchOnSession';
    ipIds?: number[];
    level?: 'City' | 'Continent' | 'Country' | 'District' | 'None' | 'Province' | 'Unknown';
    locationId?: number;
    primary?: boolean;
    shopIdList?: number[];
  };

  type ipByCategoryByCategoryGetParams = {
    /** unbound */
    unbound?: boolean;
    /** source */
    source?: string;
    /** 视图分类 */
    category:
      | 'all'
      | 'aptotic'
      | 'custom'
      | 'dynamic'
      | 'expired'
      | 'expring'
      | 'platform'
      | 'unavailable'
      | 'unbound';
    /** 视图ID(category=custom时指定) */
    viewId?: number;
    /** IP */
    query?: string;
    /** 国家代码，用逗号连接 */
    countryCodes?: string;
    /** 按标签id过滤 */
    tagIds?: string;
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
    /** props */
    props?: string;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** 排序字段 */
    sortFiled?:
      | 'auto_renew'
      | 'create_time'
      | 'ip'
      | 'locationCn'
      | 'locationEn'
      | 'name'
      | 'period_unit'
      | 'status'
      | 'testing_time'
      | 'type'
      | 'valid_end_date';
    /** 顺序 */
    sortOrder?: 'asc' | 'desc';
  };

  type ipByIdCredConfigGetParams = {
    /** id */
    id: number;
    /** encrypt */
    encrypt?: boolean;
  };

  type ipByIdRebootJobPutParams = {
    /** id */
    id: number;
  };

  type ipByIdRebootPutParams = {
    /** id */
    id: number;
  };

  type ipByIdRefreshInfoGetParams = {
    /** id */
    id: number;
  };

  type ipByIdRefreshPutParams = {
    /** id */
    id: number;
  };

  type ipByIdRestartGetParams = {
    /** id */
    id: number;
  };

  type ipByIdTransitGroupsGetParams = {
    /** id */
    id: number;
  };

  type ipByIdTunnelGroupsGetParams = {
    /** id */
    id: number;
  };

  type ipByIpIdBasicInfoPutParams = {
    /** ipId */
    ipId: number;
    /** locationId */
    locationId?: number;
    /** countryCode */
    countryCode?: string;
    /** provinceCode */
    provinceCode?: string;
    /** timezone */
    timezone: string;
    /** locale */
    locale: string;
    /** latitude */
    latitude?: number;
    /** longitude */
    longitude?: number;
    /** forbiddenLongLatitude */
    forbiddenLongLatitude?: boolean;
    /** name */
    name?: string;
    /** 描述 */
    description?: string;
    /** networkType */
    networkType?: 'cloudIdc' | 'mobile' | 'proxyIdc' | 'residential' | 'unknown' | 'unknownIdc';
    /** dynamic */
    dynamic?: boolean;
  };

  type ipByIpIdBindHisInfoGetParams = {
    /** ipId */
    ipId: number;
    /** 访问次数按多少个月统计 */
    month: number;
  };

  type ipByIpIdBindHistoryGetParams = {
    /** ipId */
    ipId: number;
    /** platformType */
    platformType: string;
  };

  type ipByIpIdBindShopHistoryGetParams = {
    /** ipId */
    ipId: number;
  };

  type ipByIpIdChargeByMonthPutParams = {
    /** ipId */
    ipId: number;
  };

  type ipByIpIdDeleteParams = {
    /** ipId */
    ipId: number;
  };

  type ipByIpIdGatewayProxyByGatewayProxyIdAuthPutParams = {
    /** ipId */
    ipId: number;
    /** gatewayProxyId */
    gatewayProxyId: number;
    /** 是否认证 */
    auth?: boolean;
    /** username */
    username?: string;
    /** password */
    password?: string;
    /** 是否开启白名单 */
    whitelistEnabled?: boolean;
    /** 白名单列表，只支持IPv4或v6，用逗号连接 */
    whitelist?: string;
  };

  type ipByIpIdGatewayProxyByGatewayProxyIdDeleteParams = {
    /** ipId */
    ipId: number;
    /** gatewayProxyId */
    gatewayProxyId: number;
  };

  type ipByIpIdGatewayProxyDeleteParams = {
    /** ipId */
    ipId: number;
  };

  type ipByIpIdGatewayProxyPostParams = {
    /** ipId */
    ipId: number;
    /** 是否认证 */
    auth?: boolean;
    /** 是否开启白名单 */
    whitelistEnabled?: boolean;
    /** 白名单列表，只支持IPv4或v6，用逗号连接 */
    whitelist?: string;
  };

  type ipByIpIdGetParams = {
    /** ipId */
    ipId: number;
  };

  type ipByIpIdIpPutParams = {
    /** ipId */
    ipId: number;
    /** ip */
    ip: string;
  };

  type ipByIpIdNamePutParams = {
    /** ipId */
    ipId: number;
    /** name */
    name: string;
  };

  type ipByIpIdProbeDirectResultPutParams = {
    /** ipId */
    ipId: number;
  };

  type ipByIpIdProbeGetParams = {
    /** ipId */
    ipId: number;
    /** 接入点ID */
    transitId: number;
  };

  type ipByIpIdProviderAndDynamicPutParams = {
    /** ipId */
    ipId: number;
    /** provider */
    provider?: string;
    /** dynamic */
    dynamic?: boolean;
    /** refreshUrl */
    refreshUrl?: string;
  };

  type ipByIpIdSessionCountGetParams = {
    /** ipId */
    ipId: number;
  };

  type ipByIpIdStatusPutParams = {
    /** ipId */
    ipId: number;
    /** status */
    status: 'Available' | 'Pending' | 'Unavailable';
    /** testingTime */
    testingTime?: number;
    /** probeError */
    probeError?: string;
  };

  type ipByIpIdTransitsGetParams = {
    /** ipId */
    ipId: number;
  };

  type ipCheckersGetParams = {
    /** ipId */
    ipId?: number;
  };

  type ipCheckOrGenerateIpNameGetParams = {
    /** name */
    name: string;
  };

  type ipCountByTeamGetParams = {
    /** teamId */
    teamId: number;
  };

  type IpCountryStatVo = {
    count?: number;
    countryCode?: string;
  };

  type ipCustomerIpRegionsByCloudIdGetParams = {
    /** cloudId */
    cloudId: number;
  };

  type IpDataDto = {
    city?: string;
    country?: string;
    countryCode?: string;
    district?: string;
    id?: number;
    ip?: string;
    isp?: string;
    locationId?: number;
    province?: string;
    revisedLocationId?: number;
    revisedProvider?: string;
    tag?: string;
    updateTime?: string;
    zip?: string;
    zone?: string;
  };

  type ipddListGetParams = {
    /** ip */
    ip: string;
    /** domain */
    domain?: string;
    /** dateFrom */
    dateFrom?: string;
    /** dateTo */
    dateTo?: string;
    /** pageNum */
    pageNum: number;
    /** pageSize */
    pageSize: number;
  };

  type ipddRecordPostParams = {
    /** ip */
    ip: string;
    /** domain */
    domain: string;
  };

  type IpDetailAllVo = {
    activeSessions?: number;
    autoRenew?: boolean;
    /** 绑定店铺的平台信息 */
    binds?: IpPlatformBindVo[];
    cloudName?: string;
    cloudProvider?: string;
    cloudRegion?: string;
    createTime?: string;
    creatorId?: number;
    description?: string;
    directDownTraffic?: number;
    directUpTraffic?: number;
    domestic?: boolean;
    downTraffic?: number;
    dynamic?: boolean;
    eipId?: number;
    enableWhitelist?: boolean;
    /** 过期状态 */
    expireStatus?: 'Expired' | 'Expiring' | 'Normal';
    forbiddenLongLatitude?: boolean;
    gateway?: GatewayDto;
    gatewayId?: number;
    gatewayProxys?: GatewayProxyDto[];
    goods?: GoodsDto;
    goodsId?: number;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    importType?: 'Platform' | 'User';
    instance?: LaunchInstanceDto;
    invalidTime?: string;
    ip?: string;
    ipv6?: boolean;
    lastProbeTime?: string;
    latitude?: number;
    locale?: string;
    location?: IpLocationDto;
    locationId?: number;
    longitude?: number;
    name?: string;
    networkType?: 'cloudIdc' | 'mobile' | 'proxyIdc' | 'residential' | 'unknown' | 'unknownIdc';
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    originalTeam?: number;
    /** 原始团队名称 */
    originalTeamName?: string;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    pipeType?: 'None' | 'Proxy' | 'Tunnel' | 'TunnelFailToProxy';
    preferTransit?: number;
    probeError?: string;
    protoType?: 'http' | 'httpTunnel' | 'ipgo' | 'luminati' | 'socks5' | 'ssh' | 'vps';
    /** 供应商名称 */
    providerName?: string;
    realIp?: string;
    refreshUrl?: string;
    remoteLogin?: boolean;
    renewPrice?: number;
    shops?: ShopBriefVo[];
    /** 代理IP的代理信息 */
    socks?: IpSocksDto;
    source?: string;
    speedLimit?: number;
    status?: 'Available' | 'Pending' | 'Unavailable';
    sticky?: boolean;
    /** IP标签 */
    tags?: TagDto[];
    teamId?: number;
    /** 所属团队名称 */
    teamName?: string;
    testingTime?: number;
    timezone?: string;
    traffic?: number;
    trafficCurrency?: 'CREDIT' | 'RMB' | 'USD';
    trafficPrice?: number;
    trafficUnlimited?: boolean;
    transitType?: 'Auto' | 'Direct' | 'Transit';
    transits?: TransitDto[];
    tunnelTypes?: string;
    upTraffic?: number;
    valid?: boolean;
    validEndDate?: string;
    vpsId?: number;
  };

  type IpDetailVo = {
    autoRenew?: boolean;
    cloudProvider?: string;
    cloudRegion?: string;
    createTime?: string;
    creator?: UserVo;
    creatorId?: number;
    description?: string;
    directDownTraffic?: number;
    directUpTraffic?: number;
    domestic?: boolean;
    downTraffic?: number;
    dynamic?: boolean;
    eipId?: number;
    enableWhitelist?: boolean;
    /** 过期状态 */
    expireStatus?: 'Expired' | 'Expiring' | 'Normal';
    forbiddenLongLatitude?: boolean;
    gatewayId?: number;
    goodsId?: number;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    importType?: 'Platform' | 'User';
    invalidTime?: string;
    ip?: string;
    ipv6?: boolean;
    lastProbeTime?: string;
    latitude?: number;
    locale?: string;
    location?: IpLocationDto;
    locationId?: number;
    longitude?: number;
    name?: string;
    networkType?: 'cloudIdc' | 'mobile' | 'proxyIdc' | 'residential' | 'unknown' | 'unknownIdc';
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    originalTeam?: number;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    pipeType?: 'None' | 'Proxy' | 'Tunnel' | 'TunnelFailToProxy';
    preferTransit?: number;
    probeError?: string;
    protoType?: 'http' | 'httpTunnel' | 'ipgo' | 'luminati' | 'socks5' | 'ssh' | 'vps';
    /** 供应商名称 */
    providerName?: string;
    realIp?: string;
    refreshUrl?: string;
    remoteLogin?: boolean;
    renewPrice?: number;
    shops?: ShopBriefVo[];
    source?: string;
    speedLimit?: number;
    status?: 'Available' | 'Pending' | 'Unavailable';
    sticky?: boolean;
    tags?: TagDto[];
    teamId?: number;
    testingTime?: number;
    timezone?: string;
    traffic?: number;
    trafficCurrency?: 'CREDIT' | 'RMB' | 'USD';
    trafficPrice?: number;
    trafficUnlimited?: boolean;
    transitType?: 'Auto' | 'Direct' | 'Transit';
    transits?: TransitDto[];
    tunnelTypes?: string;
    upTraffic?: number;
    valid?: boolean;
    validEndDate?: string;
    vpsId?: number;
  };

  type IpDomainDiaryDto = {
    checksumId?: string;
    createDay?: string;
    domain?: string;
    id?: number;
    ip?: string;
    teamId?: number;
  };

  type IpDynamicStatVo = {
    count?: number;
    dynamic?: boolean;
  };

  type IpForShopBindVo = {
    /** 在给定的条件下，访问过多少次 */
    accessCount?: number;
    autoRenew?: boolean;
    cloudProvider?: string;
    cloudRegion?: string;
    createTime?: string;
    creatorId?: number;
    description?: string;
    directDownTraffic?: number;
    directUpTraffic?: number;
    domestic?: boolean;
    downTraffic?: number;
    dynamic?: boolean;
    eipId?: number;
    enableWhitelist?: boolean;
    /** 过期状态 */
    expireStatus?: 'Expired' | 'Expiring' | 'Normal';
    forbiddenLongLatitude?: boolean;
    gatewayId?: number;
    goodsId?: number;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    importType?: 'Platform' | 'User';
    invalidTime?: string;
    ip?: string;
    /** 这个ip绑定了多少次店铺 */
    ipBindHisCount?: number;
    ipv6?: boolean;
    /** 最后一次访问时间 */
    lastAccessTime?: string;
    lastProbeTime?: string;
    latitude?: number;
    locale?: string;
    location?: IpLocationDto;
    locationId?: number;
    longitude?: number;
    name?: string;
    networkType?: 'cloudIdc' | 'mobile' | 'proxyIdc' | 'residential' | 'unknown' | 'unknownIdc';
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    originalTeam?: number;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    pipeType?: 'None' | 'Proxy' | 'Tunnel' | 'TunnelFailToProxy';
    preferTransit?: number;
    probeError?: string;
    protoType?: 'http' | 'httpTunnel' | 'ipgo' | 'luminati' | 'socks5' | 'ssh' | 'vps';
    /** 供应商名称 */
    providerName?: string;
    realIp?: string;
    refreshUrl?: string;
    remoteLogin?: boolean;
    renewPrice?: number;
    shops?: ShopWithPlatformVo[];
    source?: string;
    speedLimit?: number;
    status?: 'Available' | 'Pending' | 'Unavailable';
    sticky?: boolean;
    /** IP标签 */
    tags?: TagDto[];
    teamId?: number;
    testingTime?: number;
    timezone?: string;
    traffic?: number;
    trafficCurrency?: 'CREDIT' | 'RMB' | 'USD';
    trafficPrice?: number;
    trafficUnlimited?: boolean;
    transitType?: 'Auto' | 'Direct' | 'Transit';
    tunnelTypes?: string;
    upTraffic?: number;
    valid?: boolean;
    validEndDate?: string;
    vpsId?: number;
  };

  type ipGenerateNameExampleGetParams = {
    /** specString */
    specString: string;
  };

  type IpImportTypeStatVo = {
    count?: number;
    importType?: 'Platform' | 'User';
  };

  type ipIpForShopBindByShopIdGetParams = {
    /** shopId */
    shopId: number;
    /** 访问次数按多少个月统计 */
    month: number;
  };

  type IpLocationDto = {
    city?: string;
    cityEn?: string;
    continent?: string;
    continentCode?: string;
    continentEn?: string;
    country?: string;
    countryCode?: string;
    countryEn?: string;
    createTime?: string;
    geonameId?: number;
    id?: number;
    inEu?: boolean;
    latitude?: number;
    level?: 'City' | 'Continent' | 'Country' | 'District' | 'None' | 'Province' | 'Unknown';
    locale?: string;
    longitude?: number;
    postalCode?: string;
    province?: string;
    provinceCode?: string;
    provinceEn?: string;
    show?: boolean;
    teamId?: number;
    timezone?: string;
  };

  type IpNameSpecConfigVo = {
    /** 导入动态IP命名规范 */
    ipNameSpecDynamic?: string;
    /** 导入平台IP命名规范 */
    ipNameSpecPlatform?: string;
    /** 导入静态IP命名规范 */
    ipNameSpecStatic?: string;
  };

  type IpNameSpecVo = {
    desc?: string;
    spec?:
      | 'City'
      | 'Country'
      | 'CountryCode'
      | 'Host'
      | 'ImportDate'
      | 'InvalidDate'
      | 'Port'
      | 'ProtoType'
      | 'Provider'
      | 'RemoteIp';
  };

  type ipPageGetParams = {
    /** teamId */
    teamId: number;
    /** unbindFlag */
    unbindFlag?: boolean;
    /** ip */
    ip?: string;
    /** pageNum */
    pageNum: number;
    /** pageSize */
    pageSize: number;
  };

  type IppBindShopParamVo = {
    dynamicStrategy?: 'Off' | 'Remain' | 'SwitchOnSession';
    /** ip池Id */
    ippId?: number;
    level?: 'City' | 'Continent' | 'Country' | 'District' | 'None' | 'Province' | 'Unknown';
    locationId?: number;
    shopIdList?: number[];
  };

  type ippByIppIdImportAsyncPostParams = {
    /** ippId */
    ippId: number;
  };

  type ippByIppIdIpsGetParams = {
    /** ippId */
    ippId: number;
    /** minActiveSessions */
    minActiveSessions?: number;
    /** valid */
    valid?: boolean;
    /** ip */
    ip?: string;
    /** host */
    host?: string;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** 排序字段 */
    sortFiled?:
      | 'areaCode'
      | 'createTime'
      | 'directDownTraffic'
      | 'directUpTraffic'
      | 'expireTime'
      | 'host'
      | 'invalidTime'
      | 'lastAllocTime'
      | 'outboundIp'
      | 'proxyType'
      | 'status'
      | 'transitDownTraffic'
      | 'transitUpTraffic'
      | 'username'
      | 'valid';
    /** 顺序 */
    sortOrder?: 'asc' | 'desc';
  };

  type IppIpDetailVo = {
    activeSessions?: number;
    areaCode?: number;
    createTime?: string;
    directDownTraffic?: number;
    directUpTraffic?: number;
    expireTime?: string;
    host?: string;
    hostDomestic?: boolean;
    id?: number;
    invalidTime?: string;
    ippId?: number;
    lastAllocTime?: string;
    lastProbeTime?: string;
    locationId?: number;
    outboundIp?: string;
    password?: string;
    port?: number;
    probeError?: string;
    proxyType?: string;
    refTeamIp?: number;
    shops?: ShopDetailVo[];
    status?: 'Available' | 'Pending' | 'Unavailable';
    teamId?: number;
    transitDownTraffic?: number;
    transitUpTraffic?: number;
    username?: string;
    valid?: boolean;
  };

  type IppIpDto = {
    activeSessions?: number;
    areaCode?: number;
    createTime?: string;
    directDownTraffic?: number;
    directUpTraffic?: number;
    expireTime?: string;
    host?: string;
    hostDomestic?: boolean;
    id?: number;
    invalidTime?: string;
    ippId?: number;
    lastAllocTime?: string;
    lastProbeTime?: string;
    locationId?: number;
    outboundIp?: string;
    password?: string;
    port?: number;
    probeError?: string;
    proxyType?: string;
    refTeamIp?: number;
    status?: 'Available' | 'Pending' | 'Unavailable';
    teamId?: number;
    transitDownTraffic?: number;
    transitUpTraffic?: number;
    username?: string;
    valid?: boolean;
  };

  type IppIpWithLocationVo = {
    activeSessions?: number;
    areaCode?: number;
    createTime?: string;
    directDownTraffic?: number;
    directUpTraffic?: number;
    domestic?: boolean;
    dynamic?: boolean;
    expireTime?: string;
    forbiddenLongLatitude?: boolean;
    host?: string;
    hostDomestic?: boolean;
    id?: number;
    invalidTime?: string;
    ippId?: number;
    lastAllocTime?: string;
    lastProbeTime?: string;
    location?: IpLocationDto;
    locationId?: number;
    outboundIp?: string;
    password?: string;
    port?: number;
    probeError?: string;
    proxyType?: string;
    refTeamIp?: number;
    status?: 'Available' | 'Pending' | 'Unavailable';
    teamId?: number;
    transitDownTraffic?: number;
    transitUpTraffic?: number;
    username?: string;
    valid?: boolean;
  };

  type IpPlatformBindVo = {
    /** 当前平台绑定次数 */
    bindCount?: number;
    /** 当前平台 */
    platformType?: string;
    /** 当前平台绑定的店铺 */
    shops?: ShopDto[];
  };

  type IpPoolDto = {
    allocateStrategy?: 'ByLessTraffic' | 'ByLoad' | 'ByOrder' | 'ByRandom';
    capacity?: number;
    connectTransits?: string;
    createTime?: string;
    creator?: number;
    description?: string;
    domestic?: boolean;
    enableWhitelist?: boolean;
    exclusive?: boolean;
    id?: number;
    lastApiTime?: string;
    lifetime?: number;
    locationId?: number;
    minApiInterval?: number;
    minApiNum?: number;
    name?: string;
    produceFromTransit?: boolean;
    produceStrategy?: 'ProduceOnEmpty' | 'ProduceOnHand' | 'ProduceOnRequest';
    produced?: number;
    provider?: string;
    releaseStrategy?: 'Discard' | 'Reuse';
    teamId?: number;
    transitType?: 'Auto' | 'Direct' | 'Transit';
    transits?: string;
    tunnelTypes?: string;
  };

  type ippPoolByIppIdTeamIpsPostParams = {
    /** ippId */
    ippId: number;
    /** ipIds */
    ipIds: string;
  };

  type ippPoolPageDetailGetParams = {
    /** 供应商 */
    provider?: string;
    /** 国家 */
    country?: string;
    /** 城市 */
    city?: string;
    /** 按标签id过滤 */
    tagIds?: string;
    /** name */
    name?: string;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** 排序字段 */
    sortFiled?: 'capacity' | 'createTime' | 'lastApiTime' | 'lifetime' | 'provider';
    /** 顺序 */
    sortOrder?: 'asc' | 'desc';
  };

  type IppPoolVo = {
    allocateStrategy?: 'ByLessTraffic' | 'ByLoad' | 'ByOrder' | 'ByRandom';
    capacity?: number;
    connectTransits?: string;
    createTime?: string;
    creator?: number;
    description?: string;
    domestic?: boolean;
    enableWhitelist?: boolean;
    exclusive?: boolean;
    id?: number;
    lastApiTime?: string;
    lifetime?: number;
    location?: IpLocationDto;
    locationId?: number;
    minApiInterval?: number;
    minApiNum?: number;
    name?: string;
    produceFromTransit?: boolean;
    produceStrategy?: 'ProduceOnEmpty' | 'ProduceOnHand' | 'ProduceOnRequest';
    produced?: number;
    provider?: string;
    providerDto?: IppProviderDto;
    releaseStrategy?: 'Discard' | 'Reuse';
    shops?: ShopDetailVo[];
    size?: number;
    tags?: TagDto[];
    teamId?: number;
    transitType?: 'Auto' | 'Direct' | 'Transit';
    transits?: string;
    tunnelTypes?: string;
  };

  type IppProviderDto = {
    api?: boolean;
    description?: string;
    hasExpire?: boolean;
    homeUrl?: string;
    id?: number;
    name?: string;
    produceFromTransit?: boolean;
    promoUrl?: string;
    provider?: string;
    rangeType?: 'Domestic' | 'Global' | 'Oversea';
    valid?: boolean;
  };

  type ipProbeIpGetParams = {
    /** ipId */
    ipId: number;
    /** 如果不设置时，选择随机中转，设置为0时表示直连（用门户去连接IP） */
    transitId?: number;
  };

  type ipProbeProxyGetParams = {
    /** ipId */
    ipId: number;
    /** updateDb */
    updateDb?: boolean;
    /** 如果不设置时，选择随机中转，设置为0时表示直连（用门户去连接IP） */
    transitId?: number;
  };

  type ipProbeRemoteIpGetParams = {
    /** ipId */
    ipId: number;
    /** updateDb */
    updateDb?: boolean;
  };

  type IpRefreshInfo = {
    /** 当天最大重试次数（=0表示不限制） */
    maxRefreshCount?: number;
    /** 是否支持重置 */
    refreshSupported?: boolean;
    /** 当天已重试的次数 */
    todayRefreshCount?: number;
  };

  type ipRefreshIpStatusByIpTransitGetParams = {
    /** ipId */
    ipId: number;
  };

  type IpRegionVo = {
    /** IP区域 */
    area?: '中东' | '中国' | '亚太' | '北美' | '南极洲' | '南美' | '欧洲' | '非洲';
    areaEn?: string;
    /** 城市 */
    city?: string;
    cityEn?: string;
    /** 国家 */
    country?: string;
    /** 国家代码 */
    countryCode?: string;
    /** 国家英文 */
    countryEn?: string;
    /** 当前区域商品列表 */
    goods?: GoodsUsabilityVo[];
    /** 种类 */
    importType?: 'Platform' | 'User';
    /** 排序字段 */
    orderNo?: number;
    /** 供应商 */
    provider?:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    /** 区域ID */
    region?: string;
  };

  type ipSharingPageGetParams = {
    pageNum?: number;
    pageSize?: number;
    query?: string;
    sortField?:
      | 'auto_renew'
      | 'create_time'
      | 'ip'
      | 'name'
      | 'period_unit'
      | 'status'
      | 'team_id'
      | 'team_name'
      | 'valid_end_date';
    sortOrder?: 'asc' | 'desc';
  };

  type ipSocksByIpIdPutParams = {
    /** Ip ID */
    ipId: number;
    /** 代理类型 */
    proxyType: string;
    /** 代理用户名 */
    username?: string;
    /** 代理密码 */
    password?: string;
    /** 登录密钥 */
    sshKey?: string;
    /** 密码是否修改 */
    passwordSet?: boolean;
    /** 代理主机 */
    host: string;
    /** 代理端口 */
    port: number;
  };

  type IpSocksDto = {
    authEnabled?: boolean;
    creator?: number;
    host?: string;
    hostDomestic?: boolean;
    id?: number;
    ipId?: number;
    ipv6?: boolean;
    locationId?: number;
    password?: string;
    port?: number;
    provider?: string;
    proxyType?: 'http' | 'httpTunnel' | 'ipgo' | 'luminati' | 'socks5' | 'ssh' | 'vps';
    sshKey?: string;
    teamId?: number;
    username?: string;
  };

  type ipSocksProbePutParams = {
    /** ipVersion */
    ipVersion?: string;
    /** 测试的接入点ID，0表示直连 */
    transitId?: number;
    /** 代理类型 */
    proxyType: string;
    /** 代理用户名 */
    username?: string;
    /** 代理密码 */
    password?: string;
    /** 登录密钥 */
    sshKey?: string;
    /** 代理主机 */
    host: string;
    /** 代理端口 */
    port: number;
  };

  type ipSourcesGetParams = {
    /** onlyTeam */
    onlyTeam?: boolean;
  };

  type ipThirdFrontendGetFrontendProxyUrlGetParams = {
    /** ipId */
    ipId?: number;
    /** ippId */
    ippId?: number;
  };

  type IpTrafficVo = {
    ipId?: number;
    /** 当前IP流量是否超过配额（流量包用完） */
    ipTrafficExceedQuota?: boolean;
    /** 当前周期IP配额总流量（所有IP流量包） */
    quotaAmount?: number;
    /** 当前周期IP配额已经使用流量 */
    quotaUsed?: number;
    teamId?: number;
    /** 当前周期接入点已用流量 */
    transitUsed?: number;
  };

  type IpTransitDto = {
    chosen?: boolean;
    id?: number;
    ipId?: number;
    lastProbeTime?: string;
    probeCode?: number;
    probeError?: string;
    probeOut?: string;
    remoteIp?: string;
    status?: 'Available' | 'Pending' | 'Unavailable';
    teamId?: number;
    testingTime?: number;
    transitId?: number;
  };

  type IpTransitGroupVo = {
    autoAllocate?: boolean;
    createTime?: string;
    defaultChosen?: boolean;
    description?: string;
    domestic?: boolean;
    free?: boolean;
    id?: number;
    importType?: 'Platform' | 'User';
    jump?: boolean;
    location?: string;
    name?: string;
    sortNo?: number;
    transitIds?: string;
    transits?: TransitVo[];
  };

  type IpTunnelGroupVo = {
    groupId?: number;
    id?: number;
    ipId?: number;
    teamId?: number;
    transitIds?: string;
    /** 白名单接入点 */
    transits?: TransitDto[];
  };

  type IpViewFilterVo = {
    /** 归属地所属国家代码 */
    countryCodes?: string[];
    /** 视图可见范围:指定部门 */
    departmentIds?: number[];
    /** 是否动态 */
    dynamic?: boolean;
    /** 是否动态 */
    dynamicView?: boolean;
    /** 允许可见者编辑 */
    editable?: boolean;
    /** IP类型 */
    importType?: 'Platform' | 'User';
    /** IP的名称 */
    name?: string;
    /** 资源名称 */
    names?: string[];
    regexNames?: string[];
    /** 静态视图的资源ID */
    resourceIds?: number[];
    /** 视图可见范围：指定成员 */
    sharedUserIds?: number[];
    /** IP状态：不限制时传null */
    status?: 'Available' | 'Pending' | 'Unavailable';
    /** 关联标签 */
    tagIds?: number[];
    /** 标签的逻辑关系 */
    tagLc?: 'AND' | 'NOT' | 'OR';
    validEndDateFrom?: string;
    validEndDateTo?: string;
  };

  type IpWithLocationVo = {
    autoRenew?: boolean;
    cloudProvider?: string;
    cloudRegion?: string;
    createTime?: string;
    creatorId?: number;
    description?: string;
    directDownTraffic?: number;
    directUpTraffic?: number;
    domestic?: boolean;
    downTraffic?: number;
    dynamic?: boolean;
    eipId?: number;
    enableWhitelist?: boolean;
    /** 过期状态 */
    expireStatus?: 'Expired' | 'Expiring' | 'Normal';
    forbiddenLongLatitude?: boolean;
    gatewayId?: number;
    goodsId?: number;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    importType?: 'Platform' | 'User';
    invalidTime?: string;
    ip?: string;
    ipv6?: boolean;
    lastProbeTime?: string;
    latitude?: number;
    locale?: string;
    location?: IpLocationDto;
    locationId?: number;
    longitude?: number;
    name?: string;
    networkType?: 'cloudIdc' | 'mobile' | 'proxyIdc' | 'residential' | 'unknown' | 'unknownIdc';
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    originalTeam?: number;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    pipeType?: 'None' | 'Proxy' | 'Tunnel' | 'TunnelFailToProxy';
    preferTransit?: number;
    probeError?: string;
    protoType?: 'http' | 'httpTunnel' | 'ipgo' | 'luminati' | 'socks5' | 'ssh' | 'vps';
    /** 供应商名称 */
    providerName?: string;
    realIp?: string;
    refreshUrl?: string;
    remoteLogin?: boolean;
    renewPrice?: number;
    source?: string;
    speedLimit?: number;
    status?: 'Available' | 'Pending' | 'Unavailable';
    sticky?: boolean;
    teamId?: number;
    testingTime?: number;
    timezone?: string;
    traffic?: number;
    trafficCurrency?: 'CREDIT' | 'RMB' | 'USD';
    trafficPrice?: number;
    trafficUnlimited?: boolean;
    transitType?: 'Auto' | 'Direct' | 'Transit';
    tunnelTypes?: string;
    upTraffic?: number;
    valid?: boolean;
    validEndDate?: string;
    vpsId?: number;
  };

  type ItemPriceInfo = {
    costPrice?: number;
    /** 当前过期时间 */
    currentValidEndTime?: string;
    discount?: DiscountsVo;
    /** 打折减掉的金额，如果是打折的话 */
    discountAmount?: number;
    goodsId?: number;
    /** 应付价格 */
    payablePrice?: number;
    /** 赠送数量，如果是赠送的话。目前只出现在购买花瓣 */
    presentAmount?: number;
    /** item总价 */
    price?: number;
    /** 续费后到期时间 */
    validEndTime?: string;
  };

  type jiangfenshenshouquangeiyonghuhezuzhi = {
    cleanFirst?: boolean;
    confirm?: boolean;
    departmentIdList?: number[];
    mobileIds?: number[];
    teamId?: number;
    userIdList?: number[];
  };

  type LaunchInstanceDto = {
    adminAccount?: string;
    adminPassword?: string;
    allocTeamId?: number;
    architecture?: string;
    associatedIpCount?: number;
    cloudId?: number;
    creator?: number;
    destroyTime?: string;
    diskSize?: number;
    diskType?: string;
    domestic?: boolean;
    estimatePrice?: number;
    expiredTime?: string;
    gatewayId?: number;
    goodsId?: number;
    id?: number;
    imageId?: string;
    instanceChargeType?: 'PostPaid' | 'PrePaid';
    instanceId?: string;
    instanceName?: string;
    instanceType?: string;
    keyId?: number;
    lastRebootTime?: string;
    launchTime?: string;
    locking?: boolean;
    maxBandwidthOut?: number;
    maxIpCount?: number;
    orderItemId?: number;
    perfLevel?:
      | 'CostEffective'
      | 'HighlyConcurrent'
      | 'LargeTraffic'
      | 'None'
      | 'RemoteLogin'
      | 'UnlimitedTraffic';
    platform?: 'android' | 'linux' | 'macos' | 'unknown' | 'web' | 'windows' | 'windows7';
    priceCurrency?: 'CREDIT' | 'RMB' | 'USD';
    privateIp?: string;
    profileId?: number;
    provider?:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    publicIp?: string;
    purchaseTeam?: number;
    pwdUpdateTime?: string;
    region?: string;
    shareMode?: 'Exclusive' | 'Sharable';
    status?:
      | 'Destroyed'
      | 'Error'
      | 'Expanding'
      | 'Expired'
      | 'Pending'
      | 'Rebooting'
      | 'Running'
      | 'Starting'
      | 'Stopped'
      | 'Stopping'
      | 'Suspend'
      | 'Unknown';
    subnetId?: string;
    vpcId?: string;
    zoneId?: string;
  };

  type LoginDevice = {
    appId?: string;
    createTime?: string;
    deviceId?: string;
    deviceType?: 'App' | 'Browser' | 'Extension' | 'HYRuntime' | 'RpaExecutor';
    hostName?: string;
    id?: number;
    lastActiveTime?: string;
    osName?: string;
    userAgent?: string;
  };

  type LoginDeviceDto = {
    appId?: string;
    appVersion?: string;
    clientIp?: string;
    clientLocation?: number;
    cpus?: number;
    createTime?: string;
    deviceId?: string;
    deviceType?: 'App' | 'Browser' | 'Extension' | 'HYRuntime' | 'RpaExecutor';
    domestic?: boolean;
    hostName?: string;
    id?: number;
    ipDataId?: number;
    lastActiveTime?: string;
    lastCity?: string;
    lastLogTime?: string;
    lastRemoteIp?: string;
    lastUserId?: number;
    logUrl?: string;
    mem?: number;
    online?: boolean;
    osName?: string;
    userAgent?: string;
    version?: string;
  };

  type LoginDeviceLocationVo = {
    appId?: string;
    appVersion?: string;
    clientIp?: string;
    clientLocation?: number;
    cpus?: number;
    createTime?: string;
    deviceId?: string;
    deviceType?: 'App' | 'Browser' | 'Extension' | 'HYRuntime' | 'RpaExecutor';
    domestic?: boolean;
    hostName?: string;
    id?: number;
    ipData?: IpDataDto;
    ipDataId?: number;
    lastActiveTime?: string;
    lastCity?: string;
    lastLogTime?: string;
    lastRemoteIp?: string;
    lastUserId?: number;
    logUrl?: string;
    mem?: number;
    online?: boolean;
    osName?: string;
    userAgent?: string;
    version?: string;
  };

  type LoginResultVo = {
    account?: string;
    jwt?: string;
    jwtExpireTime?: string;
    jwtId?: string;
    /** 是否需要验证码 */
    needCaptcha?: boolean;
    nickname?: string;
    userId?: number;
    userType?: 'NORMAL' | 'PARTNER' | 'SHADOW';
  };

  type logsFindCreditTrafficLogGetParams = {
    /** channelSessionId */
    channelSessionId: number;
  };

  type logsFindOperateLogsGetParams = {
    /** id */
    id?: string;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** from */
    from?: string;
    /** to */
    to?: string;
    /** 日志大类型 */
    category: 'IP' | 'LOGIN' | 'NONE' | 'OTHER' | 'SHOP' | 'TEAM' | 'TRAFFIC';
    /** 操作类型 */
    action?:
      | 'NONE'
      | 'T_TEST'
      | 'i_add_change'
      | 'i_bind_shop'
      | 'i_buy'
      | 'i_create'
      | 'i_delete'
      | 'i_renew'
      | 'i_unbind_shop'
      | 'i_update_meta'
      | 'l_session_traffic'
      | 's_access'
      | 's_bind_ip'
      | 's_change_ip'
      | 's_clean'
      | 's_create'
      | 's_delete'
      | 's_recovery'
      | 's_transfer'
      | 's_unbind_ip'
      | 's_update_app'
      | 's_update_basic'
      | 's_update_policy'
      | 's_user_auth'
      | 't_add_dep'
      | 't_change_boss'
      | 't_create'
      | 't_del_dep'
      | 't_exit'
      | 't_fun_user_auth'
      | 't_join'
      | 't_shop_user_auth'
      | 't_update_clouds'
      | 't_update_dep'
      | 't_update_dep_user'
      | 't_update_dom_blocks'
      | 't_update_invite'
      | 't_update_meta'
      | 't_user_transfer_shop'
      | 'u_login';
    /** 过滤操作者 */
    operator?: number;
    /** 按操作者名称查询 */
    operatorName?: string;
    /** 过滤操作结果，成功或失败 */
    success?: boolean;
    /** 过滤店铺 */
    shopId?: number;
    /** 按店铺名称查询，如果shopId不为空会忽略该字段 */
    shopName?: string;
    /** 过滤IP */
    ipId?: number;
    /** 按ip地址查询，如果ipId不为空会忽略该字段 */
    ip?: string;
    /** 过滤部门 */
    departmentId?: number;
    /** 过滤被操作人员 */
    userId?: number;
    /** 成员登录日志高级查询过滤客户端IP */
    remoteIp?: string;
    /** orderBy */
    orderBy?: string;
  };

  type logsFindSessionTrafficDetailGetParams = {
    /** sessionId */
    sessionId: number;
  };

  type logsFingerConfigByConfigIdGetParams = {
    /** configId */
    configId: string;
  };

  type logsGetClientLogSignatureGetParams = {
    /** clientUuid */
    clientUuid: string;
    /** logUuid */
    logUuid: string;
  };

  type logsMarkFinishedGetParams = {
    /** clientUuid */
    clientUuid: string;
  };

  type logsShopSessionsGetParams = {
    /** id */
    id?: string;
    /** pageNum */
    pageNum: number;
    /** pageSize */
    pageSize: number;
    /** sessionId */
    sessionId?: number;
    /** operator */
    operator?: number;
    /** 按用户名称搜索，如果指定了operator，该参数会被忽略 */
    operatorName?: string;
    /** shopId */
    shopId?: number;
    /** 用店铺名称搜索，如果指定了shopId，该参数会被忽略 */
    shopName?: string;
    /** 过滤IP */
    ipId?: number;
    /** 按ip地址查询，如果ipId不为空会忽略该字段 */
    ip?: string;
    /** from */
    from?: string;
    /** to */
    to?: string;
    /** orderBy */
    orderBy?: string;
  };

  type MaiKeLongReportRequest = {
    /** 迁移之前是否先清空店铺的网站密码 */
    clearOldPasswords?: boolean;
    cloneCookies?: boolean;
    cloneFingerprint?: boolean;
    clonePasswords?: boolean;
    /** 相应的指纹详细配置 */
    config?: FingerprintConfigVo;
    cookies?: ShopCookieVo[];
    isBrowserClone?: boolean;
    /** 遇到冲突时是否覆盖原来的记录，clearOldPasswords=true的时候该属性无意义 */
    overrideOnConflict?: boolean;
    passwords?: ShopPasswordsDto[];
    shopId?: number;
  };

  type MemberVo = {
    /** 账号 */
    account?: string;
    avatar?: string;
    createTime?: string;
    /** 邮箱 */
    email?: string;
    gender?: 'FEMALE' | 'MALE' | 'UNSPECIFIC';
    id?: number;
    nickname?: string;
    partnerId?: number;
    /** 手机 */
    phone?: string;
    residentCity?: string;
    roleCode?: 'boss' | 'manager' | 'staff' | 'superadmin';
    signature?: string;
    status?: 'ACTIVE' | 'BLOCK' | 'DELETED' | 'INACTIVE';
    teamNickname?: string;
    tenant?: number;
    userType?: 'NORMAL' | 'PARTNER' | 'SHADOW';
    weixin?: string;
  };

  type metaDwlAuditGetParams = {
    /** audited */
    audited?: boolean;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type metaDwlAuditPostParams = {
    /** ipId */
    ipId?: number;
    /** domain */
    domain: string;
    /** reason */
    reason: string;
    /** userName */
    userName: string;
    /** phone */
    phone: string;
    /** company */
    company?: string;
  };

  type metaDwlFindDwlAuditByAuditIdGetParams = {
    /** auditId */
    auditId: number;
  };

  type metaDwlGetParams = {
    /** scope */
    scope?: 'Global' | 'Team' | 'UserIp';
    /** ipId */
    ipId?: number;
  };

  type metaIpCheckersGetParams = {
    /** ipId */
    ipId?: number;
  };

  type MobileAccountVo = {
    accountType?: 'Business' | 'Regular';
    /** 手机账号发送卡片频率设置 */
    cardConfig?: AccountCardConfig;
    createTime?: string;
    creatorId?: number;
    description?: string;
    deviceId?: string;
    /** 手机账号可用性检查配置 */
    healthCheckConfig?: HealthCheckConfig;
    id?: number;
    /** 手机发送卡片/邀约时的批次间隔配置 */
    intervalConfig?: shoujiyaoyuepeizhi;
    /** 手机账号私信频率设置 */
    inviteConfig?: AccountInviteConfig;
    /** 手机账号养号配置 */
    maintenanceConfig?: AccountMaintenanceConfig;
    /** 手机新消息检查配置 */
    messageCheckConfig?: shoujixiaoxijianchapeizhi;
    mobileId?: number;
    mobileName?: string;
    mobileStatus?: 'EXPIRED' | 'OFFLINE' | 'ONLINE' | 'RESETING';
    osPlatform?: 'Android' | 'IOS';
    /** 平台信息 */
    platform?: ShopPlatformVo;
    platformId?: number;
    platformType?:
      | 'Douyin'
      | 'Ins'
      | 'Kakao'
      | 'Line'
      | 'TikTok'
      | 'Whatsapp'
      | 'Xiaohongshu'
      | 'Zalo'
      | 'tkshop';
    rpaTaskId?: number;
    rpaTaskItemId?: number;
    rpaTaskName?: string;
    /** 标签 */
    tags?: TagDto[];
    teamId?: number;
    /** 该账号今日已发私信条数 */
    todayPMCount?: number;
    /** 账号名称 */
    username?: string;
    /** 对应手机的工作时间配置 */
    workTimeConfig?: WorkTimeConfig;
  };

  type MobileAiRequest = {
    mode?: string;
    prompts?: string[];
    provider?: string;
  };

  type MobileGroupVo = {
    createTime?: string;
    id?: number;
    name?: string;
    sortNumber?: number;
    teamId?: number;
  };

  type MobileShareVo = {
    id?: number;
    shareTime?: string;
    /** 被分享的团队id */
    sharedTeamId?: number;
    sharedTeamName?: string;
    /** 被分享人（被分享团队） */
    sharedUserId?: number;
    /** 分享主队id */
    sharingTeamId?: number;
    /** 分享发起人（主队） */
    sharingUserId?: number;
    sourceMobileId?: number;
    targetMobileId?: number;
  };

  type NotifyModelUpdatedRequest = {
    mobileId?: number;
    model?: string;
  };

  type NotifyNewTkMessageRequest = {
    accounts?: string[];
    handles?: string[];
    mobileId?: number;
    tags?: string[];
  };

  type OnSessionOpenRequest = {
    /** 打开失败的错误信息 */
    remarks?: string;
    /** 远程调试端口 */
    remoteDebugPort?: number;
    /** 主IP监听代理端口 */
    remoteProxyPort?: number;
    /** 主IP监听代理协议 */
    remoteProxyType?: string;
    /** 是否打开成功 */
    success?: boolean;
  };

  type OpenSessionRequest = {
    /** 特定通道指定使用ip池的IP */
    channelIppIpMap?: Record<string, any>;
    ghost?: boolean;
    openapiTaskId?: number;
    rpaFlowId?: number;
    rpaTaskId?: number;
  };

  type OpenSessionRequestV1 = {
    /** 特定通道指定使用ip池的IP */
    channelIppIpMap?: Record<string, any>;
  };

  type OperateLogsVo = {
    /** 事件类型 */
    action?:
      | 'NONE'
      | 'T_TEST'
      | 'i_add_change'
      | 'i_bind_shop'
      | 'i_buy'
      | 'i_create'
      | 'i_delete'
      | 'i_renew'
      | 'i_unbind_shop'
      | 'i_update_meta'
      | 'l_session_traffic'
      | 's_access'
      | 's_bind_ip'
      | 's_change_ip'
      | 's_clean'
      | 's_create'
      | 's_delete'
      | 's_recovery'
      | 's_transfer'
      | 's_unbind_ip'
      | 's_update_app'
      | 's_update_basic'
      | 's_update_policy'
      | 's_user_auth'
      | 't_add_dep'
      | 't_change_boss'
      | 't_create'
      | 't_del_dep'
      | 't_exit'
      | 't_fun_user_auth'
      | 't_join'
      | 't_shop_user_auth'
      | 't_update_clouds'
      | 't_update_dep'
      | 't_update_dep_user'
      | 't_update_dom_blocks'
      | 't_update_invite'
      | 't_update_meta'
      | 't_user_transfer_shop'
      | 'u_login';
    /** 日志大类 */
    category?: 'IP' | 'LOGIN' | 'NONE' | 'OTHER' | 'SHOP' | 'TEAM' | 'TRAFFIC';
    guestTeamId?: number;
    guestTeamName?: string;
    id?: string;
    loginDevice?: LoginDevice;
    principal?: PrincipalVo;
    /** 远端IP */
    remoteIp?: string;
    /** 远端IP的位置信息 */
    remoteLocation?: string;
    requestTime?: string;
    result?: ResultVo;
    /** 与该次日志相关的资源 */
    targets?: Record<string, any>;
    teamId?: number;
    /** 如果该次事件牵涉到属性更新，这里更新后的资源属性 */
    updates?: Record<string, any>;
  };

  type PagePlanShopRequest = {
    pageNum?: number;
    pageSize?: number;
    planId?: number;
    platformTypes?: string[];
    shopName?: string;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    tagIds?: number[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
  };

  type PageResultActiveShopSessionVo = {
    current?: number;
    list?: ActiveShopSessionVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultDwlAuditVo = {
    current?: number;
    list?: DwlAuditVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultExtensionsVo = {
    current?: number;
    list?: ExtensionsVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultFingerprintDetailVo = {
    current?: number;
    list?: FingerprintDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultIpDetailVo = {
    current?: number;
    list?: IpDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultIpDomainDiaryDto = {
    current?: number;
    list?: IpDomainDiaryDto[];
    pageSize?: number;
    total?: number;
  };

  type PageResultIppIpDetailVo = {
    current?: number;
    list?: IppIpDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultIppPoolVo = {
    current?: number;
    list?: IppPoolVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultMobileAccountVo = {
    current?: number;
    list?: MobileAccountVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultOperateLogsVo = {
    current?: number;
    list?: OperateLogsVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultSharingIpVo = {
    current?: number;
    list?: SharingIpVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultShopDetailVo = {
    current?: number;
    list?: ShopDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultShopGrantVo = {
    current?: number;
    list?: ShopGrantVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultShopIpLogVo = {
    current?: number;
    list?: ShopIpLogVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultShopSessionLogVo = {
    current?: number;
    list?: ShopSessionLogVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultShopTagVo = {
    current?: number;
    list?: ShopTagVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultUserLastActivityVo = {
    current?: number;
    list?: UserLastActivityVo[];
    pageSize?: number;
    total?: number;
  };

  type PageShopRequest = {
    pageNum?: number;
    pageSize?: number;
    platformTypes?: string[];
    shopName?: string;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    tagIds?: number[];
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
  };

  type PartnerDiscountDto = {
    baseDiscount?: number;
    currentDiscount?: number;
    enabled?: boolean;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    name?: string;
    partnerId?: number;
    sortNo?: number;
  };

  type paymentCalcRenewIpsPriceGetParams = {
    /** ip列表 */
    ipIds: number;
    /** 按周续费的续费几周 */
    weekDuration: number;
    /** 按月续费的续费几月 */
    monthDuration: number;
  };

  type PendingRpaPlanVo = {
    name?: string;
    nextFireTime?: string;
    planId?: number;
    planType?: 'Auto' | 'Loop' | 'Manual' | 'Trigger';
  };

  type PlatformIpMonitorDto = {
    alertAction?: 'Alert' | 'AlertAndClose';
    createTime?: string;
    id?: number;
    months?: number;
    platformType?: string;
    teamId?: number;
  };

  type PlatformUsabilityVo = {
    area?:
      | 'Argentina'
      | 'Australia'
      | 'Austria'
      | 'Belarus'
      | 'Belgium'
      | 'Bolivia'
      | 'Brazil'
      | 'Canada'
      | 'Chile'
      | 'China'
      | 'Colombia'
      | 'Costa_Rica'
      | 'Dominican'
      | 'Ecuador'
      | 'Egypt'
      | 'France'
      | 'Germany'
      | 'Global'
      | 'Guatemala'
      | 'Honduras'
      | 'HongKong'
      | 'India'
      | 'Indonesia'
      | 'Ireland'
      | 'Israel'
      | 'Italy'
      | 'Japan'
      | 'Kazakhstan'
      | 'Korea'
      | 'Malaysia'
      | 'Mexico'
      | 'Netherlands'
      | 'Nicaragua'
      | 'Panama'
      | 'Paraguay'
      | 'Peru'
      | 'Philippines'
      | 'Poland'
      | 'Portuguese'
      | 'Puerto_Rico'
      | 'Russia'
      | 'Salvador'
      | 'Saudi_Arabia'
      | 'Singapore'
      | 'Spain'
      | 'Sweden'
      | 'Switzerland'
      | 'Taiwan'
      | 'Thailand'
      | 'Turkey'
      | 'United_Arab_Emirates'
      | 'United_Kingdom'
      | 'United_States'
      | 'Uruguay'
      | 'Venezuela'
      | 'Vietnam';
    platformType?: string;
    usable?: boolean;
  };

  type poolipByIppIpIdProbeGetParams = {
    /** ippIpId */
    ippIpId: number;
    /** 接入点ID */
    transitId: number;
  };

  type PrincipalVo = {
    id?: string;
    nickname?: string;
    tokenId?: string;
    type?: string;
  };

  type ProbeResult = {
    connectTime?: number;
    error?: string;
    originalError?: string;
    reachable?: boolean;
    remoteIpEndpoint?: string;
    success?: boolean;
    testingTime?: number;
  };

  type ProxyConfig = {
    host?: string;
    ipVersion?: 'Auto' | 'IPv4' | 'IPv6';
    password?: string;
    port?: number;
    proxyType?: 'http' | 'httpTunnel' | 'https' | 'socks4' | 'socks5' | 'ssh';
    sshKey?: string;
    username?: string;
  };

  type ProxyProbeWithRemoteIp = {
    code?: number;
    connectTime?: number;
    error?: string;
    handshakeTime?: number;
    originalError?: string;
    output?: string;
    proto?: string;
    reachable?: boolean;
    remoteIp?: string;
    remoteIpEndpoint?: string;
    success?: boolean;
    testingTime?: number;
  };

  type PublishFlowToShops = {
    rpaFlowId?: number;
    shopIds?: number[];
  };

  type QCloudMobileBackup = {
    createTime?: string;
    filename?: string;
    key?: string;
    size?: number;
  };

  type recordBatchRemoveRecordFilesPostParams = {
    /** cleanLocked */
    cleanLocked?: boolean;
  };

  type recordBySessionIdLockRecordPutParams = {
    /** sessionId */
    sessionId: number;
    /** fileLocked */
    fileLocked: boolean;
  };

  type recordBySessionIdRemoveRecordFilesDeleteParams = {
    /** sessionId */
    sessionId: number;
  };

  type recordRecordFilesByTimeDeleteParams = {
    /** cleanLocked */
    cleanLocked?: boolean;
    /** fromTime */
    fromTime: string;
    /** toTime */
    toTime: string;
  };

  type recordSessionBySessionIdGetParams = {
    /** sessionId */
    sessionId: number;
    /** includeMonitors */
    includeMonitors?: boolean;
  };

  type recordSessionBySessionIdSignatureGetParams = {
    /** sessionId */
    sessionId: number;
  };

  type recordSessionBySessionIdSlicePutParams = {
    /** sessionId */
    sessionId: number;
    /** mimeType */
    mimeType: string;
  };

  type recordSliceByRecordSliceIdEndPutParams = {
    /** recordSliceId */
    recordSliceId: number;
    /** size */
    size: number;
    /** endType */
    endType: 'ClientError' | 'ForceStop' | 'Natural' | 'Overrun' | 'Pause' | 'Recording';
    /** endReason */
    endReason?: string;
  };

  type recordSliceByRecordSliceIdGetUrlGetParams = {
    /** recordSliceId */
    recordSliceId: number;
  };

  type recordSliceByRecordSliceIdHeartbeatPutParams = {
    /** recordSliceId */
    recordSliceId: number;
    /** size */
    size: number;
  };

  type RecordSliceVo = {
    bucketId?: number;
    endReason?: string;
    endTime?: string;
    endType?: 'ClientError' | 'ForceStop' | 'Natural' | 'Overrun' | 'Pause' | 'Recording';
    filePath?: string;
    id?: number;
    index?: number;
    sessionId?: number;
    shopId?: number;
    size?: number;
    startTime?: string;
    teamId?: number;
    valid?: boolean;
  };

  type RecoveryShopItem = {
    credit?: number;
    shop?: ShopDto;
  };

  type RecoveryShopResult = {
    items?: RecoveryShopItem[];
  };

  type RemoteIpCheckerConfig = {
    checkers?: RemoteIpProviderConfig[];
    huaYongCheckers?: HuaYongCheckerConfig[];
    probeUrl?: string;
    probeUrlMap?: Record<string, any>;
  };

  type RemoteIpProviderConfig = {
    dataIndex?: string[];
    isDefault?: boolean;
    provider?: string;
    url?: string;
  };

  type RemoveMobileFromGroupRequest = {
    groupId?: number;
    mobileIds?: number[];
  };

  type RepairShopFingerprintRequest = {
    /** 是否修复所有有问题的指纹，为true的时候shopId无意义 */
    allShops?: boolean;
    /** 如何生成指纹 */
    params?: GenFingerParams;
    shopIds?: number[];
  };

  type ReportSessionChannelTunnelRequest = {
    channelSessionId?: number;
    /** 节点ID，clash_profile.id or transit.id */
    nodeId?: number;
    /** clash proxy id */
    proxyId?: number;
    tunnelType?: 'clash' | 'direct' | 'jump' | 'localFrontend' | 'platform' | 'transit';
  };

  type ResetMobileRequest = {
    /** 是不改变手机型号 */
    changeModel?: boolean;
    /** 是不清除数据恢复出厂设置 */
    clearData?: boolean;
    mobileId?: number;
    /** 指定型号 */
    model?: string;
  };

  type Resource = true;

  type resourceTagsGetParams = {
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    /** resourceId */
    resourceId: number;
  };

  type RestoreMobileRequest = {
    filename?: string;
    mobileId?: number;
  };

  type ResultVo = {
    code?: number;
    extraInfo?: Record<string, any>;
    message?: string;
    success?: boolean;
  };

  type RouterItemVo = {
    description?: string;
    orderNo?: number;
    rule?: string;
    ruleType?: 'Default' | 'DomainWildcard' | 'UrlPattern' | 'UrlWildcard';
  };

  type RouterSpecVo = {
    defaultRouter?: 'Direct' | 'None' | 'Official' | 'Primary' | 'Secondary';
    directRouters?: RouterItemVo[];
    primaryRouters?: RouterItemVo[];
    secondaryRouters?: RouterItemVo[];
  };

  type RpaFlowCreateTypeStatVo = {
    count?: number;
    createType?: 'FileCopy' | 'Manual' | 'Market' | 'MarketCopy' | 'Shared' | 'TkPack' | 'Tkshop';
  };

  type RpaFlowRpaTypeStatVo = {
    count?: number;
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
  };

  type RpaFlowStatusStatVo = {
    count?: number;
    status?: 'Draft' | 'Published';
  };

  type RpaFlowStatVo = {
    createTypeStatList?: RpaFlowCreateTypeStatVo[];
    statusStatList?: RpaFlowStatusStatVo[];
  };

  type RpaFlowStatVoV2 = {
    rpaTypeStatList?: RpaFlowRpaTypeStatVo[];
    statusStatList?: RpaFlowStatusStatVo[];
  };

  type RpaFlowVersionDto = {
    configId?: string;
    console?: boolean;
    createTime?: string;
    description?: string;
    flowId?: number;
    flowName?: string;
    id?: number;
    numberVersion?: number;
    /** 流程类型，分手机和browser */
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    teamId?: number;
    version?: string;
  };

  type RpaPlatformVo = {
    flowId?: number;
    platformName?: string;
  };

  type RpaSimpleHisVo = {
    creatorId?: number;
    /** 是否结束 */
    done?: boolean;
    /** #see RpaFailReason.xxx */
    errorCode?: number;
    errorMsg?: string;
    /** 执行者身份。历史数据访字段为空，展示的时候使用creatorId */
    executorId?: number;
    failedItems?: number;
    id?: number;
    manualRun?: boolean;
    name?: string;
    planId?: number;
    planName?: string;
    /** 流程类型，分手机和browser */
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    status?:
      | 'Cancelled'
      | 'CreateFailed'
      | 'Ended'
      | 'Ended_All_Failed'
      | 'Ended_Partial_Failed'
      | 'Ignored'
      | 'NotStart'
      | 'Running'
      | 'ScheduleCancelled'
      | 'Scheduled'
      | 'Scheduling'
      | 'UnusualEnded';
    successItems?: number;
    teamId?: number;
    totalItems?: number;
  };

  type RpaSyncMobileAccountsRequest = {
    accounts?: string[];
    area?:
      | 'Argentina'
      | 'Australia'
      | 'Austria'
      | 'Belarus'
      | 'Belgium'
      | 'Bolivia'
      | 'Brazil'
      | 'Canada'
      | 'Chile'
      | 'China'
      | 'Colombia'
      | 'Costa_Rica'
      | 'Dominican'
      | 'Ecuador'
      | 'Egypt'
      | 'France'
      | 'Germany'
      | 'Global'
      | 'Guatemala'
      | 'Honduras'
      | 'HongKong'
      | 'India'
      | 'Indonesia'
      | 'Ireland'
      | 'Israel'
      | 'Italy'
      | 'Japan'
      | 'Kazakhstan'
      | 'Korea'
      | 'Malaysia'
      | 'Mexico'
      | 'Netherlands'
      | 'Nicaragua'
      | 'Panama'
      | 'Paraguay'
      | 'Peru'
      | 'Philippines'
      | 'Poland'
      | 'Portuguese'
      | 'Puerto_Rico'
      | 'Russia'
      | 'Salvador'
      | 'Saudi_Arabia'
      | 'Singapore'
      | 'Spain'
      | 'Sweden'
      | 'Switzerland'
      | 'Taiwan'
      | 'Thailand'
      | 'Turkey'
      | 'United_Arab_Emirates'
      | 'United_Kingdom'
      | 'United_States'
      | 'Uruguay'
      | 'Venezuela'
      | 'Vietnam';
    mobileId?: number;
    platformType?: string;
  };

  type Serializable = true;

  type SessionChannelTokenVo = {
    /** 通道ID */
    channelId?: number;
    /** 通道场景 */
    channelScene?:
      | 'ip'
      | 'ipp'
      | 'lanDirect'
      | 'lanProxy'
      | 'lanSystem'
      | 'officialTransit'
      | 'sessionProxy';
    /** 通道会话ID */
    channelSessionId?: number;
    /** 加速通道端点 */
    endpoints?: TunnelEndpointVo[];
    /** 官方链路 */
    official?: boolean;
    /** 是否是主通道 */
    primary?: boolean;
    /** 代理信息，官方通道中没有 */
    proxyConfig?: ProxyConfig;
    /** 全局会话ID */
    sessionId?: number;
    /** 限速，0表示不限速；单位 kbps */
    speedLimit?: number;
    /** 目标IP信息（如果存在） */
    targetIp?: TargetIpVo;
    teamId?: number;
    /** 访问接入点或IPGO时使用的token */
    token?: string;
    /** 分组权重信息 */
    weights?: GroupWeightVo[];
  };

  type SessionMonitorVo = {
    endTime?: string;
    id?: number;
    nickname?: string;
    startTime?: string;
    userId?: number;
  };

  type SessionNetworkConfig = {
    pageLoadTimeout?: number;
    pageLoadTimeoutAlerts?: number;
  };

  type SessionTimeLineVo = {
    monitors?: SessionMonitorVo[];
    records?: RecordSliceVo[];
  };

  type SessionTokenVo = {
    /** 每个通道的Token */
    channelTokens?: SessionChannelTokenVo[];
    /** 路由规则 */
    routers?: ShopRouterVo[];
    /** 会话ID */
    sessionId?: number;
    /** 本地打开会话监听的地址 */
    sessionProxyHost?: string;
    /** 所属团队 */
    teamId?: number;
  };

  type SetCloudMobileProxyRequest = {
    /** 只对腾讯云手机有意义，绑定花漾里的ip，只支持自有的socks5和http代理 */
    ipId?: number;
    mobileId?: number;
    proxy?: string;
  };

  type SetRequireIpRequest = {
    all?: boolean;
    requireIp?: boolean;
    shopIds?: number[];
  };

  type SetShopCookiesParamVo = {
    cookies?: ShopCookieVo[];
    merge?: boolean;
  };

  type SetStatelessShopPolicyRequest = {
    /** 是否全量 */
    all?: boolean;
    ids?: number[];
    statelessChangeFp?: boolean;
    syncPolicy?: ShopSyncPolicyVo;
  };

  type SharingIpVo = {
    autoRenew?: boolean;
    cloudProvider?: string;
    cloudRegion?: string;
    createTime?: string;
    creatorId?: number;
    description?: string;
    directDownTraffic?: number;
    directUpTraffic?: number;
    domestic?: boolean;
    downTraffic?: number;
    dynamic?: boolean;
    eipId?: number;
    enableWhitelist?: boolean;
    /** 过期状态 */
    expireStatus?: 'Expired' | 'Expiring' | 'Normal';
    forbiddenLongLatitude?: boolean;
    gatewayId?: number;
    goodsId?: number;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    importType?: 'Platform' | 'User';
    invalidTime?: string;
    ip?: string;
    lastProbeTime?: string;
    latitude?: number;
    locale?: string;
    locationId?: number;
    longitude?: number;
    name?: string;
    networkType?: 'cloudIdc' | 'mobile' | 'proxyIdc' | 'residential' | 'unknown' | 'unknownIdc';
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    originalTeam?: number;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    pipeType?: 'None' | 'Proxy' | 'Tunnel' | 'TunnelFailToProxy';
    preferTransit?: number;
    probeError?: string;
    /** 供应商名称 */
    providerName?: string;
    realIp?: string;
    refreshUrl?: string;
    remoteLogin?: boolean;
    renewPrice?: number;
    shops?: ShopDto[];
    source?: string;
    speedLimit?: number;
    status?: 'Available' | 'Pending' | 'Unavailable';
    sticky?: boolean;
    teamId?: number;
    teamName?: string;
    testingTime?: number;
    timezone?: string;
    traffic?: number;
    trafficCurrency?: 'CREDIT' | 'RMB' | 'USD';
    trafficPrice?: number;
    trafficUnlimited?: boolean;
    transitType?: 'Auto' | 'Direct' | 'Transit';
    tunnelTypes?: string;
    upTraffic?: number;
    valid?: boolean;
    validEndDate?: string;
    vpsId?: number;
  };

  type ShopAccessConfigVo = {
    /** 会话是否允许监视 */
    allowMonitor?: boolean;
    allowSkip?: boolean;
    exclusive?: boolean;
    monitorPerception?: boolean;
    recordPerception?: boolean;
    recordPolicy?: 'Chosen' | 'Disabled' | 'Forced';
  };

  type ShopAndPhoneStat = {
    deletingShopCount?: number;
    mobileCount?: number;
    shopCount?: number;
  };

  type shopAppMobileBaiduCallbackByActionPostParams = {
    /** action */
    action: string;
    /** appkey */
    appkey?: string;
    /** nonce */
    nonce?: string;
    /** s */
    s?: string;
    /** auth_ver */
    auth_ver?: string;
  };

  type shopAppMobileFindMobileDtoByCodeGetParams = {
    /** code */
    code: string;
  };

  type shopAppMobileFindPadCodeByAdbAddressGetParams = {
    /** adbAddress */
    adbAddress: string;
  };

  type shopAppMobileToggleArmCloudAdbPutParams = {
    /** padCode */
    padCode: string;
    /** enable */
    enable: boolean;
  };

  type shopAppMobileUpdateStatusPutParams = {
    /** code */
    code: string;
    /** status */
    status: 'EXPIRED' | 'OFFLINE' | 'ONLINE' | 'RESETING';
  };

  type shopAuditByAuditIdPassPutParams = {
    /** auditId */
    auditId: number;
  };

  type shopAuditByAuditIdRejectPutParams = {
    /** auditId */
    auditId: number;
    /** reason */
    reason?: string;
  };

  type ShopBatchRequest = {
    shopIdList?: number[];
  };

  type shopBookmarksByShopIdBookmarksGetParams = {
    /** shopId */
    shopId: number;
  };

  type shopBookmarksByShopIdOverrideShopBookmarksPostParams = {
    /** shopId */
    shopId: number;
  };

  type shopBookmarksSyncAllShopPutParams = {
    /** merge */
    merge?: boolean;
  };

  type shopBookmarksSyncBatchShopPutParams = {
    /** merge */
    merge?: boolean;
  };

  type shopBookmarksSyncDefaultPutParams = {
    /** merge */
    merge?: boolean;
  };

  type shopBookmarksSyncTeamPutParams = {
    /** merge */
    merge?: boolean;
  };

  type shopBookmarksSyncUserPutParams = {
    /** merge */
    merge?: boolean;
  };

  type ShopBriefVo = {
    /** 账户账号 */
    account?: string;
    /** 是否允许用户自行安装插件 */
    allowExtension?: boolean;
    /** 会话是否允许监视 */
    allowMonitor?: boolean;
    /** 是否允许跳过敏感操作 */
    allowSkip?: boolean;
    /** 是否自动代填 */
    autoFill?: boolean;
    bookmarkBar?: string;
    coordinateId?: string;
    /** 创建时间 */
    createTime?: string;
    /** 创建者 */
    creatorId?: number;
    deleteTime?: string;
    deleting?: boolean;
    /** 描述 */
    description?: string;
    domainPolicy?: 'Blacklist' | 'None' | 'Whitelist';
    /** 是否独占访问 */
    exclusive?: boolean;
    extension?: 'both' | 'extension' | 'huayang';
    extraProp?: string;
    /** 绑定的指纹Id */
    fingerprintId?: number;
    /** 绑定的指纹模板Id（只有无状态账号才允许绑定指纹模板） */
    fingerprintTemplateId?: number;
    frontUrl?: string;
    googleTranslateSpeed?: boolean;
    homePage?: string;
    homePageSites?: string;
    /** id */
    id?: number;
    imageForbiddenSize?: number;
    intranetEnabled?: boolean;
    ipSwitchCheckInterval?: number;
    ipSwitchStrategy?: 'Abort' | 'Alert' | 'Off';
    lastAccessTime?: string;
    lastAccessUser?: number;
    lastSyncTime?: string;
    loginStatus?: 'Offline' | 'Online' | 'Unknown';
    /** 任务栏分身标记文字 */
    markCode?: string;
    /** 任务栏分身标记背景颜色 */
    markCodeBg?: number;
    monitorPerception?: boolean;
    /** 账户名称 */
    name?: string;
    nameBookmarkEnabled?: boolean;
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    /** 经营品类 */
    operatingCategory?:
      | '医药保健'
      | '图书文具'
      | '宠物用品'
      | '家具建材'
      | '家电电器'
      | '工业用品'
      | '户外运动'
      | '手机数码'
      | '手表眼镜'
      | '护肤美妆'
      | '母婴玩具'
      | '汽车配件'
      | '生活家居'
      | '电商其他'
      | '电脑平板'
      | '艺术珠宝'
      | '花园聚会'
      | '计生情趣'
      | '软件程序'
      | '鞋服箱包'
      | '音乐影视'
      | '食品生鲜'
      | '鲜花绿植';
    parentShopId?: number;
    /** 密码 */
    password?: string;
    /** 平台信息 */
    platform?: ShopPlatformVo;
    /** 平台ID */
    platformId?: number;
    privateAddress?: string;
    privateTitle?: string;
    recordPerception?: boolean;
    recordPolicy?: 'Chosen' | 'Disabled' | 'Forced';
    requireIp?: boolean;
    resourcePolicy?: number;
    securityPolicyEnabled?: boolean;
    securityPolicyUpdateTime?: string;
    sharePolicyId?: string;
    shopDataSize?: number;
    shopNo?: string;
    stateless?: boolean;
    statelessChangeFp?: boolean;
    statelessSyncPolicy?: number;
    syncPolicy?: number;
    /** 团队ID */
    teamId?: number;
    trafficAlertStrategy?: 'Abort' | 'Off';
    trafficAlertThreshold?: number;
    trafficSaving?: boolean;
    type?: 'Global' | 'Local' | 'None';
    webSecurity?: boolean;
  };

  type shopByCategoryByCategoryGetParams = {
    /** 视图分类 */
    category:
      | 'Shop'
      | 'SocialMedia'
      | 'all'
      | 'collected'
      | 'custom'
      | 'recycleBin'
      | 'unbound'
      | 'unfingerprinted'
      | 'ungranted'
      | 'windowSync';
    /** 视图ID(category=custom时指定) */
    viewId?: number;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** 查询字符串（名称或IP） */
    query?: string;
    /** shopType */
    shopType?: 'Global' | 'Local' | 'None';
    /** 平台类型(Amazon|eBay|...)，用逗号连接 */
    platformTypes?: string;
    /** 地区Area(Global|United_States|Canada|...，用逗号连接 */
    areas?: string;
    /** 按标签id过滤 */
    tagIds?: string;
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
    /** props */
    props?: string;
    /** 排序字段 */
    sortFiled?: string;
    /** 顺序 */
    sortOrder?: 'asc' | 'desc';
  };

  type shopByDepartmentByDepartmentIdGetParams = {
    /** departmentId */
    departmentId: number;
  };

  type shopByIdConfigDeleteParams = {
    /** id */
    id: number;
    /** prefix */
    prefix?: string;
  };

  type shopByIdConfigGetParams = {
    /** id */
    id: number;
    /** prefix */
    prefix?: string;
  };

  type shopByIdPlatformGetParams = {
    /** id */
    id: number;
  };

  type shopByIdShortcutPostParams = {
    /** id */
    id: number;
    /** rpaEnabled */
    rpaEnabled?: boolean;
    /** validDays */
    validDays: number;
    /** name */
    name?: string;
    /** createSource */
    createSource: 'Desktop' | 'Share';
  };

  type shopByIdShortcutsGetParams = {
    /** id */
    id: number;
    /** createSource */
    createSource?: 'Desktop' | 'Share';
  };

  type shopByShopIdBriefGetParams = {
    /** shopId */
    shopId: number;
  };

  type shopByShopIdCancelTransferPostParams = {
    /** 分身ID */
    shopId: number;
  };

  type shopByShopIdChannelsGetParams = {
    /** shopId */
    shopId: number;
  };

  type shopByShopIdCleanCookiesDeleteParams = {
    /** shopId */
    shopId: number;
  };

  type shopByShopIdClearShopDataInOssDeleteParams = {
    /** shopId */
    shopId: number;
  };

  type shopByShopIdDynamicStrategyPutParams = {
    /** shopId */
    shopId: number;
    /** ipId */
    ipId?: number;
    /** ippId */
    ippId?: number;
    /** dynamicStrategy */
    dynamicStrategy: 'Off' | 'Remain' | 'SwitchOnSession';
    /** locationId */
    locationId?: number;
    /** locationLevel */
    locationLevel?: 'City' | 'Continent' | 'Country' | 'District' | 'None' | 'Province' | 'Unknown';
  };

  type shopByShopIdExportTrafficSpecGetParams = {
    /** shopId */
    shopId: number;
  };

  type shopByShopIdExtensionPolicyGetParams = {
    /** shopId */
    shopId: number;
    /** operateSharing */
    operateSharing?: boolean;
  };

  type shopByShopIdExtensionsGetParams = {
    /** shopId */
    shopId: number;
    /** operateSharing */
    operateSharing?: boolean;
  };

  type shopByShopIdFavoriteSitesDeleteParams = {
    /** shopId */
    shopId: number;
    /** siteIds */
    siteIds: number;
  };

  type shopByShopIdFavoriteSitesGetParams = {
    /** shopId */
    shopId: number;
  };

  type shopByShopIdFindShopBindIpsHistoryGetParams = {
    /** shopId */
    shopId: number;
  };

  type shopByShopIdGetCookiesGetParams = {
    /** shopId */
    shopId: number;
  };

  type shopByShopIdGetHistoriesGetParams = {
    /** shopId */
    shopId: number;
  };

  type shopByShopIdGetParams = {
    /** shopId */
    shopId: number;
  };

  type shopByShopIdGrantedUsersGetParams = {
    /** shopId */
    shopId: number;
  };

  type shopByShopIdLoginStatusPutParams = {
    /** shopId */
    shopId: number;
    /** loginStatus */
    loginStatus: 'Offline' | 'Online' | 'Unknown';
  };

  type shopByShopIdOpenSessionPostParams = {
    /** shopId */
    shopId: number;
  };

  type shopByShopIdOpsListGetParams = {
    /** shopId */
    shopId: number;
  };

  type shopByShopIdRemoveExtensionsDeleteParams = {
    /** shopId */
    shopId: number;
    /** extensionIds */
    extensionIds: number;
    /** operateSharing */
    operateSharing?: boolean;
  };

  type shopByShopIdSessionDryrunPostParams = {
    /** shopId */
    shopId: number;
    /** rpaTaskId */
    rpaTaskId?: number;
    /** rpaFlowId */
    rpaFlowId?: number;
    /** openapiTaskId */
    openapiTaskId?: number;
  };

  type shopByShopIdSessionPostParams = {
    /** shopId */
    shopId: number;
    /** rpaTaskId */
    rpaTaskId?: number;
    /** rpaFlowId */
    rpaFlowId?: number;
    /** openapiTaskId */
    openapiTaskId?: number;
    /** ghost */
    ghost?: boolean;
  };

  type shopByShopIdSessionsActivelyGetParams = {
    /** shopId */
    shopId: number;
  };

  type shopByShopIdSessionV2PostParams = {
    /** shopId */
    shopId: number;
    /** rpaTaskId */
    rpaTaskId?: number;
    /** rpaFlowId */
    rpaFlowId?: number;
    /** openapiTaskId */
    openapiTaskId?: number;
    /** ghost */
    ghost?: boolean;
  };

  type shopByShopIdSetCookiesPutParams = {
    /** shopId */
    shopId: number;
  };

  type shopByShopIdSignatureShopDataGetParams = {
    /** shopId */
    shopId: number;
  };

  type shopByShopIdTransferPostParams = {
    /** 分身ID */
    shopId: number;
    /** 受让团队 */
    targetTeamId: number;
    /** 受让联系人 */
    targetUserId: number;
    /** 是否转移IP */
    transferIp: boolean;
    /** 是否转让指纹 */
    transferFingerprint: boolean;
  };

  type shopByShopIdUpdateShopMarkCodePutParams = {
    /** shopId */
    shopId: number;
    /** markCode */
    markCode: string;
    /** markCodeBg */
    markCodeBg: number;
  };

  type shopByShopIdUploadHistoriesPostParams = {
    /** shopId */
    shopId: number;
  };

  type shopByTeamByTeamIdGetParams = {
    /** 团队ID */
    teamId: number;
    /** 查询字符串（名称或IP） */
    query?: string;
    /** 平台名称(Amazon|eBay) */
    platform?: string;
    /** IP */
    ip?: string;
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
    /** 授权成员ID */
    grantedUserId?: number;
    /** 全部分身 */
    all?: boolean;
    /** 过滤未绑定IP的 */
    ipUnbound?: boolean;
    /** 过滤未授权的 */
    unGranted?: boolean;
    /** 过滤未设置指纹的 */
    unFingerprinted?: boolean;
    /** 过滤我收藏的 */
    collected?: boolean;
    /** 过滤访问中的 */
    opened?: boolean;
    /** 按标签id过滤 */
    tagIds?: string;
    /** lightly */
    lightly?: boolean;
    /** props */
    props?: string;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type shopByUserByUserIdGetParams = {
    /** userId */
    userId: number;
    /** teamId */
    teamId: number;
    /** pageNum */
    pageNum: number;
    /** pageSize */
    pageSize: number;
  };

  type shopChannelSessionByChannelSessionIdTokenGetParams = {
    /** channelSessionId */
    channelSessionId: number;
  };

  type shopChannelSessionByChannelSessionIdTokenV2GetParams = {
    /** channelSessionId */
    channelSessionId: number;
  };

  type ShopChannelTokenVo = {
    /** 通道ID */
    channelId?: number;
    /** 通道会话ID */
    channelSessionId?: number;
    dynamicStrategy?: 'Off' | 'Remain' | 'SwitchOnSession';
    /** 接入点的端点 */
    endpoints?: EndpointVo[];
    /** 过期时间 */
    expireTime?: string;
    /** 绑定的IP */
    ip?: IpWithLocationVo;
    /** 代理协议的IP的代理信息 */
    ipSocks?: IpSocksDto;
    /** IP池IP */
    ippIp?: IppIpWithLocationVo;
    /** 本地代理（临时IP、OpenApi指定IP） */
    lanProxy?: ShopLanProxyDto;
    locationId?: number;
    locationLevel?: 'City' | 'Continent' | 'Country' | 'District' | 'None' | 'Province' | 'Unknown';
    /** 官方链路 */
    official?: boolean;
    /** 是否是主通道 */
    primary?: boolean;
    /** 当前会话使用的Proxy ID */
    proxyId?: number;
    /** 全局会话ID */
    sessionId?: number;
    /** 限速，0表示不限速；单位 kbps */
    speedLimit?: number;
    token?: string;
    /** 接入点连接方式 */
    transitType?: 'Auto' | 'Direct' | 'Transit';
  };

  type ShopChannelVo = {
    createTime?: string;
    dynamicStrategy?: 'Off' | 'Remain' | 'SwitchOnSession';
    id?: number;
    /** 通道的IP */
    ip?: TeamIpVo;
    ipId?: number;
    /** 通道的IP池 */
    ipPool?: IpPoolDto;
    ippId?: number;
    locationId?: number;
    locationLevel?: 'City' | 'Continent' | 'Country' | 'District' | 'None' | 'Province' | 'Unknown';
    officialChannelId?: number;
    primary?: boolean;
    shopId?: number;
    teamId?: number;
  };

  type ShopChatDto = {
    chatAccount?: string;
    chatType?:
      | 'None'
      | 'email'
      | 'facebookMessager'
      | 'line'
      | 'qq'
      | 'skype'
      | 'wechat'
      | 'whatsapp'
      | 'zalo';
    id?: number;
    shopId?: number;
    teamId?: number;
  };

  type ShopChatVo = {
    /** 沟通账号 */
    chatAccount?: string;
    /** 沟通账号类型 */
    chatType?:
      | 'None'
      | 'email'
      | 'facebookMessager'
      | 'line'
      | 'qq'
      | 'skype'
      | 'wechat'
      | 'whatsapp'
      | 'zalo';
  };

  type shopCheckNameExistsGetParams = {
    /** name */
    name: string;
  };

  type shopCheckSessionDeviceGetParams = {
    /** shopId */
    shopId?: number;
  };

  type shopCloseSessionByShopByShopIdPutParams = {
    /** shopId */
    shopId: number;
  };

  type ShopConfigDto = {
    configKey?: string;
    configValue?: string;
    id?: number;
    shopId?: number;
    teamId?: number;
  };

  type ShopConfigPrefixRequest = {
    ids?: number[];
    prefixList?: string[];
  };

  type ShopConfigRequest = {
    configs?: ConfigVo[];
    ids?: number[];
  };

  type ShopCookiesVo = {
    cookies?: ShopCookieVo[];
  };

  type ShopCookieVo = {
    domain?: string;
    expires?: number;
    httpOnly?: boolean;
    name?: string;
    path?: string;
    priority?: string;
    sameParty?: boolean;
    sameSite?: string;
    secure?: boolean;
    updateTime?: string;
    value?: string;
  };

  type shopDeletePostParams = {
    /** 是否加入回收站 */
    recycled?: boolean;
    /** deleteFingerprint */
    deleteFingerprint?: boolean;
  };

  type ShopDetailVo = {
    /** 访问账户次数 */
    accessCount?: number;
    /** 账户账号 */
    account?: string;
    /** 是否允许用户自行安装插件 */
    allowExtension?: boolean;
    /** 会话是否允许监视 */
    allowMonitor?: boolean;
    /** 是否允许跳过敏感操作 */
    allowSkip?: boolean;
    /** 是否自动代填 */
    autoFill?: boolean;
    bookmarkBar?: string;
    /** 通道列表 */
    channels?: ShopChannelVo[];
    /** 联系方式 */
    chats?: ShopChatDto[];
    coordinateId?: string;
    /** 创建时间 */
    createTime?: string;
    /** 创建人信息 */
    creator?: UserVo;
    /** 创建者 */
    creatorId?: number;
    deleteTime?: string;
    deleting?: boolean;
    /** 描述 */
    description?: string;
    domainPolicy?: 'Blacklist' | 'None' | 'Whitelist';
    /** 黑白名单 */
    domains?: ShopDomainItem[];
    /** 是否独占访问 */
    exclusive?: boolean;
    extension?: 'both' | 'extension' | 'huayang';
    extraProp?: string;
    /** 绑定的指纹Id */
    fingerprintId?: number;
    /** 绑定的指纹模板Id（只有无状态账号才允许绑定指纹模板） */
    fingerprintTemplateId?: number;
    /** 指纹模版 */
    fingerprintTemplateVo?: FingerprintTemplateDto;
    /** 指纹 */
    fingerprintVo?: FingerprintDto;
    frontUrl?: string;
    googleTranslateSpeed?: boolean;
    /** 授权的部门 */
    grantDepartmentList?: DepartmentDto[];
    /** 授权来源 */
    grantSource?: 'FromDepartment' | 'FromUser';
    /** 授权给的用户 */
    grantUserVoList?: MemberVo[];
    homePage?: string;
    homePageSites?: string;
    /** id */
    id?: number;
    imageForbiddenSize?: number;
    intranetEnabled?: boolean;
    /** IP绑定次数 */
    ipBindCount?: number;
    /** IP绑定时间 */
    ipBindTime?: string;
    ipSwitchCheckInterval?: number;
    ipSwitchStrategy?: 'Abort' | 'Alert' | 'Off';
    /** IP解绑时间 */
    ipUnbindTime?: string;
    /** 本地代理配置 */
    lanProxy?: ShopLanProxyDto;
    lastAccessTime?: string;
    lastAccessUser?: number;
    lastSyncTime?: string;
    loginStatus?: 'Offline' | 'Online' | 'Unknown';
    /** 任务栏分身标记文字 */
    markCode?: string;
    /** 任务栏分身标记背景颜色 */
    markCodeBg?: number;
    monitorPerception?: boolean;
    /** 账户名称 */
    name?: string;
    nameBookmarkEnabled?: boolean;
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    /** 经营品类 */
    operatingCategory?:
      | '医药保健'
      | '图书文具'
      | '宠物用品'
      | '家具建材'
      | '家电电器'
      | '工业用品'
      | '户外运动'
      | '手机数码'
      | '手表眼镜'
      | '护肤美妆'
      | '母婴玩具'
      | '汽车配件'
      | '生活家居'
      | '电商其他'
      | '电脑平板'
      | '艺术珠宝'
      | '花园聚会'
      | '计生情趣'
      | '软件程序'
      | '鞋服箱包'
      | '音乐影视'
      | '食品生鲜'
      | '鲜花绿植';
    /** 主账号 */
    parentShop?: ShopDto;
    parentShopId?: number;
    /** 主团队 */
    parentTeam?: IdNameVo;
    /** 密码 */
    password?: string;
    /** 平台信息 */
    platform?: ShopPlatformVo;
    /** 平台ID */
    platformId?: number;
    privateAddress?: string;
    /** 私密网站 */
    privateDomains?: string[];
    privateTitle?: string;
    recordPerception?: boolean;
    recordPolicy?: 'Chosen' | 'Disabled' | 'Forced';
    requireIp?: boolean;
    resourcePolicy?: number;
    /** 资源策略 */
    resourcePolicyVo?: ShopResourcePolicyVo;
    /** 通道路由列表 */
    routers?: ShopRouterDto[];
    /** 内置RPA流程数 */
    rpaFlowCount?: number;
    securityPolicyEnabled?: boolean;
    securityPolicyUpdateTime?: string;
    sharePolicyId?: string;
    shopDataSize?: number;
    shopNo?: string;
    /** 快捷方式个数 */
    shortcutCount?: number;
    stateless?: boolean;
    statelessChangeFp?: boolean;
    statelessSyncPolicy?: number;
    /** 无痕云端同步策略 */
    statelessSyncPolicyVo?: ShopSyncPolicyVo;
    /** 是否使用默认访问策略 */
    strategyDefault?: boolean;
    syncPolicy?: number;
    /** 云端同步策略 */
    syncPolicyVo?: ShopSyncPolicyVo;
    /** 标签 */
    tags?: TagDto[];
    /** 团队ID */
    teamId?: number;
    trafficAlertStrategy?: 'Abort' | 'Off';
    trafficAlertThreshold?: number;
    trafficSaving?: boolean;
    type?: 'Global' | 'Local' | 'None';
    webSecurity?: boolean;
  };

  type ShopDomainItem = {
    description?: string;
    domain?: string;
  };

  type ShopDomainPolicyVo = {
    domainPolicy?: 'Blacklist' | 'None' | 'Whitelist';
    domains?: ShopDomainItem[];
  };

  type shopDownloadByTokenGetParams = {
    /** token */
    token: string;
  };

  type ShopDto = {
    /** 账户账号 */
    account?: string;
    /** 是否允许用户自行安装插件 */
    allowExtension?: boolean;
    /** 会话是否允许监视 */
    allowMonitor?: boolean;
    /** 是否允许跳过敏感操作 */
    allowSkip?: boolean;
    /** 是否自动代填 */
    autoFill?: boolean;
    bookmarkBar?: string;
    coordinateId?: string;
    /** 创建时间 */
    createTime?: string;
    /** 创建者 */
    creatorId?: number;
    deleteTime?: string;
    deleting?: boolean;
    /** 描述 */
    description?: string;
    domainPolicy?: 'Blacklist' | 'None' | 'Whitelist';
    /** 是否独占访问 */
    exclusive?: boolean;
    extension?: 'both' | 'extension' | 'huayang';
    extraProp?: string;
    /** 绑定的指纹Id */
    fingerprintId?: number;
    /** 绑定的指纹模板Id（只有无状态账号才允许绑定指纹模板） */
    fingerprintTemplateId?: number;
    frontUrl?: string;
    googleTranslateSpeed?: boolean;
    homePage?: string;
    homePageSites?: string;
    /** id */
    id?: number;
    imageForbiddenSize?: number;
    intranetEnabled?: boolean;
    ipSwitchCheckInterval?: number;
    ipSwitchStrategy?: 'Abort' | 'Alert' | 'Off';
    lastAccessTime?: string;
    lastAccessUser?: number;
    lastSyncTime?: string;
    loginStatus?: 'Offline' | 'Online' | 'Unknown';
    /** 任务栏分身标记文字 */
    markCode?: string;
    /** 任务栏分身标记背景颜色 */
    markCodeBg?: number;
    monitorPerception?: boolean;
    /** 账户名称 */
    name?: string;
    nameBookmarkEnabled?: boolean;
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    /** 经营品类 */
    operatingCategory?:
      | '医药保健'
      | '图书文具'
      | '宠物用品'
      | '家具建材'
      | '家电电器'
      | '工业用品'
      | '户外运动'
      | '手机数码'
      | '手表眼镜'
      | '护肤美妆'
      | '母婴玩具'
      | '汽车配件'
      | '生活家居'
      | '电商其他'
      | '电脑平板'
      | '艺术珠宝'
      | '花园聚会'
      | '计生情趣'
      | '软件程序'
      | '鞋服箱包'
      | '音乐影视'
      | '食品生鲜'
      | '鲜花绿植';
    parentShopId?: number;
    /** 密码 */
    password?: string;
    /** 平台ID */
    platformId?: number;
    privateAddress?: string;
    privateTitle?: string;
    recordPerception?: boolean;
    recordPolicy?: 'Chosen' | 'Disabled' | 'Forced';
    requireIp?: boolean;
    resourcePolicy?: number;
    securityPolicyEnabled?: boolean;
    securityPolicyUpdateTime?: string;
    sharePolicyId?: string;
    shopDataSize?: number;
    shopNo?: string;
    stateless?: boolean;
    statelessChangeFp?: boolean;
    statelessSyncPolicy?: number;
    syncPolicy?: number;
    /** 团队ID */
    teamId?: number;
    trafficAlertStrategy?: 'Abort' | 'Off';
    trafficAlertThreshold?: number;
    trafficSaving?: boolean;
    type?: 'Global' | 'Local' | 'None';
    webSecurity?: boolean;
  };

  type shopFlowByShopIdGetFlowsGetParams = {
    /** shopId */
    shopId: number;
  };

  type shopFlowByShopIdGetFlowsV20240118GetParams = {
    /** shopId */
    shopId: number;
  };

  type shopFlowRemoveFromGlobalListDeleteParams = {
    /** rpaFlowId */
    rpaFlowId: number;
  };

  type ShopGrant2UsersParamVo = {
    cleanFirst?: boolean;
    confirm?: boolean;
    departmentIdList?: number[];
    shopIds?: number[];
    teamId?: number;
    userIdList?: number[];
  };

  type ShopGrantVo = {
    /** 账户账号 */
    account?: string;
    /** 是否允许用户自行安装插件 */
    allowExtension?: boolean;
    /** 会话是否允许监视 */
    allowMonitor?: boolean;
    /** 是否允许跳过敏感操作 */
    allowSkip?: boolean;
    /** 是否自动代填 */
    autoFill?: boolean;
    bookmarkBar?: string;
    coordinateId?: string;
    /** 创建时间 */
    createTime?: string;
    /** 创建者 */
    creatorId?: number;
    deleteTime?: string;
    deleting?: boolean;
    /** 描述 */
    description?: string;
    domainPolicy?: 'Blacklist' | 'None' | 'Whitelist';
    /** 是否独占访问 */
    exclusive?: boolean;
    extension?: 'both' | 'extension' | 'huayang';
    extraProp?: string;
    /** 绑定的指纹Id */
    fingerprintId?: number;
    /** 绑定的指纹模板Id（只有无状态账号才允许绑定指纹模板） */
    fingerprintTemplateId?: number;
    frontUrl?: string;
    googleTranslateSpeed?: boolean;
    /** 授权来源 */
    grantSource?: 'FromDepartment' | 'FromUser';
    homePage?: string;
    homePageSites?: string;
    /** id */
    id?: number;
    imageForbiddenSize?: number;
    intranetEnabled?: boolean;
    ipSwitchCheckInterval?: number;
    ipSwitchStrategy?: 'Abort' | 'Alert' | 'Off';
    lastAccessTime?: string;
    lastAccessUser?: number;
    lastSyncTime?: string;
    loginStatus?: 'Offline' | 'Online' | 'Unknown';
    /** 任务栏分身标记文字 */
    markCode?: string;
    /** 任务栏分身标记背景颜色 */
    markCodeBg?: number;
    monitorPerception?: boolean;
    /** 账户名称 */
    name?: string;
    nameBookmarkEnabled?: boolean;
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    /** 经营品类 */
    operatingCategory?:
      | '医药保健'
      | '图书文具'
      | '宠物用品'
      | '家具建材'
      | '家电电器'
      | '工业用品'
      | '户外运动'
      | '手机数码'
      | '手表眼镜'
      | '护肤美妆'
      | '母婴玩具'
      | '汽车配件'
      | '生活家居'
      | '电商其他'
      | '电脑平板'
      | '艺术珠宝'
      | '花园聚会'
      | '计生情趣'
      | '软件程序'
      | '鞋服箱包'
      | '音乐影视'
      | '食品生鲜'
      | '鲜花绿植';
    parentShopId?: number;
    /** 密码 */
    password?: string;
    /** 平台信息 */
    platform?: ShopPlatformVo;
    /** 平台ID */
    platformId?: number;
    privateAddress?: string;
    privateTitle?: string;
    recordPerception?: boolean;
    recordPolicy?: 'Chosen' | 'Disabled' | 'Forced';
    requireIp?: boolean;
    resourcePolicy?: number;
    securityPolicyEnabled?: boolean;
    securityPolicyUpdateTime?: string;
    sharePolicyId?: string;
    shopDataSize?: number;
    shopNo?: string;
    stateless?: boolean;
    statelessChangeFp?: boolean;
    statelessSyncPolicy?: number;
    syncPolicy?: number;
    /** 标签 */
    tags?: TagDto[];
    /** 团队ID */
    teamId?: number;
    trafficAlertStrategy?: 'Abort' | 'Off';
    trafficAlertThreshold?: number;
    trafficSaving?: boolean;
    type?: 'Global' | 'Local' | 'None';
    webSecurity?: boolean;
  };

  type shopImportV2PostParams = {
    /** clientPlatform */
    clientPlatform?: string;
  };

  type ShopIntranetEnableParamVo = {
    intranetEnabled?: boolean;
    shopIdList?: number[];
  };

  type ShopIpBindHistoryVo = {
    bindTime?: string;
    cloudProvider?: string;
    createTime?: string;
    id?: number;
    importType?: 'Platform' | 'User';
    ip?: string;
    /** 这个ip绑定了多少次店铺 */
    ipBindHisCount?: number;
    ipId?: number;
    location?: IpLocationDto;
    protoType?: 'http' | 'httpTunnel' | 'ipgo' | 'luminati' | 'socks5' | 'ssh' | 'vps';
    shopId?: number;
    shopName?: string;
    source?: string;
    teamId?: number;
    unbindTime?: string;
  };

  type ShopIpLogVo = {
    createTime?: string;
    id?: number;
    ip?: string;
    lastTime?: string;
    monitorId?: number;
    platformType?: string;
    shop?: ShopDto;
    shopId?: number;
    shopName?: string;
    teamId?: number;
  };

  type shopIpMonitorLogsGetParams = {
    ip?: string;
    pageNum?: number;
    pageSize?: number;
    shopId?: number;
    sortField?: 'create_time' | 'ip' | 'last_time';
    sortOrder?: 'asc' | 'desc';
  };

  type shopIpMonitorMonitorByIdDeleteParams = {
    /** id */
    id: number;
  };

  type shopIpMonitorMonitorByIdPutParams = {
    /** id */
    id: number;
    /** platformType */
    platformType: string;
    /** months */
    months: number;
    /** alertAction */
    alertAction: 'Alert' | 'AlertAndClose';
  };

  type shopIpMonitorMonitorPostParams = {
    /** platformType */
    platformType: string;
    /** months */
    months: number;
    /** alertAction */
    alertAction: 'Alert' | 'AlertAndClose';
  };

  type shopIpMonitorReportGetParams = {
    /** ip */
    ip: string;
    /** sessionId */
    sessionId: number;
  };

  type ShopLanProxyDto = {
    enabled?: boolean;
    host?: string;
    hostDomestic?: boolean;
    hostLocationId?: number;
    id?: number;
    latitude?: number;
    locale?: string;
    locationId?: number;
    longitude?: number;
    networkType?: 'UseDirect' | 'UseProxy' | 'UseSystem';
    password?: string;
    port?: number;
    probeOnSession?: boolean;
    proxyType?: string;
    remoteIp?: string;
    sshKey?: string;
    teamId?: number;
    timezone?: string;
    updateTime?: string;
    username?: string;
  };

  type ShopLanProxyVo = {
    enabled?: boolean;
    host?: string;
    latitude?: number;
    locale?: string;
    locationId?: number;
    longitude?: number;
    networkType?: 'UseDirect' | 'UseProxy' | 'UseSystem';
    password?: string;
    port?: number;
    probeOnSession?: boolean;
    proxyType?: string;
    remoteIp?: string;
    sshKey?: string;
    timezone?: string;
    updateTime?: string;
    username?: string;
  };

  type shopListDeletingsGetParams = {
    /** 平台类型(Amazon|eBay|...)，用逗号连接 */
    platformTypes?: string;
    /** 地区Area(Global|United_States|Canada|...，用逗号连接 */
    areas?: string;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** 名称 */
    name?: string;
    /** 排序字段 */
    sortFiled?: string;
    /** 顺序 */
    sortOrder?: 'asc' | 'desc';
  };

  type shopListPropsPostParams = {
    /** props */
    props?: string;
  };

  type shopMobileAccountBatchDeleteDeleteParams = {
    /** mobileIds */
    mobileIds: number;
  };

  type shopMobileAccountDeletePostParams = {
    /** id */
    id: number;
  };

  type shopMobileAccountFindMobileAccountsGetParams = {
    /** mobileId */
    mobileId: number;
  };

  type shopMobileAccountGetAuthorizedAccountGetParams = {
    /** mobileId */
    mobileId: number;
    /** username */
    username: string;
  };

  type shopMobileAccountGetGetParams = {
    /** id */
    id: number;
  };

  type shopMobileAccountListGetParams = {
    /** 按手机过滤 */
    mobileId?: number;
    /** 查询字符串（账号名称） */
    query?: string;
    /** 平台类型(Amazon|eBay|...)，用逗号连接 */
    platformTypes?: string;
    /** 地区Area(Global|United_States|Canada|...，用逗号连接 */
    areas?: string;
    /** 按标签id过滤 */
    tagIds?: string;
    /** 标签的逻辑条件，支持OR|AND */
    tagLc?: 'AND' | 'NOT' | 'OR';
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** 格式为[field_name asc|desc], 可选列：create_time,username */
    orderBy?: string;
  };

  type shopMobileAppleIosFindUdidStatusGetParams = {
    /** udid */
    udid: string;
  };

  type shopMobileCheckNameExistsGetParams = {
    /** name */
    name: string;
  };

  type shopMobileCountAuthorizedMobilesGetParams = {
    /** 是否只统计联营的手机 */
    sharedOnly?: boolean;
  };

  type shopMobileDeleteDeviceMobilesDeleteParams = {
    /** deviceId */
    deviceId: string;
  };

  type shopMobileDeleteMobileByIdDeleteParams = {
    /** id */
    id: number;
  };

  type shopMobileGetArmCloudPropertiesGetParams = {
    /** padCode */
    padCode: string;
  };

  type shopMobileGetCloudPropertiesGetParams = {
    /** padCode */
    padCode: string;
  };

  type shopMobileGetDetailInfoGetParams = {
    /** id */
    id: number;
  };

  type shopMobileGetSimpleInfoGetParams = {
    /** id */
    id: number;
  };

  type shopMobileGetWDAUrlGetParams = {
    /** udid */
    udid: string;
  };

  type shopMobileGroupCheckNameExistsGetParams = {
    /** name */
    name: string;
  };

  type shopMobileGroupDeleteDeleteParams = {
    /** groupId */
    groupId: number;
  };

  type shopMobileInitiateVideoRoomByIdPutParams = {
    /** id */
    id: number;
    /** 如果为true，表示是小屏预览的方式打开 */
    smallPreview?: boolean;
  };

  type shopMobileListArmCloudGetParams = {
    /** 是否带上手机标签 */
    fetchTags?: boolean;
  };

  type shopMobileListByDepartmentByDepartmentIdGetParams = {
    /** departmentId */
    departmentId: number;
  };

  type shopMobileListGetParams = {
    /** 如果不为空表示只查这个login_device连接的手机 */
    deviceId?: string;
    /** 如果不为空表示只查这个group的手机 */
    groupId?: number;
    /** 是否带上手机标签 */
    fetchTags?: boolean;
  };

  type shopMobileQcloudBackupsGetParams = {
    /** mobileId */
    mobileId: number;
  };

  type shopMobileQcloudDeleteBackupDeleteParams = {
    /** mobileId */
    mobileId: number;
    /** filename */
    filename: string;
  };

  type shopMobileQcloudTempFilePutOssUrlGetParams = {
    /** mobileId */
    mobileId: number;
    /** filename */
    filename: string;
  };

  type shopMobileRestartAdbServerPutParams = {
    /** deviceId */
    deviceId: string;
  };

  type shopMobileRestartArmCloudInsPostParams = {
    /** mobileId */
    mobileId: number;
  };

  type shopMobileShareDeleteSpecificShareDeleteParams = {
    /** parentMobileId */
    parentMobileId: number;
    /** shareId */
    shareId: number;
  };

  type shopMobileShareFindSharedMobilesGetParams = {
    /** mobileId */
    mobileId: number;
  };

  type shopMobileShareReturnShareByMobileIdDeleteParams = {
    /** mobileId */
    mobileId: number;
  };

  type shopMobileUpdateMetasPostParams = {
    /** mobileId */
    mobileId: number;
  };

  type shopMobileUpdateRpaSilentPutParams = {
    /** mobileId */
    mobileId: number;
    /** rpaSilent */
    rpaSilent: boolean;
    /** 当rpaSilent=true时才有意义，如果>0表示多少分钟后自动取消禁用状态,0表示不自动取消禁用状态 */
    minutes?: number;
  };

  type ShopOpsDto = {
    errorMsg?: string;
    id?: number;
    lastOpsEndTime?: string;
    lastOpsStartTime?: string;
    lastTargetId?: number;
    opsAction?: string;
    running?: boolean;
    shopId?: number;
    success?: boolean;
    teamId?: number;
  };

  type ShopPasswordsDto = {
    actionUrl?: string;
    blacklistedByUser?: number;
    dateCreated?: number;
    id?: number;
    originUrl?: string;
    passwordElement?: string;
    passwordType?: number;
    passwordValue?: string;
    platformId?: number;
    scheme?: number;
    shopId?: number;
    signonRealm?: string;
    teamId?: number;
    updateTime?: string;
    usernameElement?: string;
    usernameValue?: string;
  };

  type ShopPasswordsVo = {
    actionUrl?: string;
    blacklistedByUser?: number;
    dateCreated?: number;
    id?: number;
    originUrl?: string;
    passwordElement?: string;
    passwordType?: number;
    passwordValue?: string;
    platform?: ShopPlatformVo;
    platformId?: number;
    scheme?: number;
    shopId?: number;
    signonRealm?: string;
    teamId?: number;
    updateTime?: string;
    usernameElement?: string;
    usernameValue?: string;
  };

  type ShopPlatformStatVo = {
    count?: number;
    platformType?: string;
  };

  type ShopPlatformVo = {
    area?:
      | 'Argentina'
      | 'Australia'
      | 'Austria'
      | 'Belarus'
      | 'Belgium'
      | 'Bolivia'
      | 'Brazil'
      | 'Canada'
      | 'Chile'
      | 'China'
      | 'Colombia'
      | 'Costa_Rica'
      | 'Dominican'
      | 'Ecuador'
      | 'Egypt'
      | 'France'
      | 'Germany'
      | 'Global'
      | 'Guatemala'
      | 'Honduras'
      | 'HongKong'
      | 'India'
      | 'Indonesia'
      | 'Ireland'
      | 'Israel'
      | 'Italy'
      | 'Japan'
      | 'Kazakhstan'
      | 'Korea'
      | 'Malaysia'
      | 'Mexico'
      | 'Netherlands'
      | 'Nicaragua'
      | 'Panama'
      | 'Paraguay'
      | 'Peru'
      | 'Philippines'
      | 'Poland'
      | 'Portuguese'
      | 'Puerto_Rico'
      | 'Russia'
      | 'Salvador'
      | 'Saudi_Arabia'
      | 'Singapore'
      | 'Spain'
      | 'Sweden'
      | 'Switzerland'
      | 'Taiwan'
      | 'Thailand'
      | 'Turkey'
      | 'United_Arab_Emirates'
      | 'United_Kingdom'
      | 'United_States'
      | 'Uruguay'
      | 'Venezuela'
      | 'Vietnam';
    category?: 'IM' | 'Mail' | 'Other' | 'Payment' | 'Shop' | 'SocialMedia';
    frontUrl?: string;
    id?: number;
    loginUrl?: string;
    name?: string;
    typeName?: string;
  };

  type shopPoliciesByShopIdAccessConfigGetParams = {
    /** shopId */
    shopId: number;
    /** 是否联营策略 */
    operateSharing?: boolean;
  };

  type shopPoliciesByShopIdDirectDomainsGetParams = {
    /** shopId */
    shopId: number;
    /** 是否联营策略 */
    operateSharing?: boolean;
  };

  type shopPoliciesByShopIdDomainPolicyGetParams = {
    /** shopId */
    shopId: number;
    /** 是否联营策略 */
    operateSharing?: boolean;
  };

  type shopPoliciesByShopIdShopBlockElementsGetParams = {
    /** shopId */
    shopId: number;
    /** 是否联营策略 */
    operateSharing?: boolean;
  };

  type shopPoliciesCreateBlockElementPostParams = {
    /** operateSharing */
    operateSharing?: boolean;
  };

  type shopPoliciesSetDomainPolicyPutParams = {
    /** 是否联营策略 */
    operateSharing?: boolean;
  };

  type shopPoliciesSetPolicyConfigPutParams = {
    /** 是否联营策略 */
    operateSharing?: boolean;
  };

  type shopPoliciesUpdateBlockElementsPostParams = {
    /** 是否联营策略 */
    operateSharing?: boolean;
  };

  type shopPoliciesUpdateDirectDomainsPostParams = {
    /** 是否联营策略 */
    operateSharing?: boolean;
  };

  type shopPoliciesUpdateShopExtensionPostParams = {
    /** 是否联营策略 */
    operateSharing?: boolean;
  };

  type ShopPolicyVo = {
    allowMonitor?: boolean;
    allowSkip?: boolean;
    bookmarkBar?: string;
    exclusive?: boolean;
    favoriteSites?: SimpleFavoriteSite[];
    googleTranslateSpeed?: boolean;
    homePage?: string;
    imageForbiddenSize?: number;
    ipSwitchCheckInterval?: number;
    ipSwitchStrategy?: 'Abort' | 'Alert' | 'Off';
    nameBookmarkEnabled?: boolean;
    recordPolicy?: 'Chosen' | 'Disabled' | 'Forced';
    requireIp?: boolean;
    resourcePolicy?: ShopResourcePolicyVo;
    securityPolicyEnabled?: boolean;
    syncPolicy?: ShopSyncPolicyVo;
    trafficAlertStrategy?: 'Abort' | 'Off';
    trafficAlertThreshold?: number;
    webSecurity?: boolean;
  };

  type shopPrepareTransferPostParams = {
    /** 是否转移IP */
    transferIp: boolean;
  };

  type shopRemoveTeamExtensionsDeleteParams = {
    /** extensionIds */
    extensionIds: number;
  };

  type ShopResourcePolicyVo = {
    image?: boolean;
    video?: boolean;
  };

  type ShopRouterDto = {
    channelId?: number;
    channelType?: 'Direct' | 'None' | 'Official' | 'Primary' | 'Secondary';
    createTime?: string;
    description?: string;
    id?: number;
    orderNo?: number;
    rule?: string;
    ruleType?: 'Default' | 'DomainWildcard' | 'UrlPattern' | 'UrlWildcard';
    shopId?: number;
    teamId?: number;
    valid?: boolean;
  };

  type ShopRouterTypeItem = {
    /** 通道类型 */
    channelType?: 'Direct' | 'None' | 'Official' | 'Primary' | 'Secondary';
    description?: string;
    /** 规则描述 */
    rule?: string;
    /** 规则类型 */
    ruleType?: 'Default' | 'DomainWildcard' | 'UrlPattern' | 'UrlWildcard';
  };

  type ShopRouterVo = {
    channelId?: number;
    orderNo?: number;
    rule?: string;
    ruleType?: 'Default' | 'DomainWildcard' | 'UrlPattern' | 'UrlWildcard';
  };

  type ShopRpaFlowDto = {
    autoPublish?: boolean;
    bizCode?: string;
    createType?: 'FileCopy' | 'Manual' | 'Market' | 'MarketCopy' | 'Shared' | 'TkPack' | 'Tkshop';
    description?: string;
    flowConfigId?: string;
    flowVersion?: string;
    id?: number;
    platforms?: RpaPlatformVo[];
    rpaFlowId?: number;
    /** 如果为空表示发布到当前团队的所有分身 */
    shopId?: number;
    teamId?: number;
    version?: RpaFlowVersionDto;
  };

  type shopSessionBySessionIdClosedPutParams = {
    /** sessionId */
    sessionId: number;
  };

  type shopSessionBySessionIdCurrentMonitorGetParams = {
    /** sessionId */
    sessionId: number;
  };

  type shopSessionBySessionIdDetailGetParams = {
    /** sessionId */
    sessionId: number;
  };

  type shopSessionBySessionIdFindWatchingUsersGetParams = {
    /** sessionId */
    sessionId: number;
  };

  type shopSessionBySessionIdHeartbeatGetParams = {
    /** sessionId */
    sessionId: number;
  };

  type shopSessionBySessionIdMonitorPostParams = {
    /** sessionId */
    sessionId: number;
    /** roomName */
    roomName: string;
  };

  type shopSessionBySessionIdOnOpenPutParams = {
    /** sessionId */
    sessionId: number;
  };

  type shopSessionBySessionIdRemoteProxyPortPutParams = {
    /** sessionId */
    sessionId: number;
    /** remoteProxyPort */
    remoteProxyPort: number;
    /** remoteProxyType */
    remoteProxyType: string;
  };

  type shopSessionBySessionIdSnapshotGetParams = {
    /** sessionId */
    sessionId: number;
  };

  type shopSessionBySessionIdSnapshotOkPutParams = {
    /** sessionId */
    sessionId: number;
    /** error */
    error?: string;
  };

  type shopSessionBySessionIdSwitchTransitGetParams = {
    /** sessionId */
    sessionId: number;
    /** 切换某个通道的接入点，不设置时，切换所有通道 */
    channelId?: number;
    /** transitId */
    transitId: number;
  };

  type shopSessionChannelTokenGetParams = {
    /** channelSessionId */
    channelSessionId: number;
  };

  type shopSessionIpWeightPostParams = {
    /** ipId */
    ipId: number;
    /** tunnelType */
    tunnelType: 'clash' | 'direct' | 'jump' | 'localFrontend' | 'platform' | 'transit';
    /** groupId */
    groupId?: number;
    /** weight */
    weight: number;
  };

  type ShopSessionLogVo = {
    /** 事件类型 */
    action?:
      | 'NONE'
      | 'T_TEST'
      | 'i_add_change'
      | 'i_bind_shop'
      | 'i_buy'
      | 'i_create'
      | 'i_delete'
      | 'i_renew'
      | 'i_unbind_shop'
      | 'i_update_meta'
      | 'l_session_traffic'
      | 's_access'
      | 's_bind_ip'
      | 's_change_ip'
      | 's_clean'
      | 's_create'
      | 's_delete'
      | 's_recovery'
      | 's_transfer'
      | 's_unbind_ip'
      | 's_update_app'
      | 's_update_basic'
      | 's_update_policy'
      | 's_user_auth'
      | 't_add_dep'
      | 't_change_boss'
      | 't_create'
      | 't_del_dep'
      | 't_exit'
      | 't_fun_user_auth'
      | 't_join'
      | 't_shop_user_auth'
      | 't_update_clouds'
      | 't_update_dep'
      | 't_update_dep_user'
      | 't_update_dom_blocks'
      | 't_update_invite'
      | 't_update_meta'
      | 't_user_transfer_shop'
      | 'u_login';
    /** 日志大类 */
    category?: 'IP' | 'LOGIN' | 'NONE' | 'OTHER' | 'SHOP' | 'TEAM' | 'TRAFFIC';
    /** 结束时间 */
    closeTime?: string;
    /** 录像文件总大小 */
    fileSize?: number;
    guestTeamId?: number;
    guestTeamName?: string;
    id?: string;
    loginDevice?: LoginDevice;
    principal?: PrincipalVo;
    /** 生成了几个录像文件 */
    recordCount?: number;
    recordLocked?: boolean;
    recordStatus?: 'Deleted' | 'Undefined' | 'Valid';
    /** 远端IP */
    remoteIp?: string;
    /** 远端IP的位置信息 */
    remoteLocation?: string;
    requestTime?: string;
    result?: ResultVo;
    rpaTaskId?: number;
    /** 会话当前状态 */
    status?: 'CLOSE' | 'CREATING' | 'READY';
    /** 与该次日志相关的资源 */
    targets?: Record<string, any>;
    teamId?: number;
    /** 如果该次事件牵涉到属性更新，这里更新后的资源属性 */
    updates?: Record<string, any>;
  };

  type shopSessionMonitorByMonitorIdClosedPutParams = {
    /** monitorId */
    monitorId: number;
    /** traffic */
    traffic?: number;
  };

  type shopSessionMonitorByMonitorIdHeartbeatMonitorPutParams = {
    /** monitorId */
    monitorId: number;
    /** traffic */
    traffic?: number;
  };

  type ShopSessionMonitorSignalingRequest = {
    monitorId?: number;
    signaling?: string;
    type?: string;
    /** 发请求的人是不是观看者 */
    watcher?: boolean;
  };

  type ShopSessionMonitorVo = {
    channel?: string;
    createTime?: string;
    monitorId?: number;
    password?: string;
    roomName?: string;
    sessionId?: number;
    signaling?: string;
    teamId?: number;
    turn?: string;
    username?: string;
  };

  type ShopSessionStatVo = {
    lastAccessTime?: string;
    nickname?: string;
    shopId?: number;
    shopName?: string;
    userId?: number;
  };

  type shopSettingsByShopIdBasicInfoPutParams = {
    /** shopId */
    shopId: number;
    /** 账户名称 */
    name: string;
    /** 描述 */
    description?: string;
    /** 首页 */
    frontUrl?: string;
    /** 修改平台 */
    platformId?: number;
    /** 经营品类 */
    operatingCategory?:
      | '医药保健'
      | '图书文具'
      | '宠物用品'
      | '家具建材'
      | '家电电器'
      | '工业用品'
      | '户外运动'
      | '手机数码'
      | '手表眼镜'
      | '护肤美妆'
      | '母婴玩具'
      | '汽车配件'
      | '生活家居'
      | '电商其他'
      | '电脑平板'
      | '艺术珠宝'
      | '花园聚会'
      | '计生情趣'
      | '软件程序'
      | '鞋服箱包'
      | '音乐影视'
      | '食品生鲜'
      | '鲜花绿植';
    /** 用户名或密码是否修改了 */
    passwordSet?: boolean;
    /** 店铺分身用户名 */
    username?: string;
    /** 店铺分身密码 */
    password?: string;
    /** extraProp */
    extraProp?: string;
  };

  type shopSettingsByShopIdChatListPutParams = {
    /** shopId */
    shopId: number;
  };

  type shopSettingsByShopIdDomainCookiesGetParams = {
    /** shopId */
    shopId: number;
  };

  type shopSettingsByShopIdNameAndDescPutParams = {
    /** shopId */
    shopId: number;
    /** 账户名称 */
    name: string;
    /** 描述 */
    description?: string;
  };

  type shopSettingsByShopIdPasswordByPasswordIdPutParams = {
    /** shopId */
    shopId: number;
    /** passwordId */
    passwordId: number;
    /** url */
    url: string;
    /** platformId */
    platformId?: number;
    /** username */
    username: string;
    /** password */
    password: string;
    /** signonRealm */
    signonRealm?: string;
  };

  type shopSettingsByShopIdPasswordGetParams = {
    /** shopId */
    shopId: number;
    /** url */
    url: string;
    /** username */
    username: string;
  };

  type shopSettingsByShopIdPasswordPostParams = {
    /** shopId */
    shopId: number;
    /** url */
    url: string;
    /** platformId */
    platformId?: number;
    /** username */
    username?: string;
    /** password */
    password?: string;
    /** signonRealm */
    signonRealm?: string;
  };

  type shopSettingsByShopIdPasswordsDeleteParams = {
    /** shopId */
    shopId: number;
    /** passwordIds */
    passwordIds: number;
  };

  type shopSettingsByShopIdPasswordsGetParams = {
    /** shopId */
    shopId: number;
    /** decrypt */
    decrypt?: boolean;
  };

  type shopSettingsByShopIdPasswordsPostParams = {
    /** shopId */
    shopId: number;
  };

  type shopSettingsByShopIdRemoveCookiesByDomainDeleteParams = {
    /** shopId */
    shopId: number;
    /** domains */
    domains: string;
  };

  type shopSettingsByShopIdReportTokenGetParams = {
    /** shopId */
    shopId: number;
    /** token有效期，默认10分钟 */
    expireMinutes?: number;
  };

  type shopSettingsByShopIdSensitiveInfoGetParams = {
    /** shopId */
    shopId: number;
  };

  type shopSettingsByShopIdSensitiveInfoPutParams = {
    /** shopId */
    shopId: number;
    /** 敏感信息 */
    sensitiveInfo?: string;
  };

  type shopSettingsByShopIdShopPolicyGetParams = {
    /** shopId */
    shopId: number;
  };

  type shopSettingsByShopIdUpdateShopPolicyPostParams = {
    /** shopId */
    shopId: number;
  };

  type shopSettingsGetFetchTokenDetailGetParams = {
    /** token */
    token: string;
  };

  type shopSettingsGetIPgoInstallScriptGetParams = {
    /** token */
    token: string;
  };

  type shopSettingsRemoveFetchTokenDeleteParams = {
    /** token */
    token: string;
  };

  type shopSettingsReportShopBrowserInfoCookiesPostParams = {
    /** token */
    token: string;
  };

  type shopSettingsReportShopBrowserInfoFingerprintPostParams = {
    /** token */
    token: string;
  };

  type shopSettingsReportShopBrowserInfoFinishGetParams = {
    /** token */
    token: string;
  };

  type shopSettingsReportShopBrowserInfoPasswordsPostParams = {
    /** token */
    token: string;
  };

  type shopShareAuditByAuditIdPassPutParams = {
    /** auditId */
    auditId: number;
  };

  type shopShareAuditByAuditIdRejectPutParams = {
    /** auditId */
    auditId: number;
    /** reason */
    reason?: string;
  };

  type shopShareByShareIdIpVisiblePutParams = {
    /** shareId */
    shareId: number;
    /** visible */
    visible: boolean;
  };

  type shopShareByShopIdByShareIdDeleteParams = {
    /** shopId */
    shopId: number;
    /** shareId */
    shareId: number;
  };

  type shopShareByShopIdGetParams = {
    /** shopId */
    shopId: number;
  };

  type shopSharedByShopIdDeleteParams = {
    /** shopId */
    shopId: number;
  };

  type ShopShareVo = {
    id?: number;
    ipVisible?: boolean;
    shareTime?: string;
    sharedTeamId?: number;
    sharedTeamName?: string;
    sharedUserId?: number;
    sharedUserNickname?: string;
    sharedUserPhone?: string;
    sharingTeamId?: number;
    sharingUserId?: number;
    sourceShopId?: number;
    targetShopId?: number;
  };

  type shopShortcutByIdDeleteParams = {
    /** id */
    id: number;
  };

  type shopShortcutByTokenGetParams = {
    /** token */
    token: string;
  };

  type ShopShortcutDetailVo = {
    /** 创建来源 */
    createSource?: 'Desktop' | 'Share';
    /** 创建时间 */
    createTime?: string;
    creator?: UserDto;
    /** 创建者ID */
    creatorId?: number;
    id?: number;
    /** 名称 */
    name?: string;
    /** 备注 */
    remark?: string;
    /** 是否允许执行RPA */
    rpaEnabled?: boolean;
    /** 账号ID（分身ID） */
    shopId?: number;
    shopName?: string;
    team?: TeamDto;
    /** 团队ID */
    teamId?: number;
    /** 唯一标识token */
    token?: string;
    user?: UserDto;
    /** 关联影子用户ID */
    userId?: number;
    /** 当前释放有效 */
    valid?: boolean;
    /** 有效天数，0表示永久有效 */
    validDays?: number;
  };

  type ShopShortcutDto = {
    /** 创建来源 */
    createSource?: 'Desktop' | 'Share';
    /** 创建时间 */
    createTime?: string;
    /** 创建者ID */
    creatorId?: number;
    id?: number;
    /** 名称 */
    name?: string;
    /** 备注 */
    remark?: string;
    /** 是否允许执行RPA */
    rpaEnabled?: boolean;
    /** 账号ID（分身ID） */
    shopId?: number;
    /** 团队ID */
    teamId?: number;
    /** 唯一标识token */
    token?: string;
    /** 关联影子用户ID */
    userId?: number;
    /** 当前释放有效 */
    valid?: boolean;
    /** 有效天数，0表示永久有效 */
    validDays?: number;
  };

  type shopShortcutLoginByTokenGetParams = {
    /** token */
    token: string;
  };

  type ShopSiteHistoryVo = {
    lastVisitTime?: number;
    title?: string;
    typedCount?: number;
    url?: string;
    visitCount?: number;
  };

  type shopSnapshotDeleteDeleteParams = {
    /** snapshotId */
    snapshotId: number;
  };

  type shopSnapshotListGetParams = {
    /** shopId */
    shopId: number;
  };

  type shopSnapshotPlanAddShopsToPlanPostParams = {
    /** id */
    id: number;
  };

  type shopSnapshotPlanDeleteDeleteParams = {
    /** id */
    id: number;
  };

  type shopSnapshotPlanDeleteShopFromPlanDeleteParams = {
    /** id */
    id: number;
    /** shopId */
    shopId?: number;
  };

  type shopSnapshotPlanListPlanShopsGetParams = {
    /** id */
    id: number;
    /** 排序字段,比如'id desc','name asc,create_time desc'等等 */
    orderBy?: string;
    /** 当前页 */
    pageNum?: number;
    /** 每页的数量 */
    pageSize?: number;
  };

  type ShopSnapshotPlanVo = {
    /** 快照有哪些数据，逗号隔开，允许值 : Cookies,LocalStorage,IndexedDB,ExtensionData,Histories,Fingerprint */
    contents?: string;
    createTime?: string;
    creatorId?: number;
    /** 执行时间表达式的星期部分 */
    cronDay?: string;
    /** 执行时间表达式的时分秒部分 */
    cronTime?: string;
    description?: string;
    id?: number;
    /** 保留个数 */
    keepCount?: number;
    name?: string;
    teamId?: number;
  };

  type shopSnapshotRestorePostParams = {
    /** snapshotId */
    snapshotId: number;
  };

  type shopSnapshotUpdateLockStatusPostParams = {
    /** snapshotId */
    snapshotId: number;
    /** locked */
    locked: boolean;
  };

  type ShopSnapshotVo = {
    /** 快照有哪些数据，逗号隔开，允许值 : Cookies,LocalStorage,IndexedDB,ExtensionData,Histories,Fingerprint */
    contents?: string;
    createTime?: string;
    /** 谁创建的快照，为0表示系统自动创建 */
    creatorId?: number;
    description?: string;
    id?: number;
    /** 是否锁定 */
    locked?: boolean;
    name?: string;
    shopId?: number;
    /** 快照大小，单位Byte */
    size?: number;
    teamId?: number;
  };

  type ShopSyncPolicyVo = {
    cookies?: boolean;
    extensions?: boolean;
    history?: boolean;
    indexDb?: boolean;
    localStorage?: boolean;
    passwords?: boolean;
    shopBookmarks?: boolean;
    supportingCache?: boolean;
    teamBookmarks?: boolean;
    userBookmarks?: boolean;
  };

  type ShopTagVo = {
    /** 账户账号 */
    account?: string;
    /** 是否允许用户自行安装插件 */
    allowExtension?: boolean;
    /** 会话是否允许监视 */
    allowMonitor?: boolean;
    /** 是否允许跳过敏感操作 */
    allowSkip?: boolean;
    /** 是否自动代填 */
    autoFill?: boolean;
    bookmarkBar?: string;
    coordinateId?: string;
    /** 创建时间 */
    createTime?: string;
    /** 创建者 */
    creatorId?: number;
    deleteTime?: string;
    deleting?: boolean;
    /** 描述 */
    description?: string;
    domainPolicy?: 'Blacklist' | 'None' | 'Whitelist';
    /** 是否独占访问 */
    exclusive?: boolean;
    extension?: 'both' | 'extension' | 'huayang';
    extraProp?: string;
    /** 绑定的指纹Id */
    fingerprintId?: number;
    /** 绑定的指纹模板Id（只有无状态账号才允许绑定指纹模板） */
    fingerprintTemplateId?: number;
    frontUrl?: string;
    googleTranslateSpeed?: boolean;
    homePage?: string;
    homePageSites?: string;
    /** id */
    id?: number;
    imageForbiddenSize?: number;
    intranetEnabled?: boolean;
    ipSwitchCheckInterval?: number;
    ipSwitchStrategy?: 'Abort' | 'Alert' | 'Off';
    lastAccessTime?: string;
    lastAccessUser?: number;
    lastSyncTime?: string;
    loginStatus?: 'Offline' | 'Online' | 'Unknown';
    /** 任务栏分身标记文字 */
    markCode?: string;
    /** 任务栏分身标记背景颜色 */
    markCodeBg?: number;
    monitorPerception?: boolean;
    /** 账户名称 */
    name?: string;
    nameBookmarkEnabled?: boolean;
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    /** 经营品类 */
    operatingCategory?:
      | '医药保健'
      | '图书文具'
      | '宠物用品'
      | '家具建材'
      | '家电电器'
      | '工业用品'
      | '户外运动'
      | '手机数码'
      | '手表眼镜'
      | '护肤美妆'
      | '母婴玩具'
      | '汽车配件'
      | '生活家居'
      | '电商其他'
      | '电脑平板'
      | '艺术珠宝'
      | '花园聚会'
      | '计生情趣'
      | '软件程序'
      | '鞋服箱包'
      | '音乐影视'
      | '食品生鲜'
      | '鲜花绿植';
    parentShopId?: number;
    /** 密码 */
    password?: string;
    /** 平台信息 */
    platform?: ShopPlatformVo;
    /** 平台ID */
    platformId?: number;
    privateAddress?: string;
    privateTitle?: string;
    recordPerception?: boolean;
    recordPolicy?: 'Chosen' | 'Disabled' | 'Forced';
    requireIp?: boolean;
    resourcePolicy?: number;
    securityPolicyEnabled?: boolean;
    securityPolicyUpdateTime?: string;
    sharePolicyId?: string;
    shopDataSize?: number;
    shopNo?: string;
    stateless?: boolean;
    statelessChangeFp?: boolean;
    statelessSyncPolicy?: number;
    syncPolicy?: number;
    /** 标签 */
    tags?: TagDto[];
    /** 团队ID */
    teamId?: number;
    trafficAlertStrategy?: 'Abort' | 'Off';
    trafficAlertThreshold?: number;
    trafficSaving?: boolean;
    type?: 'Global' | 'Local' | 'None';
    webSecurity?: boolean;
  };

  type ShopTokenVo = {
    /** 每个通道的Token */
    channelTokens?: ShopChannelTokenVo[];
    /** 路由规则 */
    routers?: ShopRouterVo[];
    /** 会话ID */
    sessionId?: number;
    /** 本地打开会话监听的地址 */
    sessionProxyHost?: string;
    /** 所属团队 */
    teamId?: number;
  };

  type ShopTrafficSavingParamVo = {
    shopIdList?: number[];
    trafficSaving?: boolean;
  };

  type ShopViewCategoryVo = {
    category?:
      | 'Shop'
      | 'SocialMedia'
      | 'all'
      | 'collected'
      | 'custom'
      | 'recycleBin'
      | 'unbound'
      | 'unfingerprinted'
      | 'ungranted'
      | 'windowSync';
    desc?: string;
  };

  type ShopViewFilterVo = {
    /** 区域 */
    areas?: (
      | 'Argentina'
      | 'Australia'
      | 'Austria'
      | 'Belarus'
      | 'Belgium'
      | 'Bolivia'
      | 'Brazil'
      | 'Canada'
      | 'Chile'
      | 'China'
      | 'Colombia'
      | 'Costa_Rica'
      | 'Dominican'
      | 'Ecuador'
      | 'Egypt'
      | 'France'
      | 'Germany'
      | 'Global'
      | 'Guatemala'
      | 'Honduras'
      | 'HongKong'
      | 'India'
      | 'Indonesia'
      | 'Ireland'
      | 'Israel'
      | 'Italy'
      | 'Japan'
      | 'Kazakhstan'
      | 'Korea'
      | 'Malaysia'
      | 'Mexico'
      | 'Netherlands'
      | 'Nicaragua'
      | 'Panama'
      | 'Paraguay'
      | 'Peru'
      | 'Philippines'
      | 'Poland'
      | 'Portuguese'
      | 'Puerto_Rico'
      | 'Russia'
      | 'Salvador'
      | 'Saudi_Arabia'
      | 'Singapore'
      | 'Spain'
      | 'Sweden'
      | 'Switzerland'
      | 'Taiwan'
      | 'Thailand'
      | 'Turkey'
      | 'United_Arab_Emirates'
      | 'United_Kingdom'
      | 'United_States'
      | 'Uruguay'
      | 'Venezuela'
      | 'Vietnam'
    )[];
    /** 绑定IP类型 */
    bindType: 'Specified' | 'Unbound' | 'Unlimited';
    /** 平台类型 */
    category?: string;
    /** 只看我收藏的 */
    collected?: boolean;
    /** 视图可见范围:指定部门 */
    departmentIds?: number[];
    /** 是否动态 */
    dynamicView?: boolean;
    /** 允许可见者编辑 */
    editable?: boolean;
    /** 授权成员类型 */
    grantedType: 'SpecifiedMembers' | 'Ungranted' | 'Unlimited';
    /** 授权成员ID */
    grantedUserIds?: number[];
    /** 导入日期开始，格式:yyyy-MM-DD */
    importDateFrom?: string;
    /** 导入日期截止，格式:yyyy-MM-DD */
    importDateTo?: string;
    /** 资源名称 */
    names?: string[];
    /** 所属平台类型 */
    platformTypes?: string[];
    regexNames?: string[];
    /** 静态视图的资源ID */
    resourceIds?: number[];
    /** 视图可见范围：指定成员 */
    sharedUserIds?: number[];
    shopType?: 'Global' | 'Local' | 'None';
    /** 关联标签 */
    tagIds?: number[];
    /** 标签的逻辑关系 */
    tagLc?: 'AND' | 'NOT' | 'OR';
  };

  type ShopWithPlatformVo = {
    id?: number;
    name?: string;
    operatingCategory?:
      | '医药保健'
      | '图书文具'
      | '宠物用品'
      | '家具建材'
      | '家电电器'
      | '工业用品'
      | '户外运动'
      | '手机数码'
      | '手表眼镜'
      | '护肤美妆'
      | '母婴玩具'
      | '汽车配件'
      | '生活家居'
      | '电商其他'
      | '电脑平板'
      | '艺术珠宝'
      | '花园聚会'
      | '计生情趣'
      | '软件程序'
      | '鞋服箱包'
      | '音乐影视'
      | '食品生鲜'
      | '鲜花绿植';
    parentShopId?: number;
    platformArea?:
      | 'Argentina'
      | 'Australia'
      | 'Austria'
      | 'Belarus'
      | 'Belgium'
      | 'Bolivia'
      | 'Brazil'
      | 'Canada'
      | 'Chile'
      | 'China'
      | 'Colombia'
      | 'Costa_Rica'
      | 'Dominican'
      | 'Ecuador'
      | 'Egypt'
      | 'France'
      | 'Germany'
      | 'Global'
      | 'Guatemala'
      | 'Honduras'
      | 'HongKong'
      | 'India'
      | 'Indonesia'
      | 'Ireland'
      | 'Israel'
      | 'Italy'
      | 'Japan'
      | 'Kazakhstan'
      | 'Korea'
      | 'Malaysia'
      | 'Mexico'
      | 'Netherlands'
      | 'Nicaragua'
      | 'Panama'
      | 'Paraguay'
      | 'Peru'
      | 'Philippines'
      | 'Poland'
      | 'Portuguese'
      | 'Puerto_Rico'
      | 'Russia'
      | 'Salvador'
      | 'Saudi_Arabia'
      | 'Singapore'
      | 'Spain'
      | 'Sweden'
      | 'Switzerland'
      | 'Taiwan'
      | 'Thailand'
      | 'Turkey'
      | 'United_Arab_Emirates'
      | 'United_Kingdom'
      | 'United_States'
      | 'Uruguay'
      | 'Venezuela'
      | 'Vietnam';
    platformId?: number;
    platformName?: string;
    recordPolicy?: 'Chosen' | 'Disabled' | 'Forced';
    securityPolicyEnabled?: boolean;
    teamId?: number;
    type?: 'Global' | 'Local' | 'None';
  };

  type shoujixiaoxijianchapeizhi = {
    disabled?: boolean;
    /** 消息检查工作时间段，格式如 03:22-15:34 */
    duration?: string;
    /** 消息同步频率，单位分钟，为空表示不同步；最小10分钟，最大1000分钟 */
    interval?: number;
    /** 以逗号分隔开的用户id列表，表示如果有新消息要通知哪些用户 */
    receiveIds?: string;
    /** 调度quartz id */
    scheduleId?: string;
  };

  type shoujiyaoyuepeizhi = {
    /** 单账号每批发卡片间隔，单位分钟 */
    taskCardInterval?: number;
    /** 单账号每批私信间隔，单位分钟 */
    taskPmInterval?: number;
  };

  type SimpleFavoriteSite = {
    name?: string;
    url?: string;
  };

  type SimpleShopVo = {
    id?: number;
    name?: string;
  };

  type StatelessShopPolicyVo = {
    statelessChangeFp?: boolean;
    syncPolicy?: ShopSyncPolicyVo;
  };

  type StorageQuotaVo = {
    /** 免费配额，Byte */
    freeQuota?: number;
    /** 剩余花瓣 */
    remainCredit?: number;
    /** 存储空间单价，花瓣/GB */
    storagePrice?: number;
    /** 已经使用的存储空间，Byte */
    usedSize?: number;
  };

  type StsPostSignature = {
    accessKeyId?: string;
    accessKeySecret?: string;
    bucketName?: string;
    expiration?: string;
    fileVal?: string;
    policy?: string;
    provider?: string;
    region?: string;
    securityToken?: string;
    serverTime?: string;
    url?: string;
  };

  type SyncShopBookmarksRequest = {
    ids?: number[];
    json?: string;
  };

  type SysFingerWhitelistConfig = {
    whitelist?: Record<string, any>;
  };

  type TagBatchRequest = {
    resourceIds?: number[];
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
  };

  type tagByTagIdDeleteParams = {
    /** 标签ID */
    tagId: number;
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
  };

  type tagByTagIdPutParams = {
    /** tagId */
    tagId: number;
    /** 标签内容 */
    tag: string;
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    /** 是否系统标签（不设置时不更新） */
    system?: boolean;
  };

  type tagByTagIdResourceDeleteParams = {
    /** tagId */
    tagId: number;
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    /** resourceId */
    resourceId: number;
  };

  type tagDeleteParams = {
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    /** 标签内容 */
    tag: string;
  };

  type TagDto = {
    bizCode?: string;
    color?: number;
    createTime?: string;
    expireTime?: string;
    id?: number;
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    system?: boolean;
    tag?: string;
    teamId?: number;
  };

  type tagPostParams = {
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    /** 标签内容 */
    tag: string;
    /** 立即绑定资源ID */
    resourceId?: number;
  };

  type TagResourceRequest = {
    removeRemain?: boolean;
    resourceIds?: number[];
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    tags?: string[];
  };

  type tagResourcesAllDeleteParams = {
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    /** resourceIds */
    resourceIds: string;
  };

  type tagResourcesByTagDeleteParams = {
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    /** resourceIds */
    resourceIds: string;
    /** tag */
    tag: string;
  };

  type tagResourcesByTagPostParams = {
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    /** resourceIds */
    resourceIds: string;
    /** tag */
    tag: string;
  };

  type tagResourcesDeleteParams = {
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    /** resourceIds */
    resourceIds: string;
    /** tagIds */
    tagIds: string;
  };

  type tagResourcesPostParams = {
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    /** resourceIds */
    resourceIds: string;
    /** tagIds */
    tagIds: string;
  };

  type tagsCountMapGetParams = {
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
  };

  type tagsDeleteParams = {
    /** 标签ID，用逗号连接 */
    tagIds: string;
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
  };

  type tagsGetParams = {
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    /** count */
    count?: boolean;
  };

  type TagVo = {
    bizCode?: string;
    color?: number;
    createTime?: string;
    expireTime?: string;
    id?: number;
    /** 管理资源数 */
    resourceCount?: number;
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    system?: boolean;
    tag?: string;
    teamId?: number;
  };

  type TargetIpVo = {
    domestic?: boolean;
    /** 是否动态IP */
    dynamic?: boolean;
    forbiddenLongLatitude?: boolean;
    hostDomestic?: boolean;
    ipId?: number;
    ipName?: string;
    ippIpId?: number;
    ippName?: string;
    ipv6?: boolean;
    location?: IpLocationDto;
    provider?: string;
    providerName?: string;
    remoteIp?: string;
  };

  type TaskDto = {
    createTime?: string;
    creatorId?: number;
    detail?: string;
    finishTime?: string;
    id?: number;
    name?: string;
    progress?: number;
    remarks?: string;
    status?: 'Abort' | 'Fail' | 'Pending' | 'Running' | 'Success' | 'Timeout';
    targetId?: number;
    taskType?:
      | 'BatchUpdateBandwidth'
      | 'CleanColdTable'
      | 'CleanMongo'
      | 'CleanTable'
      | 'CopyTkCreatorToClient'
      | 'CrsTransferOrder'
      | 'DbTransfer'
      | 'DeleteIps'
      | 'FireOpsMessage'
      | 'ImportAccounts'
      | 'ImportIp'
      | 'ImportIppIps'
      | 'ImportIps'
      | 'ImportShop'
      | 'OpenaiChatGenerate'
      | 'OpenaiChatTranslate'
      | 'ProbeBatchLaunchInstance'
      | 'ProbeIps'
      | 'RebootIp'
      | 'RefreshClash'
      | 'RefreshExtensions'
      | 'RepairGhLiveTime'
      | 'RepairKolLiveRate'
      | 'RepairKolLiveTime'
      | 'RepairOps'
      | 'RepairTkCreatorFollower'
      | 'ResetJdEip'
      | 'ReviseIpHostLocation'
      | 'ShardTableOps'
      | 'ShardTeamTableSql'
      | 'SshChangePort'
      | 'SshCommands'
      | 'SshCommandsBatchLaunchInstance'
      | 'SyncKolCreator'
      | 'SyncKolRegionMap'
      | 'TkSendEmail'
      | 'TransferShardTable'
      | 'TransferTable'
      | 'TransferTagResource'
      | 'TransferTkCreator'
      | 'UpgradeGhMessage'
      | 'UploadAiKnowledge'
      | 'UploadDiskFile'
      | 'UserRefreshIp';
    teamId?: number;
  };

  type TeamDto = {
    avatar?: string;
    createTime?: string;
    creatorId?: number;
    deleteTime?: string;
    domesticCloudEnabled?: boolean;
    id?: number;
    invalidTime?: string;
    inviteCode?: number;
    name?: string;
    overseaCloudEnabled?: boolean;
    paid?: boolean;
    partnerId?: number;
    payTime?: string;
    repurchaseTime?: string;
    repurchased?: boolean;
    status?: 'Blocked' | 'Deleted' | 'Pending' | 'Ready';
    teamType?: 'crs' | 'gh' | 'krShop' | 'normal' | 'partner' | 'plugin' | 'tk' | 'tkshop';
    tenantId?: number;
    testing?: boolean;
    validateTime?: string;
    validated?: boolean;
    verified?: boolean;
  };

  type TeamExtensionStatVo = {
    all?: boolean;
    extension?: ExtensionsVo;
    extensionId?: number;
    shopCount?: number;
    sync?: boolean;
  };

  type TeamFingerprintDefaultConfig = {
    enableAudio?: boolean;
    enableCanvas?: boolean;
    enableLocation?: boolean;
    enableRect?: boolean;
    enableWebgl?: boolean;
    enableWebrtc?: boolean;
    lang?: string;
    majorVersion?: number;
    userAgentStrategy?: 'CurrentDeviceLatest' | 'Latest' | 'Specified';
  };

  type TeamIpStatVo = {
    countryStatList?: IpCountryStatVo[];
    dynamicStatList?: IpDynamicStatVo[];
    idleCount?: number;
    totalCount?: number;
    unavailableCount?: number;
  };

  type TeamIpStatVoV2 = {
    countryStatList?: IpCountryStatVo[];
    idleCount?: number;
    importTypeStatList?: IpImportTypeStatVo[];
    totalCount?: number;
    unavailableCount?: number;
  };

  type TeamIpTunnelVo = {
    autoRenew?: boolean;
    cloudProvider?: string;
    cloudRegion?: string;
    createTime?: string;
    creatorId?: number;
    description?: string;
    directDownTraffic?: number;
    directUpTraffic?: number;
    domestic?: boolean;
    downTraffic?: number;
    dynamic?: boolean;
    eipId?: number;
    enableWhitelist?: boolean;
    forbiddenLongLatitude?: boolean;
    gatewayId?: number;
    goodsId?: number;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    importType?: 'Platform' | 'User';
    invalidTime?: string;
    ip?: string;
    lastProbeTime?: string;
    latitude?: number;
    locale?: string;
    locationId?: number;
    longitude?: number;
    name?: string;
    networkType?: 'cloudIdc' | 'mobile' | 'proxyIdc' | 'residential' | 'unknown' | 'unknownIdc';
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    originalTeam?: number;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    pipeType?: 'None' | 'Proxy' | 'Tunnel' | 'TunnelFailToProxy';
    preferTransit?: number;
    probeError?: string;
    realIp?: string;
    refreshUrl?: string;
    remoteLogin?: boolean;
    renewPrice?: number;
    source?: string;
    speedLimit?: number;
    status?: 'Available' | 'Pending' | 'Unavailable';
    sticky?: boolean;
    teamId?: number;
    testingTime?: number;
    timezone?: string;
    traffic?: number;
    trafficCurrency?: 'CREDIT' | 'RMB' | 'USD';
    trafficPrice?: number;
    trafficUnlimited?: boolean;
    transitType?: 'Auto' | 'Direct' | 'Transit';
    tunnelGroups?: IpTunnelGroupVo[];
    tunnelTypes?: string;
    upTraffic?: number;
    valid?: boolean;
    validEndDate?: string;
    vpsId?: number;
  };

  type TeamIpVo = {
    autoRenew?: boolean;
    cloudProvider?: string;
    cloudRegion?: string;
    createTime?: string;
    creatorId?: number;
    description?: string;
    directDownTraffic?: number;
    directUpTraffic?: number;
    domestic?: boolean;
    downTraffic?: number;
    dynamic?: boolean;
    eipId?: number;
    enableWhitelist?: boolean;
    /** 过期状态 */
    expireStatus?: 'Expired' | 'Expiring' | 'Normal';
    forbiddenLongLatitude?: boolean;
    gatewayId?: number;
    goodsId?: number;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    importType?: 'Platform' | 'User';
    invalidTime?: string;
    ip?: string;
    lastProbeTime?: string;
    latitude?: number;
    locale?: string;
    locationId?: number;
    longitude?: number;
    name?: string;
    networkType?: 'cloudIdc' | 'mobile' | 'proxyIdc' | 'residential' | 'unknown' | 'unknownIdc';
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    originalTeam?: number;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    pipeType?: 'None' | 'Proxy' | 'Tunnel' | 'TunnelFailToProxy';
    preferTransit?: number;
    probeError?: string;
    /** 供应商名称 */
    providerName?: string;
    realIp?: string;
    refreshUrl?: string;
    remoteLogin?: boolean;
    renewPrice?: number;
    source?: string;
    speedLimit?: number;
    status?: 'Available' | 'Pending' | 'Unavailable';
    sticky?: boolean;
    teamId?: number;
    testingTime?: number;
    timezone?: string;
    traffic?: number;
    trafficCurrency?: 'CREDIT' | 'RMB' | 'USD';
    trafficPrice?: number;
    trafficUnlimited?: boolean;
    transitType?: 'Auto' | 'Direct' | 'Transit';
    tunnelTypes?: string;
    upTraffic?: number;
    valid?: boolean;
    validEndDate?: string;
    vpsId?: number;
  };

  type TeamMobileDeviceVo = {
    /** 系统版本，由于历史原因取名叫androidVersion  */
    androidVersion?: string;
    /** adb devices -l 获取到的设备标识 */
    code?: string;
    connectType?: 'ARMCLOUD' | 'Baidu' | 'QCloud' | 'USB' | 'WIFI';
    /** 导入时间 */
    createTime?: string;
    /** 创建者id */
    creatorId?: number;
    description?: string;
    device?: LoginDeviceDto;
    /** 所在设备的id，关联login_device.device_id */
    deviceId?: string;
    /** 所在设备的当前是否在线 */
    deviceOnline?: boolean;
    id?: number;
    /** adb shell getprop ro.product.model */
    mode?: string;
    /** 手机设备名字，例如 MI 14 */
    name?: string;
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    /** 从哪台手机联营而来 */
    parentMobileId?: number;
    /** android | ios */
    platform?: 'Android' | 'IOS';
    /** 是否暂时禁用rpa执行 */
    rpaSilent?: boolean;
    screenHeight?: number;
    screenWidth?: number;
    status?: 'EXPIRED' | 'OFFLINE' | 'ONLINE' | 'RESETING';
    /** 所属团队 */
    teamId?: number;
  };

  type TeamMobileDto = {
    /** 系统版本，由于历史原因取名叫androidVersion  */
    androidVersion?: string;
    /** adb devices -l 获取到的设备标识 */
    code?: string;
    connectType?: 'ARMCLOUD' | 'Baidu' | 'QCloud' | 'USB' | 'WIFI';
    /** 导入时间 */
    createTime?: string;
    /** 创建者id */
    creatorId?: number;
    description?: string;
    /** 所在设备的id，关联login_device.device_id */
    deviceId?: string;
    /** 所在设备的当前是否在线 */
    deviceOnline?: boolean;
    id?: number;
    /** adb shell getprop ro.product.model */
    mode?: string;
    /** 手机设备名字，例如 MI 14 */
    name?: string;
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    /** 从哪台手机联营而来 */
    parentMobileId?: number;
    /** android | ios */
    platform?: 'Android' | 'IOS';
    /** 是否暂时禁用rpa执行 */
    rpaSilent?: boolean;
    screenHeight?: number;
    screenWidth?: number;
    status?: 'EXPIRED' | 'OFFLINE' | 'ONLINE' | 'RESETING';
    /** 所属团队 */
    teamId?: number;
  };

  type TeamMobileSimpleVo = {
    code?: string;
    description?: string;
    deviceId?: string;
    id?: number;
    name?: string;
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    parentMobileId?: number;
    platform?: 'Android' | 'IOS';
    rpaSilent?: boolean;
    /** 当前正在执行的流程的task id */
    rpaTaskId?: number;
    /** 当前正在执行的流程的taskItem id */
    rpaTaskItemId?: number;
    /** 当前正在执行的流程的task name */
    rpaTaskName?: string;
    status?: 'EXPIRED' | 'OFFLINE' | 'ONLINE' | 'RESETING';
    teamId?: number;
  };

  type TeamMobileVo = {
    /** 当前手机是否允许使用快照功能 */
    allowBackup?: boolean;
    /** 系统版本，由于历史原因取名叫androidVersion  */
    androidVersion?: string;
    /** 当手机为云手机时才有值 */
    cloudInfo?: CloudMobileInfo;
    /** adb devices -l 获取到的设备标识 */
    code?: string;
    connectType?: 'ARMCLOUD' | 'Baidu' | 'QCloud' | 'USB' | 'WIFI';
    /** 导入时间 */
    createTime?: string;
    /** 创建者id */
    creatorId?: number;
    description?: string;
    /** 所在设备的id，关联login_device.device_id */
    deviceId?: string;
    /** 所在设备的当前是否在线 */
    deviceOnline?: boolean;
    /** 授权的部门 */
    grantDepartmentList?: DepartmentDto[];
    /** 授权给的用户 */
    grantUserVoList?: MemberVo[];
    /** 手机账号可用性检查配置 */
    healthCheckConfig?: HealthCheckConfig;
    id?: number;
    /** 手机邀约配置，如设置每手机每批次间隔 */
    intervalConfig?: shoujiyaoyuepeizhi;
    /** 手机新消息检查配置 */
    messageCheckConfig?: shoujixiaoxijianchapeizhi;
    /** 存手机当前的geo, sim等信息 */
    metas?: Record<string, any>;
    /** adb shell getprop ro.product.model */
    mode?: string;
    /** 手机设备名字，例如 MI 14 */
    name?: string;
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    /** 从哪台手机联营而来 */
    parentMobileId?: number;
    /** android | ios */
    platform?: 'Android' | 'IOS';
    /** 是否暂时禁用rpa执行 */
    rpaSilent?: boolean;
    screenHeight?: number;
    screenWidth?: number;
    status?: 'EXPIRED' | 'OFFLINE' | 'ONLINE' | 'RESETING';
    /** 标签，只有查询的时候声明 fetchTags 才会有 */
    tags?: TagDto[];
    /** 所属团队 */
    teamId?: number;
    workTimeConfig?: WorkTimeConfig;
  };

  type teamSettingsShopPoliciesDeleteBlockElementsDeleteParams = {
    /** blockElementIds */
    blockElementIds: number;
  };

  type TeamShopFieldConfig = {
    /** 全局字段Key列表 */
    fields?: string[];
    /** 启用全局字段 */
    globalField?: boolean;
  };

  type TeamViewConfig = {
    /** 全局视图同步 */
    globalSync?: boolean;
    /** 隐藏的视图列表 */
    hiddenViews?: string[];
    /** 现有视图列表 */
    views?: string[];
  };

  type TempFilePutOssUrl = {
    /** 上传完成之后用这个值通知后台往腾讯云手机传 */
    path?: string;
    url?: string;
  };

  type trafficByChannelSessionByChannelSessionIdPostParams = {
    /** channelSessionId */
    channelSessionId: number;
    /** upTraffic */
    upTraffic?: number;
    /** downTraffic */
    downTraffic?: number;
  };

  type trafficIpTrafficRemainSummaryGetParams = {
    /** ipId */
    ipId: number;
  };

  type TrafficPackCreateOrderRequest = {
    /** 已经勾选阅读和同意使用协议，没什么用 */
    agreement?: boolean;
    /** 余额抵扣金额 */
    balanceAmount?: number;
    /** 购买多少个流量包 */
    count?: number;
    /** 流量包商品ID */
    goodsId?: number;
    /** 是否立即支付（点稍候支付该属性传false） */
    immediatePay?: boolean;
    payType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    /** 代金券抵扣金额，不得大于代金券余额 */
    voucherAmount?: number;
    /** 要使用的代金券id */
    voucherId?: number;
  };

  type trailsByIpIdFindIpCreateTrailGetParams = {
    /** ipId */
    ipId: number;
  };

  type trailsByShopIdFindShopCreateTrailGetParams = {
    /** shopId */
    shopId: number;
  };

  type trailsFindIpTrailsGetParams = {
    /** pageNum */
    pageNum?: number;
    /** 以某一条记录作为标志查更早的记录，不为空时会忽略pageNum属性 */
    earlierThanMark?: string;
    /** pageSize */
    pageSize?: number;
    /** from */
    from?: string;
    /** to */
    to?: string;
    /** 账户Id */
    ipId: number;
    /** 操作者 */
    operator?: number;
    /** 操作类型 */
    action?:
      | 'NONE'
      | 'T_TEST'
      | 'i_add_change'
      | 'i_bind_shop'
      | 'i_buy'
      | 'i_create'
      | 'i_delete'
      | 'i_renew'
      | 'i_unbind_shop'
      | 'i_update_meta'
      | 'l_session_traffic'
      | 's_access'
      | 's_bind_ip'
      | 's_change_ip'
      | 's_clean'
      | 's_create'
      | 's_delete'
      | 's_recovery'
      | 's_transfer'
      | 's_unbind_ip'
      | 's_update_app'
      | 's_update_basic'
      | 's_update_policy'
      | 's_user_auth'
      | 't_add_dep'
      | 't_change_boss'
      | 't_create'
      | 't_del_dep'
      | 't_exit'
      | 't_fun_user_auth'
      | 't_join'
      | 't_shop_user_auth'
      | 't_update_clouds'
      | 't_update_dep'
      | 't_update_dep_user'
      | 't_update_dom_blocks'
      | 't_update_invite'
      | 't_update_meta'
      | 't_user_transfer_shop'
      | 'u_login';
  };

  type trailsFindMyIpTrailsGetParams = {
    /** pageNum */
    pageNum?: number;
    /** 以某一条记录作为标志查更早的记录，不为空时会忽略pageNum属性 */
    earlierThanMark?: string;
    /** pageSize */
    pageSize?: number;
    /** from */
    from?: string;
    /** to */
    to?: string;
    /** 账户Id */
    ipId: number;
    /** 操作类型 */
    action?:
      | 'NONE'
      | 'T_TEST'
      | 'i_add_change'
      | 'i_bind_shop'
      | 'i_buy'
      | 'i_create'
      | 'i_delete'
      | 'i_renew'
      | 'i_unbind_shop'
      | 'i_update_meta'
      | 'l_session_traffic'
      | 's_access'
      | 's_bind_ip'
      | 's_change_ip'
      | 's_clean'
      | 's_create'
      | 's_delete'
      | 's_recovery'
      | 's_transfer'
      | 's_unbind_ip'
      | 's_update_app'
      | 's_update_basic'
      | 's_update_policy'
      | 's_user_auth'
      | 't_add_dep'
      | 't_change_boss'
      | 't_create'
      | 't_del_dep'
      | 't_exit'
      | 't_fun_user_auth'
      | 't_join'
      | 't_shop_user_auth'
      | 't_update_clouds'
      | 't_update_dep'
      | 't_update_dep_user'
      | 't_update_dom_blocks'
      | 't_update_invite'
      | 't_update_meta'
      | 't_user_transfer_shop'
      | 'u_login';
  };

  type trailsFindMyShopTrailsGetParams = {
    /** pageNum */
    pageNum?: number;
    /** 以某一条记录作为标志查更早的记录，不为空时会忽略pageNum属性 */
    earlierThanMark?: string;
    /** pageSize */
    pageSize?: number;
    /** from */
    from?: string;
    /** to */
    to?: string;
    /** 账户Id */
    shopId: number;
    /** 操作类型 */
    action?:
      | 'NONE'
      | 'T_TEST'
      | 'i_add_change'
      | 'i_bind_shop'
      | 'i_buy'
      | 'i_create'
      | 'i_delete'
      | 'i_renew'
      | 'i_unbind_shop'
      | 'i_update_meta'
      | 'l_session_traffic'
      | 's_access'
      | 's_bind_ip'
      | 's_change_ip'
      | 's_clean'
      | 's_create'
      | 's_delete'
      | 's_recovery'
      | 's_transfer'
      | 's_unbind_ip'
      | 's_update_app'
      | 's_update_basic'
      | 's_update_policy'
      | 's_user_auth'
      | 't_add_dep'
      | 't_change_boss'
      | 't_create'
      | 't_del_dep'
      | 't_exit'
      | 't_fun_user_auth'
      | 't_join'
      | 't_shop_user_auth'
      | 't_update_clouds'
      | 't_update_dep'
      | 't_update_dep_user'
      | 't_update_dom_blocks'
      | 't_update_invite'
      | 't_update_meta'
      | 't_user_transfer_shop'
      | 'u_login';
  };

  type trailsFindShopTrailsGetParams = {
    /** pageNum */
    pageNum?: number;
    /** 以某一条记录作为标志查更早的记录，不为空时会忽略pageNum属性 */
    earlierThanMark?: string;
    /** pageSize */
    pageSize?: number;
    /** from */
    from?: string;
    /** to */
    to?: string;
    /** 账户Id */
    shopId: number;
    /** 操作者 */
    operator?: number;
    /** 操作类型 */
    action?:
      | 'NONE'
      | 'T_TEST'
      | 'i_add_change'
      | 'i_bind_shop'
      | 'i_buy'
      | 'i_create'
      | 'i_delete'
      | 'i_renew'
      | 'i_unbind_shop'
      | 'i_update_meta'
      | 'l_session_traffic'
      | 's_access'
      | 's_bind_ip'
      | 's_change_ip'
      | 's_clean'
      | 's_create'
      | 's_delete'
      | 's_recovery'
      | 's_transfer'
      | 's_unbind_ip'
      | 's_update_app'
      | 's_update_basic'
      | 's_update_policy'
      | 's_user_auth'
      | 't_add_dep'
      | 't_change_boss'
      | 't_create'
      | 't_del_dep'
      | 't_exit'
      | 't_fun_user_auth'
      | 't_join'
      | 't_shop_user_auth'
      | 't_update_clouds'
      | 't_update_dep'
      | 't_update_dep_user'
      | 't_update_dom_blocks'
      | 't_update_invite'
      | 't_update_meta'
      | 't_user_transfer_shop'
      | 'u_login';
  };

  type TransferShopPrepareResult = {
    creditFail?: boolean;
    errors?: string[];
  };

  type transitAllGetParams = {
    /** enabled */
    enabled?: boolean;
    /** ready */
    ready?: boolean;
    /** free */
    free?: boolean;
    /** domestic */
    domestic?: boolean;
  };

  type TransitDto = {
    alive?: boolean;
    apiEnabled?: boolean;
    apiStatus?: 'DELETED' | 'READY' | 'STOPPED';
    bandwidth?: number;
    bandwidthPrevent?: boolean;
    bgpPro?: boolean;
    cloudId?: number;
    cloudProvider?: string;
    connections?: number;
    description?: string;
    domestic?: boolean;
    enabled?: boolean;
    endpoints?: string;
    id?: number;
    incomingDomestic?: boolean;
    incomingIp?: string;
    incomingIpv6?: string;
    incomingLocation?: string;
    instanceId?: string;
    ipEndpoint?: string;
    ipEndpointEnabled?: boolean;
    ipv4Endpoint?: string;
    /** 启用IPv6。 */
    ipv6Enabled?: boolean;
    ipv6Endpoint?: string;
    /** ipv6Enabled=true时，必须设置ipv6RemoteIp */
    ipv6RemoteIp?: string;
    jump?: boolean;
    jumpDelay?: number;
    jumpTo?: number;
    load?: number;
    location?: string;
    name?: string;
    onlyIpv6?: boolean;
    rateUpdateTime?: string;
    region?: string;
    remoteIp?: string;
    rxRate?: number;
    scope?: 'Global' | 'Team';
    sessions?: number;
    status?: 'DELETED' | 'READY' | 'STOPPED';
    trafficPrice?: number;
    txRate?: number;
    version?: string;
  };

  type TransitGroupDto = {
    autoAllocate?: boolean;
    createTime?: string;
    description?: string;
    domestic?: boolean;
    free?: boolean;
    id?: number;
    importType?: 'Platform' | 'User';
    jump?: boolean;
    location?: string;
    name?: string;
  };

  type transitGroupListGetParams = {
    /** ipId */
    ipId?: number;
    /** importType */
    importType?: 'Platform' | 'User';
  };

  type transitGroupsGetParams = {
    /** ipId */
    ipId?: number;
  };

  type TransitGroupVo = {
    autoAllocate?: boolean;
    createTime?: string;
    defaultChosen?: boolean;
    description?: string;
    domestic?: boolean;
    free?: boolean;
    id?: number;
    importType?: 'Platform' | 'User';
    jump?: boolean;
    location?: string;
    name?: string;
    sortNo?: number;
    transits?: TransitVo[];
  };

  type TransitProbeJwtVo = {
    endpoint?: string;
    expireTime?: string;
    token?: string;
  };

  type TransitVo = {
    alive?: boolean;
    apiEnabled?: boolean;
    apiStatus?: 'DELETED' | 'READY' | 'STOPPED';
    bandwidth?: number;
    bandwidthPrevent?: boolean;
    bgpPro?: boolean;
    cloudId?: number;
    cloudProvider?: string;
    connections?: number;
    description?: string;
    domestic?: boolean;
    enabled?: boolean;
    endpoints?: string;
    id?: number;
    incomingDomestic?: boolean;
    incomingIp?: string;
    incomingIpv6?: string;
    incomingLocation?: string;
    instanceId?: string;
    ipEndpoint?: string;
    ipEndpointEnabled?: boolean;
    ipv4Endpoint?: string;
    /** 启用IPv6。 */
    ipv6Enabled?: boolean;
    ipv6Endpoint?: string;
    /** ipv6Enabled=true时，必须设置ipv6RemoteIp */
    ipv6RemoteIp?: string;
    jump?: boolean;
    jumpDelay?: number;
    jumpTo?: number;
    load?: number;
    location?: string;
    name?: string;
    onlyIpv6?: boolean;
    rateUpdateTime?: string;
    region?: string;
    remoteIp?: string;
    rxRate?: number;
    scope?: 'Global' | 'Team';
    sessions?: number;
    specialTrafficPrice?: number;
    status?: 'DELETED' | 'READY' | 'STOPPED';
    trafficPrice?: number;
    txRate?: number;
    version?: string;
  };

  type TransitWithLocationVo = {
    alive?: boolean;
    apiEnabled?: boolean;
    apiStatus?: 'DELETED' | 'READY' | 'STOPPED';
    bandwidth?: number;
    bandwidthPrevent?: boolean;
    bgpPro?: boolean;
    cloudId?: number;
    cloudProvider?: string;
    connections?: number;
    description?: string;
    domestic?: boolean;
    enabled?: boolean;
    endpoints?: string;
    id?: number;
    incomingDomestic?: boolean;
    incomingIp?: string;
    incomingIpv6?: string;
    incomingLocation?: string;
    instanceId?: string;
    ipEndpoint?: string;
    ipEndpointEnabled?: boolean;
    ipLocation?: IpLocationDto;
    ipv4Endpoint?: string;
    /** 启用IPv6。 */
    ipv6Enabled?: boolean;
    ipv6Endpoint?: string;
    /** ipv6Enabled=true时，必须设置ipv6RemoteIp */
    ipv6RemoteIp?: string;
    jump?: boolean;
    jumpDelay?: number;
    jumpTo?: number;
    load?: number;
    location?: string;
    name?: string;
    onlyIpv6?: boolean;
    rateUpdateTime?: string;
    region?: string;
    remoteIp?: string;
    rxRate?: number;
    scope?: 'Global' | 'Team';
    sessions?: number;
    specialTrafficPrice?: number;
    status?: 'DELETED' | 'READY' | 'STOPPED';
    trafficPrice?: number;
    txRate?: number;
    version?: string;
  };

  type TunnelEndpointVo = {
    /** 连接地址:transit的Endpoint或clash_proxy的url */
    endpoint?: string;
    /** 通道分组ID */
    groupId?: number;
    /** 通道分组名称 */
    groupName?: string;
    /** 节点ID，clash_profile.id or transit.id */
    nodeId?: number;
    /** 通道节点名称 */
    nodeName?: string;
    /** 管道类型：Tunnel（IPGO）或Proxy */
    pipeType?: 'None' | 'Proxy' | 'Tunnel' | 'TunnelFailToProxy';
    /** clash proxy id */
    proxyId?: number;
    /** 通道类型 */
    tunnelType?: 'clash' | 'direct' | 'jump' | 'localFrontend' | 'platform' | 'transit';
    /** 权重 */
    weight?: number;
    weightVo?: GroupWeightVo;
  };

  type TunnelGroupDetailVo = {
    autoAllocate?: boolean;
    createTime?: string;
    defaultWeight?: number;
    description?: string;
    domestic?: boolean;
    enabled?: boolean;
    id?: number;
    jdbox?: boolean;
    name?: string;
    nodes?: TunnelGroupNodeVo[];
    onlyFailover?: boolean;
    tunnelType?: 'clash' | 'direct' | 'jump' | 'localFrontend' | 'platform' | 'transit';
  };

  type TunnelGroupNodeVo = {
    location?: string;
    nodeId?: number;
    nodeName?: string;
    remoteIp?: string;
  };

  type TunnelGroupProbeResult = {
    /** 当前分组ID */
    groupId?: number;
    /** 测速使用的接入点ID或profile id */
    nodeId?: number;
    /** 探测输出信息，可用于排查 */
    probeOut?: string;
    /** 测速使用的代理节点ID，可选 */
    proxyId?: number;
    /** 测试线路是否可用 */
    status?: 'Available' | 'Pending' | 'Unavailable';
    /** 测速毫秒值 */
    testingTime?: number;
    /** 计算的权重 */
    weight?: number;
  };

  type tunnelProbeNodesGetParams = {
    /** teamId */
    teamId?: number;
  };

  type TunnelProbeNodeVo = {
    /** clash类型的分组使用的探测用代理 */
    clashProxy?: ClashProxyDto;
    /** tunnel_group.id */
    groupId?: number;
    /** transit.id or clash_profile.id */
    nodeId?: number;
    nodeName?: string;
    /** 接入点类型的分组使用的探测信息 */
    transitProbe?: TransitProbeJwtVo;
  };

  type UnbindShopAllRequest = {
    all?: boolean;
    ids?: number[];
  };

  type UnBindShopListParamVo = {
    /** 解绑主IP还是副IP，默认：true */
    primary?: boolean;
    shopIdList?: number[];
  };

  type UntagBySuffixRequest = {
    resourceIds?: number[];
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    tagSuffixList?: string[];
  };

  type UpdateBlockElementsRequest = {
    elements?: BlockElementItem[];
    shopIds?: number[];
  };

  type UpdateBookmarkRequest = {
    all?: boolean;
    bookmarkBar?: string;
    shopIds?: number[];
  };

  type UpdateChatListRequest = {
    /** 沟通账户 */
    chats?: ShopChatVo[];
  };

  type UpdateDirectDomainsRequest = {
    domains?: DirectDomainItem[];
    shopIds?: number[];
  };

  type UpdateDomainPolicyRequest = {
    domainPolicy?: 'Blacklist' | 'None' | 'Whitelist';
    items?: ShopDomainItem[];
    shopIds?: number[];
  };

  type UpdateFavoriteSiteBatchRequest = {
    all?: boolean;
    /** 分身ID列表 */
    ids?: number[];
    /** 网址列表 */
    sites?: FavoriteSiteItem[];
  };

  type UpdateFavoriteSiteRequest = {
    description?: string;
    /** 如果是编辑，该项会被忽略 */
    favoriteScope?: 'BM_Shop' | 'BM_User' | 'Shop' | 'Team' | 'User';
    /** 如果是编辑，必须不为空 */
    id?: number;
    name?: string;
    /** 根据favoriteScope，可能表示团队id，用户id，店铺id。如果是编辑，该项会被忽略 */
    ownerId?: number;
    /** 技术/Java 表示该项位于目录  技术/Java 下，为空表示根目录 */
    path?: string;
    /** 排序号，后台只记录，不维护 */
    sortNumber?: number;
    teamId?: number;
    url?: string;
  };

  type UpdateFrontUrlRequest = {
    all?: boolean;
    frontUrl?: string;
    shopIds?: number[];
  };

  type UpdateGeoLocationRequest = {
    /** 经纬度，格式为：<经度>,<纬度> 如：114.064524,22.549054 */
    location?: string;
    mobileIds?: number[];
  };

  type UpdateHomePageRequest = {
    all?: boolean;
    homePage?: string;
    homePageSites?: string;
    nameBookmarkEnabled?: boolean;
    shopIds?: number[];
  };

  type UpdateIpNameAndTagRequest = {
    ipIds?: number[];
    namePrefix?: string;
    nameSuffix?: string;
    tags?: string[];
  };

  type UpdateMobileAccountRequest = {
    accountType?: 'Business' | 'Regular';
    createTime?: string;
    creatorId?: number;
    description?: string;
    id?: number;
    mobileId?: number;
    platformId?: number;
    tags?: string[];
    teamId?: number;
    /** 账号名称 */
    username?: string;
  };

  type UpdateMobileDeviceRequest = {
    /** 只有当新方式是通过USB连接时才有意义。新的udid */
    code?: string;
    connectType?: 'ARMCLOUD' | 'Baidu' | 'QCloud' | 'USB' | 'WIFI';
    mobileId?: number;
    /** 只有当新方式是通过wifi连接时才有意义。新的连接地址 */
    newAddress?: string;
    newDeviceId?: string;
  };

  type UpdateMobileGroupRequest = {
    id?: number;
    name?: string;
    sortNumber?: number;
  };

  type UpdateMobileRequest = {
    /** adb shell getprop ro.product.vndk.version */
    androidVersion?: string;
    /** 仅当通过wifi连接的时候，允许修改code */
    code?: string;
    description?: string;
    id?: number;
    /** adb shell getprop ro.product.model */
    mode?: string;
    /** 手机设备名字，例如 MI 14 */
    name?: string;
    screenHeight?: number;
    screenWidth?: number;
  };

  type UpdatePrivateDomainRequest = {
    /** 域名 */
    domains?: string[];
    /** 私密地址栏 */
    privateAddress?: string;
    /** 私密标题 */
    privateTitle?: string;
    shopIds?: number[];
  };

  type UpdateResourcePolicyRequest = {
    all?: boolean;
    image?: boolean;
    imageForbiddenSize?: number;
    shopIds?: number[];
    video?: boolean;
  };

  type UpdateSecurityPolicyRequest = {
    all?: boolean;
    enabled?: boolean;
    shopIds?: number[];
  };

  type UpdateSharedShopIpVisibleRequest = {
    ids?: number[];
    visibleTeamIds?: number[];
  };

  type UpdateShopBasicInfoRequest = {
    /** 会话是否允许监视 */
    allowMonitor?: boolean;
    allowSkip?: boolean;
    monitorPerception?: boolean;
    recordPerception?: boolean;
    recordPolicy?: 'Chosen' | 'Disabled' | 'Forced';
    shopIds?: number[];
  };

  type UpdateShopExtensionRequest = {
    all?: boolean;
    /** 是否允许自行安装插件 */
    allowExtension?: boolean;
    extensionIds?: number[];
    shopIds?: number[];
  };

  type UpdateShopFlowsRequest = {
    all?: boolean;
    rpaFlowIds?: number[];
    shopIds?: number[];
  };

  type UpdateShopOtherPolicyRequest = {
    all?: boolean;
    exclusive?: boolean;
    googleTranslateSpeed?: boolean;
    ipSwitchCheckInterval?: number;
    ipSwitchStrategy?: 'Abort' | 'Alert' | 'Off';
    shopIds?: number[];
    trafficAlertStrategy?: 'Abort' | 'Off';
    trafficAlertThreshold?: number;
    webSecurity?: boolean;
  };

  type UpdateShopPlatformRequest = {
    all?: boolean;
    operatingCategory?:
      | '医药保健'
      | '图书文具'
      | '宠物用品'
      | '家具建材'
      | '家电电器'
      | '工业用品'
      | '户外运动'
      | '手机数码'
      | '手表眼镜'
      | '护肤美妆'
      | '母婴玩具'
      | '汽车配件'
      | '生活家居'
      | '电商其他'
      | '电脑平板'
      | '艺术珠宝'
      | '花园聚会'
      | '计生情趣'
      | '软件程序'
      | '鞋服箱包'
      | '音乐影视'
      | '食品生鲜'
      | '鲜花绿植';
    /** 平台ID */
    platformId?: number;
    shopIds?: number[];
    shopType?: 'Global' | 'Local' | 'None';
  };

  type UpdateShopSnapshotPlanRequest = {
    /** 快照有哪些数据，逗号隔开，允许值 : Cookies,LocalStorage,IndexedDB,ExtensionData,Histories,Fingerprint */
    contents?: string;
    /** 执行时间表达式的星期部分 */
    cronDay?: string;
    /** 执行时间表达式的时分秒部分 */
    cronTime?: string;
    description?: string;
    id?: number;
    /** 保留个数 */
    keepCount?: number;
    name?: string;
  };

  type UpdateShopSnapshotRequest = {
    description?: string;
    id?: number;
    name?: string;
  };

  type UpdateSimRequest = {
    mobileId?: number;
    msisdn?: string;
    operatorCode?: string;
    operatorName?: string;
    phoneNumber?: string;
    shortName?: string;
    simCode?: string;
    spn?: string;
  };

  type UpdateStatelessPolicyRequest = {
    all?: boolean;
    cookies?: boolean;
    extensions?: boolean;
    history?: boolean;
    indexDb?: boolean;
    localStorage?: boolean;
    passwords?: boolean;
    shopBookmarks?: boolean;
    shopIds?: number[];
    statelessChangeFp?: boolean;
    supportingCache?: boolean;
    teamBookmarks?: boolean;
    userBookmarks?: boolean;
  };

  type UpdateSyncPolicyRequest = {
    all?: boolean;
    cookies?: boolean;
    extensions?: boolean;
    history?: boolean;
    indexDb?: boolean;
    localStorage?: boolean;
    passwords?: boolean;
    shopBookmarks?: boolean;
    shopIds?: number[];
    supportingCache?: boolean;
    teamBookmarks?: boolean;
    userBookmarks?: boolean;
  };

  type UpdateTeamDirectDomainsRequest = {
    domains?: DirectDomainItem[];
  };

  type UpdateTimeZoneRequest = {
    mobileIds?: number[];
    /** 时区，格式如: Asia/Shanghai */
    timezone?: string;
  };

  type UpdateTunnelProbeResultRequest = {
    results?: TunnelGroupProbeResult[];
  };

  type UpdateTunnelsRequest = {
    all?: boolean;
    /** 是否开启白名单 */
    enableWhitelist?: boolean;
    /** 允许的链接方式 */
    groups?: ChosenTransitGroupVo[];
    ipIds?: number[];
    /** 切换策略 */
    transitType?: 'Auto' | 'Direct' | 'Transit';
    tunnelTypes?: ('clash' | 'direct' | 'jump' | 'localFrontend' | 'platform' | 'transit')[];
  };

  type upgradeViewShareGetParams = {
    /** token */
    token: string;
  };

  type UserActiveDayVo = {
    /** 连续登录的天数 */
    continuousLogin?: number;
    /** 连续多少天不在线 */
    continuousOut?: number;
    createTime?: string;
    day?: string;
    deviceId?: number;
    domestic?: boolean;
    /** 是否当天注册 */
    firstDay?: boolean;
    id?: number;
    location?: string;
    loginDevice?: LoginDeviceLocationVo;
    logoutTime?: string;
    remoteIp?: string;
    userAgent?: string;
    userId?: number;
  };

  type UserDto = {
    avatar?: string;
    createTime?: string;
    gender?: 'FEMALE' | 'MALE' | 'UNSPECIFIC';
    id?: number;
    nickname?: string;
    partnerId?: number;
    residentCity?: string;
    signature?: string;
    status?: 'ACTIVE' | 'BLOCK' | 'DELETED' | 'INACTIVE';
    tenant?: number;
    userType?: 'NORMAL' | 'PARTNER' | 'SHADOW';
  };

  type UserLastActivityVo = {
    appRemainLogin?: boolean;
    avatar?: string;
    continuousLogin?: number;
    domestic?: boolean;
    id?: number;
    lastCity?: string;
    lastLoginTime?: string;
    lastLogoutTime?: string;
    lastRemoteIps?: string;
    loginCount?: number;
    nickname?: string;
    online?: boolean;
    onlineTotalTime?: number;
    vitality?: number;
  };

  type UserVo = {
    /** 账号 */
    account?: string;
    avatar?: string;
    createTime?: string;
    /** 邮箱 */
    email?: string;
    gender?: 'FEMALE' | 'MALE' | 'UNSPECIFIC';
    id?: number;
    nickname?: string;
    partnerId?: number;
    /** 手机 */
    phone?: string;
    residentCity?: string;
    signature?: string;
    status?: 'ACTIVE' | 'BLOCK' | 'DELETED' | 'INACTIVE';
    tenant?: number;
    userType?: 'NORMAL' | 'PARTNER' | 'SHADOW';
    weixin?: string;
  };

  type viewByResourceTypeExistsByNameGetParams = {
    /** 类型 */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    /** name */
    name?: string;
  };

  type viewByViewIdDeleteParams = {
    /** 视图ID */
    viewId: number;
  };

  type viewByViewIdResourcesPutParams = {
    /** viewId */
    viewId: number;
  };

  type ViewDto = {
    conditions?: string;
    createTime?: string;
    creator?: number;
    description?: string;
    dynamic?: boolean;
    editable?: boolean;
    id?: number;
    name?: string;
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    teamId?: number;
    type?:
      | 'AdminRole'
      | 'ImplicitPrivilege'
      | 'Personal'
      | 'SpecifyDepartment'
      | 'SpecifyMember'
      | 'TeamMember';
  };

  type viewIpByViewIdPutParams = {
    /** 视图ID */
    viewId: number;
    /** 名称 */
    name: string;
    /** 描述 */
    description?: string;
    /** 视图可见范围 */
    type:
      | 'AdminRole'
      | 'ImplicitPrivilege'
      | 'Personal'
      | 'SpecifyDepartment'
      | 'SpecifyMember'
      | 'TeamMember';
  };

  type viewIpPostParams = {
    /** 名称 */
    name: string;
    /** 描述 */
    description?: string;
    /** 视图可见范围 */
    type:
      | 'AdminRole'
      | 'ImplicitPrivilege'
      | 'Personal'
      | 'SpecifyDepartment'
      | 'SpecifyMember'
      | 'TeamMember';
  };

  type viewOrderPutParams = {
    /** 类型 */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    /** 顺序串：用all,ipUnbound,unGranted,unFingerprinted,collected,opened,{viewId1},{viewId2}这种格式表达 */
    order: string;
  };

  type viewsByTypeByResourceTypeGetParams = {
    /** 类型 */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
  };

  type viewShopByViewIdPutParams = {
    /** 视图ID */
    viewId: number;
    /** 名称 */
    name: string;
    /** 描述 */
    description?: string;
    /** 视图可见范围 */
    type:
      | 'AdminRole'
      | 'ImplicitPrivilege'
      | 'Personal'
      | 'SpecifyDepartment'
      | 'SpecifyMember'
      | 'TeamMember';
  };

  type viewShopPostParams = {
    /** 名称 */
    name: string;
    /** 描述 */
    description?: string;
    /** 视图可见范围 */
    type:
      | 'AdminRole'
      | 'ImplicitPrivilege'
      | 'Personal'
      | 'SpecifyDepartment'
      | 'SpecifyMember'
      | 'TeamMember';
  };

  type ViewVo = {
    conditions?: string;
    createTime?: string;
    creator?: number;
    /** 创建者（分享者）头像 */
    creatorAvatar?: string;
    /** 创建者（分享者）昵称 */
    creatorName?: string;
    description?: string;
    dynamic?: boolean;
    editable?: boolean;
    id?: number;
    name?: string;
    /** 静态视图包含的资源ID */
    resourceIds?: number[];
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    teamId?: number;
    type?:
      | 'AdminRole'
      | 'ImplicitPrivilege'
      | 'Personal'
      | 'SpecifyDepartment'
      | 'SpecifyMember'
      | 'TeamMember';
  };

  type WatchingUserVo = {
    /** 账号 */
    account?: string;
    avatar?: string;
    createTime?: string;
    /** 邮箱 */
    email?: string;
    gender?: 'FEMALE' | 'MALE' | 'UNSPECIFIC';
    id?: number;
    nickname?: string;
    partnerId?: number;
    /** 手机 */
    phone?: string;
    residentCity?: string;
    signature?: string;
    /** 开始时间 */
    startTime?: string;
    status?: 'ACTIVE' | 'BLOCK' | 'DELETED' | 'INACTIVE';
    tenant?: number;
    userType?: 'NORMAL' | 'PARTNER' | 'SHADOW';
    weixin?: string;
  };

  type webhookLongRequestGetParams = {
    /** seconds */
    seconds?: number;
  };

  type webhookTgAccessPostParams = {
    /** 访问的URL */
    accessUrl?: string;
    /** RefUrl */
    refUrl?: string;
    /** 访问的模块 */
    module?: string;
    /** App UUID */
    appId?: string;
    /** 主机名 */
    hostName?: string;
    /** CPU核数 */
    cpus?: number;
    /** 内存bytes */
    mem?: number;
    /** appVersion */
    appVersion?: string;
  };

  type webhookTgDownloadPostParams = {
    /** nickname */
    nickname?: string;
    /** company */
    company?: string;
    /** contentType */
    contentType?: string;
    /** 访问的URL */
    accessUrl?: string;
    /** refUrl */
    refUrl?: string;
  };

  type WebResult = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultAppleDeveloperIosVo = {
    code?: number;
    data?: AppleDeveloperIosVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultAppVersionVo = {
    code?: number;
    data?: AppVersionVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultArraystring = {
    code?: number;
    data?: string[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultAuditDto = {
    code?: number;
    data?: AuditDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultBatchTransferShopPrepareResult = {
    code?: number;
    data?: BatchTransferShopPrepareResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultbigdecimal = {
    code?: number;
    data?: number;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultBlockElementVo = {
    code?: number;
    data?: BlockElementVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultboolean = {
    code?: number;
    data?: boolean;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCalcRenewIpResponse = {
    code?: number;
    data?: CalcRenewIpResponse;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCloudAdbAddress = {
    code?: number;
    data?: CloudAdbAddress;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCommonExcel = {
    code?: number;
    data?: CommonExcel;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCreateOrderResponse = {
    code?: number;
    data?: CreateOrderResponse;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCustomerIpInfo = {
    code?: number;
    data?: CustomerIpInfo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultDistributionCodeVo = {
    code?: number;
    data?: DistributionCodeVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultDwlAuditDto = {
    code?: number;
    data?: DwlAuditDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultExtensionPolicyVo = {
    code?: number;
    data?: ExtensionPolicyVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultExtensionsVo = {
    code?: number;
    data?: ExtensionsVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultFavoriteSiteVo = {
    code?: number;
    data?: FavoriteSiteVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultFeeSummaryVo = {
    code?: number;
    data?: FeeSummaryVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultFetchTokenDetailVo = {
    code?: number;
    data?: FetchTokenDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultFetchTokenVo = {
    code?: number;
    data?: FetchTokenVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultFingerCountResult = {
    code?: number;
    data?: FingerCountResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultFingerFetchTokenVo = {
    code?: number;
    data?: FingerFetchTokenVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultFingerprintConfigVo = {
    code?: number;
    data?: FingerprintConfigVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultFingerprintDuplicateVo = {
    code?: number;
    data?: FingerprintDuplicateVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultFingerprintTemplateConfigVo = {
    code?: number;
    data?: FingerprintTemplateConfigVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultFingerprintTemplateVo = {
    code?: number;
    data?: FingerprintTemplateVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultFingerprintVo = {
    code?: number;
    data?: FingerprintVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultFingerTemplateFetchTokenVo = {
    code?: number;
    data?: FingerTemplateFetchTokenVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultFingerUpgradeCheckResult = {
    code?: number;
    data?: FingerUpgradeCheckResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultFreeTemplateQuota = {
    code?: number;
    data?: FreeTemplateQuota;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGatewayProxyDto = {
    code?: number;
    data?: GatewayProxyDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGiftCardPackDetailVo = {
    code?: number;
    data?: GiftCardPackDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultInstallScriptVo = {
    code?: number;
    data?: InstallScriptVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultint = {
    code?: number;
    data?: number;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultIpDetailAllVo = {
    code?: number;
    data?: IpDetailAllVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultIpLocationDto = {
    code?: number;
    data?: IpLocationDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultIpNameSpecConfigVo = {
    code?: number;
    data?: IpNameSpecConfigVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultIpRefreshInfo = {
    code?: number;
    data?: IpRefreshInfo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultIpSocksDto = {
    code?: number;
    data?: IpSocksDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultIpTrafficVo = {
    code?: number;
    data?: IpTrafficVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListActiveShopSessionVo = {
    code?: number;
    data?: ActiveShopSessionVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListAreaVo = {
    code?: number;
    data?: AreaVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListBlockElementVo = {
    code?: number;
    data?: BlockElementVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListCloudMobileVo = {
    code?: number;
    data?: CloudMobileVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListCountryDto = {
    code?: number;
    data?: CountryDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListDirectDomainsDto = {
    code?: number;
    data?: DirectDomainsDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListDomainCookieVo = {
    code?: number;
    data?: DomainCookieVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListDomainWhitelistItem = {
    code?: number;
    data?: DomainWhitelistItem[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListExcelAccountWithIpVo = {
    code?: number;
    data?: ExcelAccountWithIpVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListExcelIpImportVo = {
    code?: number;
    data?: ExcelIpImportVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListExcelIppIPImportVo = {
    code?: number;
    data?: ExcelIppIPImportVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListExtensionImageVo = {
    code?: number;
    data?: ExtensionImageVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListExtensionsVo = {
    code?: number;
    data?: ExtensionsVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListFavoriteSiteVo = {
    code?: number;
    data?: FavoriteSiteVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListFingerBindHisVo = {
    code?: number;
    data?: FingerBindHisVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListFingerprintTemplateVo = {
    code?: number;
    data?: FingerprintTemplateVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListGiftCardPackDetailVo = {
    code?: number;
    data?: GiftCardPackDetailVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListGlobalViewVo = {
    code?: number;
    data?: GlobalViewVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListIpBindDto = {
    code?: number;
    data?: IpBindDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListIpBindHistoryVo = {
    code?: number;
    data?: IpBindHistoryVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListIpForShopBindVo = {
    code?: number;
    data?: IpForShopBindVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListIpNameSpecVo = {
    code?: number;
    data?: IpNameSpecVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListIppIpDto = {
    code?: number;
    data?: IppIpDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListIpTransitDto = {
    code?: number;
    data?: IpTransitDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListIpTransitGroupVo = {
    code?: number;
    data?: IpTransitGroupVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListLoginDeviceDto = {
    code?: number;
    data?: LoginDeviceDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListlong = {
    code?: number;
    data?: number[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListMemberVo = {
    code?: number;
    data?: MemberVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListMobileAccountVo = {
    code?: number;
    data?: MobileAccountVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListMobileGroupVo = {
    code?: number;
    data?: MobileGroupVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListMobileShareVo = {
    code?: number;
    data?: MobileShareVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListPendingRpaPlanVo = {
    code?: number;
    data?: PendingRpaPlanVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListPlatformIpMonitorDto = {
    code?: number;
    data?: PlatformIpMonitorDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListQCloudMobileBackup = {
    code?: number;
    data?: QCloudMobileBackup[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListRpaSimpleHisVo = {
    code?: number;
    data?: RpaSimpleHisVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListShopChannelVo = {
    code?: number;
    data?: ShopChannelVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListShopConfigDto = {
    code?: number;
    data?: ShopConfigDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListShopDetailVo = {
    code?: number;
    data?: ShopDetailVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListShopDto = {
    code?: number;
    data?: ShopDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListShopIpBindHistoryVo = {
    code?: number;
    data?: ShopIpBindHistoryVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListShopLanProxyDto = {
    code?: number;
    data?: ShopLanProxyDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListShopOpsDto = {
    code?: number;
    data?: ShopOpsDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListShopPasswordsVo = {
    code?: number;
    data?: ShopPasswordsVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListShopPlatformStatVo = {
    code?: number;
    data?: ShopPlatformStatVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListShopRpaFlowDto = {
    code?: number;
    data?: ShopRpaFlowDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListShopSessionStatVo = {
    code?: number;
    data?: ShopSessionStatVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListShopShareVo = {
    code?: number;
    data?: ShopShareVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListShopShortcutDto = {
    code?: number;
    data?: ShopShortcutDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListShopSiteHistoryVo = {
    code?: number;
    data?: ShopSiteHistoryVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListShopSnapshotPlanVo = {
    code?: number;
    data?: ShopSnapshotPlanVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListShopSnapshotVo = {
    code?: number;
    data?: ShopSnapshotVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListShopViewCategoryVo = {
    code?: number;
    data?: ShopViewCategoryVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListstring = {
    code?: number;
    data?: string[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTagDto = {
    code?: number;
    data?: TagDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTagVo = {
    code?: number;
    data?: TagVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTeamExtensionStatVo = {
    code?: number;
    data?: TeamExtensionStatVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTeamMobileDeviceVo = {
    code?: number;
    data?: TeamMobileDeviceVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTeamMobileVo = {
    code?: number;
    data?: TeamMobileVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTransitDto = {
    code?: number;
    data?: TransitDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTransitGroupVo = {
    code?: number;
    data?: TransitGroupVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTransitWithLocationVo = {
    code?: number;
    data?: TransitWithLocationVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTunnelGroupDetailVo = {
    code?: number;
    data?: TunnelGroupDetailVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTunnelProbeNodeVo = {
    code?: number;
    data?: TunnelProbeNodeVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListViewVo = {
    code?: number;
    data?: ViewVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListWatchingUserVo = {
    code?: number;
    data?: WatchingUserVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultLoginResultVo = {
    code?: number;
    data?: LoginResultVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultlong = {
    code?: number;
    data?: number;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMap = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMaplong = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMapstring = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMobileAccountVo = {
    code?: number;
    data?: MobileAccountVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMobileGroupVo = {
    code?: number;
    data?: MobileGroupVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultOperateLogsVo = {
    code?: number;
    data?: OperateLogsVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultActiveShopSessionVo = {
    code?: number;
    data?: PageResultActiveShopSessionVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultDwlAuditVo = {
    code?: number;
    data?: PageResultDwlAuditVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultExtensionsVo = {
    code?: number;
    data?: PageResultExtensionsVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultFingerprintDetailVo = {
    code?: number;
    data?: PageResultFingerprintDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultIpDetailVo = {
    code?: number;
    data?: PageResultIpDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultIpDomainDiaryDto = {
    code?: number;
    data?: PageResultIpDomainDiaryDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultIppIpDetailVo = {
    code?: number;
    data?: PageResultIppIpDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultIppPoolVo = {
    code?: number;
    data?: PageResultIppPoolVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultMobileAccountVo = {
    code?: number;
    data?: PageResultMobileAccountVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultOperateLogsVo = {
    code?: number;
    data?: PageResultOperateLogsVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultSharingIpVo = {
    code?: number;
    data?: PageResultSharingIpVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultShopDetailVo = {
    code?: number;
    data?: PageResultShopDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultShopGrantVo = {
    code?: number;
    data?: PageResultShopGrantVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultShopIpLogVo = {
    code?: number;
    data?: PageResultShopIpLogVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultShopSessionLogVo = {
    code?: number;
    data?: PageResultShopSessionLogVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultShopTagVo = {
    code?: number;
    data?: PageResultShopTagVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultUserLastActivityVo = {
    code?: number;
    data?: PageResultUserLastActivityVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPlatformIpMonitorDto = {
    code?: number;
    data?: PlatformIpMonitorDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultProbeResult = {
    code?: number;
    data?: ProbeResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultProxyProbeWithRemoteIp = {
    code?: number;
    data?: ProxyProbeWithRemoteIp;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRecordSliceVo = {
    code?: number;
    data?: RecordSliceVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRecoveryShopResult = {
    code?: number;
    data?: RecoveryShopResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRemoteIpCheckerConfig = {
    code?: number;
    data?: RemoteIpCheckerConfig;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRouterSpecVo = {
    code?: number;
    data?: RouterSpecVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRpaFlowStatVo = {
    code?: number;
    data?: RpaFlowStatVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRpaFlowStatVoV2 = {
    code?: number;
    data?: RpaFlowStatVoV2;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultSessionChannelTokenVo = {
    code?: number;
    data?: SessionChannelTokenVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultSessionNetworkConfig = {
    code?: number;
    data?: SessionNetworkConfig;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultSessionTimeLineVo = {
    code?: number;
    data?: SessionTimeLineVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultSessionTokenVo = {
    code?: number;
    data?: SessionTokenVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultShopAccessConfigVo = {
    code?: number;
    data?: ShopAccessConfigVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultShopAndPhoneStat = {
    code?: number;
    data?: ShopAndPhoneStat;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultShopBriefVo = {
    code?: number;
    data?: ShopBriefVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultShopChannelTokenVo = {
    code?: number;
    data?: ShopChannelTokenVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultShopCookiesVo = {
    code?: number;
    data?: ShopCookiesVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultShopDetailVo = {
    code?: number;
    data?: ShopDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultShopDomainPolicyVo = {
    code?: number;
    data?: ShopDomainPolicyVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultShopPasswordsDto = {
    code?: number;
    data?: ShopPasswordsDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultShopPolicyVo = {
    code?: number;
    data?: ShopPolicyVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultShopSessionMonitorVo = {
    code?: number;
    data?: ShopSessionMonitorVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultShopShortcutDetailVo = {
    code?: number;
    data?: ShopShortcutDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultShopShortcutDto = {
    code?: number;
    data?: ShopShortcutDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultShopSnapshotVo = {
    code?: number;
    data?: ShopSnapshotVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultShopSyncPolicyVo = {
    code?: number;
    data?: ShopSyncPolicyVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultShopTokenVo = {
    code?: number;
    data?: ShopTokenVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultStatelessShopPolicyVo = {
    code?: number;
    data?: StatelessShopPolicyVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultStorageQuotaVo = {
    code?: number;
    data?: StorageQuotaVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultstring = {
    code?: number;
    data?: string;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultStsPostSignature = {
    code?: number;
    data?: StsPostSignature;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultSysFingerWhitelistConfig = {
    code?: number;
    data?: SysFingerWhitelistConfig;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTagDto = {
    code?: number;
    data?: TagDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTaskDto = {
    code?: number;
    data?: TaskDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamFingerprintDefaultConfig = {
    code?: number;
    data?: TeamFingerprintDefaultConfig;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamIpStatVo = {
    code?: number;
    data?: TeamIpStatVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamIpStatVoV2 = {
    code?: number;
    data?: TeamIpStatVoV2;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamIpTunnelVo = {
    code?: number;
    data?: TeamIpTunnelVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamMobileDto = {
    code?: number;
    data?: TeamMobileDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamMobileSimpleVo = {
    code?: number;
    data?: TeamMobileSimpleVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamMobileVo = {
    code?: number;
    data?: TeamMobileVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamShopFieldConfig = {
    code?: number;
    data?: TeamShopFieldConfig;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamViewConfig = {
    code?: number;
    data?: TeamViewConfig;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTempFilePutOssUrl = {
    code?: number;
    data?: TempFilePutOssUrl;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTransferShopPrepareResult = {
    code?: number;
    data?: TransferShopPrepareResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultUserActiveDayVo = {
    code?: number;
    data?: UserActiveDayVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultViewDto = {
    code?: number;
    data?: ViewDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WorkTimeConfig = {
    FRI?: number[];
    /** 每天的数组长度一定是24，分别表示每小时属于什么时间 0: 空闲， 1: 工作时间， 2: 养号时间 */
    MON?: number[];
    SAT?: number[];
    SUN?: number[];
    THU?: number[];
    TUE?: number[];
    WED?: number[];
  };
}
