// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 消息历史记录 GET /api/gh/message/page */
export async function ghMessagePageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghMessagePageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultGhMessageDto>('/api/gh/message/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修改达人会话状态 PUT /api/gh/message/session/${param0}/status */
export async function ghMessageSessionByIdStatusPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghMessageSessionByIdStatusPutParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/gh/message/session/${param0}/status`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 查询待回复消息列表 POST /api/gh/message/session/page */
export async function ghMessageSessionPagePost(
  body: API.PageGhSessionRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultGhSessionVo>('/api/gh/message/session/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量修改达人会话状态 PUT /api/gh/message/session/status */
export async function ghMessageSessionStatusPut(
  body: API.UpdateGhSessionStatusRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/message/session/status', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 同步达人私信 返回达人新发送消息数目 POST /api/gh/message/sync */
export async function ghMessageSyncPost(
  body: API.AddGhMessageRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultSyncMessageResult>('/api/gh/message/sync', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
