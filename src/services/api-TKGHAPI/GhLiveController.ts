// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 直播历史记录 GET /api/gh/live/page */
export async function ghLivePageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghLivePageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultKolLiveDto>('/api/gh/live/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 同步（创建）直播记录 POST /api/gh/live/sync */
export async function ghLiveSyncPost(body: API.GhLiveDocument, options?: { [key: string]: any }) {
  return request<API.WebResultKolLiveDto>('/api/gh/live/sync', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
