import I18N from '@/i18n';
import React, { useState } from 'react';
import { Space, Typography } from 'antd';
import classNames from 'classnames';
import styles from './index.less';
import { getPortalUrl } from '@/utils/utils';
import constants from '@/constants';
import <PERSON><PERSON> from 'lottie-react';
import json from './animations/tk-logo.json';
import json2 from './animations/media-list.json';
import SwitchLocale from '@/i18n/SwitchLocale';
import HelpLink from '@/components/HelpLink';

interface Props {
  style?: {};
  className?: string;
  isFull?: boolean;
  hideSessionStatus?: boolean;
}

const LoginLayout: React.FC<Props> = (props) => {
  const { className, style, children, isFull } = props;
  // const [isMobile, setIsMobile] = useLocalStorageState('isMobile', () => {
  //   return navigator.userAgent.includes('Mobile');
  // });
  const [isMobile, setIsMobile] = useState(false);
  return (
    <div className={classNames(styles.wrap, { [styles.isWeb]: true }, { [styles.full]: isFull })}>
      <div
        className={classNames(styles.container, className)}
        style={
          isMobile
            ? {
                width: '100%',
                border: 0,
                boxShadow: 'none',
              }
            : style
        }
      >
        <Space style={{ position: 'absolute', top: 24, right: 36, zIndex: 2 }}>
          <SwitchLocale
            onChange={(val) => {
              if (val === 'en-US') {
                I18N.switchToEn();
              } else {
                I18N.switchToCn();
              }
            }}
          />
        </Space>

        {!isMobile && (
          <div
            className={classNames('bg', {
              [styles.leftSide]: true,
            })}
          >
            <div
              style={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                color: 'white',
                padding: '5px 20px',
                fontSize: 12,
              }}
            >
              {I18N.t(`${I18N.t('热线电话：{{hotline}}')}`, {
                hotline: constants.hotline,
              })}
            </div>
            <Lottie className="top" animationData={json} />
            <div className="title">
              <div style={{ display: 'inline-block', textAlign: 'left' }}>
                <span>花漾TK</span>
                <br />
                <span>达人营销决策与管理系统</span>
              </div>
            </div>
            <div className="bottom">
              <Lottie className={'media-list'} animationData={json2} />
            </div>
          </div>
        )}
        <div className={classNames('content', styles.content)}>
          <div className={classNames('login-form-container', styles.loginFormContainer)}>
            <div className={styles.top}>
              <a
                id="logo-link"
                className={classNames('logo-bg', styles.logo)}
                href={getPortalUrl('/')}
                target="_blank"
              />
            </div>
            <div className={classNames(styles.main, 'main')}>{children}</div>
          </div>
        </div>
        <div
          style={{
            textAlign: 'center',
            display: 'flex',
            width: '100%',
            flexDirection: 'column',
            fontSize: 16,
            gap: 16,
            margin: 'auto',
            position: 'absolute',
            left: '50%',
            transform: 'translate(-50%)',
            bottom: -132,
          }}
        >
          <div>
            <Typography.Text>
              {I18N.t('欲了解花漾TK达人营销决策与管理系统主要功能特性与各版本差异，请阅读')}
            </Typography.Text>
            <Typography.Link
              style={{ marginLeft: 16 }}
              href={'https://www.szdamai.com/tk'}
              target={'_blank'}
            >
              {I18N.t('使用说明')}
            </Typography.Link>
          </div>
          <div>
            <Typography.Text>
              {I18N.t('欲购买、升级、或希望了解产品如何使用，请阅读')}
            </Typography.Text>
            <HelpLink style={{ marginLeft: 16 }} href={'/tkshop2/buy'}>
              {I18N.t('快速入门')}
            </HelpLink>
          </div>
          <div>
            <Typography.Text>{I18N.t('请下载并安装花漾客户端')}</Typography.Text>
            <a
              style={{ marginLeft: 16 }}
              target={'_blank'}
              href={'https://www.szdamai.com/download'}
              rel="noreferrer"
            >
              {I18N.t('下载连接')}
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginLayout;
