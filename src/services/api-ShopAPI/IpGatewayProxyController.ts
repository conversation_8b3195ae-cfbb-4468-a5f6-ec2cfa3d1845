// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 在IP所在IPGO上面创建Socks监听，代理当前IP（自动生成用户名密码） POST /api/ip/${param0}/gatewayProxy */
export async function ipByIpIdGatewayProxyPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIpIdGatewayProxyPostParams,
  options?: { [key: string]: any },
) {
  const { ipId: param0, ...queryParams } = params;
  return request<API.WebResultGatewayProxyDto>(`/api/ip/${param0}/gatewayProxy`, {
    method: 'POST',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 停止IPGO上面所有代理服务 DELETE /api/ip/${param0}/gatewayProxy */
export async function ipByIpIdGatewayProxyDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIpIdGatewayProxyDeleteParams,
  options?: { [key: string]: any },
) {
  const { ipId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/ip/${param0}/gatewayProxy`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 停止IPGO上面特定代理服务 DELETE /api/ip/${param0}/gatewayProxy/${param1} */
export async function ipByIpIdGatewayProxyByGatewayProxyIdDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIpIdGatewayProxyByGatewayProxyIdDeleteParams,
  options?: { [key: string]: any },
) {
  const { ipId: param0, gatewayProxyId: param1, ...queryParams } = params;
  return request<API.WebResult>(`/api/ip/${param0}/gatewayProxy/${param1}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改Socks代理服务的认证信息 PUT /api/ip/${param0}/gatewayProxy/${param1}/auth */
export async function ipByIpIdGatewayProxyByGatewayProxyIdAuthPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIpIdGatewayProxyByGatewayProxyIdAuthPutParams,
  options?: { [key: string]: any },
) {
  const { ipId: param0, gatewayProxyId: param1, ...queryParams } = params;
  return request<API.WebResultGatewayProxyDto>(`/api/ip/${param0}/gatewayProxy/${param1}/auth`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}
