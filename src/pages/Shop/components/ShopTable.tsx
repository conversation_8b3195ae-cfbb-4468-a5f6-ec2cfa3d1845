import I18N from '@/i18n';
import { ProTable } from '@ant-design/pro-table';
import { scrollProTableOptionFn } from '@/mixins/table';
import type { Column } from '@/hooks/useResizableColumns';
import useResizableColumns from '@/hooks/useResizableColumns';
import type { TkShopDetailVo } from '@/pages/Shop/components/utils';
import openShopBrowser from '@/pages/Shop/components/utils';
import { getShopHealthStatus, getShopScore } from '@/pages/Shop/components/utils';
import { deleteShops } from '@/pages/Shop/components/utils';
import {
  getShopArea,
  getShopExtensionType,
  getShopLastSyncTime,
  getShopType,
  triggerShopTask,
} from '@/pages/Shop/components/utils';
import CountryIcon from '@/components/Common/CountryIcon';
import { Typography } from 'antd';
import Placeholder from '@/components/Common/Placeholder';
import colors from '@/style/color.less';
import type { ReactNode } from 'react';
import { useCallback } from 'react';
import { useMemo } from 'react';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { getTaskShelfJobIcon } from '@/pages/TikTok/Live/components/TaskShelfModal';
import { GhostModalCaller } from '@/mixins/modal';
import InviteByFilterModal from '@/pages/TikTok/components/InviteByFilterModal';
import SendMsgByFilterModal from '@/pages/TikTok/components/SendMsgByFilterModal';
import { history } from '@@/core/history';
import DemandPaymentModal from '@/pages/TikTok/components/DemandPaymentModal';
import { ResponsiveOptions } from '@/components/Common/MoreDropdown/ResponsiveOptions';
import { dateFormat } from '@/utils/utils';
import TkShopSetting from '@/pages/Shop/components/TkShopSetting';
import { showFunctionCodeAlert, useAuthJudgeCallback } from '@/components/Common/FunctionButton';
import Functions from '@/constants/Functions';
import ShopIcon from '@/pages/Shop/assets/shop-icon.png';

const ShopColumns: Column<TkShopDetailVo>[] = [
  {
    title: I18N.t('店铺名称'),
    sortable: {},
    dataIndex: 'name',
    disabled: true,
    resizable: {
      minWidth: 140,
    },
  },
  {
    title: I18N.t('类型'),
    sortable: {},
    dataIndex: 'type',
    render(text, record) {
      return getShopType(record);
    },
    width: 90,
  },
  {
    title: I18N.t('站点'),
    dataIndex: 'platform',
    width: 120,
    render(text, record) {
      return getShopArea(record);
    },
  },
  {
    title: I18N.t('浏览器'),
    dataIndex: 'extension',
    width: 190,
    render: (text, record) => {
      return getShopExtensionType(record);
    },
  },
  {
    title: I18N.t('健康状态'),
    dataIndex: 'healthStatus',
    sortable: {},
    width: 130,
    render: (text, record) => {
      return getShopHealthStatus(record);
    },
  },
  {
    title: I18N.t('店铺得分'),
    dataIndex: 'healthScore',
    sortable: {},
    width: 110,
    render: (text, record) => {
      return getShopScore(record);
    },
  },
  {
    title: I18N.t('最近更新'),
    dataIndex: 'lastSyncTime',
    sortable: {},
    width: 110,
    render: (text, record) => {
      return getShopLastSyncTime(record);
    },
  },
  {
    title: I18N.t('创建时间'),
    dataIndex: 'createTime',
    sortable: {},
    width: 170,
    render: (text, record) => {
      return dateFormat(record.createTime);
    },
  },
  {
    title: I18N.t('备注'),
    dataIndex: 'description',
    resizable: {
      minWidth: 100,
    },
    render: (text, record) => {
      const { description } = record;
      return description ? (
        <Typography.Text ellipsis={{ tooltip: description }}>{description}</Typography.Text>
      ) : (
        <Placeholder />
      );
    },
  },
  {
    title: I18N.t('操作'),
    dataIndex: 'option',
    disabled: true,
    width: 135,
  },
];
const ShopTable = (props: {
  order: any;
  changeOrder: any;
  dataSource: TkShopDetailVo[];
  onChange: (reset?: boolean) => void;
  selected: TkShopDetailVo[];
  onSelect: (rows: TkShopDetailVo[]) => void;
}) => {
  const { order, changeOrder, onChange, dataSource, selected, onSelect } = props;
  const { columns, header, tableWidth, isInitialized } = useResizableColumns({
    fixWidth: 32,
    columns: ShopColumns,
    scope: 'tkshop_shop_table',
    order,
    changeOrder,
  });
  const hasAuth = useAuthJudgeCallback();
  const onSetting = useCallback(
    (id: number) => {
      if (!hasAuth(Functions.SHOP_CONFIG)) {
        showFunctionCodeAlert(Functions.SHOP_CONFIG);
      } else {
        GhostModalCaller(
          <TkShopSetting
            onUpdate={() => {
              onChange(false);
            }}
            id={id!}
          />,
        );
      }
    },
    [hasAuth, onChange],
  );
  const tableColumns = useMemo(() => {
    return columns.map((item) => {
      if (item.dataIndex === 'option') {
        return {
          ...item,
          render: (text: ReactNode, record: TkShopDetailVo) => {
            const { id } = record;
            return (
              <ResponsiveOptions
                gap={8}
                itemWidth={16}
                moreBtnWidth={20}
                items={[
                  {
                    onClick: (e) => {
                      openShopBrowser(record);
                    },
                    label: '访问店铺',
                    icon: <IconFontIcon iconName="fangwendianpu_24" />,
                    key: 'visit',
                  },
                  {
                    onClick: () => {
                      triggerShopTask([record], 'TS_SyncShopInfo');
                    },
                    label: '店铺信息同步',
                    tooltip: '执行“店铺信息同步”流程',
                    icon: getTaskShelfJobIcon('TS_SyncShopInfo', { iconType: 'iconfont' }),
                    key: 'sync',
                  },
                  {
                    onClick: () => {
                      GhostModalCaller(<InviteByFilterModal shop={record} />);
                    },
                    label: '定向邀约',
                    icon: getTaskShelfJobIcon('TS_TargetPlanByFilter', { iconType: 'iconfont' }),
                    key: 'invite',
                  },
                  {
                    onClick: () => {
                      GhostModalCaller(<SendMsgByFilterModal shop={record} />);
                    },
                    label: '站内消息',
                    icon: getTaskShelfJobIcon('TS_IMChatByFilter', { iconType: 'iconfont' }),
                    key: 'site_message',
                  },
                  {
                    onClick: () => {
                      history.push(`/team/${record.teamId!}/sample/${record.id!}`);
                    },
                    label: I18N.t('索样审批'),
                    icon: getTaskShelfJobIcon('TS_SampleApprove', { iconType: 'iconfont' }),
                    key: 'sample',
                  },
                  {
                    onClick: () => {
                      GhostModalCaller(<DemandPaymentModal shop={record} />, 'TS_DemandPayment');
                    },
                    label: I18N.t('未付款订单催付'),
                    icon: <IconFontIcon iconName="xuyaoxufei_24" />,
                    key: 'demandPayment',
                  },
                  {
                    onClick: () => {
                      triggerShopTask([record], 'TS_TargetPlanClear');
                    },
                    label: I18N.t('清理定向邀约计划'),
                    icon: <IconFontIcon iconName="qingchu_24" />,
                    key: 'targetPlanClear',
                  },
                  {
                    onClick: () => {
                      onSetting(id!);
                    },
                    label: I18N.t('店铺属性'),
                    icon: <IconFontIcon iconName="shezhi_24" />,
                    key: 'setting',
                  },
                  {
                    onClick: () => {
                      if (!hasAuth(Functions.SHOP_IMPORT_DELETE)) {
                        showFunctionCodeAlert(Functions.SHOP_IMPORT_DELETE);
                      } else {
                        deleteShops([id!], () => {
                          onChange(true);
                        });
                      }
                    },
                    label: I18N.t('删除店铺'),
                    icon: <IconFontIcon iconName="Trash_24" />,
                    key: 'delete',
                  },
                ]}
              />
            );
          },
        };
      }
      if (item.dataIndex === 'name') {
        return {
          ...item,
          render: (text: ReactNode, record: TkShopDetailVo) => {
            const { name, id } = record;
            return (
              <div
                style={{
                  overflow: 'hidden',
                  whiteSpace: 'nowrap',
                  display: 'inline-flex',
                  maxWidth: '100%',
                  gap: 4,
                  alignItems: 'center',
                }}
                onClick={() => {
                  onSetting(id!);
                }}
              >
                <img style={{ width: 16, height: 16, display: 'block' }} src={ShopIcon} />
                <div
                  style={{
                    flex: 1,
                    overflow: 'hidden',
                    color: colors.primaryColor,
                    cursor: 'pointer',
                  }}
                >
                  <Typography.Text style={{ color: 'inherit' }} ellipsis={{ tooltip: name }}>
                    {name}
                  </Typography.Text>
                </div>
              </div>
            );
          },
        };
      }
      return item;
    });
  }, [columns, hasAuth, onChange, onSetting]);
  return (
    <ProTable
      style={!isInitialized ? { opacity: 0, pointerEvents: 'none' } : {}}
      components={{
        header,
      }}
      columns={tableColumns}
      dataSource={dataSource}
      {...scrollProTableOptionFn({
        pagination: false,
        scroll: {
          x: tableWidth,
        },
      })}
      rowSelection={{
        selectedRowKeys: selected.map((item) => item.id!),
        onChange: (selectedRowKeys, rows) => {
          onSelect(rows);
        },
      }}
    />
  );
};
export default ShopTable;
