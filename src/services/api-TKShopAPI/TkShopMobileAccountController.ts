// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 更改手机账号的发送消息设置 PUT /api/tkshop/mobile/account/batchUpdateMessageConfig */
export async function tkshopMobileAccountBatchUpdateMessageConfigPut(
  body: API.BatchUpdateMessageConfigRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/mobile/account/batchUpdateMessageConfig', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取某个手机账号的发送消息设置 GET /api/tkshop/mobile/account/messageConfig */
export async function tkshopMobileAccountMessageConfigGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopMobileAccountMessageConfigGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMobileAccountMessageConfig>(
    '/api/tkshop/mobile/account/messageConfig',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}
