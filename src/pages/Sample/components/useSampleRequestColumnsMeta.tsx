import I18N from '@/i18n';
import { useCallback, useEffect, useMemo, useState } from 'react';
import EventEmitter from 'events';
import _ from 'lodash';
import { StatusColumnWidth } from '@/pages/TikTok/Live/components/SampleRequestCard';

const eventEmitter = new EventEmitter();

export type SampleRequestColumn =
  | 'creator'
  | 'productName'
  | 'productPrice'
  | 'shopName'
  | 'applyTime'
  | 'status'
  | 'creatorStatus'
  | 'followerCnt'
  | 'gmv'
  | 'itemsSold'
  | 'creatorCategoryList'
  | 'creatorAlias'
  | 'creatorRemark'
  | 'creatorTags'
  | 'commissionRate'
  | 'applyId'
  | 'lastSyncTime'
  | 'option';

const DEFAULT_COLUMNS: SampleRequestColumn[] = [
  'creator',
  'productName',
  'productPrice',
  'shopName',
  'applyTime',
  'commissionRate',
  'status',
  'lastSyncTime',
  'option',
];

export function useSampleRequestColumnsMeta(shopId?: number) {
  const storage_key = `tk_sample_request_columns_V20250806`;
  const [columns, setColumns] = useState<SampleRequestColumn[]>(() => {
    let _columns = DEFAULT_COLUMNS;
    try {
      if (localStorage.getItem(storage_key)) {
        _columns = JSON.parse(
          localStorage.getItem(storage_key) || '[]',
        ) as unknown as SampleRequestColumn[];
      }
    } catch (e) {
      console.log(e);
    }
    return _columns;
  });
  const meta = useMemo(() => {
    return [
      {
        dataIndex: 'creator',
        title: I18N.t('索样达人'),
        resizable: {
          minWidth: 200,
        },
        disabled: true,
      },
      {
        dataIndex: 'productName',
        title: I18N.t('索样商品'),
        disabled: true,
        resizable: {
          minWidth: 160,
        },
      },
      { dataIndex: 'productPrice', title: I18N.t('商品价格'), width: 110 },
      !shopId && {
        dataIndex: 'shopName',
        title: I18N.t('店铺名称'),
        resizable: {
          minWidth: 160,
        },
      },
      {
        dataIndex: 'applyTime',
        title: I18N.t('索样时间'),
        width: 165,
        sortable: {},
      },
      {
        dataIndex: 'commissionRate',
        title: I18N.t('佣金'),
        width: 100,
        sortable: {},
      },
      {
        dataIndex: 'status',
        title: '索样状态',
        description: '索样状态',
        disabled: true,
        width: StatusColumnWidth,
      },
      {
        dataIndex: 'lastSyncTime',
        title: I18N.t('更新时间'),
        width: 165,
        sortable: {},
      },
      {
        dataIndex: 'creatorStatus',
        title: I18N.t('达人合作状态'),
        width: 110,
      },
      {
        dataIndex: 'followerCnt',
        title: I18N.t('达人粉丝数'),
        width: 95,
        sortable: {},
      },
      {
        dataIndex: 'gmv',
        title: I18N.t('达人GMV'),
        resizable: {
          minWidth: 100,
        },
        sortable: {},
      },
      {
        dataIndex: 'itemsSold',
        title: I18N.t('达人Items Sold'),
        resizable: {
          minWidth: 150,
        },
        sortable: {},
      },
      {
        dataIndex: 'creatorCategoryList',
        title: I18N.t('达人类目'),
        resizable: {
          minWidth: 150,
        },
      },
      {
        dataIndex: 'creatorAlias',
        title: I18N.t('达人昵称'),
        resizable: {
          minWidth: 100,
        },
      },
      {
        dataIndex: 'creatorRemark',
        title: I18N.t('达人备注'),
        resizable: {
          minWidth: 130,
        },
      },
      {
        dataIndex: 'creatorTags',
        title: I18N.t('达人标签'),
        resizable: {
          minWidth: 208,
        },
      },
      {
        dataIndex: 'applyId',
        title: I18N.t('索样记录ID'),
        width: 190,
      },
      // {
      //   title: I18N.t('操作'),
      //   dataIndex: 'option',
      //   width: 140,
      //   disabled: true,
      // },
    ].filter(Boolean);
  }, [shopId]);
  const changeColumns = useCallback(() => {
    let _columns = DEFAULT_COLUMNS;
    try {
      if (localStorage.getItem(storage_key)) {
        _columns = JSON.parse(localStorage.getItem(storage_key) || '[]');
      }
    } catch (e) {
      console.log(e);
    }
    setColumns(_columns);
  }, [storage_key]);
  useEffect(() => {
    eventEmitter.on('UPDATE', changeColumns);
    return () => {
      eventEmitter.off('UPDATE', changeColumns);
    };
  }, [changeColumns]);
  const _columns = useMemo(() => {
    return columns.filter((key) => {
      return (
        _.findIndex(meta, (item) => {
          return item.dataIndex === key;
        }) !== -1
      );
    });
  }, [columns, meta]);
  const _updateColumns = useCallback(
    (cols: string[]) => {
      localStorage.setItem(storage_key, JSON.stringify(cols));
      eventEmitter.emit('UPDATE');
    },
    [storage_key],
  );
  return {
    columns: _columns,
    update: _updateColumns,
    meta,
  };
}
