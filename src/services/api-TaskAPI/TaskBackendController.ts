// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 任务结束 PUT /api/task/${param0}/finished */
export async function taskByTaskIdFinishedPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.taskByTaskIdFinishedPutParams,
  options?: { [key: string]: any },
) {
  const { taskId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/task/${param0}/finished`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 更新任务进度 PUT /api/task/${param0}/progress */
export async function taskByTaskIdProgressPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.taskByTaskIdProgressPutParams,
  options?: { [key: string]: any },
) {
  const { taskId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/task/${param0}/progress`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}
