// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 扣除花瓣并且将udid提交苹果开发者审核 POST /api/shop/mobile/apple/ios/deductionAndSubmit */
export async function shopMobileAppleIosDeductionAndSubmitPost(
  body: API.DeductionAndSubmitRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/apple/ios/deductionAndSubmit', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取某个ios udid当前的状态 GET /api/shop/mobile/apple/ios/findUdidStatus */
export async function shopMobileAppleIosFindUdidStatusGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileAppleIosFindUdidStatusGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultAppleDeveloperIosVo>('/api/shop/mobile/apple/ios/findUdidStatus', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
