// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取通道连接统计 GET /api/proxy/${param0}/tunnelConnStat */
export async function proxyByProxyIdTunnelConnStatGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.proxyByProxyIdTunnelConnStatGetParams,
  options?: { [key: string]: any },
) {
  const { proxyId: param0, ...queryParams } = params;
  return request<API.WebResultTunnelConnStat>(`/api/proxy/${param0}/tunnelConnStat`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取安装脚本 GET /api/proxy/installScript */
export async function proxyInstallScriptGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.proxyInstallScriptGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultInstallScriptVo>('/api/proxy/installScript', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询Proxy列表 GET /api/proxy/list */
export async function proxyListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.proxyListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListGatewayDto>('/api/proxy/list', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建一个批量导入Proxy的模版 POST /api/proxy/template */
export async function proxyTemplatePost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.proxyTemplatePostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultProxyTemplateVo>('/api/proxy/template', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建一个Proxy的连接Token POST /api/proxy/tunnelToken */
export async function proxyTunnelTokenPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.proxyTunnelTokenPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultProxyTokenCodeVo>('/api/proxy/tunnelToken', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取网关版本和下载信息 GET /api/proxy/versionInfo */
export async function proxyVersionInfoGet(options?: { [key: string]: any }) {
  return request<API.WebResultProxyVersionInfo>('/api/proxy/versionInfo', {
    method: 'GET',
    ...(options || {}),
  });
}
