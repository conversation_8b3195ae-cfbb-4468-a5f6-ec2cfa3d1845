// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 判断是否对路径有权限 GET /api/disk/checkPathGranted */
export async function diskCheckPathGrantedGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.diskCheckPathGrantedGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultboolean>('/api/disk/checkPathGranted', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 收藏文件（夹） POST /api/disk/collect */
export async function diskCollectPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.diskCollectPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/disk/collect', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 文件（夹） POST /api/disk/copy */
export async function diskCopyPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.diskCopyPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/disk/copy', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建目录（无任务） POST /api/disk/directory */
export async function diskDirectoryPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.diskDirectoryPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCreateFileResult>('/api/disk/directory', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询收藏列表 GET /api/disk/favorites/page */
export async function diskFavoritesPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.diskFavoritesPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultFavoriteDto>('/api/disk/favorites/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建文件 POST /api/disk/file */
export async function diskFilePost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.diskFilePostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCreateFileResult>('/api/disk/file', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 更新上传文件的状态 PUT /api/disk/file/${param0}/finished */
export async function diskFileByFileIdFinishedPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.diskFileByFileIdFinishedPutParams,
  options?: { [key: string]: any },
) {
  const { fileId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/disk/file/${param0}/finished`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 删除文件或目录（会递归删除子目录和文件） DELETE /api/disk/fileOrDirectory */
export async function diskFileOrDirectoryDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.diskFileOrDirectoryDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/disk/fileOrDirectory', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 移动文件（夹） POST /api/disk/move */
export async function diskMovePost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.diskMovePostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/disk/move', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询可用的上传路径 GET /api/disk/prepareName */
export async function diskPrepareNameGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.diskPrepareNameGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/disk/prepareName', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取下载签名URL GET /api/disk/preSignUrl */
export async function diskPreSignUrlGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.diskPreSignUrlGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/disk/preSignUrl', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 团队网盘根目录授权给用户 POST /api/disk/team/grantToUsers */
export async function diskTeamGrantToUsersPost(
  body: API.DiskRootGrant2UsersRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/disk/team/grantToUsers', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 列出团队网盘中的文件 GET /api/disk/team/list */
export async function diskTeamListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.diskTeamListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultOssListResult>('/api/disk/team/list', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取团队网盘根目录的详情 GET /api/disk/team/root */
export async function diskTeamRootGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.diskTeamRootGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTeamDiskRootVo>('/api/disk/team/root', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取团队网盘签名 GET /api/disk/team/signature */
export async function diskTeamSignatureGet(options?: { [key: string]: any }) {
  return request<API.WebResultDiskStsVo>('/api/disk/team/signature', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 客户端出发一个网盘路径修改事件 GET /api/disk/triggerChanged */
export async function diskTriggerChangedGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.diskTriggerChangedGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/disk/triggerChanged', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取个人网盘签名 GET /api/disk/user/signature */
export async function diskUserSignatureGet(options?: { [key: string]: any }) {
  return request<API.WebResultDiskStsVo>('/api/disk/user/signature', {
    method: 'GET',
    ...(options || {}),
  });
}
