declare namespace API {
  type AbstractRpaShopInfo = {
    description?: string;
    id?: number;
    itemId?: number;
    name?: string;
  };

  type AcceptShareFlowRequest = {
    /** 接受哪些流程并指定它们的名字 */
    acceptFlows?: Record<string, any>;
    shareCode?: string;
  };

  type AddFlowGroupRequest = {
    description?: string;
    name?: string;
    sortNumber?: number;
  };

  type CalcPriceResponse = {
    /** 折扣,[0-1] */
    discount?: number;
    /** 打折减掉的金额(如果是打折的话) */
    discountAmount?: number;
    /** 记录每个item的价格及折扣或赠送信息，<key=goodsId, value=PriceInfo> */
    items?: Record<string, any>;
    /** 订单应付价(减掉了打折等信息) */
    payablePrice?: number;
    /** 赠送金额，目前只出现在购买花瓣 */
    presentAmount?: number;
    /** 订单总成本 */
    totalCost?: number;
    /** 订单总价(原价) */
    totalPrice?: number;
  };

  type CalcRenewRpaVoucherResponse = {
    /** 打折减掉的金额(如果是打折的话) */
    discountAmount?: number;
    distributionInfo?: DistributionInfo;
    /** 记录按月续费的价格及折扣，<key=ipId || rpaVoucherId, value=PriceInfo> */
    monthItems?: Record<string, any>;
    /** 订单应付价(减掉了打折等信息) */
    payablePrice?: number;
    /** 订单总成本 */
    totalCost?: number;
    /** 订单总价(原价) */
    totalPrice?: number;
    /** 记录按周续费的价格及折扣，<key=ipId || rpaVoucherId, value=PriceInfo> */
    weekItems?: Record<string, any>;
  };

  type CaptchaOcrRequest = {
    codeType?: string;
    image?: string;
    image1?: string;
    param1?: string;
    provider?: string;
    taskId?: number;
  };

  type CaptchaOcrResponse = {
    message?: string;
    provider?: string;
    success?: boolean;
    text?: string;
  };

  type CheckShareCodeResponse = {
    allowPushUpdate?: boolean;
    allowRead?: boolean;
    creatorId?: number;
    creatorName?: string;
    flows?: ShareFlowCheckInfo[];
    includeGroups?: boolean;
    shareCode?: string;
    teamId?: number;
    teamName?: string;
  };

  type CopyPlanRequest = {
    /** 重复日期，只包含星期信息的表达式 */
    cronExpression?: string;
    enableSchedule?: boolean;
    /** 如果是随机执行，用-隔开开始时间和结束时间[start, start-end, start, ...] */
    expressions?: string[];
    /** 复制后的计划名称 */
    name?: string;
    /** 每个店铺自己的变量 */
    params?: Record<string, any>;
    shopIds?: number[];
  };

  type CreateEmailRpaTriggerVo = {
    content?: string;
    sender?: string;
    token?: string;
  };

  type CreateFileRpaTriggerVo = {
    deviceId?: string;
    dir?: string;
    fileExt?: string;
    includeSub?: boolean;
    watchCreate?: boolean;
    watchDelete?: boolean;
    watchModify?: boolean;
  };

  type CreateHttpRpaTriggerVo = {
    /** 触发间隔（秒）：>0时，在这个时间段内触发当做同一次触发 */
    clientInterval?: number;
    /** 请求的Http方法，支持GET|POST,默认不限制 */
    httpMethod?: string;
    token?: string;
  };

  type CreateMessageRpaTriggerVo = {
    messageId?: string;
  };

  type CreateOrderResponse = {
    bankAccount?: string;
    bankAccountName?: string;
    bankName?: string;
    bankRemark?: string;
    createTime?: string;
    /** 扣减金额 */
    deductedPrice?: number;
    orderId?: number;
    /** 支付输出的内容 */
    payOutContent?: string;
    /** 支付输出的内容类型 */
    payOutType?: string;
    /** 如果不需要现金支付，该订单状态会直接变成已支付 */
    payStatus?:
      | 'CANCELED'
      | 'Created'
      | 'Locked'
      | 'PAID'
      | 'PartialRefund'
      | 'REFUNDED'
      | 'WAIT_CONFIRM';
    /** 支付方式 */
    payType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    /** 需要现金支付的money */
    realPrice?: number;
    salesReduction?: number;
    serialNumber?: string;
  };

  type CreatePlanRequest = {
    /** 是否允许他人编辑此计划 */
    allowOthersEdit?: boolean;
    /** 当账号的浏览器已打开时的策略：stop | reopen | reuse */
    caseBrowserOpened?: string;
    /** 流程结束后是否关闭账号浏览器：close | keep */
    closeBrowserOnEnd?: string;
    /** 账号并发数量 */
    concurrent?: number;
    /** 并发等待时间间隔 */
    concurrentDelay?: number;
    /** 重复日期，只包含星期信息的表达式 */
    cronExpression?: string;
    description?: string;
    /** login_device表里的device_id */
    deviceId?: string;
    duration?: number;
    /** 邮件触发器 */
    emailTrigger?: CreateEmailRpaTriggerVo;
    enableSchedule?: boolean;
    /** 如果是随机执行，用-隔开开始时间和结束时间[start, start-end, start, ...] */
    expressions?: string[];
    /** 文件触发器 */
    fileTrigger?: CreateFileRpaTriggerVo;
    flowId?: number;
    forceRecord?: boolean;
    /** 计划分组ID */
    groupId?: number;
    /** 是否以无头模式运行 */
    headless?: boolean;
    /** Http触发器 */
    httpTrigger?: CreateHttpRpaTriggerVo;
    /** 窗口布局配置,json格式 */
    layoutConfig?: string;
    /** 该计划最大执行时长，单位分钟。相应的流程如果执行超过这个值会被强制结束。为空或为0表示不限制 */
    maxMinutes?: number;
    /** 事件触发器 */
    messageTrigger?: CreateMessageRpaTriggerVo;
    name?: string;
    /** 他人执行此流程的策略。 为空表示只能创建者可以执行，非空可选值为：creator | others | creator_others */
    othersRunPolicy?: string;
    /** 如果流程输入变量来自网盘，保存文件路径，以 team_disk:// 或 user_disk:// 开头 */
    paramFilePath?: string;
    /** 每个店铺自己的变量 */
    params?: Record<string, any>;
    planEventDelay?: number;
    planEventName?: string;
    /** 计划类型 */
    planType?: 'Auto' | 'Loop' | 'Manual' | 'Trigger';
    /** 是否自动弹出流程日志窗口，为空表示false */
    popupTaskLog?: boolean;
    /** 云端执行的云厂商 */
    provider?:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    /** 云端执行的区域 */
    region?: string;
    /** 是否云端执行 */
    runOnCloud?: boolean;
    /** 所有店铺共用的变量 */
    sharingParams?: Record<string, any>;
    shopIds?: number[];
    /** 是否显示鼠标轨迹 */
    showMouseTrack?: boolean;
    /** 执行时侧边栏显示策略。hide | show，为空表示继承流程配置 */
    sidePanel?: string;
    snapshot?: 'Node' | 'Not' | 'OnFail';
    snapshotScope?: 'fullPage' | 'pdf' | 'viewport' | 'window';
    startEventDelay?: number;
    startEventName?: string;
    systemType?: 'normal' | 'tk';
    /** 任务命名规则 */
    taskNameEl?: string;
    /** 计划结束后是否触发一个计划事件 */
    triggerPlanEvent?: boolean;
    /** 计划开始后是否触发一个计划事件 */
    triggerStartEvent?: boolean;
  };

  type CreateRpaFlowFromHisRequest = {
    /** 是否控制台流程 */
    console?: boolean;
    description?: string;
    groupIds?: number[];
    /** 历史版本id */
    hisVersionId?: number;
    name?: string;
    platforms?: string[];
    /** 编辑用到账号id */
    shopId?: number;
    version?: string;
  };

  type CreateRpaFlowRequest = {
    /** 是否控制台流程 */
    console?: boolean;
    description?: string;
    /** 是不是从文件恢复过来的 */
    fromFile?: boolean;
    groupIds?: number[];
    name?: string;
    platforms?: string[];
    /** 流程类型，分手机和browser */
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    /** 编辑用到账号id */
    shopId?: number;
    version?: string;
  };

  type CreateRpaFlowShareRequest = {
    /** 是否允许分享方主动推送更新，默认false */
    allowPushUpdate?: boolean;
    /** 是否可读，默认false */
    allowRead?: boolean;
    /** 具体要带上的分组信息，{1111: [分组1, 分组2], 2222: [分组2]} */
    groupNames?: Record<string, any>;
    /** 是否包含流程的分组信息，默认false */
    includeGroups?: boolean;
    /** 分享哪些流程 */
    rpaFlowIds?: number[];
    /** 有效期（分钟） */
    validMinutes?: number;
  };

  type DiscountsVo = {
    /** 赠送数量或折扣百分比或阶梯折扣百分比 */
    amount?: number;
    /** 打折code */
    discountCode?: string;
    /** 打折还是赠送 */
    discountType?: 'Discount' | 'LadderPrice' | 'Present';
    /** 周期或数量单位 */
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    /** 备注 */
    remarks?: string;
    /** 期数或数量 */
    threshold?: number;
  };

  type DispatchRpaEventRequest = {
    eventName?: string;
    params?: Record<string, any>;
    rpaTaskId?: number;
    /** 如果不为空，表示只触发指定的item */
    rpaTaskItemIds?: number[];
  };

  type DistributionCodeDto = {
    amount?: number;
    code?: string;
    createTime?: string;
    description?: string;
    discountId?: number;
    distributionType?: 'Deduction' | 'Discount' | 'Official';
    distributor?: number;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    limited?: boolean;
    name?: string;
    systemDefault?: boolean;
    usageCount?: number;
    usedCount?: number;
    valid?: boolean;
    validDays?: number;
  };

  type DistributionInfo = {
    code?: DistributionCodeDto;
    deductedPrice?: number;
    drawPrice?: number;
  };

  type DomShadow = {
    description?: string;
    /** 被映射的元素位于iframe下 */
    iframe?: string;
    name?: string;
    /** 映射节点的selector或者xpath */
    selector?: string;
    sid?: string;
    /** 目前支持：input | textarea | button */
    type?: string;
  };

  type DynamicSetTaskItemRequest = {
    rpaTaskId?: number;
    shopIds?: number[];
  };

  type Element = {
    backupSelectors?: string[];
    description?: string;
    /** 页面标题 */
    documentTitle?: string;
    /** 网页链接 */
    href?: string;
    /** 一个随机字符串 */
    id?: string;
    iframe?: string;
    /** 优先使用何种方式定位元素，可选值: selector|xpath|none */
    majorInspect?: string;
    /** 然后使用何种方式定位元素，可选值: selector|xpath|none */
    minorInspect?: string;
    name?: string;
    /** 父元素ID */
    parent?: string;
    /** 预览图，指向一个附件的key，如: attachment://record_bucket/11111/2222-22220-2222-2222.png */
    preview?: string;
    selector?: string;
    /** 元素类型 */
    type?: string;
    xpath?: string;
  };

  type ImageUnderstandingRequest = {
    height?: number;
    /** base64格式的图片 */
    image?: string;
    prompts?: string[];
    provider?: string;
    width?: number;
  };

  type ImportMarketFlowRequest = {
    /** 是否控制台流程 */
    console?: boolean;
    copy?: boolean;
    description?: string;
    draft?: boolean;
    /** 市场流程发生更新时是否自动更新。当copy=false时才有意义 */
    followMarket?: boolean;
    groupIds?: number[];
    marketFlowId?: number;
    name?: string;
    platforms?: string[];
    /** 编辑用到账号id */
    shopId?: number;
    version?: string;
  };

  type IpPoolDto = {
    allocateStrategy?: 'ByLessTraffic' | 'ByLoad' | 'ByOrder' | 'ByRandom';
    capacity?: number;
    connectTransits?: string;
    createTime?: string;
    creator?: number;
    description?: string;
    domestic?: boolean;
    enableWhitelist?: boolean;
    exclusive?: boolean;
    id?: number;
    lastApiTime?: string;
    lifetime?: number;
    locationId?: number;
    minApiInterval?: number;
    minApiNum?: number;
    name?: string;
    produceFromTransit?: boolean;
    produceStrategy?: 'ProduceOnEmpty' | 'ProduceOnHand' | 'ProduceOnRequest';
    produced?: number;
    provider?: string;
    releaseStrategy?: 'Discard' | 'Reuse';
    teamId?: number;
    transitType?: 'Auto' | 'Direct' | 'Transit';
    transits?: string;
    tunnelTypes?: string;
  };

  type ItemPriceInfo = {
    costPrice?: number;
    /** 当前过期时间 */
    currentValidEndTime?: string;
    discount?: DiscountsVo;
    /** 打折减掉的金额，如果是打折的话 */
    discountAmount?: number;
    goodsId?: number;
    /** 应付价格 */
    payablePrice?: number;
    /** 赠送数量，如果是赠送的话。目前只出现在购买花瓣 */
    presentAmount?: number;
    /** item总价 */
    price?: number;
    /** 续费后到期时间 */
    validEndTime?: string;
  };

  type ItemShopInfo = {
    id?: number;
    name?: string;
  };

  type liuchengfujian = {
    /** 附件类型，为空或者是 user 表示用户上传，对用户可见，否则是系统生成的 */
    attachType?: string;
    createTime?: number;
    /** 附件路径，key含有uuid，可以当id用 */
    key?: string;
    /** 附件名称 */
    name?: string;
    /** 附件大小 */
    size?: number;
    /** 附件类型,如 png, jpg, jpeg, xlsx */
    type?: string;
  };

  type LocalTime = {
    hour?: number;
    minute?: number;
    nano?: number;
    second?: number;
  };

  type LoginDeviceDto = {
    appId?: string;
    appVersion?: string;
    clientIp?: string;
    clientLocation?: number;
    cpus?: number;
    createTime?: string;
    deviceId?: string;
    deviceType?: 'App' | 'Browser' | 'Extension' | 'HYRuntime' | 'RpaExecutor';
    domestic?: boolean;
    hostName?: string;
    id?: number;
    ipDataId?: number;
    lastActiveTime?: string;
    lastCity?: string;
    lastLogTime?: string;
    lastRemoteIp?: string;
    lastUserId?: number;
    logUrl?: string;
    mem?: number;
    online?: boolean;
    osName?: string;
    userAgent?: string;
    version?: string;
  };

  type MarketFlowGainsVo = {
    createTime?: string;
    creatorId?: number;
    credit?: number;
    duration?: number;
    expired?: boolean;
    expiredTime?: string;
    extra?: string;
    feesType?: 'Fee' | 'Free' | 'LimitedFree';
    id?: number;
    marketFlowId?: string;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    teamId?: number;
  };

  type MarketFlowImageVo = {
    description?: string;
    id?: number;
    url?: string;
    /** 不为空表示该张图片有可播放的视频 */
    videoUrl?: string;
  };

  type MarketFlowPlatformVo = {
    id?: number;
    marketFlowId?: number;
    platformName?: string;
  };

  type MarketFlowVo = {
    allowRead?: boolean;
    /** 买断价格，为空表示不支持该购买方式 */
    buyoutPrice?: number;
    category?: 'Demo' | 'Ecommerce' | 'Others' | 'Payment' | 'Social' | 'TikTok';
    configId?: string;
    cornerMark?: string;
    createTime?: string;
    description?: string;
    /** 收费类型 */
    feesType?: 'Fee' | 'Free' | 'LimitedFree';
    icon?: string;
    id?: number;
    longDescription?: string;
    /** 月价格，为空表示不支持该购买方式 */
    monthPrice?: number;
    name?: string;
    /** 适用平台列表 */
    platforms?: MarketFlowPlatformVo[];
    provider?: string;
    publishTime?: string;
    /** 流程类型，分手机和browser */
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    score?: number;
    sortNo?: number;
    status?: 'Deleted' | 'Normal' | 'OffLine';
    updateTime?: string;
    url?: string;
    /** 用户数 */
    userCount?: number;
    version?: string;
    /** 周价格，为空表示不支持该购买方式 */
    weekPrice?: number;
  };

  type MarkRpaItemCancelledRequest = {
    errorMsg?: string;
    rpaTaskItemIds?: number[];
  };

  type MarkRpaItemEndRequest = {
    error?: string;
    force?: boolean;
    rpaStatus?:
      | 'Cancelled'
      | 'CreateFailed'
      | 'Ended'
      | 'Ended_All_Failed'
      | 'Ended_Partial_Failed'
      | 'Ignored'
      | 'NotStart'
      | 'Running'
      | 'ScheduleCancelled'
      | 'Scheduled'
      | 'Scheduling'
      | 'UnusualEnded';
    rpaTaskItemId?: number;
    success?: boolean;
  };

  type NewRpaTaskRequest = {
    /** 当账号的浏览器已打开时的策略：stop | reopen | reuse */
    caseBrowserOpened?: string;
    /** 流程结束后是否关闭账号浏览器：close | keep */
    closeBrowserOnEnd?: string;
    cloudInstanceId?: number;
    concurrent?: number;
    /** 并发等待时间间隔 */
    concurrentDelay?: number;
    description?: string;
    deviceId?: string;
    /** 任务里可以通过 rpa.getEnv(key) 来获取传递的值 */
    environments?: Record<string, any>;
    forceRecord?: boolean;
    formId?: string;
    /** 是否以无头模式运行 */
    headless?: boolean;
    /** 窗口布局配置,json格式 */
    layoutConfig?: string;
    /** 是否手动执行 */
    manualRun?: boolean;
    name?: string;
    /** 创建一个task时用来指定变量值。如果指定的key不在流程定义里会被忽略 */
    params?: Record<string, any>;
    /** 是否自动弹出流程日志窗口，为空表示false */
    popupTaskLog?: boolean;
    /** 云端执行的云厂商 */
    provider?:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    /** 云端执行的区域 */
    region?: string;
    rpaFlowId?: number;
    /** 手动直接执行一个流程的时候（不包含计划）支持直接执行指定版本 */
    rpaFlowVersion?: string;
    /** 是否云端执行 */
    runOnCloud?: boolean;
    scheduleId?: number;
    /** 被哪个quartz调度任务触发 */
    scheduleJobId?: string;
    /** 有哪些账户参与该流程Task执行，如果是手机流程，指有哪些 mobile 参与该流程的执行 */
    shopIds?: number[];
    /** 是否显示鼠标轨迹 */
    showMouseTrack?: boolean;
    /** 执行时侧边栏显示策略。hide | show，为空表示继承流程配置 */
    sidePanel?: string;
    snapshot?: 'Node' | 'Not' | 'OnFail';
    /** 快捷方式的Token */
    sscToken?: string;
  };

  type NotifyFileAddedRequest = {
    filename?: string;
    filesize?: number;
    rpaFileType?: 'Data' | 'Log' | 'Record' | 'Screenshot' | 'Unknown' | 'Upload';
    rpaTaskItemId?: number;
  };

  type NotifyOssFileAddedRequest = {
    /** 仅rpa工作目录有意义 */
    fileType?: 'Data' | 'Log' | 'Record' | 'Screenshot' | 'Unknown' | 'Upload';
    files?: Record<string, any>;
    rpaTaskItemId?: number;
  };

  type OpenAiRequest = {
    accessToken?: string;
    aiProvider?: string;
    customerModelId?: string;
    origin?: boolean;
    prompts?: string[];
  };

  type OpenAiResponse = {
    response?: string;
  };

  type PageResultMarketFlowVo = {
    current?: number;
    list?: MarketFlowVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultRpaFlowShareVo = {
    current?: number;
    list?: RpaFlowShareVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultRpaFlowVo = {
    current?: number;
    list?: RpaFlowVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultRpaPlanVo = {
    current?: number;
    list?: RpaPlanVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultRpaTaskVo = {
    current?: number;
    list?: RpaTaskVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultRpaVoucherVo = {
    current?: number;
    list?: RpaVoucherVo[];
    pageSize?: number;
    total?: number;
  };

  type paymentCalcBuyRpaVoucherPriceGetParams = {
    /** 购买多少个周期 */
    duration: number;
    /** 续费周期单位, 周或者月 */
    periodUnit:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    /** baseCount, 4核以下买多少个 */
    baseCount: number;
    /** performanceCount, 4核及以上买多少个 */
    performanceCount: number;
    /** 优惠码 */
    distributionCode?: string;
  };

  type paymentCalcRenewRpaVoucherPriceGetParams = {
    /** 要续费的包时卡列表 */
    rpaVoucherIds: number;
    /** 按周续费的续费几周 */
    weekDuration: number;
    /** 按月续费的续费几月 */
    monthDuration: number;
    /** 优惠码 */
    distributionCode?: string;
  };

  type PublishFlowRequest = {
    /** 此版本发版说明 */
    description?: string;
    /** 同步升级共享给其它团队的流程定义与流程计划 */
    pushToSharedFlows?: boolean;
    rpaFlowId?: number;
    /** 同步升级团队内计划的流程版本 */
    upgradePlan?: boolean;
    /** 同步升级在当前团队内包含此流程定义的浏览器分身 */
    upgradeShop?: boolean;
  };

  type remoteRpaTriggerRpaCloudWorkerCalcGetParams = {
    /** groupKey */
    groupKey: string;
  };

  type RenewRpaVoucherRequest = {
    /** 已经勾选阅读和同意使用协议，没什么用 */
    agreement?: boolean;
    /** 余额抵扣金额 */
    balanceAmount?: number;
    /** 优惠码 */
    distributionCode?: string;
    distributionInfo?: DistributionInfo;
    /** 是否立即支付（点稍候支付该属性传false） */
    immediatePay?: boolean;
    /** 其中按月续费的续几月 */
    monthDuration?: number;
    payType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    rpaVoucherIds?: number[];
    /** 代金券抵扣金额，不得大于代金券余额 */
    voucherAmount?: number;
    /** 要使用的代金券id */
    voucherId?: number;
    /** 其中按周续费的续几周 */
    weekDuration?: number;
  };

  type RpaAvailableRegionVo = {
    city?: string;
    country?: string;
    price?: number;
    provider?:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    region?: string;
  };

  type rpaByRpaFlowIdCheckFlowBoundByPlanGetParams = {
    /** rpaFlowId */
    rpaFlowId: number;
  };

  type rpaByRpaFlowIdDraftPutParams = {
    /** rpaFlowId */
    rpaFlowId: number;
    /** version */
    version: string;
  };

  type rpaByRpaFlowIdFindSharingResultGetParams = {
    /** rpaFlowId */
    rpaFlowId: number;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type rpaByRpaFlowIdFindValidShareCodeByFlowIdGetParams = {
    /** rpaFlowId */
    rpaFlowId: number;
  };

  type rpaByRpaFlowIdGetLatestVersionGetParams = {
    /** rpaFlowId */
    rpaFlowId: number;
  };

  type rpaByRpaFlowIdGetLinkSubFlowLatestVersionGetParams = {
    /** rpaFlowId */
    rpaFlowId: number;
    /** subFlowId */
    subFlowId: number;
  };

  type rpaByRpaFlowIdGetVersionHisGetParams = {
    /** rpaFlowId */
    rpaFlowId: number;
  };

  type rpaByRpaFlowIdIsVersionExistsGetParams = {
    /** rpaFlowId */
    rpaFlowId: number;
    /** version */
    version: string;
  };

  type rpaByRpaFlowIdStopAFlowSharePutParams = {
    /** rpaFlowId */
    rpaFlowId: number;
    /** 要被删除的目标流程Id */
    targetFlowId: number;
  };

  type rpaByRpaFlowIdUpdateFlowConfigPostParams = {
    /** rpaFlowId */
    rpaFlowId: number;
  };

  type rpaByRpaFlowIdUpdateLinkMarketFlowVersionPutParams = {
    /** rpaFlowId */
    rpaFlowId: number;
    /** upgradePlan */
    upgradePlan?: boolean;
  };

  type rpaByRpaFlowIdUpdateSharedFlowVersionPutParams = {
    /** rpaFlowId */
    rpaFlowId: number;
    /** upgradePlan */
    upgradePlan?: boolean;
  };

  type rpaByRpaFlowShareCodeIdRemoveShareCodeDeleteParams = {
    /** rpaFlowShareCodeId */
    rpaFlowShareCodeId: number;
  };

  type rpaCheckFlowShareCodeGetParams = {
    /** shareCode */
    shareCode: string;
  };

  type rpaCloudExecutorContactPostParams = {
    /** App UUID */
    appId?: string;
    /** 主机名 */
    hostName?: string;
    /** 操作系统名 */
    osName?: string;
    /** CPU核数 */
    cpus?: number;
    /** 内存bytes */
    mem?: number;
    /** appVersion */
    appVersion?: string;
  };

  type rpaCloudRegionsGetParams = {
    /** flowId */
    flowId?: number;
  };

  type RpaConfig = {
    /** 该流程保存时的客户端版本号 */
    appVersion?: string;
    /** 是否归档 */
    archive?: boolean;
    /** 附件列表 */
    attachments?: liuchengfujian[];
    /** 当监听到浏览器退出时的动作。为空也表示 ExitTask */
    browserExitPolicy?: 'ExitTask' | 'Ignore';
    /** 打开分身浏览器的策略。兼容历史数据，为空或true都表示自动打开 */
    browserPolicy?: 'auto' | 'manually';
    /** 遇到alert等对话框时处理方式，为空表示取消，否则点确定。如果dialog是prompt，该字符串会当成值传递给prompt */
    dialogHandling?: string;
    /** 元素库列表 */
    elements?: Element[];
    events?: RpaEvent[];
    exitOnFail?: string;
    extra?: Record<string, any>;
    /** id */
    id?: string;
    imageForbiddenSize?: number;
    /** 分身策略 */
    itemPolicy?: 'dynamic' | 'manually';
    loadImage?: boolean;
    loadVideo?: boolean;
    /** 执行该流程所需要的最低客户端版本号，大版本号，如客户端是6.7.0.xxxx，那该值就是6.7。为空或0表示可以执行在任何版本的客户端 */
    minorVersion?: number;
    nodeInterval?: number;
    /** 节点是否开启拟人操作 */
    nodeSim?: boolean;
    nodeTimeout?: number;
    nodes?: Record<string, any>;
    paramGroups?: RpaParamGroup[];
    /** 用户自定义变量 */
    params?: RpaParam[];
    /** 插件列表 */
    plugins?: string[];
    /** 流程说明 */
    readme?: string;
    /** 将节点的错误日志重定向到哪里？debug | info | err | none:不输出，默认err */
    redirectNodeErr?: string;
    /** 代码库列表 */
    scripts?: Script[];
    /** 元素映射 */
    shadows?: DomShadow[];
    /** 执行时侧边栏显示策略。hide | show，为空表示hide。 */
    sidePanel?: string;
    /** 子流程定义，key即为子流程的sid */
    subFlows?: Record<string, any>;
    timeout?: number;
    /** 子流程类型 */
    type?: string;
    /** 分身最小化策略。alert | ignore，为空表示 alert */
    windowMinimizedPolicy?: string;
    windowPosition?: string;
    windowSize?: string;
  };

  type rpaDeleteGroupByGroupIdDeleteParams = {
    /** groupId */
    groupId: number;
  };

  type RpaEvent = {
    description?: string;
    /** 要执行的事件链的第一个节点的nid */
    header?: string;
    name?: string;
    params?: Record<string, any>;
  };

  type rpaFeesBindVoucherByRpaVoucherIdPutParams = {
    /** rpaVoucherId */
    rpaVoucherId: number;
    /** deviceId */
    deviceId: number;
  };

  type rpaFeesBuyRpaVouchersPostParams = {
    /** 已经勾选阅读和同意使用协议，没什么用 */
    agreement?: boolean;
    /** 到期是否自动续费 */
    autoRenew?: boolean;
    /** 余额抵扣金额 */
    balanceAmount?: number;
    baseCount?: number;
    count?: number;
    /** 优惠码 */
    distributionCode?: string;
    'distributionInfo.code.amount'?: number;
    'distributionInfo.code.code'?: string;
    'distributionInfo.code.createTime'?: string;
    'distributionInfo.code.description'?: string;
    'distributionInfo.code.discountId'?: number;
    'distributionInfo.code.distributionType'?: 'Deduction' | 'Discount' | 'Official';
    'distributionInfo.code.distributor'?: number;
    'distributionInfo.code.goodsType'?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    'distributionInfo.code.id'?: number;
    'distributionInfo.code.limited'?: boolean;
    'distributionInfo.code.name'?: string;
    'distributionInfo.code.systemDefault'?: boolean;
    'distributionInfo.code.usageCount'?: number;
    'distributionInfo.code.usedCount'?: number;
    'distributionInfo.code.valid'?: boolean;
    'distributionInfo.code.validDays'?: number;
    'distributionInfo.deductedPrice'?: number;
    'distributionInfo.drawPrice'?: number;
    duration?: number;
    /** 是否立即支付（点稍候支付该属性传false） */
    immediatePay?: boolean;
    payType?: 'AliPay' | 'BalancePay' | 'BankPay' | 'WechatPay';
    performanceCount?: number;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    /** 代金券抵扣金额，不得大于代金券余额 */
    voucherAmount?: number;
    /** 要使用的代金券id */
    voucherId?: number;
  };

  type rpaFeesCheckRunningTaskOnVoucherByRpaVoucherIdGetParams = {
    /** rpaVoucherId */
    rpaVoucherId: number;
  };

  type rpaFeesDeleteVouchersDeleteParams = {
    /** rpaVoucherIds */
    rpaVoucherIds: number;
  };

  type rpaFeesUnbindVoucherByRpaVoucherIdPutParams = {
    /** rpaVoucherId */
    rpaVoucherId: number;
  };

  type rpaFeesVoucherBindHisByRpaVoucherIdGetParams = {
    /** rpaVoucherId */
    rpaVoucherId: number;
  };

  type rpaFeesVoucherByRpaVoucherIdGetParams = {
    /** rpaVoucherId */
    rpaVoucherId: number;
  };

  type rpaFeesVouchersGetParams = {
    /** serialNumber */
    serialNumber?: string;
    /** pageNum */
    pageNum: number;
    /** pageSize */
    pageSize: number;
    /** startTimeFrom */
    startTimeFrom?: string;
    /** startTimeTo */
    startTimeTo?: string;
    /** 格式为[field_name asc|desc], create_time,goods_id,valid_end_time */
    orderBy?: string;
  };

  type rpaFetchFlowConfigTreeByConfigIdGetParams = {
    /** 注意是mongoId不是mysqlId */
    configId: string;
  };

  type RpaFileTriggerEventVo = {
    createTime?: string;
    extraParams?: Record<string, any>;
    fileEventType?: 'Created' | 'Deleted' | 'Modified';
    id?: number;
    planId?: number;
    rpaTaskId?: number;
    teamId?: number;
    triggerFile?: string;
    triggerId?: number;
  };

  type RpaFileTriggerVo = {
    createTime?: string;
    creatorId?: number;
    deviceId?: string;
    dir?: string;
    diskType?: 'LocalDisk' | 'TeamDisk' | 'UserDisk';
    fileExt?: string;
    id?: number;
    includeSub?: boolean;
    planId?: number;
    realPath?: string;
    teamId?: number;
    triggerType?: 'Email' | 'File' | 'Http' | 'Message';
    valid?: boolean;
    watchCreate?: boolean;
    watchDelete?: boolean;
    watchModify?: boolean;
  };

  type rpaFlowByRpaFlowIdCheckHasSubFlowsGetParams = {
    /** rpaFlowId */
    rpaFlowId: number;
  };

  type rpaFlowByRpaFlowIdDeleteParams = {
    /** rpaFlowId */
    rpaFlowId: number;
  };

  type rpaFlowByRpaFlowIdGetParams = {
    /** rpaFlowId */
    rpaFlowId: number;
  };

  type rpaFlowConfigByConfigIdGetParams = {
    /** 注意是mongoId不是mysqlId */
    configId: string;
  };

  type RpaFlowGroupVo = {
    description?: string;
    id?: number;
    name?: string;
    sortNumber?: number;
    teamId?: number;
  };

  type rpaFlowIsNameExistsGetParams = {
    /** name */
    name: string;
    /** 不为空表示排除某个flow */
    excludeId?: number;
  };

  type rpaFlowsGetParams = {
    /** 允许按名称模糊查找 */
    name?: string;
    /** 过滤流程创建类型，为空表示查询所有 */
    rpaFlowType?: 'FileCopy' | 'Manual' | 'Market' | 'MarketCopy' | 'Shared' | 'TkPack' | 'Tkshop';
    /** 按手机还是浏览器流程过滤，为空表示所有 */
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** groupId */
    groupId?: number;
    /** 格式为[field_name asc|desc], field_name must in id,name,create_time,update_time,exec_count,last_exec_time,create_type,sort_no */
    orderBy?: string;
  };

  type RpaFlowShareCodeVo = {
    allowPushUpdate?: boolean;
    allowRead?: boolean;
    createTime?: string;
    creatorId?: number;
    expiredTime?: string;
    id?: number;
    includeGroups?: boolean;
    shareCode?: string;
    teamId?: number;
    validMinutes?: number;
  };

  type RpaFlowShareVo = {
    allowPushUpdate?: boolean;
    allowRead?: boolean;
    creatorId?: number;
    creatorName?: string;
    shareCode?: string;
    shareTime?: string;
    targetFlowId?: number;
    teamId?: number;
    teamName?: string;
    /** 被分享流程当前的版本 */
    version?: string;
  };

  type RpaFlowVersionVo = {
    configId?: string;
    createTime?: string;
    description?: string;
    id?: number;
    numberVersion?: number;
    version?: string;
  };

  type RpaFlowVo = {
    allowPushUpdate?: boolean;
    /** 是否可读。针对分享流程和市场流程 */
    allowRead?: boolean;
    bizCode?: string;
    configId?: string;
    console?: boolean;
    createTime?: string;
    createType?: 'FileCopy' | 'Manual' | 'Market' | 'MarketCopy' | 'Shared' | 'TkPack' | 'Tkshop';
    creatorId?: number;
    description?: string;
    dirty?: boolean;
    /** 过期时间，仅针对引用市场流程 */
    expireTime?: string;
    /** 是否已过期，仅针对引用市场流程，根据 expireTime 计算得出来的 */
    expired?: boolean;
    extra?: Record<string, any>;
    flowShareCode?: string;
    groups?: RpaFlowGroupVo[];
    id?: number;
    /** 对应的市场模板ID */
    marketId?: number;
    /** 如果是市场流程，显示市场流程的最新版本 */
    marketLatestVersion?: string;
    name?: string;
    nameBrief?: string;
    /** 数字版本号，会从1开始累加 */
    numberVersion?: number;
    platforms?: RpaPlatformVo[];
    publishTime?: string;
    /** 流程类型，分手机和browser */
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    sessionInner?: boolean;
    shareFromTeamId?: number;
    shareFromTeamName?: string;
    /** 如果是分享过来的流程，显示被分享的流程最新的版本 */
    shareLatestVersion?: string;
    /** 不为空表示是他人分享的流程 */
    sharedFlowId?: number;
    shopId?: number;
    sortNo?: number;
    status?: 'Draft' | 'Published';
    supportConcurrent?: boolean;
    /** 团队ID; */
    teamId?: number;
    teamName?: string;
    tkFlowId?: number;
    updateTime?: string;
    version?: string;
  };

  type rpaGetAttachmentLinkGetParams = {
    /** attachmentPath */
    attachmentPath: string;
    /** contentDisposition */
    contentDisposition?: string;
    /** contentType */
    contentType?: string;
  };

  type rpaGetRpaFlowByBizCodeGetParams = {
    /** bizCode */
    bizCode: string;
  };

  type RpaHttpTriggerEventVo = {
    clientToken?: string;
    createTime?: string;
    extraParams?: Record<string, any>;
    httpMethod?: string;
    id?: number;
    planId?: number;
    remoteIp?: string;
    requestId?: string;
    rpaTaskId?: number;
    teamId?: number;
    token?: string;
    triggerId?: number;
    userAgent?: string;
  };

  type RpaHttpTriggerResponseVo = {
    event?: RpaHttpTriggerEventVo;
    requestId?: string;
    task?: RpaSimpleHisVo;
  };

  type rpaIsGroupNameExistsGetParams = {
    /** name */
    name: string;
    /** 分组改名时用到，检查重复时排除自己 */
    excludeId?: number;
  };

  type rpaMarketBuyMarketFlowPutParams = {
    /** marketFlowId */
    marketFlowId: number;
    /** 按周，月，买断购买 */
    periodUnit:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
  };

  type rpaMarketByMarketFlowIdGetParams = {
    /** marketFlowId */
    marketFlowId: number;
  };

  type rpaMarketByMarketFlowIdImagesGetParams = {
    /** marketFlowId */
    marketFlowId: number;
  };

  type rpaMarketFindBuyHisGetParams = {
    /** marketFlowId */
    marketFlowId: number;
  };

  type rpaMarketListGetParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** 流程类别，为空表示全部类别 */
    category?: 'Demo' | 'Ecommerce' | 'Others' | 'Payment' | 'Social' | 'TikTok';
    /** 手机还是浏览器流程，为空表示所有 */
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    /** 只查找已发布的流程 */
    publishedOnly?: boolean;
    /** q */
    q?: string;
  };

  type rpaMarketRenewMarketFlowPutParams = {
    /** marketFlowId */
    marketFlowId: number;
    /** 按周，月，买断续费 */
    periodUnit:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
  };

  type RpaMessageTriggerVo = {
    createTime?: string;
    creatorId?: number;
    id?: number;
    messageId?: string;
    planId?: number;
    teamId?: number;
    triggerType?: 'Email' | 'File' | 'Http' | 'Message';
    valid?: boolean;
  };

  type RpaMobileInfo = {
    connectType?: 'ARMCLOUD' | 'Baidu' | 'QCloud' | 'USB' | 'WIFI';
    description?: string;
    deviceId?: string;
    id?: number;
    itemId?: number;
    name?: string;
    /** 从哪台手机联营而来 */
    parentMobileId?: number;
    status?: 'EXPIRED' | 'OFFLINE' | 'ONLINE' | 'RESETING';
    udid?: string;
  };

  type RpaNode = {
    description?: string;
    /** 该节点是否禁用，禁用节点不参与执行 */
    disabled?: boolean;
    exitOnFail?: string;
    iframe?: string;
    /** 当前节点执行前的间隔时间 */
    interval?: number;
    logLevel?: string;
    logMsg?: string;
    name?: string;
    /** 下一节点的nid */
    next?: string;
    /** 其它的非公用的属性 */
    props?: Record<string, any>;
    /** 将节点的错误日志重定向到哪里？inherit:继续自流程 | debug | info | err | none:不输出。默认inherit */
    redirectNodeErr?: string;
    script?: string;
    sim?: boolean;
    /** 当前节点执行超时时间，单位秒，0表示不超时，空表示继承全局配置 */
    timeout?: number;
    type?: string;
  };

  type RpaParam = {
    description?: string;
    extra?: Record<string, any>;
    label?: string;
    name?: string;
    predefine?: boolean;
    /** 是否必填字段 */
    required?: boolean;
    /** 是否敏感字段 */
    sensitive?: boolean;
    type?: string;
    val?: Record<string, any>;
    validVals?: Record<string, any>[];
  };

  type RpaParamGroup = {
    extra?: Record<string, any>;
    name?: string;
    params?: RpaParam[];
  };

  type rpaPlanBatchToggleEnableScheduleGetParams = {
    /** planIds */
    planIds: number;
    /** enableSchedule */
    enableSchedule: boolean;
  };

  type rpaPlanByPlanIdGroupPutParams = {
    /** planId */
    planId: number;
    /** 不传入时，从分组移除 */
    groupId?: number;
  };

  type rpaPlanByRpaPlanIdCopyPostParams = {
    /** rpaPlanId */
    rpaPlanId: number;
  };

  type rpaPlanByRpaPlanIdDeleteParams = {
    /** rpaPlanId */
    rpaPlanId: number;
  };

  type rpaPlanByRpaPlanIdFindPlanSchedulersGetParams = {
    /** rpaPlanId */
    rpaPlanId: number;
  };

  type rpaPlanByRpaPlanIdGetParamFileSignUrlGetParams = {
    /** rpaPlanId */
    rpaPlanId: number;
  };

  type rpaPlanByRpaPlanIdGetParams = {
    /** rpaPlanId */
    rpaPlanId: number;
    /** shopPageNum */
    shopPageNum?: number;
    /** shopPageSize */
    shopPageSize?: number;
  };

  type rpaPlanByRpaPlanIdHisGetParams = {
    /** rpaPlanId */
    rpaPlanId: number;
    /** n */
    n?: number;
  };

  type rpaPlanByRpaPlanIdModifyPostParams = {
    /** rpaPlanId */
    rpaPlanId: number;
  };

  type rpaPlanByRpaPlanIdParamsGetParams = {
    /** rpaPlanId */
    rpaPlanId: number;
    /** shopPageNum */
    shopPageNum?: number;
    /** shopPageSize */
    shopPageSize?: number;
  };

  type rpaPlanByRpaPlanIdPausePostParams = {
    /** rpaPlanId */
    rpaPlanId: number;
    /** paused */
    paused: boolean;
  };

  type rpaPlanByRpaPlanIdToggleSchedulePutParams = {
    /** rpaPlanId */
    rpaPlanId: number;
    /** enableSchedule */
    enableSchedule: boolean;
  };

  type rpaPlanByRpaPlanIdUpdateBasicPostParams = {
    /** rpaPlanId */
    rpaPlanId: number;
  };

  type rpaPlanByRpaPlanIdUpdateParamsPostParams = {
    /** rpaPlanId */
    rpaPlanId: number;
  };

  type rpaPlanByRpaPlanIdUpdatePolicyPostParams = {
    /** rpaPlanId */
    rpaPlanId: number;
  };

  type rpaPlanByRpaPlanIdUpdateShopsPostParams = {
    /** rpaPlanId */
    rpaPlanId: number;
  };

  type rpaPlanByRpaPlanIdUpdateToLatestFlowVersionPutParams = {
    /** rpaPlanId */
    rpaPlanId: number;
  };

  type rpaPlanCheckNameExistsGetParams = {
    /** name */
    name: string;
    /** groupId */
    groupId?: number;
  };

  type rpaPlanDeletePlansDeleteParams = {
    /** planIds */
    planIds: number;
  };

  type RpaPlanEntry = {
    extra?: string;
    id?: number;
    /** 元信息，用来恢复mysql */
    meta?: RpaPlanVo;
    /** 流程参数 */
    params?: Record<string, any>;
    /** 调度列表 */
    schedulers?: RpaPlanSchedulerVo[];
    /** 允许导入的时候指定多个账号 */
    shopIds?: number[];
    /** 触发器，todo */
    triggers?: RpaTriggerDto[];
  };

  type rpaPlanExportPlansGetParams = {
    /** planIds */
    planIds: number;
  };

  type RpaPlanExports = {
    /** 描述，保留字段 */
    description?: string;
    /** 导出时间 */
    exportTime?: string;
    extra?: Record<string, any>;
    plans?: RpaPlanEntry[];
  };

  type RpaPlanGroup = {
    createTime?: string;
    creatorId?: number;
    deviceId?: string;
    groupName?: string;
    id?: number;
    paused?: boolean;
    runOnCloud?: boolean;
    teamId?: number;
    timezone?: string;
  };

  type rpaPlanGroupByGroupIdDeleteParams = {
    /** groupId */
    groupId: number;
  };

  type rpaPlanGroupByGroupIdGetParams = {
    /** groupId */
    groupId: number;
  };

  type rpaPlanGroupByGroupIdNamePutParams = {
    /** groupId */
    groupId: number;
    /** name */
    name: string;
  };

  type rpaPlanGroupByGroupIdPausePutParams = {
    /** groupId */
    groupId: number;
    /** paused */
    paused: boolean;
  };

  type rpaPlanGroupByGroupIdTimezonePutParams = {
    /** groupId */
    groupId: number;
    /** timezone */
    timezone: string;
  };

  type rpaPlanGroupPostParams = {
    /** groupName */
    groupName: string;
    /** runOnCloud */
    runOnCloud: boolean;
    /** deviceId */
    deviceId?: string;
    /** timezone */
    timezone?: string;
  };

  type RpaPlanGroupVo = {
    createTime?: string;
    creatorId?: number;
    deviceId?: string;
    groupName?: string;
    id?: number;
    loginDevice?: LoginDeviceDto;
    paused?: boolean;
    planCount?: number;
    runOnCloud?: boolean;
    shops?: ShopWithChannelsVo[];
    teamId?: number;
    timezone?: string;
  };

  type rpaPlanList2GetParams = {
    /** systemType */
    systemType?: 'normal' | 'tk';
    /** 转换为目标时区 */
    timezone?: string;
    /** shopId */
    shopId?: number;
    /** flowId */
    flowId?: number;
    /** 按分组过滤。不传时，查询未分组计划 */
    groupId?: number;
    /** 按创建者ID进行过滤 */
    creatorId?: number;
  };

  type rpaPlanListGetParams = {
    /** 允许按名称模糊查找 */
    name?: string;
    /** 按创建者ID进行过滤 */
    creatorId?: number;
    /** systemType */
    systemType?: 'normal' | 'tk';
    /** clientId */
    clientId?: string;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** 格式为[field_name asc|desc], id,name,create_time,update_time,creator_id,plan_type,enable_schedule */
    orderBy?: string;
  };

  type rpaPlanManualRunPlanPostParams = {
    /** token */
    token: string;
  };

  type rpaPlanManualTriggerPlanByRpaPlanIdPostParams = {
    /** rpaPlanId */
    rpaPlanId: number;
  };

  type rpaPlanPrepareNameGetParams = {
    /** name */
    name: string;
  };

  type rpaPlanRunByRpaPlanIdPostParams = {
    /** rpaPlanId */
    rpaPlanId: number;
    /** token */
    token: string;
  };

  type RpaPlanSchedulerVo = {
    createTime?: string;
    id?: number;
    planId?: number;
    scheduleType?: 'Duration' | 'Fixed' | 'Temp';
    teamId?: number;
    triggerCron?: string;
    triggerEndCron?: string;
  };

  type rpaPlanSuggestImportPlanFlowsGetParams = {
    /** originRpaFlows */
    originRpaFlows: number;
  };

  type RpaPlanVo = {
    /** 是否允许他人编辑此计划 */
    allowOthersEdit?: boolean;
    /** 当账号的浏览器已打开时的策略：stop | reopen | reuse */
    caseBrowserOpened?: string;
    city?: string;
    /** 调度到指定客户端执行，为空表示选任一个 */
    clientId?: string;
    /** 流程结束后是否关闭账号浏览器：close | keep */
    closeBrowserOnEnd?: string;
    cloudInstanceId?: number;
    concurrent?: number;
    /** 并发等待时间间隔 */
    concurrentDelay?: number;
    country?: string;
    createTime?: string;
    creatorId?: number;
    /** 调度表达式，例如：[30 15 15 ? * MON,SUN,FRI] 见https://www.bejson.com/othertools/cron/ */
    cronExpression?: string;
    description?: string;
    deviceInfo?: Record<string, any>;
    /** 间隔时间（只有循环任务才有意义） */
    duration?: number;
    enableSchedule?: boolean;
    flowBizCode?: string;
    flowConfigId?: string;
    flowId?: number;
    flowName?: string;
    /** 流程对应的版本号 */
    flowVersion?: string;
    forceRecord?: boolean;
    groupId?: number;
    /** 是否以无头模式运行 */
    headless?: boolean;
    id?: number;
    /** 分身策略 */
    itemPolicy?: 'dynamic' | 'manually';
    /** 相应流程的最新版本 */
    latestVersion?: string;
    /** 窗口布局配置,json格式 */
    layoutConfig?: string;
    /** 该计划最大执行时长，单位分钟。相应的流程如果执行超过这个值会被强制结束。为空或为0表示不限制 */
    maxMinutes?: number;
    name?: string;
    /** 如果是自动或循环计划，该字段为下次触发时间 */
    nextFireTime?: string;
    /** 他人执行此流程的策略。 为空表示只能创建者可以执行，非空可选值为：creator | others | creator_others */
    othersRunPolicy?: string;
    /** 变量定义文件路径;user_disk:// 或者 team_disk:// 开头才有效 */
    paramFilePath?: string;
    paused?: boolean;
    planEventDelay?: number;
    planEventName?: string;
    planType?: 'Auto' | 'Loop' | 'Manual' | 'Trigger';
    /** 是否自动弹出流程日志窗口，为空表示false */
    popupTaskLog?: boolean;
    provider?:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    region?: string;
    /** 流程类型，分手机和browser */
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    runOnCloud?: boolean;
    shops?: ItemShopInfo[];
    /** 是否显示鼠标轨迹 */
    showMouseTrack?: boolean;
    /** 执行时侧边栏显示策略。hide | show，为空表示继承流程配置 */
    sidePanel?: string;
    snapshot?: 'Node' | 'Not' | 'OnFail';
    snapshotScope?: 'fullPage' | 'pdf' | 'viewport' | 'window';
    startEventDelay?: number;
    startEventName?: string;
    /** 遇到错误是否退出循环任务（只有循环任务才有意义） */
    stopOnError?: boolean;
    systemType?: 'normal' | 'tk';
    /** 任务命名规则 */
    taskNameEl?: string;
    teamId?: number;
    timezone?: string;
    /** 该计划总账号个数 */
    totalShopCount?: number;
    /** 计划结束后是否触发一个计划事件 */
    triggerPlanEvent?: boolean;
    /** 计划开始后是否触发一个计划事件 */
    triggerStartEvent?: boolean;
    triggers?: RpaTriggerDto[];
    updateTime?: string;
    zonedTime?: LocalTime;
  };

  type RpaPlatformVo = {
    flowId?: number;
    platformName?: string;
  };

  type RpaRunDeviceVo = {
    appId?: string;
    cpus?: number;
    createTime?: string;
    currentDevice?: boolean;
    deviceId?: string;
    deviceType?: 'App' | 'Browser' | 'Extension' | 'HYRuntime' | 'RpaExecutor';
    hostName?: string;
    id?: number;
    lastActiveTime?: string;
    lastLoginTime?: string;
    mem?: number;
    online?: boolean;
    osName?: string;
    price?: number;
    scope?: 'Console' | 'Ipp' | 'Openapi' | 'Partner' | 'Portal';
    userAgent?: string;
    userId?: number;
    vouchers?: RpaVoucherVo[];
  };

  type RpaShopInfo = {
    account?: string;
    description?: string;
    id?: number;
    itemId?: number;
    name?: string;
    platform?: ShopPlatformVo;
  };

  type RpaShopPasswordVo = {
    actionUrl?: string;
    blacklistedByUser?: number;
    dateCreated?: number;
    id?: number;
    originUrl?: string;
    passwordElement?: string;
    passwordType?: number;
    passwordValue?: string;
    platformId?: number;
    scheme?: number;
    shopId?: number;
    signonRealm?: string;
    teamId?: number;
    updateTime?: string;
    usernameElement?: string;
    usernameValue?: string;
  };

  type RpaSimpleHisVo = {
    creatorId?: number;
    /** 是否结束 */
    done?: boolean;
    /** #see RpaFailReason.xxx */
    errorCode?: number;
    errorMsg?: string;
    /** 执行者身份。历史数据访字段为空，展示的时候使用creatorId */
    executorId?: number;
    failedItems?: number;
    id?: number;
    manualRun?: boolean;
    name?: string;
    planId?: number;
    planName?: string;
    /** 流程类型，分手机和browser */
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    status?:
      | 'Cancelled'
      | 'CreateFailed'
      | 'Ended'
      | 'Ended_All_Failed'
      | 'Ended_Partial_Failed'
      | 'Ignored'
      | 'NotStart'
      | 'Running'
      | 'ScheduleCancelled'
      | 'Scheduled'
      | 'Scheduling'
      | 'UnusualEnded';
    successItems?: number;
    teamId?: number;
    totalItems?: number;
  };

  type RpaSubConfig = {
    /** 该流程保存时的客户端版本号 */
    appVersion?: string;
    /** 是否归档 */
    archive?: boolean;
    /** 附件列表 */
    attachments?: liuchengfujian[];
    /** 当监听到浏览器退出时的动作。为空也表示 ExitTask */
    browserExitPolicy?: 'ExitTask' | 'Ignore';
    /** 打开分身浏览器的策略。兼容历史数据，为空或true都表示自动打开 */
    browserPolicy?: 'auto' | 'manually';
    createTime?: string;
    description?: string;
    /** 遇到alert等对话框时处理方式，为空表示取消，否则点确定。如果dialog是prompt，该字符串会当成值传递给prompt */
    dialogHandling?: string;
    /** 元素库列表 */
    elements?: Element[];
    events?: RpaEvent[];
    exitOnFail?: string;
    extra?: Record<string, any>;
    /** id */
    id?: string;
    imageForbiddenSize?: number;
    /** 分身策略 */
    itemPolicy?: 'dynamic' | 'manually';
    loadImage?: boolean;
    loadVideo?: boolean;
    /** 执行该流程所需要的最低客户端版本号，大版本号，如客户端是6.7.0.xxxx，那该值就是6.7。为空或0表示可以执行在任何版本的客户端 */
    minorVersion?: number;
    name?: string;
    nodeInterval?: number;
    /** 节点是否开启拟人操作 */
    nodeSim?: boolean;
    nodeTimeout?: number;
    nodes?: Record<string, any>;
    paramGroups?: RpaParamGroup[];
    /** 用户自定义变量 */
    params?: RpaParam[];
    /** 插件列表 */
    plugins?: string[];
    /** 流程说明 */
    readme?: string;
    /** 将节点的错误日志重定向到哪里？debug | info | err | none:不输出，默认err */
    redirectNodeErr?: string;
    /** 子流程所对应的流程id，可能为空 */
    refFlowId?: number;
    /** 子流程版本号，可能为空 */
    refFlowVersion?: string;
    /** 代码库列表 */
    scripts?: Script[];
    /** 元素映射 */
    shadows?: DomShadow[];
    /** 执行时侧边栏显示策略。hide | show，为空表示hide。 */
    sidePanel?: string;
    /** 子流程定义，key即为子流程的sid */
    subFlows?: Record<string, any>;
    timeout?: number;
    /** 子流程类型 */
    type?: string;
    /** 分身最小化策略。alert | ignore，为空表示 alert */
    windowMinimizedPolicy?: string;
    windowPosition?: string;
    windowSize?: string;
  };

  type rpaTaskAiCompletionPostParams = {
    /** rpaTaskId */
    rpaTaskId?: number;
  };

  type rpaTaskAiImageUnderstandingPostParams = {
    /** rpaTaskId */
    rpaTaskId?: number;
  };

  type rpaTaskByRpaTaskFileIdUpdateFileSizePutParams = {
    /** rpaTaskFileId */
    rpaTaskFileId: number;
    /** filesize */
    filesize: number;
  };

  type rpaTaskByRpaTaskIdByRpaTaskItemIdParamsGetParams = {
    /** rpaTaskId */
    rpaTaskId: number;
    /** rpaTaskItemId */
    rpaTaskItemId: number;
  };

  type rpaTaskByRpaTaskIdByTaskItemIdRemoveTaskItemFilesDeleteParams = {
    /** rpaTaskId */
    rpaTaskId: number;
    /** taskItemId */
    taskItemId: number;
    /** 以逗号分隔开的文件id: 1,2,3 。如果不传会删除该item下所有的文件 */
    fileIds?: string;
  };

  type rpaTaskByRpaTaskIdCancelTaskSchedulingPostParams = {
    /** rpaTaskId */
    rpaTaskId: number;
  };

  type rpaTaskByRpaTaskIdEnvironmentsGetParams = {
    /** rpaTaskId */
    rpaTaskId: number;
  };

  type rpaTaskByRpaTaskIdForceEndPutParams = {
    /** rpaTaskId */
    rpaTaskId: number;
    /** stopPostRun */
    stopPostRun?: boolean;
  };

  type rpaTaskByRpaTaskIdGetParams = {
    /** rpaTaskId */
    rpaTaskId: number;
  };

  type rpaTaskByRpaTaskIdGetTaskPredefineParamsGetParams = {
    /** rpaTaskId */
    rpaTaskId: number;
  };

  type rpaTaskByRpaTaskIdItemsGetParams = {
    /** rpaTaskId */
    rpaTaskId: number;
  };

  type rpaTaskByRpaTaskIdLockFilePutParams = {
    /** rpaTaskId */
    rpaTaskId: number;
    /** fileLocked */
    fileLocked: boolean;
  };

  type rpaTaskByRpaTaskIdMarkTaskEndPutParams = {
    /** rpaTaskId */
    rpaTaskId: number;
  };

  type rpaTaskByRpaTaskIdRemoveTaskFilesDeleteParams = {
    /** rpaTaskId */
    rpaTaskId: number;
  };

  type rpaTaskByRpaTaskIdShopsInfoGetParams = {
    /** rpaTaskId */
    rpaTaskId: number;
  };

  type rpaTaskByRpaTaskItemIdFindRpaSessionsGetParams = {
    /** rpaTaskItemId */
    rpaTaskItemId: number;
  };

  type rpaTaskByRpaTaskItemIdSignatureGetParams = {
    /** rpaTaskItemId */
    rpaTaskItemId: number;
  };

  type rpaTaskByShopIdFindPreviewTaskShopInfoGetParams = {
    /** shopId */
    shopId: number;
  };

  type rpaTaskCaptchaCheckRoomByRoomNameGetParams = {
    /** roomName */
    roomName: string;
  };

  type rpaTaskCaptchaCloseRoomByRoomNameDeleteParams = {
    /** roomName */
    roomName: string;
  };

  type rpaTaskCaptchaCreateRoomByRoomNamePutParams = {
    /** roomName */
    roomName: string;
    /** duration */
    duration: number;
  };

  type rpaTaskChangePauseStatusByRpaTaskIdPutParams = {
    /** rpaTaskId */
    rpaTaskId: number;
    /** itemId，为空表示改变整个流程的状态 */
    rpaTaskItemId?: number;
    /** paused */
    paused: boolean;
  };

  type rpaTaskCountNotEndByBizCodeGetParams = {
    /** bizCode */
    bizCode: string;
    /** shopId */
    shopId?: number;
  };

  type rpaTaskDbAppendToSetByKeyPutParams = {
    /** key */
    key: string;
    /** scope */
    scope: string;
    /** rpaFlowId */
    rpaFlowId?: number;
  };

  type rpaTaskDbDelByKeyDeleteParams = {
    /** key */
    key: string;
    /** scope */
    scope: string;
    /** rpaFlowId */
    rpaFlowId?: number;
  };

  type rpaTaskDbGetByKeyGetParams = {
    /** key */
    key: string;
    /** scope */
    scope: string;
    /** rpaFlowId */
    rpaFlowId?: number;
  };

  type rpaTaskDbListGetParams = {
    /** scope */
    scope: string;
    /** rpaFlowId */
    rpaFlowId?: number;
  };

  type rpaTaskDbPutByKeyPutParams = {
    /** key */
    key: string;
    /** scope */
    scope: string;
    /** rpaFlowId */
    rpaFlowId?: number;
  };

  type rpaTaskDetectRpaPluginsGetParams = {
    /** rpaFlowId */
    rpaFlowId?: number;
  };

  type rpaTaskDevicesGetParams = {
    /** 是否在线，不设置返回全部 */
    online?: boolean;
    /** flowId */
    flowId?: number;
  };

  type RpaTaskFileVo = {
    fileName?: string;
    fileType?: 'Data' | 'Log' | 'Record' | 'Screenshot' | 'Unknown' | 'Upload';
    id?: number;
    size?: number;
    status?: 'Deleted' | 'Undefined' | 'Valid';
    taskId?: number;
    taskItemId?: number;
    teamId?: number;
  };

  type rpaTaskFindItemByRpaTaskItemIdGetParams = {
    /** rpaTaskItemId */
    rpaTaskItemId: number;
  };

  type rpaTaskFindPreviewTaskMobileInfoGetParams = {
    /** mobileId */
    mobileId: number;
  };

  type rpaTaskFindShopsByPlatformGetParams = {
    /** platform */
    platform: string;
  };

  type rpaTaskFindShopsByTagNameGetParams = {
    /** tagName */
    tagName: string;
  };

  type rpaTaskForceEndByBizCodePutParams = {
    /** bizCode */
    bizCode: string;
    /** stopPostRun */
    stopPostRun?: boolean;
  };

  type rpaTaskGeneMouseTracksGetParams = {
    /** x1 */
    x1: number;
    /** y1 */
    y1: number;
    /** x2 */
    x2: number;
    /** y2 */
    y2: number;
  };

  type rpaTaskGetLatestTaskItemGetParams = {
    /** bizCode */
    bizCode: string;
    /** shopId */
    shopId: number;
  };

  type rpaTaskHeartbeatByRpaTaskIdPutParams = {
    /** rpaTaskId */
    rpaTaskId: number;
  };

  type RpaTaskHeartbeatRequest = {
    liveItems?: number[];
    rpaTaskId?: number;
  };

  type rpaTaskHeartbeatV2ByRpaTaskIdPutParams = {
    /** rpaTaskId */
    rpaTaskId: number;
  };

  type rpaTaskItemAppendLogPutParams = {
    /** rpaTaskItemId */
    rpaTaskItemId: number;
  };

  type rpaTaskItemByRpaTaskItemIdFilesGetParams = {
    /** rpaTaskItemId */
    rpaTaskItemId: number;
  };

  type rpaTaskItemByRpaTaskItemIdGetLogFileInfoGetParams = {
    /** rpaTaskItemId */
    rpaTaskItemId: number;
  };

  type rpaTaskItemByRpaTaskItemIdRanNodesGetParams = {
    /** rpaTaskItemId */
    rpaTaskItemId: number;
  };

  type rpaTaskItemByShopIdPasswordsGetParams = {
    /** shopId */
    shopId: number;
    /** domain */
    domain: string;
  };

  type RpaTaskItemVo = {
    baseDir?: string;
    bucketId?: number;
    errorMsg?: string;
    executeEndTime?: string;
    executeTime?: string;
    id?: number;
    /** 流程类型，分手机和browser */
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    sessionId?: number;
    /** 带上相应的店铺信息 */
    shop?: ItemShopInfo;
    shopId?: number;
    status?:
      | 'Cancelled'
      | 'CreateFailed'
      | 'Ended'
      | 'Ended_All_Failed'
      | 'Ended_Partial_Failed'
      | 'Ignored'
      | 'NotStart'
      | 'Running'
      | 'ScheduleCancelled'
      | 'Scheduled'
      | 'Scheduling'
      | 'UnusualEnded';
    success?: boolean;
    taskId?: number;
    teamId?: number;
    type?: 'postrun' | 'prerun' | 'shop';
  };

  type rpaTaskListGetParams = {
    /** 允许按流程过滤 */
    rpaFlowId?: number;
    /** 允许按名称模糊查找 */
    name?: string;
    /** 允许按计划查找 */
    rpaPlanId?: number;
    /** 按状态过滤 */
    status?:
      | 'Cancelled'
      | 'CreateFailed'
      | 'Ended'
      | 'Ended_All_Failed'
      | 'Ended_Partial_Failed'
      | 'Ignored'
      | 'NotStart'
      | 'Running'
      | 'ScheduleCancelled'
      | 'Scheduled'
      | 'Scheduling'
      | 'UnusualEnded';
    /** 开始时间起 */
    createTimeFrom?: string;
    /** 开始时间止 */
    createTimeTo?: string;
    /** 执行设置的deviceId */
    deviceId?: string;
    /** 优先级比mobileId高，有shopId时mobileId无意义 */
    shopId?: number;
    /** mobileId */
    mobileId?: number;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** 格式为[field_name asc|desc], create_time,execute_end_time,name,flow_id,creator_id,executor_id,status,device_name */
    orderBy?: string;
  };

  type RpaTaskNodeVo = {
    error?: string;
    nid?: string;
    success?: boolean;
  };

  type rpaTaskNotifyAllItemsEndPostParams = {
    /** rpaTaskId */
    rpaTaskId: number;
  };

  type rpaTaskNotifyAllShopsEndPostParams = {
    /** rpaTaskId */
    rpaTaskId: number;
  };

  type rpaTaskParseToShopIdsGetParams = {
    /** rpaType */
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    /** by */
    by: string;
    /** items */
    items: string;
  };

  type RpaTaskReport = {
    commonParams?: Record<string, any>;
    environments?: Record<string, any>;
    /** id */
    id?: string;
    taskId?: number;
    teamId?: number;
  };

  type rpaTaskReportOpenShopSessionPostParams = {
    /** rpaTaskItemId */
    rpaTaskItemId: number;
    /** sessionId */
    sessionId?: number;
    /** shopSessionId */
    shopSessionId?: number;
  };

  type rpaTaskRunTaskPostParams = {
    /** token */
    token: string;
  };

  type rpaTaskSignDiskFileGetParams = {
    /** file */
    file: string;
  };

  type rpaTaskSignFileByRpaTaskItemIdRpaTaskFileIdGetParams = {
    /** rpaTaskItemId */
    rpaTaskItemId: number;
    /** rpaTaskFileId */
    rpaTaskFileId: number;
  };

  type RpaTaskVo = {
    /** 当账号的浏览器已打开时的策略：stop | reopen | reuse */
    caseBrowserOpened?: string;
    city?: string;
    clientId?: string;
    clientIp?: string;
    /** 流程结束后是否关闭账号浏览器：close | keep */
    closeBrowserOnEnd?: string;
    cloudInstanceId?: number;
    concurrent?: number;
    /** 并发等待时间间隔 */
    concurrentDelay?: number;
    configId?: string;
    console?: boolean;
    country?: string;
    createTime?: string;
    createType?: 'FileCopy' | 'Manual' | 'Market' | 'MarketCopy' | 'Shared' | 'TkPack' | 'Tkshop';
    creatorId?: number;
    /** 扣掉了多少个花瓣 */
    credit?: number;
    creditDetailId?: number;
    creditDetailSerialNumber?: string;
    description?: string;
    deviceName?: string;
    /** 是否结束 */
    done?: boolean;
    /** #see RpaFailReason.xxx */
    errorCode?: number;
    errorMsg?: string;
    executeEndTime?: string;
    executeTime?: string;
    /** 执行者身份。历史数据访字段为空，展示的时候使用creatorId */
    executorId?: number;
    failedItems?: number;
    fileLocked?: boolean;
    /** 总文件大小，不包括.log文件 */
    fileSize?: number;
    fileStatus?: 'Deleted' | 'Undefined' | 'Valid';
    flowId?: number;
    flowName?: string;
    flowVersion?: string;
    forceRecord?: boolean;
    headless?: boolean;
    id?: number;
    /** 窗口布局配置,json格式 */
    layoutConfig?: string;
    manualRun?: boolean;
    name?: string;
    planId?: number;
    planName?: string;
    /** 计划类型（如果是计划触发） */
    planType?: 'Auto' | 'Loop' | 'Manual' | 'Trigger';
    /** 是否自动弹出流程日志窗口，为空表示false */
    popupTaskLog?: boolean;
    preview?: boolean;
    /** 消耗单价:花瓣/分钟 */
    price?: number;
    provider?:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    region?: string;
    /** 流程类型，分手机和browser */
    rpaType?: 'Browser' | 'Extension' | 'IOS' | 'Mobile';
    rpaVoucherId?: number;
    runOnCloud?: boolean;
    shops?: ItemShopInfo[];
    /** 是否显示鼠标轨迹 */
    showMouseTrack?: boolean;
    /** 执行时侧边栏显示策略。hide | show，为空表示继承流程配置 */
    sidePanel?: string;
    snapshot?: 'Node' | 'Not' | 'OnFail';
    status?:
      | 'Cancelled'
      | 'CreateFailed'
      | 'Ended'
      | 'Ended_All_Failed'
      | 'Ended_Partial_Failed'
      | 'Ignored'
      | 'NotStart'
      | 'Running'
      | 'ScheduleCancelled'
      | 'Scheduled'
      | 'Scheduling'
      | 'UnusualEnded';
    successItems?: number;
    teamId?: number;
    totalItems?: number;
    /** 触发类型 */
    triggerType?: 'Email' | 'File' | 'Http' | 'Loop' | 'Message' | 'Schedule';
  };

  type RpaTriggerDto = {
    createTime?: string;
    creatorId?: number;
    id?: number;
    planId?: number;
    teamId?: number;
    triggerType?: 'Email' | 'File' | 'Http' | 'Message';
    valid?: boolean;
  };

  type rpaTriggerFileEventPostParams = {
    /** triggerId */
    triggerId: number;
    /** eventType */
    eventType: 'Created' | 'Deleted' | 'Modified';
    /** file */
    file: string;
  };

  type rpaTriggerHookEventGetParams = {
    /** requestId */
    requestId: string;
  };

  type rpaTriggerHookGetParams = {
    /** token */
    token: string;
    /** clientToken */
    clientToken?: string;
    /** format */
    format?: string;
    /** deviceId */
    deviceId?: string;
  };

  type rpaTriggerHookPostParams = {
    /** token */
    token: string;
    /** clientToken */
    clientToken?: string;
    /** format */
    format?: string;
    /** deviceId */
    deviceId?: string;
  };

  type RpaVoucherHisVo = {
    bindTime?: string;
    creatorId?: number;
    creatorName?: string;
    deviceCode?: string;
    deviceId?: number;
    deviceName?: string;
    id?: number;
    rpaVoucherId?: number;
    teamId?: number;
    unbindTime?: string;
  };

  type RpaVoucherVo = {
    autoRenew?: boolean;
    bornType?: 'PartnerGiving' | 'TeamBuy' | 'Unknown';
    createTime?: string;
    creatorId?: number;
    creatorName?: string;
    deleted?: boolean;
    device?: LoginDeviceDto;
    deviceId?: number;
    goodsId?: number;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    maxCpuCount?: number;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    renewPrice?: number;
    rpaVoucherStatus?: 'Expired' | 'Valid';
    serialNumber?: string;
    teamId?: number;
    validEndTime?: string;
    validStartTime?: string;
  };

  type RunPlanRequest = {
    deviceId?: string;
    /** 以谁的身份去执行 */
    executorId?: number;
    formId?: string;
    name?: string;
    /** 创建一个task时用来指定变量值。如果指定的key不在流程定义里会被忽略 */
    params?: Record<string, any>;
    /** 云端执行的云厂商 */
    provider?:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    /** 云端执行的区域 */
    region?: string;
    /** 是否云端执行 */
    runOnCloud?: boolean;
    scheduleId?: number;
    scheduleJobId?: string;
  };

  type RunTaskItemRequest = {
    rpaTaskItemId?: number;
    /** 相应的账户会话id */
    sessionId?: number;
  };

  type Script = {
    content?: string;
    id?: string;
    name?: string;
  };

  type SendNotifyRequest = {
    attachments?: string[];
    bizId?: string;
    businessType?: string;
    content?: string;
    detail?: string;
    method?: string;
    openIds?: string[];
    rpaFlowId?: number;
    rpaTaskId?: number;
    rpaTaskItemId?: number;
    shopId?: number;
    subject?: string;
    type?: string;
    url?: string;
    userIds?: number[];
    userStrs?: string[];
  };

  type SendNotifyResponse = {
    /** 预计发送多少个 */
    expectCount?: number;
    /** 成功多少个人 */
    successCount?: number;
  };

  type SendNotifyUsers = {
    method?: string;
    userIds?: number[];
    userStrs?: string[];
  };

  type ShareFlowCheckInfo = {
    id?: number;
    name?: string;
    platforms?: RpaPlatformVo[];
    version?: string;
  };

  type ShopChannelVo = {
    createTime?: string;
    dynamicStrategy?: 'Off' | 'Remain' | 'SwitchOnSession';
    id?: number;
    /** 通道的IP */
    ip?: TeamIpVo;
    ipId?: number;
    /** 通道的IP池 */
    ipPool?: IpPoolDto;
    ippId?: number;
    locationId?: number;
    locationLevel?: 'City' | 'Continent' | 'Country' | 'District' | 'None' | 'Province' | 'Unknown';
    officialChannelId?: number;
    primary?: boolean;
    shopId?: number;
    teamId?: number;
  };

  type ShopPlatformVo = {
    area?:
      | 'Argentina'
      | 'Australia'
      | 'Austria'
      | 'Belarus'
      | 'Belgium'
      | 'Bolivia'
      | 'Brazil'
      | 'Canada'
      | 'Chile'
      | 'China'
      | 'Colombia'
      | 'Costa_Rica'
      | 'Dominican'
      | 'Ecuador'
      | 'Egypt'
      | 'France'
      | 'Germany'
      | 'Global'
      | 'Guatemala'
      | 'Honduras'
      | 'HongKong'
      | 'India'
      | 'Indonesia'
      | 'Ireland'
      | 'Israel'
      | 'Italy'
      | 'Japan'
      | 'Kazakhstan'
      | 'Korea'
      | 'Malaysia'
      | 'Mexico'
      | 'Netherlands'
      | 'Nicaragua'
      | 'Panama'
      | 'Paraguay'
      | 'Peru'
      | 'Philippines'
      | 'Poland'
      | 'Portuguese'
      | 'Puerto_Rico'
      | 'Russia'
      | 'Salvador'
      | 'Saudi_Arabia'
      | 'Singapore'
      | 'Spain'
      | 'Sweden'
      | 'Switzerland'
      | 'Taiwan'
      | 'Thailand'
      | 'Turkey'
      | 'United_Arab_Emirates'
      | 'United_Kingdom'
      | 'United_States'
      | 'Uruguay'
      | 'Venezuela'
      | 'Vietnam';
    category?: 'IM' | 'Mail' | 'Other' | 'Payment' | 'Shop' | 'SocialMedia';
    frontUrl?: string;
    id?: number;
    loginUrl?: string;
    name?: string;
    typeName?: string;
  };

  type ShopWithChannelsVo = {
    /** 通道列表 */
    channels?: ShopChannelVo[];
    id?: number;
    name?: string;
    operatingCategory?:
      | '医药保健'
      | '图书文具'
      | '宠物用品'
      | '家具建材'
      | '家电电器'
      | '工业用品'
      | '户外运动'
      | '手机数码'
      | '手表眼镜'
      | '护肤美妆'
      | '母婴玩具'
      | '汽车配件'
      | '生活家居'
      | '电商其他'
      | '电脑平板'
      | '艺术珠宝'
      | '花园聚会'
      | '计生情趣'
      | '软件程序'
      | '鞋服箱包'
      | '音乐影视'
      | '食品生鲜'
      | '鲜花绿植';
    parentShopId?: number;
    platformArea?:
      | 'Argentina'
      | 'Australia'
      | 'Austria'
      | 'Belarus'
      | 'Belgium'
      | 'Bolivia'
      | 'Brazil'
      | 'Canada'
      | 'Chile'
      | 'China'
      | 'Colombia'
      | 'Costa_Rica'
      | 'Dominican'
      | 'Ecuador'
      | 'Egypt'
      | 'France'
      | 'Germany'
      | 'Global'
      | 'Guatemala'
      | 'Honduras'
      | 'HongKong'
      | 'India'
      | 'Indonesia'
      | 'Ireland'
      | 'Israel'
      | 'Italy'
      | 'Japan'
      | 'Kazakhstan'
      | 'Korea'
      | 'Malaysia'
      | 'Mexico'
      | 'Netherlands'
      | 'Nicaragua'
      | 'Panama'
      | 'Paraguay'
      | 'Peru'
      | 'Philippines'
      | 'Poland'
      | 'Portuguese'
      | 'Puerto_Rico'
      | 'Russia'
      | 'Salvador'
      | 'Saudi_Arabia'
      | 'Singapore'
      | 'Spain'
      | 'Sweden'
      | 'Switzerland'
      | 'Taiwan'
      | 'Thailand'
      | 'Turkey'
      | 'United_Arab_Emirates'
      | 'United_Kingdom'
      | 'United_States'
      | 'Uruguay'
      | 'Venezuela'
      | 'Vietnam';
    platformId?: number;
    platformName?: string;
    recordPolicy?: 'Chosen' | 'Disabled' | 'Forced';
    securityPolicyEnabled?: boolean;
    teamId?: number;
    type?: 'Global' | 'Local' | 'None';
  };

  type StsPostSignature = {
    accessKeyId?: string;
    accessKeySecret?: string;
    bucketName?: string;
    expiration?: string;
    fileVal?: string;
    policy?: string;
    provider?: string;
    region?: string;
    securityToken?: string;
    serverTime?: string;
    url?: string;
  };

  type StunInfo = {
    channel?: string;
    password?: string;
    signaling?: string;
    turn?: string;
    username?: string;
  };

  type SyncFilesRequest = {
    existAction?: string;
    rpaTaskItemId?: number;
    source?: string;
    target?: string;
  };

  type TeamAiConfig = {
    /** 是否允许使用aiAgent */
    aiAgent?: boolean;
    /** 是否开启聊天功能 */
    chat?: boolean;
    /** 是否允许写代码 */
    chatAllowCoding?: boolean;
    /** 是否限制聊天范围 */
    chatLimitScope?: boolean;
    /** 是否开启RPA聊天功能 */
    rpaChat?: boolean;
    /** 是否允许RPA写代码 */
    rpaChatAllowCoding?: boolean;
    /** 是否限制RPA聊天范围 */
    rpaChatLimitScope?: boolean;
  };

  type TeamIpVo = {
    autoRenew?: boolean;
    cloudProvider?: string;
    cloudRegion?: string;
    createTime?: string;
    creatorId?: number;
    description?: string;
    directDownTraffic?: number;
    directUpTraffic?: number;
    domestic?: boolean;
    downTraffic?: number;
    dynamic?: boolean;
    eipId?: number;
    enableWhitelist?: boolean;
    /** 过期状态 */
    expireStatus?: 'Expired' | 'Expiring' | 'Normal';
    forbiddenLongLatitude?: boolean;
    gatewayId?: number;
    goodsId?: number;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    importType?: 'Platform' | 'User';
    invalidTime?: string;
    ip?: string;
    lastProbeTime?: string;
    latitude?: number;
    locale?: string;
    locationId?: number;
    longitude?: number;
    name?: string;
    networkType?: 'cloudIdc' | 'mobile' | 'proxyIdc' | 'residential' | 'unknown' | 'unknownIdc';
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    originalTeam?: number;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    pipeType?: 'None' | 'Proxy' | 'Tunnel' | 'TunnelFailToProxy';
    preferTransit?: number;
    probeError?: string;
    /** 供应商名称 */
    providerName?: string;
    realIp?: string;
    refreshUrl?: string;
    remoteLogin?: boolean;
    renewPrice?: number;
    source?: string;
    speedLimit?: number;
    status?: 'Available' | 'Pending' | 'Unavailable';
    sticky?: boolean;
    teamId?: number;
    testingTime?: number;
    timezone?: string;
    traffic?: number;
    trafficCurrency?: 'CREDIT' | 'RMB' | 'USD';
    trafficPrice?: number;
    trafficUnlimited?: boolean;
    transitType?: 'Auto' | 'Direct' | 'Transit';
    tunnelTypes?: string;
    upTraffic?: number;
    valid?: boolean;
    validEndDate?: string;
    vpsId?: number;
  };

  type ToggleRpaVoucherAutoRenewRequest = {
    autoRenew?: boolean;
    rpaVoucherIds?: number[];
  };

  type Track = {
    t?: number;
    x?: number;
    y?: number;
  };

  type TriggerFlowByBizCodeRequest = {
    deviceId?: string;
    params?: Record<string, any>;
    /** 流程bizCode，和rpaFlowId至少提供一个，且只在rpaFlowId为空时有意义 */
    rpaFlowBizCode?: string;
    /** 流程id，和rpaFlowBizCode至少提供一个 */
    rpaFlowId?: number;
    /** 在哪个分身上执行，如果 bizCode 对应的流程是手机流程，代表手机id */
    shopId?: number;
  };

  type UpdateFlowGroupRequest = {
    description?: string;
    id?: number;
    name?: string;
    sortNumber?: number;
  };

  type UpdatePlanBasicRequest = {
    description?: string;
    flowId?: number;
    name?: string;
    /** 任务命名规则 */
    taskNameEl?: string;
  };

  type UpdatePlanParamsRequest = {
    /** 如果流程输入变量来自网盘，保存文件路径，以 team_disk:// 或 user_disk:// 开头 */
    paramFilePath?: string;
    /** 每个店铺自己的变量，如果某个shopId不在map里，则不对其进行改变 */
    params?: Record<string, any>;
    /** 所有店铺共用的变量 */
    sharingParams?: Record<string, any>;
  };

  type UpdatePlanPolicyRequest = {
    /** 是否允许他人编辑此计划 */
    allowOthersEdit?: boolean;
    /** 当账号的浏览器已打开时的策略：stop | reopen | reuse */
    caseBrowserOpened?: string;
    /** 流程结束后是否关闭账号浏览器：close | keep */
    closeBrowserOnEnd?: string;
    /** 账号并发数量 */
    concurrent?: number;
    concurrentDelay?: number;
    /** 重复日期，只包含星期信息的表达式 */
    cronExpression?: string;
    /** login_device表里的device_id */
    deviceId?: string;
    /** 间隔时间（只有循环任务才有意义） */
    duration?: number;
    /** 邮件触发器 */
    emailTrigger?: CreateEmailRpaTriggerVo;
    /** 如果是随机执行，用-隔开开始时间和结束时间[start, start-end, start, ...] */
    expressions?: string[];
    /** 文件触发器 */
    fileTrigger?: CreateFileRpaTriggerVo;
    forceRecord?: boolean;
    /** 是否以无头模式运行 */
    headless?: boolean;
    /** Http触发器 */
    httpTrigger?: CreateHttpRpaTriggerVo;
    /** 窗口布局配置,json格式 */
    layoutConfig?: string;
    /** 该计划最大执行时长，单位分钟。相应的流程如果执行超过这个值会被强制结束。为空或为0表示不限制 */
    maxMinutes?: number;
    /** 事件触发器 */
    messageTrigger?: CreateMessageRpaTriggerVo;
    /** 他人执行此流程的策略。 为空表示只能创建者可以执行，非空可选值为：creator | others | creator_others */
    othersRunPolicy?: string;
    planEventDelay?: number;
    planEventName?: string;
    /** 计划类型 */
    planType?: 'Auto' | 'Loop' | 'Manual' | 'Trigger';
    /** 是否自动弹出流程日志窗口，为空表示false */
    popupTaskLog?: boolean;
    /** 云端执行的云厂商 */
    provider?:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    /** 云端执行的区域 */
    region?: string;
    /** 是否云端执行 */
    runOnCloud?: boolean;
    /** 是否显示鼠标轨迹 */
    showMouseTrack?: boolean;
    /** 执行时侧边栏显示策略。hide | show，为空表示继承流程配置 */
    sidePanel?: string;
    snapshot?: 'Node' | 'Not' | 'OnFail';
    snapshotScope?: 'fullPage' | 'pdf' | 'viewport' | 'window';
    startEventDelay?: number;
    startEventName?: string;
    /** 遇到错误是否退出循环任务（只有循环任务才有意义） */
    stopOnError?: boolean;
    /** 计划结束后是否触发一个计划事件 */
    triggerPlanEvent?: boolean;
    /** 计划开始后是否触发一个计划事件 */
    triggerStartEvent?: boolean;
  };

  type UpdatePlanShopsRequest = {
    shopIds?: number[];
  };

  type UpdateRpaFlowGroupRequest = {
    groupIds?: number[];
    rpaFlowId?: number;
  };

  type UpdateRpaFlowRequest = {
    /** 是否控制台流程 */
    console?: boolean;
    description?: string;
    groupIds?: number[];
    name?: string;
    platforms?: string[];
    rpaFlowId?: number;
    /** 编辑用到账号id */
    shopId?: number;
    version?: string;
  };

  type WebResult = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultboolean = {
    code?: number;
    data?: boolean;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCalcPriceResponse = {
    code?: number;
    data?: CalcPriceResponse;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCalcRenewRpaVoucherResponse = {
    code?: number;
    data?: CalcRenewRpaVoucherResponse;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCaptchaOcrResponse = {
    code?: number;
    data?: CaptchaOcrResponse;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCheckShareCodeResponse = {
    code?: number;
    data?: CheckShareCodeResponse;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCreateOrderResponse = {
    code?: number;
    data?: CreateOrderResponse;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultint = {
    code?: number;
    data?: number;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListAbstractRpaShopInfo = {
    code?: number;
    data?: AbstractRpaShopInfo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListLoginDeviceDto = {
    code?: number;
    data?: LoginDeviceDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListlong = {
    code?: number;
    data?: number[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListMarketFlowImageVo = {
    code?: number;
    data?: MarketFlowImageVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListRpaAvailableRegionVo = {
    code?: number;
    data?: RpaAvailableRegionVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListRpaFileTriggerVo = {
    code?: number;
    data?: RpaFileTriggerVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListRpaFlowGroupVo = {
    code?: number;
    data?: RpaFlowGroupVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListRpaFlowShareCodeVo = {
    code?: number;
    data?: RpaFlowShareCodeVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListRpaFlowVersionVo = {
    code?: number;
    data?: RpaFlowVersionVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListRpaFlowVo = {
    code?: number;
    data?: RpaFlowVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListRpaMessageTriggerVo = {
    code?: number;
    data?: RpaMessageTriggerVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListRpaMobileInfo = {
    code?: number;
    data?: RpaMobileInfo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListRpaPlanGroupVo = {
    code?: number;
    data?: RpaPlanGroupVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListRpaPlanVo = {
    code?: number;
    data?: RpaPlanVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListRpaRunDeviceVo = {
    code?: number;
    data?: RpaRunDeviceVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListRpaShopInfo = {
    code?: number;
    data?: RpaShopInfo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListRpaShopPasswordVo = {
    code?: number;
    data?: RpaShopPasswordVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListRpaSimpleHisVo = {
    code?: number;
    data?: RpaSimpleHisVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListRpaTaskFileVo = {
    code?: number;
    data?: RpaTaskFileVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListRpaTaskItemVo = {
    code?: number;
    data?: RpaTaskItemVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListRpaTaskNodeVo = {
    code?: number;
    data?: RpaTaskNodeVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListRpaVoucherHisVo = {
    code?: number;
    data?: RpaVoucherHisVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListstring = {
    code?: number;
    data?: string[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTrack = {
    code?: number;
    data?: Track[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMaplong = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMaplong = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMaplong = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMapstring = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMarketFlowGainsVo = {
    code?: number;
    data?: MarketFlowGainsVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMarketFlowVo = {
    code?: number;
    data?: MarketFlowVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultOpenAiResponse = {
    code?: number;
    data?: OpenAiResponse;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultMarketFlowVo = {
    code?: number;
    data?: PageResultMarketFlowVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultRpaFlowShareVo = {
    code?: number;
    data?: PageResultRpaFlowShareVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultRpaFlowVo = {
    code?: number;
    data?: PageResultRpaFlowVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultRpaPlanVo = {
    code?: number;
    data?: PageResultRpaPlanVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultRpaTaskVo = {
    code?: number;
    data?: PageResultRpaTaskVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultRpaVoucherVo = {
    code?: number;
    data?: PageResultRpaVoucherVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRpaConfig = {
    code?: number;
    data?: RpaConfig;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRpaFileTriggerEventVo = {
    code?: number;
    data?: RpaFileTriggerEventVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRpaFlowGroupVo = {
    code?: number;
    data?: RpaFlowGroupVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRpaFlowVersionVo = {
    code?: number;
    data?: RpaFlowVersionVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRpaFlowVo = {
    code?: number;
    data?: RpaFlowVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRpaHttpTriggerResponseVo = {
    code?: number;
    data?: RpaHttpTriggerResponseVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRpaPlanExports = {
    code?: number;
    data?: RpaPlanExports;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRpaPlanGroup = {
    code?: number;
    data?: RpaPlanGroup;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRpaPlanGroupVo = {
    code?: number;
    data?: RpaPlanGroupVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRpaPlanVo = {
    code?: number;
    data?: RpaPlanVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRpaTaskFileVo = {
    code?: number;
    data?: RpaTaskFileVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRpaTaskItemVo = {
    code?: number;
    data?: RpaTaskItemVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRpaTaskReport = {
    code?: number;
    data?: RpaTaskReport;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRpaTaskVo = {
    code?: number;
    data?: RpaTaskVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRpaVoucherVo = {
    code?: number;
    data?: RpaVoucherVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultSendNotifyResponse = {
    code?: number;
    data?: SendNotifyResponse;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultstring = {
    code?: number;
    data?: string;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultStsPostSignature = {
    code?: number;
    data?: StsPostSignature;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultStunInfo = {
    code?: number;
    data?: StunInfo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamAiConfig = {
    code?: number;
    data?: TeamAiConfig;
    message?: string;
    requestId?: string;
    success?: boolean;
  };
}
