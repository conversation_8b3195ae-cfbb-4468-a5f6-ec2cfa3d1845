// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 探测IP与中转之间的连接情况（返回remoteIp） GET /api/ip/${param0}/probe */
export async function ipByIpIdProbeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIpIdProbeGetParams,
  options?: { [key: string]: any },
) {
  const { ipId: param0, ...queryParams } = params;
  return request<API.WebResultProxyProbeWithRemoteIp>(`/api/ip/${param0}/probe`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 前端直连探测后更新结果 PUT /api/ip/${param0}/probe/directResult */
export async function ipByIpIdProbeDirectResultPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIpIdProbeDirectResultPutParams,
  body: API.ProxyProbeWithRemoteIp,
  options?: { [key: string]: any },
) {
  const { ipId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/ip/${param0}/probe/directResult`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 更新IP的状态（包括 代理IP 和 Tunnel IP） PUT /api/ip/${param0}/status */
export async function ipByIpIdStatusPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIpIdStatusPutParams,
  options?: { [key: string]: any },
) {
  const { ipId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/ip/${param0}/status`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取IP检查器 GET /api/ip/checkers */
export async function ipCheckersGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipCheckersGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultRemoteIpCheckerConfig>('/api/ip/checkers', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取IP检查器 GET /api/meta/ip/checkers */
export async function metaIpCheckersGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.metaIpCheckersGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultRemoteIpCheckerConfig>('/api/meta/ip/checkers', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
