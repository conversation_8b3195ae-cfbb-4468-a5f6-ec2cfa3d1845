// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取团队的加速通道分组 GET /api/tunnel/groups */
export async function tunnelGroupsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTunnelGroupDetailVo>('/api/tunnel/groups', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取待探测节点 GET /api/tunnel/probeNodes */
export async function tunnelProbeNodesGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tunnelProbeNodesGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTunnelProbeNodeVo>('/api/tunnel/probeNodes', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 更新加速通道测速结果 PUT /api/tunnel/probeResult */
export async function tunnelProbeResultPut(
  body: API.UpdateTunnelProbeResultRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tunnel/probeResult', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
