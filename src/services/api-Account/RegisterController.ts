// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 检查手机号或邮箱是否合法 如果是手机号，区号连接到account前面 GET /api/account/checkEmailOrPhone */
export async function accountCheckEmailOrPhoneGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.accountCheckEmailOrPhoneGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/account/checkEmailOrPhone', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 校验邀请码 GET /api/account/checkInviteCode */
export async function accountCheckInviteCodeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.accountCheckInviteCodeGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/account/checkInviteCode', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 注册账号 POST /api/account/register */
export async function accountRegisterPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.accountRegisterPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultRegisterAccountVo>('/api/account/register', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 系统注册配置信息 GET /api/account/registerConfig */
export async function accountRegisterConfigGet(options?: { [key: string]: any }) {
  return request<API.WebResultRegisterConfig>('/api/account/registerConfig', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取验证码 GET /api/account/verifyCode */
export async function accountVerifyCodeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.accountVerifyCodeGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/account/verifyCode', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
