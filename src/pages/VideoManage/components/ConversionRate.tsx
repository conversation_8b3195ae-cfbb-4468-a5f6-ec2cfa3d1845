import _ from 'lodash';
import { <PERSON><PERSON>, Card, Typography } from 'antd';
import DMModal from '@/components/Common/Modal/DMModal';
import { useMemo, useState } from 'react';
import type { GhostModalWrapperComponentProps } from '@/mixins/modal';
import { GhostModalCaller } from '@/mixins/modal';
import { useSystemConfig } from '@/hooks/useSystemConfig';
import DMFormItem, { DMFormItemContext } from '@/components/Common/DMFormItem';
import I18N from '@/i18n';
import { dateFormat } from '@/utils/utils';
import Placeholder from '@/components/Common/Placeholder';
import { history } from 'umi';
import { getCurrentTeamId } from '@/hooks/useCurrentTeam';
import { useShopLastSyncTime } from '@/hooks/useShopLastSyncTime';
import InfoTip from '@/components/Tips/InfoTip';
import ColoursIcon from '@/components/Common/ColoursIcon';

// 优秀：绿色
// 正常：黑色
// 较差：灰色
// 数据更新不及时：红色,更新时间距离现在24小时以上
const ConversionRateText = (props: { video?: API.TkshopVideoVo; style?: React.CSSProperties }) => {
  const { video, style = {} } = props;
  const type = useMemo(() => {
    const _type = video?.ordersPmType;
    if (_type === 'abnormal') {
      return 'danger';
    }
    if (_type === 'good') {
      return 'success';
    }
    if (_type === 'bad') {
      return 'secondary';
    }
    return;
  }, [video?.ordersPmType]);
  const value = video?.ordersPm;
  if (_.isNil(value)) {
    return <Placeholder />;
  }
  return (
    <Typography.Text type={type} style={style}>
      {value}
    </Typography.Text>
  );
};

const ConversionRateAsidePanel = (props: { video: API.TkshopVideoVo; orderSyncTime?: string }) => {
  const { video } = props;
  const orderCount = useMemo(() => {
    // 向下取整
    return Math.floor(video.ordersPm ? video.ordersPm * 1000 : 0);
  }, [video.ordersPm]);
  const content = useMemo(() => {
    if (video.orderCount === 0) {
      return (
        <div>
          订单为 <Typography.Text type={'danger'}>0</Typography.Text> ，千次播放转化没有参考意义
        </div>
      );
    }
    if (_.isNil(video.viewCnt)) {
      return (
        <div>
          播放量为 <Typography.Text type={'danger'}>空</Typography.Text> ，千次播放转化没有参考意义
        </div>
      );
    }
    return (
      <div>
        千次播放转化率{' '}
        <Typography.Text type="success" style={{ fontSize: 20 }}>
          {video.ordersPm}
        </Typography.Text>{' '}
        意味着每{' '}
        <Typography.Link style={{ cursor: 'default', fontSize: 20 }}>100万次</Typography.Link>{' '}
        播放会产生{' '}
        <Typography.Text type={'success'} style={{ fontSize: 20 }}>
          {orderCount}
        </Typography.Text>{' '}
        笔订单
      </div>
    );
  }, [orderCount, video.orderCount, video.ordersPm, video.viewCnt]);
  return (
    <Card style={{ height: '100%' }} title={false} bodyStyle={{ height: '100%', paddingBottom: 0 }}>
      <div
        style={{
          height: '100%',
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          gap: 16,
          alignItems: 'center',
          paddingBottom: 10,
        }}
      >
        <ColoursIcon className={'qiancibofangzhuanhuashuai_24'} size={72} />
        <header style={{ textAlign: 'center', fontSize: 16 }}>
          {I18N.t('关于千次播放转化率的解读')}
        </header>
        <main
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: 16,
            fontSize: 14,
            color: '#666',
            textIndent: '2em',
            textAlign: 'justify',
          }}
        >
          {content}
          <div>千次播放转化率越高，意味着该视频越有投流价值</div>
        </main>
      </div>
    </Card>
  );
};

const ConversionRateModal = (
  props: GhostModalWrapperComponentProps & { video: API.TkshopVideoVo; orderSyncTime?: string },
) => {
  const { modalProps, video, orderSyncTime } = props;
  const [visible, changeVisible] = useState(true);
  const { data } = useSystemConfig();
  const viewCntNode = useMemo(() => {
    const label = video?.lastSyncTime
      ? `更新于 ${dateFormat(video.lastSyncTime, 'MM-DD HH:mm')}`
      : '--';
    return (
      <div>
        <span>{video?.viewCnt?.toLocaleString()}</span>
        <Typography.Text type={'secondary'}>（{label}）</Typography.Text>
      </div>
    );
  }, [video?.viewCnt, video.lastSyncTime]);
  const orderCntNode = useMemo(() => {
    const label = video?.lastOrderSyncTime
      ? `更新于 ${dateFormat(video.lastOrderSyncTime, 'MM-DD HH:mm')}`
      : '--';
    return (
      <div>
        <span>{video?.orderCount?.toLocaleString()}</span>
        <Typography.Text type={'secondary'}>（{label}）</Typography.Text>
      </div>
    );
  }, [video?.orderCount, video.lastOrderSyncTime]);
  const type = video.ordersPmType;
  const status = useMemo(() => {
    if (type === 'abnormal') {
      if (video.viewCnt < data?.ordersPmQueryVo?.abnormalViewCnt) {
        return (
          <Typography.Text type="danger">
            异常（播放量小于 {data?.ordersPmQueryVo?.abnormalViewCnt}）
          </Typography.Text>
        );
      }
      return (
        <Typography.Text type="danger">
          异常（
          {I18N.t('视频更新时间与订单更新时间超过 {{hours}}小时', {
            hours: data?.ordersPmQueryVo?.abnormalSyncTimeDiff,
          })}
          ）
        </Typography.Text>
      );
    }

    if (type === 'good') {
      return (
        <Typography.Text type="success">
          {I18N.t('优秀')}（&gt; {data?.ordersPmQueryVo?.goodRate}）
        </Typography.Text>
      );
    }
    if (type === 'bad') {
      return (
        <Typography.Text type="secondary">
          {I18N.t('较差')}（&lt; {data?.ordersPmQueryVo?.badRate}）
        </Typography.Text>
      );
    }
    return (
      <Typography.Text>
        {I18N.t('正常')}（ {data?.ordersPmQueryVo?.badRate} - {data?.ordersPmQueryVo?.goodRate} ）
      </Typography.Text>
    );
  }, [
    type,
    data?.ordersPmQueryVo?.badRate,
    data?.ordersPmQueryVo?.goodRate,
    data?.ordersPmQueryVo?.abnormalViewCnt,
    data?.ordersPmQueryVo?.abnormalSyncTimeDiff,
    video.viewCnt,
  ]);
  return (
    <DMModal
      title={I18N.t('千次播放转化率')}
      width={640 + 360}
      bodyStyle={{
        height: 320,
      }}
      footer={
        <Button type="primary" onClick={() => changeVisible(false)}>
          {I18N.t('关闭')}
        </Button>
      }
      asidePanel={<ConversionRateAsidePanel video={video} orderSyncTime={orderSyncTime} />}
      asideWrapperStyle={{ flex: '0 0 360px' }}
      {...modalProps}
      open={visible}
      onCancel={() => changeVisible(false)}
    >
      <DMFormItemContext.Provider value={{ labelWidth: 140 }}>
        <DMFormItem label={I18N.t('转换率')}>
          <ConversionRateText style={{ fontSize: 20 }} video={video} />
        </DMFormItem>
        <DMFormItem label={I18N.t('转化率所在区间 ')}>{status}</DMFormItem>
        <DMFormItem label={I18N.t('播放量')}>{viewCntNode}</DMFormItem>
        <DMFormItem label={I18N.t('订单数量')}>{orderCntNode}</DMFormItem>
        <InfoTip
          message={
            <div style={{ whiteSpace: 'normal' }}>
              <div>
                您可以在
                <Typography.Link
                  style={{ margin: '0 8px' }}
                  onClick={() => {
                    // 跳到系统设置
                    history.push(`/team/${getCurrentTeamId()}/setting?tab=base#hash_system_config`);
                  }}
                >
                  {I18N.t('系统设置')}
                </Typography.Link>
                中查看关于千次播放转化率区间的划分与异常状态的条件
              </div>
            </div>
          }
          style={{ marginTop: 8, alignItems: 'flex-start' }}
        />
      </DMFormItemContext.Provider>
    </DMModal>
  );
};

const ConversionRateInteractiveComponent = (props: { video: API.TkshopVideoVo }) => {
  const { video } = props;
  const { data } = useShopLastSyncTime(video?.shopIds);
  const clickable = useMemo(() => {
    return !_.isNil(video.ordersPm);
  }, [video.ordersPm]);
  if (!clickable) {
    return <ConversionRateText video={video} />;
  }
  return (
    <span
      style={{ cursor: 'pointer' }}
      onClick={() => {
        GhostModalCaller(<ConversionRateModal video={video} orderSyncTime={data} />);
      }}
    >
      <ConversionRateText video={video} />
    </span>
  );
};
export default ConversionRateInteractiveComponent;
