// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 添加收藏 POST /api/team/favorites */
export async function teamFavoritesPost(
  body: API.AddFavorateRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/team/favorites', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 按类型查询收藏列表 GET /api/team/favorites/byType/${param0} */
export async function teamFavoritesByTypeByResourceTypeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamFavoritesByTypeByResourceTypeGetParams,
  options?: { [key: string]: any },
) {
  const { resourceType: param0, ...queryParams } = params;
  return request<API.WebResultListTeamFavoriteDto>(`/api/team/favorites/byType/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 按类型查询是否收藏 POST /api/team/favorites/checkByType/${param0} */
export async function teamFavoritesCheckByTypeByResourceTypePost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamFavoritesCheckByTypeByResourceTypePostParams,
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  const { resourceType: param0, ...queryParams } = params;
  return request<API.WebResultMaplong>(`/api/team/favorites/checkByType/${param0}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 取消收藏 POST /api/team/favorites/delete */
export async function teamFavoritesDeletePost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamFavoritesDeletePostParams,
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/team/favorites/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 按类型查询收藏列表 GET /api/team/favorites/detailByType/${param0} */
export async function teamFavoritesDetailByTypeByResourceTypeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamFavoritesDetailByTypeByResourceTypeGetParams,
  options?: { [key: string]: any },
) {
  const { resourceType: param0, ...queryParams } = params;
  return request<API.WebResultPageResultTeamFavoriteDetailVo>(
    `/api/team/favorites/detailByType/${param0}`,
    {
      method: 'GET',
      params: {
        ...queryParams,
      },
      ...(options || {}),
    },
  );
}

/** 分页查询收藏列表 GET /api/team/favorites/page */
export async function teamFavoritesPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamFavoritesPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTeamFavoriteDto>('/api/team/favorites/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
