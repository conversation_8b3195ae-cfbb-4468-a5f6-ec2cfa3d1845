declare namespace API {
  type CheckCodeResult = {
    endpoint?: string;
    gatewayId?: number;
    identifier?: string;
    team?: ProxyTeamVo;
    teamId?: number;
    teamName?: string;
    token?: string;
    transits?: Record<string, any>;
  };

  type DynamicForwardStat = {
    pipes?: number;
    sessions?: number;
  };

  type ExecResult = {
    cmd?: string[];
    error?: string;
    exitCode?: number;
    out?: string;
  };

  type FieldInfo = {
    hashCode?: number;
    name?: string;
    path?: string;
    printable?: boolean;
    size?: number;
    valueClass?: string;
    valueString?: string;
  };

  type FieldsInfo = {
    clazz?: string;
    fields?: FieldInfo[];
  };

  type ForceDisconnectResult = {
    disconnectedAddresses?: TunnelAddress[];
  };

  type GatewayDetailVo = {
    associatedId?: number;
    associatedType?: string;
    buildTime?: string;
    cloudProvider?:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    commitId?: string;
    cores?: number;
    createTime?: string;
    creator?: number;
    description?: string;
    dfPort?: number;
    domestic?: boolean;
    gatewayIps?: GatewayIpDto[];
    hostName?: string;
    id?: number;
    identifier?: string;
    innerIp?: string;
    instanceId?: string;
    ipVersion?: 'All' | 'Custom' | 'IPv4' | 'IPv6' | 'None';
    memory?: number;
    orderItemId?: number;
    osArch?: string;
    osName?: string;
    osVersion?: string;
    publicIp?: string;
    region?: string;
    remoteIp?: string;
    secretKey?: string;
    status?: 'DELETED' | 'READY' | 'STOPPED';
    teamId?: number;
    templateId?: number;
    updateEnabled?: boolean;
    updateTime?: string;
    uptime?: string;
    version?: string;
    vpsId?: number;
  };

  type GatewayDto = {
    associatedId?: number;
    associatedType?: string;
    buildTime?: string;
    cloudProvider?:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    commitId?: string;
    cores?: number;
    createTime?: string;
    creator?: number;
    description?: string;
    dfPort?: number;
    domestic?: boolean;
    hostName?: string;
    id?: number;
    identifier?: string;
    innerIp?: string;
    instanceId?: string;
    ipVersion?: 'All' | 'Custom' | 'IPv4' | 'IPv6' | 'None';
    memory?: number;
    orderItemId?: number;
    osArch?: string;
    osName?: string;
    osVersion?: string;
    publicIp?: string;
    region?: string;
    remoteIp?: string;
    secretKey?: string;
    status?: 'DELETED' | 'READY' | 'STOPPED';
    teamId?: number;
    templateId?: number;
    updateEnabled?: boolean;
    updateTime?: string;
    uptime?: string;
    version?: string;
    vpsId?: number;
  };

  type GatewayIpDto = {
    createTime?: string;
    gatewayId?: number;
    id?: number;
    iface?: string;
    imported?: boolean;
    innerIp?: string;
    invalidTime?: string;
    ip?: string;
    ipId?: number;
    ipv6?: boolean;
    locale?: string;
    macAddr?: string;
    teamId?: number;
    timezone?: string;
    valid?: boolean;
  };

  type GatewayTemplateDto = {
    createTime?: string;
    id?: number;
    identifier?: string;
    importType?: 'Platform' | 'User';
    secretKey?: string;
    teamId?: number;
    token?: string;
    valid?: boolean;
  };

  type GitInfoVo = {
    branch?: string;
    buildNumber?: string;
    buildTime?: string;
    buildVersion?: string;
    commitIdAbbrev?: string;
    commitIdFull?: string;
    commitTime?: string;
  };

  type ImportIpgoIpRequest = {
    importedIps?: number[];
    ipVersion?: 'All' | 'Custom' | 'IPv4' | 'IPv6' | 'None';
    unimportedIps?: number[];
  };

  type InstallScriptVo = {
    batScript?: string;
    endpoint?: string;
    importKey?: string;
    linuxDownloadUrl?: string;
    psScript?: string;
    shScript?: string;
    windowsDownloadUrl?: string;
  };

  type ipgoByIdForceReportIpGetParams = {
    /** id */
    id: number;
  };

  type ipgoByIdImportPutParams = {
    /** id */
    id: number;
  };

  type ipgoByIdRestartGetParams = {
    /** id */
    id: number;
  };

  type ipgoByIdUninstallGetParams = {
    /** id */
    id: number;
    /** confirm */
    confirm: boolean;
  };

  type ipgoPageGetParams = {
    hostName?: string;
    ip?: string;
    osName?: string;
    pageNum?: number;
    pageSize?: number;
    sortField?:
      | 'create_time'
      | 'host_name'
      | 'inner_ip'
      | 'os_name'
      | 'public_ip'
      | 'update_time'
      | 'uptime';
    sortOrder?: 'asc' | 'desc';
    status?: 'DELETED' | 'READY' | 'STOPPED';
  };

  type IpLocation = {
    city?: string;
    cityEn?: string;
    continent?: string;
    continentCode?: string;
    continentEn?: string;
    country?: string;
    countryEn?: string;
    countyCode?: string;
    geonameId?: number;
    inEu?: boolean;
    ip?: string;
    latitude?: number;
    locale?: string;
    longitude?: number;
    postalCode?: string;
    province?: string;
    provinceCode?: string;
    provinceEn?: string;
    timezone?: string;
  };

  type IppParamVo = {
    paramKey?: string;
    paramType?:
      | 'ApiEndpoint'
      | 'ApiEndpointRaw'
      | 'ApiHeader'
      | 'ApiHttpMethod'
      | 'ApiPassword'
      | 'ApiQueryParam'
      | 'ApiSignKey'
      | 'ApiUsername'
      | 'ApiWhitelist'
      | 'GlobalWhitelist'
      | 'MinApiInterval'
      | 'MinApiNum'
      | 'ProxyPassword'
      | 'ProxyType'
      | 'ProxyUsername'
      | 'ProxyWhitelist'
      | 'Unknown';
    paramValue?: string;
  };

  type IpProfile = {
    checkDelay?: number;
    id?: number;
    iface?: string;
    innerIp?: string;
    ipv6?: boolean;
    locale?: string;
    macAddr?: string;
    remoteIp?: string;
    timezone?: string;
  };

  type ObjectInfo = {
    clazz?: string;
    key?: string;
    keyUrl?: string;
    toString?: string;
  };

  type PageResultGatewayDetailVo = {
    current?: number;
    list?: GatewayDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PeerConnStat = {
    clientDfSessionStat?: DynamicForwardStat;
    peerAddress?: TunnelAddress;
    serverDfSessionStat?: DynamicForwardStat;
  };

  type ProbeProxyRequest = {
    parserType?: 'ifconfigme' | 'ipsb' | 'lumtest' | 'transit';
    proxyConfig?: ProxyConfig;
    timeout?: number;
    transitEndpoint?: string;
    transitId?: number;
  };

  type ProbeResult = {
    connectTime?: number;
    error?: string;
    originalError?: string;
    reachable?: boolean;
    remoteIpEndpoint?: string;
    success?: boolean;
    testingTime?: number;
  };

  type ProduceIpRequest = {
    connectTimeout?: number;
    ippId?: number;
    method?: string;
    num?: number;
    params?: IppParamVo[];
    provider?: string;
    readTimeout?: number;
  };

  type ProduceIpResultVo = {
    ips?: RotatingIp[];
    remainQuantity?: number;
  };

  type ProduceIpSpecVo = {
    /** 需要追加的请求头 */
    headers?: Record<string, any>;
    /** 请求方法，比如GET/POST等 */
    method?: string;
    /** 请求URL，携带参数 */
    url?: string;
  };

  type proxyByProxyIdTunnelConnStatGetParams = {
    /** proxyId */
    proxyId: number;
  };

  type ProxyConfig = {
    host?: string;
    ipVersion?: 'Auto' | 'IPv4' | 'IPv6';
    password?: string;
    port?: number;
    proxyType?: 'http' | 'httpTunnel' | 'https' | 'socks4' | 'socks5' | 'ssh';
    sshKey?: string;
    username?: string;
  };

  type proxyInstallScriptGetParams = {
    /** 超时时间：秒 */
    expireSeconds?: number;
  };

  type proxyListGetParams = {
    /** 所属团队 */
    teamId: number;
    /** 云厂商类型 */
    provider?:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    /** 云厂商区域 */
    region?: string;
    /** 状态 */
    status?: 'DELETED' | 'READY' | 'STOPPED';
  };

  type ProxyMeta = {
    endpoint?: string;
    team?: ProxyTeamVo;
    transits?: Record<string, any>;
  };

  type ProxyProbeWithRemoteIp = {
    code?: number;
    connectTime?: number;
    error?: string;
    handshakeTime?: number;
    originalError?: string;
    output?: string;
    proto?: string;
    reachable?: boolean;
    remoteIp?: string;
    remoteIpEndpoint?: string;
    success?: boolean;
    testingTime?: number;
  };

  type proxyRemoteTemplateTunnelTokenGetParams = {
    /** 云厂商类型 */
    provider?: string;
    /** 云厂商区域 */
    region?: string;
    /** 主机instanceId */
    instanceId?: string;
    /** 超时（秒） */
    expireSeconds?: number;
  };

  type proxyRemoteTunnelTokenByCodeGetParams = {
    /** code */
    code: string;
  };

  type ProxyTeamVo = {
    domestic?: boolean;
    gatewayId?: number;
    teamId?: number;
    teamName?: string;
  };

  type proxyTemplatePostParams = {
    /** 所属团队 */
    teamId: number;
  };

  type ProxyTemplateVo = {
    createTime?: string;
    id?: number;
    identifier?: string;
    importType?: 'Platform' | 'User';
    teamId?: number;
    token?: string;
    valid?: boolean;
  };

  type ProxyTokenCodeVo = {
    /** 接入码 */
    code?: string;
    /** 过期时间 */
    expireTime?: string;
    /** 标识 */
    identifier?: string;
    /** Proxy授权码 */
    token?: string;
    /** token是否编码 */
    tokenEncoded?: boolean;
  };

  type ProxyTokenVo = {
    /** 过期时间 */
    expireTime?: string;
    /** 标识 */
    identifier?: string;
    /** Proxy授权码 */
    token?: string;
    /** token是否编码 */
    tokenEncoded?: boolean;
  };

  type proxyTunnelTokenPostParams = {
    /** 所属团队 */
    teamId: number;
    /** 云厂商类型 */
    provider?:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    /** 云厂商区域 */
    region: string;
    /** 是否编码 */
    encoded?: boolean;
    /** 超时时间（秒），0表示永不超时 */
    expireSeconds?: number;
  };

  type ProxyVersionInfo = {
    latestVersion?: string;
    linuxDownloadUrl?: string;
    windowsDownloadUrl?: string;
  };

  type remoteApiWorkerByIdentifierIppCheckParamsPostParams = {
    /** identifier */
    identifier: string;
  };

  type remoteApiWorkerByIdentifierIppPrepareProduceIpSpecPostParams = {
    /** identifier */
    identifier: string;
  };

  type remoteApiWorkerByIdentifierIppProducePostParams = {
    /** identifier */
    identifier: string;
  };

  type RemoteProbeRemoteIpRequest = {
    pc?: ProxyConfig;
    timeout?: number;
    url?: string;
  };

  type remoteTunnelBlockProxyGetParams = {
    /** identifier */
    identifier: string;
    /** seconds */
    seconds: number;
  };

  type remoteTunnelConnectedPeersGetParams = {
    /** target */
    target?: string;
  };

  type remoteTunnelCreateProxyPendingTokenGetParams = {
    /** teamId */
    teamId: number;
    /** creatorId */
    creatorId: number;
    /** provider */
    provider:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    /** region */
    region: string;
    /** expireSeconds */
    expireSeconds: number;
    /** orderItemId */
    orderItemId?: number;
  };

  type remoteTunnelDumpThreadGetParams = {
    /** target */
    target?: string;
  };

  type remoteTunnelExitProxyGetParams = {
    /** identifier */
    identifier: string;
    /** exitCode */
    exitCode: number;
  };

  type remoteTunnelGitInfoGetParams = {
    /** target */
    target?: string;
  };

  type remoteTunnelGitSpyGetParams = {
    /** target */
    target?: string;
    /** path */
    path: string;
    /** filter */
    filter?: boolean;
    /** page */
    page?: number;
    /** size */
    size?: number;
  };

  type remoteTunnelProxyChangePasswordPutParams = {
    /** identifier */
    identifier: string;
    /** user */
    user: string;
    /** password */
    password: string;
  };

  type remoteTunnelProxyChangePwdLoginPutParams = {
    /** identifier */
    identifier: string;
    /** enabled */
    enabled: boolean;
  };

  type remoteTunnelProxyConnectTransitPostParams = {
    /** identifier */
    identifier: string;
    /** forceReconnect */
    forceReconnect?: boolean;
    /** transitId */
    transitId: number;
    /** jumpIfNeed */
    jumpIfNeed?: boolean;
  };

  type remoteTunnelProxyForceReportIpPostParams = {
    /** identifier */
    identifier: string;
  };

  type remoteTunnelProxyForceUpdatePostParams = {
    /** identifier */
    identifier: string;
  };

  type remoteTunnelProxyGetTimestampPostParams = {
    /** identifier */
    identifier: string;
  };

  type remoteTunnelProxyPingFasttestAndConnectTransit2PostParams = {
    /** identifier */
    identifier: string;
    /** forceReconnect */
    forceReconnect?: boolean;
    /** transitIds */
    transitIds: string;
  };

  type remoteTunnelProxyPingFasttestAndConnectTransitByIdPostParams = {
    /** proxyId */
    proxyId: number;
    /** forceReconnect */
    forceReconnect?: boolean;
    /** domestic */
    domestic?: boolean;
  };

  type remoteTunnelProxyPingFasttestAndConnectTransitPostParams = {
    /** identifier */
    identifier: string;
    /** forceReconnect */
    forceReconnect?: boolean;
    /** domestic */
    domestic?: boolean;
  };

  type remoteTunnelProxyProbeTcpPostParams = {
    /** identifier */
    identifier: string;
    /** host */
    host: string;
    /** port */
    port: number;
  };

  type remoteTunnelProxyProbeToTransitPostParams = {
    /** identifier */
    identifier: string;
    /** localIp */
    localIp?: string;
    /** transitId */
    transitId: number;
  };

  type remoteTunnelProxyRunCmdPostParams = {
    /** identifier */
    identifier: string;
    /** cmds */
    cmds: string;
    /** timeout */
    timeout?: number;
    /** charset */
    charset?: string;
  };

  type remoteTunnelProxyStartDf2ServerPostParams = {
    /** identifier */
    identifier: string;
    /** bindAddress */
    bindAddress?: string;
    /** bindPort */
    bindPort: number;
    /** contextPath */
    contextPath: string;
    /** ignoreLocalBindAddress */
    ignoreLocalBindAddress?: boolean;
    /** signKey */
    signKey: string;
    /** https */
    https?: boolean;
  };

  type remoteTunnelProxyStartProxyForwardServerPostParams = {
    /** identifier */
    identifier: string;
  };

  type remoteTunnelProxyStartSocks5ServerPostParams = {
    /** identifier */
    identifier: string;
    /** bindAddress */
    bindAddress?: string;
    /** bindPort */
    bindPort: number;
    /** auth */
    auth?: boolean;
    /** username */
    username?: string;
    /** password */
    password?: string;
  };

  type remoteTunnelProxyStopBindServerPostParams = {
    /** identifier */
    identifier: string;
    /** bindAddress */
    bindAddress?: string;
    /** bindPort */
    bindPort: number;
  };

  type remoteTunnelProxyStopProxyForwardServersPostParams = {
    /** identifier */
    identifier: string;
  };

  type remoteTunnelProxyStoreSshKeyPutParams = {
    /** identifier */
    identifier: string;
    /** user */
    user: string;
    /** sshKey */
    sshKey: string;
  };

  type remoteTunnelProxyUpdateProxyForwardServerAuthPostParams = {
    /** identifier */
    identifier: string;
  };

  type remoteTunnelProxyUserDataGetParams = {
    /** ipgoScriptType */
    ipgoScriptType: 'initialize' | 'install';
    /** provider */
    provider:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    /** regionId */
    regionId: string;
    /** tcpFp */
    tcpFp: boolean;
    /** platform */
    platform: 'android' | 'linux' | 'macos' | 'unknown' | 'web' | 'windows' | 'windows7';
    /** teamId */
    teamId: number;
    /** creatorId */
    creatorId: number;
    /** base64 */
    base64: boolean;
    /** orderItemId */
    orderItemId?: number;
  };

  type remoteTunnelRestartProxyGetParams = {
    /** identifier */
    identifier: string;
  };

  type remoteTunnelStartTunnelTraceGetParams = {
    /** target */
    target: string;
    /** module */
    module: string;
    /** minites */
    minites: number;
  };

  type remoteTunnelStopProxyGetParams = {
    /** identifier */
    identifier: string;
  };

  type remoteTunnelStopTunnelTraceGetParams = {
    /** target */
    target: string;
    /** module */
    module: string;
  };

  type remoteTunnelSystemInfoGetParams = {
    /** target */
    target?: string;
  };

  type remoteTunnelTemplateGetOrCreateGetParams = {
    /** teamId */
    teamId?: number;
  };

  type remoteTunnelTemplatePostParams = {
    /** teamId */
    teamId?: number;
  };

  type remoteTunnelTemplatesGetParams = {
    /** teamId */
    teamId?: number;
  };

  type remoteTunnelTransitProbeTcpPostParams = {
    /** transitId */
    transitId: number;
    /** host */
    host: string;
    /** port */
    port: number;
  };

  type remoteTunnelTunnelConnStatGetParams = {
    /** target */
    target?: string;
  };

  type remoteTunnelTunnelDisconnectToTargetByTypePostParams = {
    /** source */
    source: string;
    /** type */
    type: string;
  };

  type remoteTunnelTunnelDisconnectToTargetPostParams = {
    /** source */
    source: string;
    /** target */
    target: string;
  };

  type remoteTunnelTunnelRouterL2GetParams = {
    /** target */
    target: string;
  };

  type remoteTunnelUninstallProxyGetParams = {
    /** identifier */
    identifier: string;
  };

  type ReportedIpVo = {
    bindTeam?: number;
    bindTeamName?: string;
    checkDelay?: number;
    domestic?: boolean;
    id?: number;
    iface?: string;
    innerIp?: string;
    ipv6?: boolean;
    locale?: string;
    location?: IpLocation;
    locationId?: number;
    macAddr?: string;
    message?: string;
    previousIp?: string;
    remoteIp?: string;
    status?: 'BoundByOtherTeam' | 'Imported' | 'NotImported';
    timezone?: string;
  };

  type ReportIpRequest = {
    autoImportNewIp?: boolean;
    autoRemoveIp?: boolean;
    autoUpdateIp?: boolean;
    ips?: IpProfile[];
  };

  type ReportIpResult = {
    ips?: ReportedIpVo[];
  };

  type RotatingIp = {
    city?: string;
    cityCode?: string;
    countryCode?: string;
    expireTime?: string;
    host?: string;
    isp?: string;
    outboundIp?: string;
    password?: string;
    port?: number;
    provinceCode?: string;
    proxyType?: string;
    tunnel?: boolean;
    username?: string;
  };

  type RpcConnectedPeers = {
    addresses?: TunnelAddress[];
    tunnelAddress?: TunnelAddress;
  };

  type SpyListBeansVo = {
    count?: number;
    keys?: ObjectInfo[];
  };

  type SpyObjectVo = {
    classes?: FieldsInfo[];
  };

  type SpyVo = {
    beans?: SpyListBeansVo;
    object?: SpyObjectVo;
  };

  type StartProxyForwardResult = {
    alreadyStarted?: boolean;
    bindIp?: string;
    port?: number;
    proxyId?: number;
  };

  type StartProxyForwardVo = {
    auth?: boolean;
    bindIp?: string;
    enabled?: boolean;
    forwardIp?: string;
    password?: string;
    port?: number;
    proxyId?: number;
    proxyType?: string;
    username?: string;
    whitelist?: string[];
    whitelistEnabled?: boolean;
  };

  type StopProxyForwardVo = {
    ports?: number[];
  };

  type TransitConnectionInfo = {
    domestic?: boolean;
    endpoints?: string;
    favorite?: boolean;
    forceReconnect?: boolean;
    id?: number;
    ping?: number;
    working?: boolean;
  };

  type TransitInfo = {
    domestic?: boolean;
    endpoints?: string;
    id?: number;
    working?: boolean;
  };

  type TunnelAddress = {
    identity?: string;
    type?: string;
  };

  type TunnelConnStat = {
    clientDfSessionStat?: DynamicForwardStat;
    peerTypeStatMap?: Record<string, any>;
    serverDfSessionStat?: DynamicForwardStat;
    statMap?: Record<string, any>;
  };

  type tunnelPingTransitsGetParams = {
    /** token */
    token: string;
  };

  type UpdateProxyForwardAuth = {
    auth?: boolean;
    password?: string;
    proxyId?: number;
    username?: string;
    whitelist?: string[];
    whitelistEnabled?: boolean;
  };

  type WebResult = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCheckCodeResult = {
    code?: number;
    data?: CheckCodeResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultExecResult = {
    code?: number;
    data?: ExecResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultForceDisconnectResult = {
    code?: number;
    data?: ForceDisconnectResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGitInfoVo = {
    code?: number;
    data?: GitInfoVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultInstallScriptVo = {
    code?: number;
    data?: InstallScriptVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultint = {
    code?: number;
    data?: number;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListGatewayDto = {
    code?: number;
    data?: GatewayDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListGatewayTemplateDto = {
    code?: number;
    data?: GatewayTemplateDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTunnelAddress = {
    code?: number;
    data?: TunnelAddress[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultlong = {
    code?: number;
    data?: number;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMap = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMapstring = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultGatewayDetailVo = {
    code?: number;
    data?: PageResultGatewayDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultProbeResult = {
    code?: number;
    data?: ProbeResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultProduceIpResultVo = {
    code?: number;
    data?: ProduceIpResultVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultProduceIpSpecVo = {
    code?: number;
    data?: ProduceIpSpecVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultProxyMeta = {
    code?: number;
    data?: ProxyMeta;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultProxyProbeWithRemoteIp = {
    code?: number;
    data?: ProxyProbeWithRemoteIp;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultProxyTemplateVo = {
    code?: number;
    data?: ProxyTemplateVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultProxyTokenCodeVo = {
    code?: number;
    data?: ProxyTokenCodeVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultProxyTokenVo = {
    code?: number;
    data?: ProxyTokenVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultProxyVersionInfo = {
    code?: number;
    data?: ProxyVersionInfo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultReportIpResult = {
    code?: number;
    data?: ReportIpResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRpcConnectedPeers = {
    code?: number;
    data?: RpcConnectedPeers;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultSpyVo = {
    code?: number;
    data?: SpyVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultStartProxyForwardResult = {
    code?: number;
    data?: StartProxyForwardResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultstring = {
    code?: number;
    data?: string;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTransitConnectionInfo = {
    code?: number;
    data?: TransitConnectionInfo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTunnelAddress = {
    code?: number;
    data?: TunnelAddress;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTunnelConnStat = {
    code?: number;
    data?: TunnelConnStat;
    message?: string;
    requestId?: string;
    success?: boolean;
  };
}
