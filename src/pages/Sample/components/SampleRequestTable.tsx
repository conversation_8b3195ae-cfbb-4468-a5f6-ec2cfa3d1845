import { StyledLayout } from '@/style/styled';
import useSampleRequestHeader from '@/pages/Sample/components/useSampleRequestHeader';
import type { ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { scrollProTableOptionFn } from '@/mixins/table';
import { tkshopSampleRequestPageV2Post } from '@/services/api-TKShopAPI/TkshopSampleRequestController';
import type { CSSProperties } from 'react';
import { forwardRef, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { Button, Tooltip } from 'antd';
import IconFontIcon from '@/components/Common/IconFontIcon';
import I18N from '@/i18n';
import { StyledOverflow } from '@/components/Common/MoreDropdown';
import { GhostModalCaller } from '@/mixins/modal';
import SampleGroupActionsModal from '@/pages/Sample/components/SampleGroupActionsModal';
import { SampleBreadCrumb } from '@/pages/Sample/components/utils';

const SampleRequestTable = forwardRef((props: { shopId?: number; style?: CSSProperties }, ref) => {
  const { shopId, style } = props;
  const actionRef = useRef<ActionType>();
  const [selectedVos, changeSelectedVos] = useState<API.TkshopSampleRequestAuditVo[]>([]);
  const onReset = useRef((reset?: boolean) => {
    if (reset) {
      actionRef.current?.reloadAndRest?.();
    } else {
      actionRef.current?.reload?.();
    }
  });

  useImperativeHandle(ref, () => {
    return actionRef.current;
  });
  const disabled = useMemo(() => selectedVos.length === 0, [selectedVos]);

  const nodes = useMemo(() => {
    return [
      {
        node: (_props) => {
          return (
            <Button
              disabled={disabled}
              icon={<IconFontIcon size={16} iconName={'daren_24'} />}
              onClick={() => {
                if (disabled) {
                  return;
                }
                GhostModalCaller(
                  <SampleGroupActionsModal
                    data={selectedVos}
                    shopId={selectedVos[0].shopId!}
                    onUpdate={() => {
                      onReset.current(false);
                    }}
                  />,
                );
              }}
              {..._props}
            >
              <span>达人批量操作</span>
            </Button>
          );
        },
        key: 'creators',
      },
    ];
  }, [disabled, selectedVos]);
  const {
    header,
    tableWidth,
    tableHeader,
    getSearchParams,
    getSortParams,
    columns,
    isInitialized,
  } = useSampleRequestHeader({
    onChange: onReset.current,
    shopId,
  });
  return (
    <StyledLayout style={style}>
      <div style={{ paddingLeft: 0 }} className="header">
        <SampleBreadCrumb shopId={shopId} />
        {header}
      </div>
      <div className="main">
        <ProTable
          columns={columns}
          actionRef={actionRef}
          components={{
            header: tableHeader,
          }}
          rowSelection={{
            selectedRowKeys: selectedVos.map((item) => item.id!),
            onChange: (keys, selectedRows) => {
              changeSelectedVos(selectedRows);
            },
          }}
          {...scrollProTableOptionFn({
            scroll: {
              x: tableWidth,
            },
            pageId: 'SampleRequestTable',
            alwaysShowFooter: true,
            footer: () => {
              return (
                <Tooltip
                  placement={'topLeft'}
                  title={disabled ? I18N.t('请至少选择一笔记录') : undefined}
                >
                  <StyledOverflow disabled={disabled} itemWidth={110} data={nodes} />
                </Tooltip>
              );
            },
          })}
          request={(params) => {
            return tkshopSampleRequestPageV2Post({
              includeMedia: true,
              notStatus: 'ToReview',
              pageNum: params.current,
              pageSize: params.pageSize,
              ...getSearchParams(),
              ...getSortParams(),
            }).then((res) => {
              // 对selectedVos进行过滤
              changeSelectedVos((prev) => {
                return prev.filter((item) => res.data?.list?.some((item2) => item2.id === item.id));
              });
              return {
                data: res.data?.list,
                total: res.data?.total,
              };
            });
          }}
          style={
            !isInitialized
              ? {
                  opacity: 0,
                  pointerEvents: 'none',
                }
              : {}
          }
        />
      </div>
    </StyledLayout>
  );
});
export default SampleRequestTable;
