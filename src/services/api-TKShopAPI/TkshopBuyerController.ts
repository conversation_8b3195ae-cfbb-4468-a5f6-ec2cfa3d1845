// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取买家详情 GET /api/tkshop/buyer/${param0} */
export async function tkshopBuyerByIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopBuyerByIdGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultTkshopBuyerDetailVo>(`/api/tkshop/buyer/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 更新买家联系方式 PUT /api/tkshop/buyer/${param0}/contact */
export async function tkshopBuyerByIdContactPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopBuyerByIdContactPutParams,
  body: API.UpdateContactRequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/tkshop/buyer/${param0}/contact`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 修改一种联系方式 PUT /api/tkshop/buyer/${param0}/oneContact */
export async function tkshopBuyerByIdOneContactPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopBuyerByIdOneContactPutParams,
  body: API.UpdateOneContactRequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/tkshop/buyer/${param0}/oneContact`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 更新买家备注 PUT /api/tkshop/buyer/${param0}/remark */
export async function tkshopBuyerByIdRemarkPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopBuyerByIdRemarkPutParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/tkshop/buyer/${param0}/remark`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取买家详情 GET /api/tkshop/buyer/byHandle */
export async function tkshopBuyerByHandleGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopBuyerByHandleGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkshopBuyerDto>('/api/tkshop/buyer/byHandle', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 计算一批买家关联的店铺情况 POST /api/tkshop/buyer/calcBuyerShops */
export async function tkshopBuyerCalcBuyerShopsPost(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkshopShopBuyerResult>('/api/tkshop/buyer/calcBuyerShops', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询店铺特定类型交互的次数 GET /api/tkshop/buyer/countTodayInteractions */
export async function tkshopBuyerCountTodayInteractionsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopBuyerCountTodayInteractionsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/tkshop/buyer/countTodayInteractions', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 新增好友关系 POST /api/tkshop/buyer/friendship */
export async function tkshopBuyerFriendshipPost(
  body: API.TkshopAddFriendshipRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/buyer/friendship', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量添加手动记录 POST /api/tkshop/buyer/interaction/addManualRemark */
export async function tkshopBuyerInteractionAddManualRemarkPost(
  body: API.AddTkshopManualRemarkRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/buyer/interaction/addManualRemark', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除手动记录 PUT /api/tkshop/buyer/interaction/deleteManualRemark */
export async function tkshopBuyerInteractionDeleteManualRemarkPut(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/tkshop/buyer/interaction/deleteManualRemark', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量修改手动记录 PUT /api/tkshop/buyer/interaction/updateManualRemark */
export async function tkshopBuyerInteractionUpdateManualRemarkPut(
  body: API.AddTkshopManualRemarkRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/buyer/interaction/updateManualRemark', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询买家交互记录 GET /api/tkshop/buyer/interactions */
export async function tkshopBuyerInteractionsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopBuyerInteractionsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkshopBuyerInteractionDto>(
    '/api/tkshop/buyer/interactions',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 查询特定类型的最后一次交互 GET /api/tkshop/buyer/lastInteraction */
export async function tkshopBuyerLastInteractionGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopBuyerLastInteractionGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkshopBuyerInteractionDto>('/api/tkshop/buyer/lastInteraction', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询团队买家 POST /api/tkshop/buyer/page */
export async function tkshopBuyerPagePost(
  body: API.PageTkshopBuyerRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkshopBuyerDetailVo>('/api/tkshop/buyer/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取达人的区域（根据用户权限过滤） GET /api/tkshop/buyer/regions */
export async function tkshopBuyerRegionsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListstring>('/api/tkshop/buyer/regions', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 同步买家（批量） POST /api/tkshop/syncBuyers */
export async function tkshopSyncBuyersPost(
  body: API.SyncBuyerRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/syncBuyers', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
