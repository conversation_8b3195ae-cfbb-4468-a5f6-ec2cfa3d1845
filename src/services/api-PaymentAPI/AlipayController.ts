// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** alipayNotify POST /api/payment/alipay/notify */
export async function paymentAlipayNotifyPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.paymentAlipayNotifyPostParams,
  options?: { [key: string]: any },
) {
  return request<string>('/api/payment/alipay/notify', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** alipayNotify POST /api/payment/alipay/notify/${param0} */
export async function paymentAlipayNotifyByTeamIdPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.paymentAlipayNotifyByTeamIdPostParams,
  options?: { [key: string]: any },
) {
  const { teamId: param0, ...queryParams } = params;
  return request<string>(`/api/payment/alipay/notify/${param0}`, {
    method: 'POST',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** alipayReturn POST /api/payment/alipay/return */
export async function paymentAlipayReturnPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.paymentAlipayReturnPostParams,
  options?: { [key: string]: any },
) {
  return request<any>('/api/payment/alipay/return', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** alipayReturn POST /api/payment/alipay/return/${param0} */
export async function paymentAlipayReturnByTeamIdPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.paymentAlipayReturnByTeamIdPostParams,
  options?: { [key: string]: any },
) {
  const { teamId: param0, ...queryParams } = params;
  return request<any>(`/api/payment/alipay/return/${param0}`, {
    method: 'POST',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}
