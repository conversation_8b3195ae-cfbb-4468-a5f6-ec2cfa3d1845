// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 修改账户基本信息 PUT /api/shop/settings/${param0}/basicInfo */
export async function shopSettingsByShopIdBasicInfoPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSettingsByShopIdBasicInfoPutParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/shop/settings/${param0}/basicInfo`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 修改账户IM账号 PUT /api/shop/settings/${param0}/chatList */
export async function shopSettingsByShopIdChatListPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSettingsByShopIdChatListPutParams,
  body: API.UpdateChatListRequest,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/shop/settings/${param0}/chatList`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 按域名group返回账户的cookie数量 GET /api/shop/settings/${param0}/domainCookies */
export async function shopSettingsByShopIdDomainCookiesGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSettingsByShopIdDomainCookiesGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultListDomainCookieVo>(`/api/shop/settings/${param0}/domainCookies`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改账户名称和描述 PUT /api/shop/settings/${param0}/nameAndDesc */
export async function shopSettingsByShopIdNameAndDescPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSettingsByShopIdNameAndDescPutParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/shop/settings/${param0}/nameAndDesc`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 根据url和username查询密码 GET /api/shop/settings/${param0}/password */
export async function shopSettingsByShopIdPasswordGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSettingsByShopIdPasswordGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultShopPasswordsDto>(`/api/shop/settings/${param0}/password`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 新增账户代填密码 POST /api/shop/settings/${param0}/password */
export async function shopSettingsByShopIdPasswordPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSettingsByShopIdPasswordPostParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultShopPasswordsDto>(`/api/shop/settings/${param0}/password`, {
    method: 'POST',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 修改账户代填密码 PUT /api/shop/settings/${param0}/password/${param1} */
export async function shopSettingsByShopIdPasswordByPasswordIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSettingsByShopIdPasswordByPasswordIdPutParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, passwordId: param1, ...queryParams } = params;
  return request<API.WebResultShopPasswordsDto>(`/api/shop/settings/${param0}/password/${param1}`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取账户浏览器密码 GET /api/shop/settings/${param0}/passwords */
export async function shopSettingsByShopIdPasswordsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSettingsByShopIdPasswordsGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultListShopPasswordsVo>(`/api/shop/settings/${param0}/passwords`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 账户会话关闭的时候重新上传密码。 POST /api/shop/settings/${param0}/passwords */
export async function shopSettingsByShopIdPasswordsPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSettingsByShopIdPasswordsPostParams,
  body: Record<string, any>[],
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/shop/settings/${param0}/passwords`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 删除账户的某些密码 DELETE /api/shop/settings/${param0}/passwords */
export async function shopSettingsByShopIdPasswordsDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSettingsByShopIdPasswordsDeleteParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/shop/settings/${param0}/passwords`, {
    method: 'DELETE',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 根据域名删除cookie DELETE /api/shop/settings/${param0}/removeCookiesByDomain */
export async function shopSettingsByShopIdRemoveCookiesByDomainDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSettingsByShopIdRemoveCookiesByDomainDeleteParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/shop/settings/${param0}/removeCookiesByDomain`, {
    method: 'DELETE',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取一个关联账户桥接码token GET /api/shop/settings/${param0}/reportToken */
export async function shopSettingsByShopIdReportTokenGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSettingsByShopIdReportTokenGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultFetchTokenVo>(`/api/shop/settings/${param0}/reportToken`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 查询账户敏感信息 GET /api/shop/settings/${param0}/sensitiveInfo */
export async function shopSettingsByShopIdSensitiveInfoGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSettingsByShopIdSensitiveInfoGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultstring>(`/api/shop/settings/${param0}/sensitiveInfo`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改账户敏感信息 PUT /api/shop/settings/${param0}/sensitiveInfo */
export async function shopSettingsByShopIdSensitiveInfoPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSettingsByShopIdSensitiveInfoPutParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/shop/settings/${param0}/sensitiveInfo`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取分身云端同步策略 GET /api/shop/settings/${param0}/shopPolicy */
export async function shopSettingsByShopIdShopPolicyGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSettingsByShopIdShopPolicyGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultShopSyncPolicyVo>(`/api/shop/settings/${param0}/shopPolicy`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 更改分身云端同步策略 POST /api/shop/settings/${param0}/updateShopPolicy */
export async function shopSettingsByShopIdUpdateShopPolicyPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSettingsByShopIdUpdateShopPolicyPostParams,
  body: API.ShopSyncPolicyVo,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/shop/settings/${param0}/updateShopPolicy`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** getFingerWhitelistConfig GET /api/shop/settings/fingerWhiteConfig */
export async function shopSettingsFingerWhiteConfigGet(options?: { [key: string]: any }) {
  return request<API.WebResultSysFingerWhitelistConfig>('/api/shop/settings/fingerWhiteConfig', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取一个关联账户桥接码token详细信息 GET /api/shop/settings/getFetchTokenDetail */
export async function shopSettingsGetFetchTokenDetailGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSettingsGetFetchTokenDetailGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultFetchTokenDetailVo>('/api/shop/settings/getFetchTokenDetail', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 麦可龙获取安装IPGO的脚本 GET /api/shop/settings/getIPgoInstallScript */
export async function shopSettingsGetIPgoInstallScriptGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSettingsGetIPgoInstallScriptGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultInstallScriptVo>('/api/shop/settings/getIPgoInstallScript', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 删除一个关联账户桥接码token DELETE /api/shop/settings/removeFetchToken */
export async function shopSettingsRemoveFetchTokenDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSettingsRemoveFetchTokenDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/settings/removeFetchToken', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 麦可龙汇报账户cookie信息 POST /api/shop/settings/reportShopBrowserInfo/cookies */
export async function shopSettingsReportShopBrowserInfoCookiesPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSettingsReportShopBrowserInfoCookiesPostParams,
  body: API.MaiKeLongReportRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/settings/reportShopBrowserInfo/cookies', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 麦可龙汇报账户指纹信息 POST /api/shop/settings/reportShopBrowserInfo/fingerprint */
export async function shopSettingsReportShopBrowserInfoFingerprintPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSettingsReportShopBrowserInfoFingerprintPostParams,
  body: API.MaiKeLongReportRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultInstallScriptVo>(
    '/api/shop/settings/reportShopBrowserInfo/fingerprint',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      params: {
        ...params,
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 麦可龙汇报（账户克隆）完毕 GET /api/shop/settings/reportShopBrowserInfo/finish */
export async function shopSettingsReportShopBrowserInfoFinishGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSettingsReportShopBrowserInfoFinishGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/settings/reportShopBrowserInfo/finish', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 麦可龙汇报账户passwords信息 POST /api/shop/settings/reportShopBrowserInfo/passwords */
export async function shopSettingsReportShopBrowserInfoPasswordsPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSettingsReportShopBrowserInfoPasswordsPostParams,
  body: API.MaiKeLongReportRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultInstallScriptVo>(
    '/api/shop/settings/reportShopBrowserInfo/passwords',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      params: {
        ...params,
      },
      data: body,
      ...(options || {}),
    },
  );
}
