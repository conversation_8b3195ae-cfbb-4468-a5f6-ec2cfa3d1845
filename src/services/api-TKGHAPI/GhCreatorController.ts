// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 为该达人分配一个发私信的手机账号 PUT /api/gh/bindMobileAccountToCreator */
export async function ghBindMobileAccountToCreatorPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghBindMobileAccountToCreatorPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/bindMobileAccountToCreator', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 为该达人分配一个发私信的TikTok分身，返回绑定的shopId PUT /api/gh/bindPmShopToCreator */
export async function ghBindPmShopToCreatorPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghBindPmShopToCreatorPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultlong>('/api/gh/bindPmShopToCreator', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取kol四类用户对象之一 GET /api/gh/byHandle */
export async function ghByHandleGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghByHandleGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultKolUserInfo>('/api/gh/byHandle', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询达人详情 GET /api/gh/creator/${param0} */
export async function ghCreatorByCreatorIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghCreatorByCreatorIdGetParams,
  options?: { [key: string]: any },
) {
  const { creatorId: param0, ...queryParams } = params;
  return request<API.WebResultGhCreatorDetailVo>(`/api/gh/creator/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 认领达人 PUT /api/gh/creator/acquire */
export async function ghCreatorAcquirePut(
  body: API.GhCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/creator/acquire', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分配达人 PUT /api/gh/creator/allocate */
export async function ghCreatorAllocatePut(
  body: API.GhCreatorAllocateRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/creator/allocate', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分配达人【按查询】 PUT /api/gh/creator/allocateByQuery */
export async function ghCreatorAllocateByQueryPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghCreatorAllocateByQueryPutParams,
  body: API.FindGhCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/creator/allocateByQuery', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量查询达人是否可见（用handle） GET /api/gh/creator/availableList */
export async function ghCreatorAvailableListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghCreatorAvailableListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListGhAvailableVo>('/api/gh/creator/availableList', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取TK达人头像 GET /api/gh/creator/avatar */
export async function ghCreatorAvatarGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghCreatorAvatarGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/gh/creator/avatar', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 根据缓存配置删除达人 DELETE /api/gh/creator/byKeepInfo */
export async function ghCreatorByKeepInfoDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghCreatorByKeepInfoDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/gh/creator/byKeepInfo', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 取消分配 PUT /api/gh/creator/cancelAllocate */
export async function ghCreatorCancelAllocatePut(
  body: API.GhCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/creator/cancelAllocate', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 统计团队达人的数量 GET /api/gh/creator/countV2 */
export async function ghCreatorCountV2Get(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghCreatorCountV2GetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultlong>('/api/gh/creator/countV2', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 删除达人 POST /api/gh/creator/delete */
export async function ghCreatorDeletePost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghCreatorDeletePostParams,
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/creator/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除达人（按查询） POST /api/gh/creator/deleteByQuery */
export async function ghCreatorDeleteByQueryPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghCreatorDeleteByQueryPostParams,
  body: API.FindGhCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/creator/deleteByQuery', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量更新达人是否金牌主播 PUT /api/gh/creator/elite */
export async function ghCreatorElitePut(
  body: API.GhCreatorUpdateEliteRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/creator/elite', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取筛选配置和统计信息 GET /api/gh/creator/filterStat */
export async function ghCreatorFilterStatGet(options?: { [key: string]: any }) {
  return request<API.WebResultFilterStatInfo>('/api/gh/creator/filterStat', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 批量查询达人是否已经落库 POST /api/gh/creator/findExists */
export async function ghCreatorFindExistsPost(
  body: API.FilterExistsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListKolAvailableVo>('/api/gh/creator/findExists', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询团队达人 GET /api/gh/creator/page */
export async function ghCreatorPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghCreatorPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultGhCreatorDetailVo>('/api/gh/creator/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 根据筛选状态分页查询达人 GET /api/gh/creator/pageByAvailable */
export async function ghCreatorPageByAvailableGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghCreatorPageByAvailableGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultstring>('/api/gh/creator/pageByAvailable', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询我认领的达人 GET /api/gh/creator/pageResponsible */
export async function ghCreatorPageResponsibleGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghCreatorPageResponsibleGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultGhCreatorDetailVo>('/api/gh/creator/pageResponsible', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询团队达人V2（不返回总数） GET /api/gh/creator/pageV2 */
export async function ghCreatorPageV2Get(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghCreatorPageV2GetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultGhCreatorDetailVo>('/api/gh/creator/pageV2', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 恢复达人 POST /api/gh/creator/recover */
export async function ghCreatorRecoverPost(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/creator/recover', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据TK creatorId查询所在区域 POST /api/gh/creator/regionByCreatorIds */
export async function ghCreatorRegionByCreatorIdsPost(
  body: API.GhGetRegionByCreatorIdRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListKolRegionMapDto>('/api/gh/creator/regionByCreatorIds', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取TK达人区域 GET /api/gh/creator/regions */
export async function ghCreatorRegionsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghCreatorRegionsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListstring>('/api/gh/creator/regions', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 取消认领达人 PUT /api/gh/creator/release */
export async function ghCreatorReleasePut(
  body: API.GhCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/creator/release', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** getResponsibleUserList GET /api/gh/creator/responsibleUserList */
export async function ghCreatorResponsibleUserListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghCreatorResponsibleUserListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListlong>('/api/gh/creator/responsibleUserList', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 批量查询达人状态（用handle） GET /api/gh/creator/statusList */
export async function ghCreatorStatusListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghCreatorStatusListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListGhCreatorStatusVo>('/api/gh/creator/statusList', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 同步（创建）TK达人 POST /api/gh/creator/sync */
export async function ghCreatorSyncPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghCreatorSyncPostParams,
  body: API.GhCreatorDocument,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/creator/sync', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 同步TK达人头像 POST /api/gh/creator/syncAvatar */
export async function ghCreatorSyncAvatarPost(
  body: API.SyncCreatorAvatarRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/creator/syncAvatar', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量同步（创建）TK达人 POST /api/gh/creator/syncBatch */
export async function ghCreatorSyncBatchPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghCreatorSyncBatchPostParams,
  body: API.GhCreatorDocumentBatch,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/creator/syncBatch', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量更新达人是否有新消息（用handle更新） PUT /api/gh/creator/syncHasNewMsg */
export async function ghCreatorSyncHasNewMsgPut(
  body: API.GhCreatorSyncHasNewMsgRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/creator/syncHasNewMsg', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取待同步的达人handle列表 GET /api/gh/creator/syncList */
export async function ghCreatorSyncListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghCreatorSyncListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListHandleCreatorIdVo>('/api/gh/creator/syncList', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 批量更新达人不存在 POST /api/gh/creator/syncNotFoundBatch */
export async function ghCreatorSyncNotFoundBatchPost(
  body: API.GhSyncNotFoundRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/gh/creator/syncNotFoundBatch', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 同步达人的最高xx流水 POST /api/gh/creator/syncRevenue */
export async function ghCreatorSyncRevenuePost(
  body: API.GhRevenueDocument,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/creator/syncRevenue', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量更新达人状态（用handle） PUT /api/gh/creator/syncStatus */
export async function ghCreatorSyncStatusPut(
  body: API.GhCreatorSyncStatusRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListGhCreatorDto>('/api/gh/creator/syncStatus', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 给指定达人打标签 POST /api/gh/creator/tag */
export async function ghCreatorTagPost(
  body: API.TagCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/creator/tag', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 取消指定达人的特定标签 POST /api/gh/creator/untag */
export async function ghCreatorUntagPost(
  body: API.TagCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/creator/untag', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量更新达人是否为主播 PUT /api/gh/creator/updateAnchor */
export async function ghCreatorUpdateAnchorPut(
  body: API.GhCreatorUpdateAnchorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/creator/updateAnchor', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量更新达人可用性 PUT /api/gh/creator/updateAvailable */
export async function ghCreatorUpdateAvailablePut(
  body: API.GhCreatorUpdateAvailableRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListGhCreatorDto>('/api/gh/creator/updateAvailable', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量更新达人的BackstageShopId PUT /api/gh/creator/updateBackstageShopId */
export async function ghCreatorUpdateBackstageShopIdPut(
  body: API.GhCreatorUpdateBackstageShopIdRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/creator/updateBackstageShopId', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量更新达人是否为主播 PUT /api/gh/creator/updateGeneral */
export async function ghCreatorUpdateGeneralPut(
  body: API.GhCreatorUpdateGeneralRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/creator/updateGeneral', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量更新达人是否有新消息 PUT /api/gh/creator/updateHasNewMsg */
export async function ghCreatorUpdateHasNewMsgPut(
  body: API.GhCreatorUpdateHasNewMsgRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListGhCreatorDto>('/api/gh/creator/updateHasNewMsg', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量更新达人是否关注后私信 PUT /api/gh/creator/updateMessageStatus */
export async function ghCreatorUpdateMessageStatusPut(
  body: API.GhCreatorUpdateMessageStatusRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/creator/updateMessageStatus', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量更新达人是否开通橱窗 PUT /api/gh/creator/updateShowcase */
export async function ghCreatorUpdateShowcasePut(
  body: API.GhCreatorUpdateShowcaseRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/creator/updateShowcase', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量更新达人状态 PUT /api/gh/creator/updateStatus */
export async function ghCreatorUpdateStatusPut(
  body: API.GhCreatorUpdateStatusRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListGhCreatorDto>('/api/gh/creator/updateStatus', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 下载达人信息【按查询】 GET /api/gh/downloadByQuery/${param0} */
export async function ghDownloadByQueryByTokenGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghDownloadByQueryByTokenGetParams,
  options?: { [key: string]: any },
) {
  const { token: param0, ...queryParams } = params;
  return request<API.Resource>(`/api/gh/downloadByQuery/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 下载达人的token POST /api/gh/exportToken */
export async function ghExportTokenPost(
  body: API.ExportTkCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/gh/exportToken', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 导出达人（按查询） POST /api/gh/exportTokenByQuery */
export async function ghExportTokenByQueryPost(
  body: API.ExportTkCreatorByQueryRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/gh/exportTokenByQuery', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 达人可导出的字段元信息 GET /api/gh/fields */
export async function ghFieldsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTkCreatorFiledVo>('/api/gh/fields', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 用来通知有哪些手机账号有新消息了 PUT /api/gh/mobile/notifyNewTkMessage */
export async function ghMobileNotifyNewTkMessagePut(
  body: API.NotifyNewTkMessageRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/mobile/notifyNewTkMessage', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 给分身批量打标签 POST /api/gh/shop/tag */
export async function ghShopTagPost(body: API.GhTagRequest, options?: { [key: string]: any }) {
  return request<API.WebResult>('/api/gh/shop/tag', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 给分身批量删除标签 POST /api/gh/shop/untag */
export async function ghShopUntagPost(body: API.GhTagRequest, options?: { [key: string]: any }) {
  return request<API.WebResult>('/api/gh/shop/untag', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
