declare namespace API {
  type AccountCredentialVo = {
    account?: string;
    keyPair?: KeyPairDto;
    lastUpdatedTime?: string;
    password?: string;
  };

  type AccountInfo = {
    account?: string;
    arn?: string;
    userId?: string;
  };

  type customerCloudByCloudIdDeleteParams = {
    /** cloudId */
    cloudId: number;
  };

  type customerCloudByCloudIdGetParams = {
    /** cloudId */
    cloudId: number;
  };

  type customerCloudByCloudIdKeypairPutParams = {
    /** cloudId */
    cloudId: number;
    /** keyId */
    keyId: number;
  };

  type customerCloudByCloudIdPutParams = {
    /** cloudId */
    cloudId: number;
    /** accessKeyId */
    accessKeyId?: string;
    /** accessKeySecret */
    accessKeySecret?: string;
    /** name */
    name?: string;
    /** description */
    description?: string;
  };

  type customerCloudCheckGetParams = {
    /** provider */
    provider:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    /** accessKeyId */
    accessKeyId: string;
    /** accessKeySecret */
    accessKeySecret: string;
    /** cloudName */
    cloudName: string;
  };

  type customerCloudPageGetParams = {
    /** provider */
    provider?:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type customerCloudPostParams = {
    /** provider */
    provider:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    /** accessKeyId */
    accessKeyId: string;
    /** accessKeySecret */
    accessKeySecret: string;
    /** name */
    name: string;
    /** description */
    description?: string;
  };

  type CustomerCloudVo = {
    accessKeyId?: string;
    accessKeySecret?: string;
    account?: string;
    description?: string;
    extra?: string;
    hostCount?: number;
    id?: number;
    ipCount?: number;
    keyId?: number;
    name?: string;
    ownerArn?: string;
    provider?:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    teamId?: number;
    valid?: boolean;
  };

  type customerKeypairByKeyIdCheckGetParams = {
    /** keyId */
    keyId: number;
    /** privateKeyPem */
    privateKeyPem: string;
  };

  type customerKeypairByKeyIdGetParams = {
    /** keyId */
    keyId: number;
  };

  type customerKeypairByPubkeyPostParams = {
    /** 通常是以'ssh-rsa AAAA'开头的一段ssh授权登录公钥 */
    pubkey: string;
    /** 关联云账号 */
    cloudId?: number;
  };

  type customerKeypairPostParams = {
    /** 关联云账号 */
    cloudId?: number;
  };

  type eipInstanceByIdAccessInfoGetParams = {
    /** id */
    id: number;
  };

  type eipInstanceByIdDecryptWindowsPasswordGetParams = {
    /** id */
    id: number;
    /** privateKeyPem */
    privateKeyPem: string;
  };

  type KeyPairDto = {
    createTime?: string;
    fingerprint?: string;
    id?: number;
    importType?: 'Platform' | 'User';
    name?: string;
    privateKey?: string;
    publicKey?: string;
    teamId?: number;
  };

  type OperatingCloudDto = {
    accessKeyId?: string;
    accessKeySecret?: string;
    account?: string;
    description?: string;
    extra?: string;
    id?: number;
    keyId?: number;
    name?: string;
    ownerArn?: string;
    provider?:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    teamId?: number;
    valid?: boolean;
  };

  type PageResultCustomerCloudVo = {
    current?: number;
    list?: CustomerCloudVo[];
    pageSize?: number;
    total?: number;
  };

  type WebResult = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultAccountCredentialVo = {
    code?: number;
    data?: AccountCredentialVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultAccountInfo = {
    code?: number;
    data?: AccountInfo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultboolean = {
    code?: number;
    data?: boolean;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCustomerCloudVo = {
    code?: number;
    data?: CustomerCloudVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultKeyPairDto = {
    code?: number;
    data?: KeyPairDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultOperatingCloudDto = {
    code?: number;
    data?: OperatingCloudDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultCustomerCloudVo = {
    code?: number;
    data?: PageResultCustomerCloudVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultstring = {
    code?: number;
    data?: string;
    message?: string;
    requestId?: string;
    success?: boolean;
  };
}
