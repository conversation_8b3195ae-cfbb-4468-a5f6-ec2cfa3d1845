// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 过滤IP商品列表 POST /api/webhook/ip/regionsByFilter */
export async function webhookIpRegionsByFilterPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.webhookIpRegionsByFilterPostParams,
  body: API.IpGoodsFilterVo,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListIpRegionVo>('/api/webhook/ip/regionsByFilter', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取商品列表 GET /api/webhook/op/goodsList */
export async function webhookOpGoodsListGet(options?: { [key: string]: any }) {
  return request<API.WebResultGoodsPriceVo>('/api/webhook/op/goodsList', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取商品列表 GET /api/webhook/op/goodsList/v61 */
export async function webhookOpGoodsListV61Get(options?: { [key: string]: any }) {
  return request<API.WebResultGoodsPriceVo>('/api/webhook/op/goodsList/v61', {
    method: 'GET',
    ...(options || {}),
  });
}
