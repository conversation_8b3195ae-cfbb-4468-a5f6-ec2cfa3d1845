// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** greet GET /api/wx/redirect/${param0}/greet */
export async function wxRedirectByAppidGreetGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.wxRedirectByAppidGreetGetParams,
  options?: { [key: string]: any },
) {
  const { appid: param0, ...queryParams } = params;
  return request<Record<string, any>>(`/api/wx/redirect/${param0}/greet`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}
