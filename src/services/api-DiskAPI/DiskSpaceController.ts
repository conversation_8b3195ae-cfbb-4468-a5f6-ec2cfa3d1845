// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** getDiskSpacePriceInfo GET /api/disk/diskSpacePriceInfo */
export async function diskDiskSpacePriceInfoGet(options?: { [key: string]: any }) {
  return request<API.WebResultDiskSpacePriceVo>('/api/disk/diskSpacePriceInfo', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取团队录像和rpa文件保留天数 GET /api/disk/getFileKeepDays */
export async function diskGetFileKeepDaysGet(options?: { [key: string]: any }) {
  return request<API.WebResultFileKeepDaysVo>('/api/disk/getFileKeepDays', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 空间分布 GET /api/disk/spaceGrid */
export async function diskSpaceGridGet(options?: { [key: string]: any }) {
  return request<API.WebResultListDiskTreeNode>('/api/disk/spaceGrid', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 使用空间概览 GET /api/disk/spaceStat */
export async function diskSpaceStatGet(options?: { [key: string]: any }) {
  return request<API.WebResultDiskStatVo>('/api/disk/spaceStat', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 返回10大文件 GET /api/disk/top10Files */
export async function diskTop10FilesGet(options?: { [key: string]: any }) {
  return request<API.WebResultListDiskFileItem>('/api/disk/top10Files', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 设置团队录像和rpa文件保留天数 PUT /api/disk/updateFileKeepDays */
export async function diskUpdateFileKeepDaysPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.diskUpdateFileKeepDaysPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/disk/updateFileKeepDays', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修改云盘存储空间告警 PUT /api/disk/updateStorageAlert */
export async function diskUpdateStorageAlertPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.diskUpdateStorageAlertPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/disk/updateStorageAlert', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
