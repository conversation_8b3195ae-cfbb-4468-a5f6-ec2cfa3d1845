import I18N from '@/i18n';
import { Helmet, useRequest } from 'umi';
import { Button, Tooltip, Typography } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { CreatorBreadCrumb } from '@/pages/TikTok/utils/utils';
import MiddleSpin from '@/components/Common/MiddleSpin';
import { dateFormat } from '@/utils/utils';
import { tkshopCreatorByIdGet } from '@/services/api-TKShopAPI/TkshopCreatorController';
import { GhostModalCaller } from '@/mixins/modal';
import SyncCreatorSelectedModal from '@/pages/TikTok/components/SyncCreatorSelectedModal';
import type { TikTokCreatorRefer } from '@/pages/TikTok/utils/types';
import { FunctionCodeComponent } from '@/components/Common/FunctionButton';
import Functions from '@/constants/Functions';
import { StyledCenterTabs, StyledLayout } from '@/style/styled';
import styled from 'styled-components';
import CreatorSalesPanel from '@/pages/TikTok/Live/components/CreatorSalesPanel';
import CreatorBasicPanel from '@/pages/TikTok/Live/components/CreatorBasicPanel';
import { GlobalHeaderAction } from '@/utils/pageUtils';

const StyledHeader = styled.div`
  padding-left: 0 !important;
  position: relative;
  && {
    .ant-tabs {
      position: absolute;
      right: 0;
      left: 0;
      z-index: 2;
      width: 300px;
      margin: 0 auto;
      .ant-tabs-tab {
        padding: 14px 12px;
      }
    }
  }
`;
const CreatorDetailTabs = (props: { refer: TikTokCreatorRefer; id: number }) => {
  const { refer, id } = props;
  const { data, run: reload, loading } = useRequest(() => tkshopCreatorByIdGet({ id }));
  const [tab, setTab] = useState<'basic' | 'cooperation'>('basic');
  const actions = useMemo(() => {
    return (
      <Button
        ghost
        style={{ background: 'none' }}
        icon={<IconFontIcon iconName={'shuaxin_24'} />}
        onClick={reload}
      >
        {I18N.t('刷新')}
      </Button>
    );
  }, [reload]);
  const logOfUpdate = useMemo(() => {
    if (data) {
      return (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <Tooltip
            title={I18N.t(
              '通过执行“达人基础信息更新”流程，可以抓取达人的带货能力等各个维度的指标（如最近30天销售数据、协作指标、最近30天视频带货数据、最近30天直播带货数据等），如果达人留有联系方式，还会抓取达人的联系方式',
            )}
          >
            <Typography.Link>
              <IconFontIcon iconName={'info_241'} />
            </Typography.Link>
          </Tooltip>
          <div style={{ display: 'flex', alignItems: 'center', gap: 24 }}>
            <div style={{ color: '#999' }}>
              {I18N.t('基础信息更新时间：')}
              {data.basicSyncTime ? (
                dateFormat(new Date(data.basicSyncTime), 'YYYY-MM-DD HH:mm')
              ) : (
                <Typography.Text type={'secondary'}>{I18N.t('未曾更新')}</Typography.Text>
              )}
            </div>
            <FunctionCodeComponent
              as={'a'}
              code={[Functions.RPA_LIST, Functions.RPA_RUN]}
              onClick={() => {
                GhostModalCaller(<SyncCreatorSelectedModal selected={[data]} />);
              }}
            >
              {I18N.t('立即更新')}
            </FunctionCodeComponent>
          </div>
        </div>
      );
    }
    return null;
  }, [data]);

  useEffect(() => {
    return () => {
      // 组件卸载时恢复默认标题
      document.title = I18N.t('花漾TK');
    };
  }, []);

  const content = useMemo(() => {
    if (!data || loading) {
      return <MiddleSpin />;
    }
    if (tab === 'basic') {
      return <CreatorBasicPanel data={data} onUpdate={reload} />;
    }
    // 带货能力
    return <CreatorSalesPanel data={data} />;
  }, [data, loading, reload, tab]);

  return (
    <StyledLayout>
      {data?.handle && (
        <Helmet>
          <title>
            {I18N.t('花漾TK@{{name}}详情', {
              name: data.handle,
            })}
          </title>
        </Helmet>
      )}
      <GlobalHeaderAction.Emit>{actions}</GlobalHeaderAction.Emit>
      <StyledHeader className="header">
        <CreatorBreadCrumb refer={refer} id={id} />
        <StyledCenterTabs
          accessKey={tab}
          onChange={setTab}
          items={[
            {
              label: '基本信息',
              key: 'basic',
            },
            {
              label: '带货能力',
              key: 'cooperation',
            },
          ]}
        />
        <span>{logOfUpdate}</span>
      </StyledHeader>
      <div className="main" style={{ padding: 16, overflow: 'auto' }}>
        {content}
      </div>
    </StyledLayout>
  );
};
export default CreatorDetailTabs;
