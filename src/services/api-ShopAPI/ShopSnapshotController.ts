// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 给指定的分身创建一个快照 POST /api/shop/snapshot/create */
export async function shopSnapshotCreatePost(
  body: API.CreateShopSnapshotRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/snapshot/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 给当前用户有权限的所有分身创建快照 POST /api/shop/snapshot/createAll */
export async function shopSnapshotCreateAllPost(
  body: API.CreateSnapshotForAllShopsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/snapshot/createAll', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除一个快照 DELETE /api/shop/snapshot/delete */
export async function shopSnapshotDeleteDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSnapshotDeleteDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/snapshot/delete', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取某个分身的快照列表 GET /api/shop/snapshot/list */
export async function shopSnapshotListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSnapshotListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListShopSnapshotVo>('/api/shop/snapshot/list', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 恢复到一个快照 POST /api/shop/snapshot/restore */
export async function shopSnapshotRestorePost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSnapshotRestorePostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/snapshot/restore', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 更新一个快照信息 POST /api/shop/snapshot/update */
export async function shopSnapshotUpdatePost(
  body: API.UpdateShopSnapshotRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultShopSnapshotVo>('/api/shop/snapshot/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更改一个快照的锁定状态 POST /api/shop/snapshot/updateLockStatus */
export async function shopSnapshotUpdateLockStatusPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSnapshotUpdateLockStatusPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/snapshot/updateLockStatus', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
