import type { CardProps } from 'antd';
import { Typography } from 'antd';
import { Card, Space } from 'antd';
import ColoursIcon from '@/components/Common/ColoursIcon';
import { CreatorCorpShopSelector } from '@/components/Common/Selector/ShopSelector';
import cardStyles from '@/style/card.less';
import { useMemo, useState } from 'react';
import ProTable from '@ant-design/pro-table';
import { scrollProTableOptionFn } from '@/mixins/table';
import { tkshopSampleRequestPageV2Post } from '@/services/api-TKShopAPI/TkshopSampleRequestController';
import Placeholder from '@/components/Common/Placeholder';
import { dateFormat } from '@/utils/utils';
import { ProductAvatar } from '@/pages/VideoManage/components/utils';
import { useDetailOutsideProduct } from '@/pages/ProductManage/components/ProductDetailModal';
import { ShopNameLinkById } from '@/components/Common/ShopNode';
import IconFontIcon from '@/components/Common/IconFontIcon';
import styled from 'styled-components';
import { StyledTableWrapper } from '@/style/styled';
import classNames from 'classnames';

const StyledDiv = styled.div<{ progress?: boolean }>`
  display: flex;
  overflow: hidden;
  align-items: stretch;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 4px;
  right: 4px;
  .dm-iconFontIcon {
    display: block;
  }
  > div {
    position: relative;
    display: flex;
    flex: 1;
    align-items: center;
    padding-right: 4px;
    padding-left: 4px;
    overflow: hidden;
    white-space: nowrap;
    &.progress {
      &:after {
        position: absolute;
        top: 50%;
        right: 0;
        left: 24px;
        border-bottom: 1px dashed #0f7cf4;
        transform: translateY(-50%);
        content: '';
      }
    }
  }
`;
export const StatusColumns = (props: { items?: any[]; progress?: boolean }) => {
  const { items = ['待审批', '待发货', '待产出', '媒体成果'], progress = true } = props;
  return (
    <StyledDiv>
      <div className={classNames({ progress: progress && items.length > 1 })}>{items[0]}</div>
      <div className={classNames({ progress: progress && items.length > 2 })}>{items[1]}</div>
      <div className={classNames({ progress: progress && items.length > 3 })}>{items[2]}</div>
      <div>{items[3]}</div>
    </StyledDiv>
  );
};
export const StatusColumnWidth = 400;
export const SampleRequestProgressCell = (props: {
  data: API.TkshopSampleRequestAuditVo;
  onDetail: (type: 'lives' | 'videos') => void;
}) => {
  const { data, onDetail } = props;
  const { status, videos, lives } = data;
  const ICON_SIZE = 16;
  if (status === 'Reject') {
    //已拒绝
    return (
      <StatusColumns
        items={[
          <Typography.Text key={'type'} type={'danger'}>
            <Space size={4}>
              <IconFontIcon size={ICON_SIZE} iconName={'Close-Circle_24'} />
              <span>已拒绝</span>
            </Space>
          </Typography.Text>,
        ]}
      />
    );
  }
  if (status === 'Expired') {
    // 已过期
    return (
      <StatusColumns
        items={[
          <Placeholder
            key={'type'}
            text={
              <Space size={4}>
                <IconFontIcon size={ICON_SIZE} iconName={'yiguoqi_24'} />
                <span>已过期</span>
              </Space>
            }
          />,
        ]}
      />
    );
  }
  if (status === 'Cancelled') {
    // 已取消
    return (
      <StatusColumns
        items={[
          <Typography.Text key={'type'} type={'secondary'}>
            <Space size={4}>
              <IconFontIcon size={ICON_SIZE} iconName={'huifuluxiang_24'} />
              <span>已取消</span>
            </Space>
          </Typography.Text>,
        ]}
      />
    );
  }
  if (status === 'ToReview') {
    // 待审批
    return (
      <StatusColumns
        items={[
          <Typography.Text key={'type'} type={'warning'}>
            <Space size={4}>
              <IconFontIcon size={ICON_SIZE} iconName={'shijian_24'} />
              <span>待审批</span>
            </Space>
          </Typography.Text>,
        ]}
      />
    );
  }
  if (status === 'ReadyToShip') {
    // 已审批，待发货
    return (
      <StatusColumns
        items={[
          <Typography.Text key={'type1'} type={'success'}>
            <IconFontIcon size={ICON_SIZE} iconName={'Check-Circle_24'} />
          </Typography.Text>,
          <Typography.Text key={'type2'} type={'warning'}>
            <IconFontIcon size={ICON_SIZE} iconName={'shijian_24'} />
          </Typography.Text>,
        ]}
      />
    );
  }
  if (status === 'Shipped' || status === 'InProgress') {
    // 已发货，待产出
    return (
      <StatusColumns
        items={[
          <Typography.Text key={'type1'} type={'success'}>
            <IconFontIcon size={ICON_SIZE} iconName={'Check-Circle_24'} />
          </Typography.Text>,
          <Typography.Text key={'type2'} type={'success'}>
            <IconFontIcon size={ICON_SIZE} iconName={'Check-Circle_24'} />
          </Typography.Text>,
          <Typography.Text key={'type3'} type={'warning'}>
            <IconFontIcon size={ICON_SIZE} iconName={'shijian_24'} />
          </Typography.Text>,
        ]}
      />
    );
  }
  let livesNode: any;
  let videosNode: any;
  if (lives?.length) {
    livesNode = (
      <div
        onClick={() => {
          onDetail('lives');
        }}
        style={{
          display: 'inline-flex',
          alignItems: 'center',
          gap: 4,
          lineHeight: 1,
          cursor: 'pointer',
        }}
      >
        <div>
          <ColoursIcon size={ICON_SIZE} className="luxiang_24" />
        </div>
        <Typography.Link ellipsis>x{lives?.length}</Typography.Link>
      </div>
    );
  }
  if (videos?.length) {
    videosNode = (
      <div
        onClick={() => {
          onDetail('videos');
        }}
        style={{
          display: 'inline-flex',
          alignItems: 'center',
          gap: 4,
          lineHeight: 1,
          cursor: 'pointer',
        }}
      >
        <div>
          <ColoursIcon size={ICON_SIZE} className="duanshipin_24" />
        </div>
        <Typography.Link ellipsis>x{videos?.length}</Typography.Link>
      </div>
    );
  }
  let output = (
    <div key={'output'} style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
      {videosNode}
      {livesNode}
    </div>
  );
  if (!videosNode && !livesNode) {
    output = (
      <Typography.Text key={'output'} type={'warning'}>
        暂无产出
      </Typography.Text>
    );
  }
  // 已完成产出
  return (
    <StatusColumns
      items={[
        <Typography.Text key={'type1'} type={'success'}>
          <IconFontIcon size={ICON_SIZE} iconName={'Check-Circle_24'} />
        </Typography.Text>,
        <Typography.Text key={'type2'} type={'success'}>
          <IconFontIcon size={ICON_SIZE} iconName={'Check-Circle_24'} />
        </Typography.Text>,
        <Typography.Text key={'type3'} type={'success'}>
          <IconFontIcon size={ICON_SIZE} iconName={'Check-Circle_24'} />
        </Typography.Text>,
        output,
      ]}
    />
  );
};

const SampleRequestCard = (
  props: {
    creator: API.TkshopCreatorDetailVo;
    refer?: 'Modal' | 'Page';
    onDetail: (type: 'lives' | 'videos') => void;
  } & CardProps,
) => {
  const { creator, refer = 'Page', onDetail, ...cardProps } = props;
  const [shopSelectOpen, setShopSelectOpen] = useState(false);
  const [shopId, setShopId] = useState<number | undefined>();
  const { run: openDetailModal } = useDetailOutsideProduct();

  const shopSelector = useMemo(() => {
    return (
      <CreatorCorpShopSelector
        creatorId={creator.id!}
        open={shopSelectOpen}
        onDropdownVisibleChange={setShopSelectOpen}
        style={{ flex: '0 0 150px', width: 150 }}
        value={shopId}
        onChange={(v) => {
          setShopId(v);
        }}
      />
    );
  }, [creator.id, shopSelectOpen, shopId]);
  const table = useMemo(() => {
    return (
      <ProTable<API.TkshopSampleRequestDetailVo>
        params={{
          shopId,
        }}
        request={(params) => {
          return tkshopSampleRequestPageV2Post({
            shopId: params.shopId,
            creatorId: creator.id!,
            includeMedia: true,
            sortField: 'applyTime',
            sortOrder: 'desc',
            pageNum: 1,
            pageSize: 15,
          }).then((res) => {
            return {
              data: res.data?.list,
              total: res.data?.total,
            };
          });
        }}
        {...scrollProTableOptionFn({
          scroll: {
            x: 600,
          },
        })}
        columns={[
          {
            title: '索样时间',
            dataIndex: 'applyTime',
            width: '100px',
            render: (_text, record) => {
              return record.applyTime ? (
                dateFormat(record.applyTime, 'YYYY-MM-DD')
              ) : (
                <Placeholder />
              );
            },
          },
          {
            title: '商品/店铺',
            dataIndex: 'productShop',
            render: (_, record) => (
              <div style={{ display: 'flex', alignItems: 'center', gap: 8, overflow: 'hidden' }}>
                <ProductAvatar
                  productNo={record.productNo!}
                  onPreview={() => {
                    openDetailModal(record.productNo!, record.shopId!);
                  }}
                />
                <div style={{ flex: 1, overflow: 'hidden' }}>
                  <ShopNameLinkById id={record.shopId!} />
                </div>
              </div>
            ),
          },
          {
            title: <StatusColumns progress={false} />,
            dataIndex: 'status',
            width: StatusColumnWidth + 'px',
            render: (_, record) => {
              return <SampleRequestProgressCell data={record} onDetail={onDetail} />;
            },
          },
        ]}
      />
    );
  }, [creator.id, onDetail, openDetailModal, shopId]);
  if (refer === 'Modal') {
    return (
      <div
        style={{
          height: '100%',
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          gap: 8,
        }}
      >
        <div style={{ display: 'flex', justifyContent: 'flex-end' }}>{shopSelector}</div>
        <StyledTableWrapper style={{ flex: 1, overflow: 'hidden' }}>{table}</StyledTableWrapper>
      </div>
    );
  }

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Space className="title">
            <ColoursIcon className="duzhanshifangwen_24" />
            索样记录
          </Space>
          {shopSelector}
        </div>
      }
      className={cardStyles.detailCommonCard}
      bodyStyle={{
        paddingLeft: 8,
        paddingRight: 8,
      }}
      {...cardProps}
    >
      <StyledTableWrapper style={{ flex: 1, overflow: 'hidden' }}>{table}</StyledTableWrapper>
    </Card>
  );
};
export default SampleRequestCard;
