// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 批量更改手机的 message check 配置 POST /api/shop/mobile/batchUpdateAccountMessageCheckConfig */
export async function shopMobileBatchUpdateAccountMessageCheckConfigPost(
  body: API.BatchUpdateAccountMessageCheckConfigRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/batchUpdateAccountMessageCheckConfig', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量更改手机的 可用性检查 配置 POST /api/shop/mobile/batchUpdateHealthCheckConfig */
export async function shopMobileBatchUpdateHealthCheckConfigPost(
  body: API.BatchUpdateAccountHealthCheckRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/batchUpdateHealthCheckConfig', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量更改手机的 发送邀约/卡片 批次间隔 配置 POST /api/shop/mobile/batchUpdateIntervalConfig */
export async function shopMobileBatchUpdateIntervalConfigPost(
  body: API.BatchUpdateIntervalConfigRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/batchUpdateIntervalConfig', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量更改手机的 工作时间 配置 POST /api/shop/mobile/batchUpdateWorkTimeConfig */
export async function shopMobileBatchUpdateWorkTimeConfigPost(
  body: API.BatchUpdateWorkTimeConfigRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/batchUpdateWorkTimeConfig', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 检查手机名称是否已经存在 GET /api/shop/mobile/checkNameExists */
export async function shopMobileCheckNameExistsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileCheckNameExistsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultboolean>('/api/shop/mobile/checkNameExists', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 连接腾讯云手机 POST /api/shop/mobile/connectQCloudServerSession */
export async function shopMobileConnectQCloudServerSessionPost(
  body: API.ConnectQCloudMobileRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/shop/mobile/connectQCloudServerSession', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 统计当前用户在当前团队有权限的手机个数 GET /api/shop/mobile/countAuthorizedMobiles */
export async function shopMobileCountAuthorizedMobilesGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileCountAuthorizedMobilesGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/shop/mobile/countAuthorizedMobiles', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 导入一个手机 POST /api/shop/mobile/create */
export async function shopMobileCreatePost(
  body: API.CreateMobileRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTeamMobileVo>('/api/shop/mobile/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 清空一个设备下的所有手机 DELETE /api/shop/mobile/deleteDeviceMobiles */
export async function shopMobileDeleteDeviceMobilesDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileDeleteDeviceMobilesDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/deleteDeviceMobiles', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 删除一个手机 DELETE /api/shop/mobile/deleteMobile/${param0} */
export async function shopMobileDeleteMobileByIdDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileDeleteMobileByIdDeleteParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/shop/mobile/deleteMobile/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 查询团队的手机所在的客户端列表 GET /api/shop/mobile/devices */
export async function shopMobileDevicesGet(options?: { [key: string]: any }) {
  return request<API.WebResultListLoginDeviceDto>('/api/shop/mobile/devices', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取一个手机的简易信息(旧客户端会调这个接口，新客户端调用 /getCloudProperties) GET /api/shop/mobile/getArmCloudProperties */
export async function shopMobileGetArmCloudPropertiesGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileGetArmCloudPropertiesGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMap>('/api/shop/mobile/getArmCloudProperties', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取一个手机的简易信息 GET /api/shop/mobile/getCloudProperties */
export async function shopMobileGetCloudPropertiesGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileGetCloudPropertiesGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMap>('/api/shop/mobile/getCloudProperties', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取一个手机的详细信息 GET /api/shop/mobile/getDetailInfo */
export async function shopMobileGetDetailInfoGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileGetDetailInfoGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTeamMobileVo>('/api/shop/mobile/getDetailInfo', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取一个手机的简易信息 GET /api/shop/mobile/getSimpleInfo */
export async function shopMobileGetSimpleInfoGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileGetSimpleInfoGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTeamMobileSimpleVo>('/api/shop/mobile/getSimpleInfo', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取安装下载WDA文件的url GET /api/shop/mobile/getWDAUrl */
export async function shopMobileGetWdaUrlGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileGetWDAUrlGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/shop/mobile/getWDAUrl', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 将手机授权给用户和组织，逻辑和授权分身给用户/组织的逻辑一模一样 POST /api/shop/mobile/grantMobileToUsers */
export async function shopMobileGrantMobileToUsersPost(
  body: API.jiangfenshenshouquangeiyonghuhezuzhi,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/grantMobileToUsers', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 将手机加入分组 PUT /api/shop/mobile/group/addMobiles */
export async function shopMobileGroupAddMobilesPut(
  body: API.AddMobileToGroupRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/group/addMobiles', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 检查分组名称是否已经存在 GET /api/shop/mobile/group/checkNameExists */
export async function shopMobileGroupCheckNameExistsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileGroupCheckNameExistsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultboolean>('/api/shop/mobile/group/checkNameExists', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建一个自定义的手机分组 POST /api/shop/mobile/group/create */
export async function shopMobileGroupCreatePost(
  body: API.CreateMobileGroupRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMobileGroupVo>('/api/shop/mobile/group/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除一个自定义的手机分组 DELETE /api/shop/mobile/group/delete */
export async function shopMobileGroupDeleteDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileGroupDeleteDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/group/delete', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 将手机从分组中移除 POST /api/shop/mobile/group/removeMobiles */
export async function shopMobileGroupRemoveMobilesPost(
  body: API.RemoveMobileFromGroupRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/group/removeMobiles', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改一个自定义的手机分组(名称or排序) POST /api/shop/mobile/group/update */
export async function shopMobileGroupUpdatePost(
  body: API.UpdateMobileGroupRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMobileGroupVo>('/api/shop/mobile/group/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询团队的手机分组列表 GET /api/shop/mobile/groups */
export async function shopMobileGroupsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListMobileGroupVo>('/api/shop/mobile/groups', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 导入一个cloud云真手机 POST /api/shop/mobile/importCloudMobile */
export async function shopMobileImportCloudMobilePost(
  body: API.ImportCloudMobileRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTeamMobileVo>('/api/shop/mobile/importCloudMobile', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 发起一个webrtc room以便获取视频流，返回一个roomId PUT /api/shop/mobile/initiateVideoRoom/${param0} */
export async function shopMobileInitiateVideoRoomByIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileInitiateVideoRoomByIdPutParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultMap>(`/api/shop/mobile/initiateVideoRoom/${param0}`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 查询团队的手机列表，deviceId和groupId同时不为空会忽略groupId GET /api/shop/mobile/list */
export async function shopMobileListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTeamMobileVo>('/api/shop/mobile/list', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询团队的arm cloud云手机(不包含联营过来的云手机，联营过来的云手机在联营分组里) GET /api/shop/mobile/list/armCloud */
export async function shopMobileListArmCloudGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileListArmCloudGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTeamMobileVo>('/api/shop/mobile/list/armCloud', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询当前团队被联营的手机列表 GET /api/shop/mobile/list/shared */
export async function shopMobileListSharedGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTeamMobileVo>('/api/shop/mobile/list/shared', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 按部门查询授权的手机 GET /api/shop/mobile/listByDepartment/${param0} */
export async function shopMobileListByDepartmentByDepartmentIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileListByDepartmentByDepartmentIdGetParams,
  options?: { [key: string]: any },
) {
  const { departmentId: param0, ...queryParams } = params;
  return request<API.WebResultListTeamMobileDeviceVo>(
    `/api/shop/mobile/listByDepartment/${param0}`,
    {
      method: 'GET',
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}

/** 前台通过adb命令直接更改了手机model之后，通知后台更改数据库 POST /api/shop/mobile/notifyModelUpdated */
export async function shopMobileNotifyModelUpdatedPost(
  body: API.NotifyModelUpdatedRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/notifyModelUpdated', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 一键新机，目前只有腾讯云手机支持，发送该请求后，在 mobileId 上监听 reset-mobile-complete 事件，重置完成后会推送事件 POST /api/shop/mobile/resetMobile */
export async function shopMobileResetMobilePost(
  body: API.ResetMobileRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/resetMobile', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 重启某个客户端的adb server PUT /api/shop/mobile/restartAdbServer */
export async function shopMobileRestartAdbServerPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileRestartAdbServerPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/restartAdbServer', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 重启云手机(名字不好改了，所有平台云手机都调这个) POST /api/shop/mobile/restartArmCloudIns */
export async function shopMobileRestartArmCloudInsPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileRestartArmCloudInsPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/restartArmCloudIns', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 批量切换手机的是否允许网络连接。如果传的mobileId不是云手机或者是联联过来的手机会被跳过 POST /api/shop/mobile/toggleCloudMobileNetwork */
export async function shopMobileToggleCloudMobileNetworkPost(
  body: API.BatchToggleCloudMobileNetworkRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/toggleCloudMobileNetwork', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新手机基本信息 POST /api/shop/mobile/update */
export async function shopMobileUpdatePost(
  body: API.UpdateMobileRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTeamMobileVo>('/api/shop/mobile/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 给一台cloud云真手机设置代理(path向前兼容不改了，所有类型云真机都调这个) PUT /api/shop/mobile/updateArmCloudProxy */
export async function shopMobileUpdateArmCloudProxyPut(
  body: API.SetCloudMobileProxyRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/updateArmCloudProxy', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更改手机的地理位置，目前只有腾讯云手机支持 POST /api/shop/mobile/updateGeoLocation */
export async function shopMobileUpdateGeoLocationPost(
  body: API.UpdateGeoLocationRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultArraystring>('/api/shop/mobile/updateGeoLocation', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新手机元信息 POST /api/shop/mobile/updateMetas */
export async function shopMobileUpdateMetasPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileUpdateMetasPostParams,
  body: Record<string, any>,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/updateMetas', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 更改所在的设备 POST /api/shop/mobile/updateMobileDevice */
export async function shopMobileUpdateMobileDevicePost(
  body: API.UpdateMobileDeviceRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/updateMobileDevice', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 设置是否暂时禁用rpa执行 PUT /api/shop/mobile/updateRpaSilent */
export async function shopMobileUpdateRpaSilentPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileUpdateRpaSilentPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/updateRpaSilent', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 设置sim卡信息，目前只有腾讯云手机支持 POST /api/shop/mobile/updateSimCard */
export async function shopMobileUpdateSimCardPost(
  body: API.UpdateSimRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/shop/mobile/updateSimCard', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更改手机的时区，目前只有腾讯云手机支持 POST /api/shop/mobile/updateTimeZone */
export async function shopMobileUpdateTimeZonePost(
  body: API.UpdateTimeZoneRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultArraystring>('/api/shop/mobile/updateTimeZone', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
