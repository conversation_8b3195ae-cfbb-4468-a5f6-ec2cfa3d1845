// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 设置商品的备注 PUT /api/tkshop/product/${param0}/remark */
export async function tkshopProductByIdRemarkPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopProductByIdRemarkPutParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/tkshop/product/${param0}/remark`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取产品的主图 POST /api/tkshop/product/avatarsByNo */
export async function tkshopProductAvatarsByNoPost(
  body: API.ProductNoRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMapstring>('/api/tkshop/product/avatarsByNo', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据productNo查询商品 GET /api/tkshop/product/byNo */
export async function tkshopProductByNoGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopProductByNoGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkshopProductDto>('/api/tkshop/product/byNo', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 商品的字段元数据 GET /api/tkshop/product/fields */
export async function tkshopProductFieldsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTkshopCreatorFiledVo>('/api/tkshop/product/fields', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 标记TK商品Live POST /api/tkshop/product/makeExists */
export async function tkshopProductMakeExistsPost(
  body: API.MarkProductLiveRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/tkshop/product/makeExists', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询TK商品 GET /api/tkshop/product/page */
export async function tkshopProductPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopProductPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkshopProductDto>('/api/tkshop/product/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 同步TK商品 POST /api/tkshop/product/sync */
export async function tkshopProductSyncPost(
  body: API.SyncProductRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/tkshop/product/sync', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
