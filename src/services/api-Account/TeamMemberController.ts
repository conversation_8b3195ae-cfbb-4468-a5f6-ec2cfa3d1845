// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取成员的登录设备 GET /api/member/${param0}/devices */
export async function memberByUserIdDevicesGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.memberByUserIdDevicesGetParams,
  options?: { [key: string]: any },
) {
  const { userId: param0, ...queryParams } = params;
  return request<API.WebResultListUserLoginDeviceVo>(`/api/member/${param0}/devices`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 设置成员设备配置 PUT /api/member/authDevice */
export async function memberAuthDevicePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.memberAuthDevicePutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/member/authDevice', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 检查当前登录策略（抛出异常、创建审核） GET /api/member/checkOps */
export async function memberCheckOpsGet(options?: { [key: string]: any }) {
  return request<API.WebResult>('/api/member/checkOps', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 使团队成员在设备上退出登录 DELETE /api/member/device/logout */
export async function memberDeviceLogoutDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.memberDeviceLogoutDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/member/device/logout', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取设备的配置 GET /api/member/deviceConfig */
export async function memberDeviceConfigGet(options?: { [key: string]: any }) {
  return request<API.WebResultTeamDeviceConfig>('/api/member/deviceConfig', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 设置团队设备配置 PUT /api/member/deviceConfig */
export async function memberDeviceConfigPut(
  body: API.TeamDeviceConfig,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/member/deviceConfig', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 团队成员 GET /api/member/list */
export async function memberListGet(options?: { [key: string]: any }) {
  return request<API.WebResultListUserActivityDeviceVo>('/api/member/list', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 团队成员（按最近活动排列） GET /api/member/page */
export async function memberPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.memberPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultUserActivityDeviceVo>('/api/member/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询成员配置 GET /api/team-member/config */
export async function teamMemberConfigGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamMemberConfigGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/team-member/config', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询成员配置 PUT /api/team-member/config */
export async function teamMemberConfigPut(
  body: API.UpdateMemberConfigRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/team-member/config', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
