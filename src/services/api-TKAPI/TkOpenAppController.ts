// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** app授权回调 GET /api/tk/open/authorize/callback/${param0} */
export async function tkOpenAuthorizeCallbackByServiceIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkOpenAuthorizeCallbackByServiceIdGetParams,
  options?: { [key: string]: any },
) {
  const { service_id: param0, ...queryParams } = params;
  return request<any>(`/api/tk/open/authorize/callback/${param0}`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 查询授权结果 GET /api/tk/open/authorizeResult/${param0} */
export async function tkOpenAuthorizeResultByStateGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkOpenAuthorizeResultByStateGetParams,
  options?: { [key: string]: any },
) {
  const { state: param0, ...queryParams } = params;
  return request<API.WebResultTkAuthorizeUrlVo>(`/api/tk/open/authorizeResult/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取店铺的授权URL（24小时有效） GET /api/tk/open/authorizeUrl */
export async function tkOpenAuthorizeUrlGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkOpenAuthorizeUrlGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/tk/open/authorizeUrl', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 接收TK平台回调 POST /api/tk/open/notification/${param0} */
export async function tkOpenNotificationByServiceIdPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkOpenNotificationByServiceIdPostParams,
  options?: { [key: string]: any },
) {
  const { service_id: param0, ...queryParams } = params;
  return request<any>(`/api/tk/open/notification/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 触发TK用户refreshToken（仅用于测试） GET /api/tk/open/user/refreshToken */
export async function tkOpenUserRefreshTokenGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkOpenUserRefreshTokenGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tk/open/user/refreshToken', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
