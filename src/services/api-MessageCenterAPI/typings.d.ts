declare namespace API {
  type BroadcastMsgVo = {
    content?: string;
    id?: number;
    msgLevel?: 'Error' | 'Trace' | 'Warn';
    summary?: string;
  };

  type DwlNotifyRequest = {
    auditId?: number;
    teamId?: number;
  };

  type MessageSubscribeVo = {
    internal?: boolean;
    messageType?:
      | 'Activity'
      | 'Bank_Pay_Confirmed'
      | 'Credit_Buy_Notify'
      | 'Critical_Message'
      | 'Crs_Orders_Fetched'
      | 'Domain_White_Pass'
      | 'Domain_White_Reject'
      | 'General'
      | 'Ip_Address_Changed'
      | 'Ip_Destroyed'
      | 'Ip_Expire'
      | 'Ip_Import'
      | 'Ip_Product_Failed'
      | 'Ip_Product_Start'
      | 'Ip_Product_Success'
      | 'Ip_Renew_Failed'
      | 'Ip_Renew_Success'
      | 'Ip_UnImport'
      | 'Ip_Will_Destroy'
      | 'Ip_Will_Expire'
      | 'Ip_Will_Expire_OneDay'
      | 'Kakao_Chats_Synced'
      | 'Kol_New_TK_Message'
      | 'Kol_Plan_Failed'
      | 'Mobile_Sharing_Notify'
      | 'Mobile_Sharing_Result'
      | 'New_Order_Need_Pay'
      | 'OP_Message'
      | 'OP_Money_Come_Notify'
      | 'OP_Order_Product_Failed'
      | 'OP_Order_Revert_product_failed'
      | 'Shop_Info_ReportSuccess'
      | 'Shop_Receive_Notify'
      | 'Shop_Receive_Result'
      | 'Shop_Sharing_Notify'
      | 'Shop_Sharing_Result'
      | 'Shop_Transfer_Audit_Notify'
      | 'Shop_Transfer_Audit_Pass'
      | 'Shop_Transfer_Audit_Reject'
      | 'User_Exit_team'
      | 'User_Join_Team'
      | 'User_Join_Team_Audit_Notify'
      | 'User_Join_Team_Audit_Pass'
      | 'User_Join_Team_Audit_Reject'
      | 'User_New_Device';
    sms?: boolean;
    userId?: number;
    wechat?: boolean;
  };

  type msgCenterMarkHasReadPutParams = {
    /** ids */
    ids: number;
  };

  type msgCenterMessagesGetParams = {
    /** pageNum */
    pageNum: number;
    /** pageSize */
    pageSize: number;
    /** messageZoo */
    messageZoo?:
      | 'Activity'
      | 'Audit'
      | 'Crs'
      | 'General'
      | 'Ips'
      | 'Kol'
      | 'Mobiles'
      | 'Payment'
      | 'Shops'
      | 'Team';
    /** messageType */
    messageType?: string;
    /** 是否只获取未读消息 */
    unread?: boolean;
    /** 粒度目前是做到天，时分秒会被抹掉 */
    from?: string;
    /** to */
    to?: string;
  };

  type msgCenterUnreadCountGetParams = {
    /** 不为空表示获取指定团队的，为空表示获取所有团队的 */
    targetTeamId?: number;
  };

  type PageResultUserMessageVo = {
    current?: number;
    list?: UserMessageVo[];
    pageSize?: number;
    total?: number;
  };

  type ProductInfo = {
    name?: string;
    principal?: string;
    version?: string;
  };

  type ReloadSmsProviderResultVo = {
    currentProduct?: ProductInfo;
  };

  type UpdateUserSubscribeRequest = {
    subscribes?: MessageSubscribeVo[];
  };

  type UserMessageVo = {
    /** 消息内容 */
    content?: string;
    createTime?: string;
    id?: number;
    messageId?: number;
    messageType?:
      | 'Activity'
      | 'Bank_Pay_Confirmed'
      | 'Credit_Buy_Notify'
      | 'Critical_Message'
      | 'Crs_Orders_Fetched'
      | 'Domain_White_Pass'
      | 'Domain_White_Reject'
      | 'General'
      | 'Ip_Address_Changed'
      | 'Ip_Destroyed'
      | 'Ip_Expire'
      | 'Ip_Import'
      | 'Ip_Product_Failed'
      | 'Ip_Product_Start'
      | 'Ip_Product_Success'
      | 'Ip_Renew_Failed'
      | 'Ip_Renew_Success'
      | 'Ip_UnImport'
      | 'Ip_Will_Destroy'
      | 'Ip_Will_Expire'
      | 'Ip_Will_Expire_OneDay'
      | 'Kakao_Chats_Synced'
      | 'Kol_New_TK_Message'
      | 'Kol_Plan_Failed'
      | 'Mobile_Sharing_Notify'
      | 'Mobile_Sharing_Result'
      | 'New_Order_Need_Pay'
      | 'OP_Message'
      | 'OP_Money_Come_Notify'
      | 'OP_Order_Product_Failed'
      | 'OP_Order_Revert_product_failed'
      | 'Shop_Info_ReportSuccess'
      | 'Shop_Receive_Notify'
      | 'Shop_Receive_Result'
      | 'Shop_Sharing_Notify'
      | 'Shop_Sharing_Result'
      | 'Shop_Transfer_Audit_Notify'
      | 'Shop_Transfer_Audit_Pass'
      | 'Shop_Transfer_Audit_Reject'
      | 'User_Exit_team'
      | 'User_Join_Team'
      | 'User_Join_Team_Audit_Notify'
      | 'User_Join_Team_Audit_Pass'
      | 'User_Join_Team_Audit_Reject'
      | 'User_New_Device';
    messageZoo?:
      | 'Activity'
      | 'Audit'
      | 'Crs'
      | 'General'
      | 'Ips'
      | 'Kol'
      | 'Mobiles'
      | 'Payment'
      | 'Shops'
      | 'Team';
    parameters?: string;
    /** 阅读时间，如果为空表示当前消息未读 */
    readTime?: string;
    resourceId?: number;
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    /** 消息摘要 */
    summary?: string;
  };

  type UserUnreadMsgCountVo = {
    messageZoo?:
      | 'Activity'
      | 'Audit'
      | 'Crs'
      | 'General'
      | 'Ips'
      | 'Kol'
      | 'Mobiles'
      | 'Payment'
      | 'Shops'
      | 'Team';
    msgCount?: number;
    teamId?: number;
  };

  type WebResult = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListBroadcastMsgVo = {
    code?: number;
    data?: BroadcastMsgVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListMessageSubscribeVo = {
    code?: number;
    data?: MessageSubscribeVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListUserUnreadMsgCountVo = {
    code?: number;
    data?: UserUnreadMsgCountVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultUserMessageVo = {
    code?: number;
    data?: PageResultUserMessageVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultReloadSmsProviderResultVo = {
    code?: number;
    data?: ReloadSmsProviderResultVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };
}
