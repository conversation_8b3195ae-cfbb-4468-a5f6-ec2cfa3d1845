// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取团队浏览器开启browserDebug配置: enabled | disabled ; 空表示由团队成员自行决定 GET /api/team/settings/browserDebug */
export async function teamSettingsBrowserDebugGet(options?: { [key: string]: any }) {
  return request<API.WebResultstring>('/api/team/settings/browserDebug', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 团队浏览器开启browserDebug配置: enabled | disabled ; 空表示由团队成员自行决定  PUT /api/team/settings/browserDebug */
export async function teamSettingsBrowserDebugPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamSettingsBrowserDebugPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/team/settings/browserDebug', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取团队指纹自动升级配置: notify | autoUpgrade ; GET /api/team/settings/fingerUpgrade */
export async function teamSettingsFingerUpgradeGet(options?: { [key: string]: any }) {
  return request<API.WebResult>('/api/team/settings/fingerUpgrade', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 设置团队指纹自动升级配置: notify | autoUpgrade PUT /api/team/settings/fingerUpgrade */
export async function teamSettingsFingerUpgradePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamSettingsFingerUpgradePutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/team/settings/fingerUpgrade', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取团队浏览器语言配置: en-US, zh-CN, zh-TW； null或未配置表示由团队成员自行决定 GET /api/team/settings/language */
export async function teamSettingsLanguageGet(options?: { [key: string]: any }) {
  return request<API.WebResultstring>('/api/team/settings/language', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 设置团队浏览器语言配置: en-US, zh-CN, zh-TW；； 未配置表示由团队成员自行决定 PUT /api/team/settings/language */
export async function teamSettingsLanguagePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamSettingsLanguagePutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/team/settings/language', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 打开会话要求绑定IP GET /api/team/settings/sessionRequireIp */
export async function teamSettingsSessionRequireIpGet(options?: { [key: string]: any }) {
  return request<API.WebResultboolean>('/api/team/settings/sessionRequireIp', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 打开会话要求绑定IP PUT /api/team/settings/sessionRequireIp */
export async function teamSettingsSessionRequireIpPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamSettingsSessionRequireIpPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/team/settings/sessionRequireIp', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取团队浏览器开启shopCodeStatus配置: enabled | disabled  GET /api/team/settings/shopCodeStatus */
export async function teamSettingsShopCodeStatusGet(options?: { [key: string]: any }) {
  return request<API.WebResultstring>('/api/team/settings/shopCodeStatus', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 更新获取团队浏览器开启shopCodeStatus配置: enabled | disabled  PUT /api/team/settings/shopCodeStatus */
export async function teamSettingsShopCodeStatusPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamSettingsShopCodeStatusPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/team/settings/shopCodeStatus', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询teamConfig GET /api/team/settings/teamConfig */
export async function teamSettingsTeamConfigGet(options?: { [key: string]: any }) {
  return request<API.WebResultTeamConfigDto>('/api/team/settings/teamConfig', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 设置删除分身时，是否删除指纹 PUT /api/team/settings/teamConfig/deleteShopFp/${param0} */
export async function teamSettingsTeamConfigDeleteShopFpByDeleteShopFpPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamSettingsTeamConfigDeleteShopFpByDeleteShopFpPutParams,
  options?: { [key: string]: any },
) {
  const { deleteShopFp: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/team/settings/teamConfig/deleteShopFp/${param0}`, {
    method: 'PUT',
    params: { ...queryParams },
    ...(options || {}),
  });
}
