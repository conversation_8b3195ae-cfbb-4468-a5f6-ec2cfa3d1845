// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 查询达人自定义字段 GET /api/tk/creator/${param0}/props */
export async function tkCreatorByCreatorIdPropsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkCreatorByCreatorIdPropsGetParams,
  options?: { [key: string]: any },
) {
  const { creatorId: param0, ...queryParams } = params;
  return request<API.WebResultListTkCreatorPropDto>(`/api/tk/creator/${param0}/props`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 创建（或修改）自定义字段 PUT /api/tk/creator/${param0}/props */
export async function tkCreatorByCreatorIdPropsPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkCreatorByCreatorIdPropsPutParams,
  body: API.CreateCreatorPropRequest,
  options?: { [key: string]: any },
) {
  const { creatorId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/tk/creator/${param0}/props`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 创建（或修改）自定义字段 POST /api/tk/prop/define */
export async function tkPropDefinePost(
  body: API.CreateTkPropDefRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkPropDefDto>('/api/tk/prop/define', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除自定义字段 DELETE /api/tk/prop/define */
export async function tkPropDefineDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkPropDefineDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tk/prop/define', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询自定义字段的定义列表 GET /api/tk/prop/defines */
export async function tkPropDefinesGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTkPropDefDto>('/api/tk/prop/defines', {
    method: 'GET',
    ...(options || {}),
  });
}
