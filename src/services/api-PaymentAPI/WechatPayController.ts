// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** wechatpay_notify POST /api/payment/wechatpay/notify */
export async function paymentWechatpayNotifyPost(body: string, options?: { [key: string]: any }) {
  return request<string>('/api/payment/wechatpay/notify', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** wechatpay_notify POST /api/payment/wechatpay/notify/${param0} */
export async function paymentWechatpayNotifyByTeamIdPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.paymentWechatpayNotifyByTeamIdPostParams,
  body: string,
  options?: { [key: string]: any },
) {
  const { teamId: param0, ...queryParams } = params;
  return request<string>(`/api/payment/wechatpay/notify/${param0}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** qrcode GET /api/payment/wechatpay/qrcode */
export async function paymentWechatpayQrcodeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.paymentWechatpayQrcodeGetParams,
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>('/api/payment/wechatpay/qrcode', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
