declare namespace API {
  type LoginResultVo = {
    account?: string;
    jwt?: string;
    jwtExpireTime?: string;
    jwtId?: string;
    /** 是否需要验证码 */
    needCaptcha?: boolean;
    nickname?: string;
    userId?: number;
    userType?: 'NORMAL' | 'PARTNER' | 'SHADOW';
  };

  type RpaMessageVo = {
    attachmentUrls?: Record<string, any>;
    attachments?: string[];
    content?: string;
    createTime?: string;
    detail?: string;
    flowId?: number;
    flowName?: string;
    /** id */
    id?: string;
    openIds?: string[];
    shopName?: string;
    taskId?: number;
    taskItemId?: number;
    taskName?: string;
    teamId?: number;
    userIds?: number[];
  };

  type SessionInfoVo = {
    clientIp?: string;
    loginTime?: string;
    nickname?: string;
    openId?: string;
    phone?: string;
    userId?: number;
  };

  type SubscribeQrCodeRequest = {
    attachment?: Record<string, any>;
    bizId?: string;
    subscribeType?: 'RpaNotify';
  };

  type WebResult = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRpaMessageVo = {
    code?: number;
    data?: RpaMessageVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultSessionInfoVo = {
    code?: number;
    data?: SessionInfoVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultstring = {
    code?: number;
    data?: string;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultWxBindQrCodeResult = {
    code?: number;
    data?: WxBindQrCodeResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultWxJsapiSignature = {
    code?: number;
    data?: WxJsapiSignature;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultWxLoginQrCodeResult = {
    code?: number;
    data?: WxLoginQrCodeResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultWxQrCodeBizResult = {
    code?: number;
    data?: WxQrCodeBizResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultWxQrCodeInviteResult = {
    code?: number;
    data?: WxQrCodeInviteResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultWxQrCodeVo = {
    code?: number;
    data?: WxQrCodeVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultWxSubscribeDto = {
    code?: number;
    data?: WxSubscribeDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WxBindQrCodeResult = {
    message?: string;
    qrcodeId?: string;
    rewardObject?: Record<string, any>;
    status?: 'CONFLICTED' | 'EXPIRED' | 'FAIL' | 'REJECTED' | 'SCANNED' | 'SUCCESS' | 'WAIT_SCAN';
  };

  type wxBindQrCodeResultGetParams = {
    /** qrcodeId */
    qrcodeId: string;
  };

  type wxConfirmRegisterPostParams = {
    /** nickname */
    nickname: string;
    /** qrcodeId */
    qrcodeId: string;
    /** inviteCode */
    inviteCode?: string;
  };

  type wxInviteJoinPostParams = {
    /** uuid */
    uuid: string;
    /** nickname */
    nickname: string;
  };

  type wxInviteQrcodeGetParams = {
    /** inviteCode */
    inviteCode: string;
  };

  type wxInviteQrcodeResultGetParams = {
    /** uuid */
    uuid: string;
  };

  type WxJsapiSignature = {
    appId?: string;
    nonceStr?: string;
    signature?: string;
    timestamp?: number;
    url?: string;
  };

  type wxJsSignatureGetParams = {
    /** url */
    url: string;
  };

  type wxLoginQrCodeGetParams = {
    /** rememberMe */
    rememberMe?: boolean;
  };

  type WxLoginQrCodeResult = {
    loginResult?: LoginResultVo;
    message?: string;
    /** true表示未绑定登录需要注册 */
    needRegister?: boolean;
    nickname?: string;
    qrcodeId?: string;
    rewardObject?: Record<string, any>;
    status?: 'CONFLICTED' | 'EXPIRED' | 'FAIL' | 'REJECTED' | 'SCANNED' | 'SUCCESS' | 'WAIT_SCAN';
  };

  type wxLoginQrCodeResultGetParams = {
    /** qrcodeId */
    qrcodeId: string;
  };

  type wxMessageByMessageIdGetParams = {
    /** messageId */
    messageId: string;
  };

  type wxOauth2GetUrlGetParams = {
    /** targetUrl */
    targetUrl: string;
  };

  type wxPortalByAppidGetParams = {
    /** appid */
    appid: string;
    /** signature */
    signature?: string;
    /** timestamp */
    timestamp?: string;
    /** nonce */
    nonce?: string;
    /** echostr */
    echostr?: string;
  };

  type wxPortalByAppidPostParams = {
    /** appid */
    appid: string;
    /** signature */
    signature: string;
    /** timestamp */
    timestamp: string;
    /** nonce */
    nonce: string;
    /** openid */
    openid: string;
    /** encrypt_type */
    encrypt_type?: string;
    /** msg_signature */
    msg_signature?: string;
  };

  type WxQrCodeBizResult = {
    message?: string;
    openIds?: string[];
    status?: 'CONFLICTED' | 'EXPIRED' | 'FAIL' | 'REJECTED' | 'SCANNED' | 'SUCCESS' | 'WAIT_SCAN';
    uuid?: string;
  };

  type WxQrCodeInviteResult = {
    inTeam?: boolean;
    message?: string;
    nickname?: string;
    openId?: string;
    status?: 'CONFLICTED' | 'EXPIRED' | 'FAIL' | 'REJECTED' | 'SCANNED' | 'SUCCESS' | 'WAIT_SCAN';
    userId?: number;
    uuid?: string;
  };

  type WxQrCodeVo = {
    createTime?: string;
    expireSeconds?: number;
    url?: string;
    uuid?: string;
  };

  type wxRedirectByAppidGreetGetParams = {
    /** appid */
    appid: string;
    /** state */
    state?: string;
    /** code */
    code?: string;
    /** to */
    to?: string;
    /** map */
    map?: string;
  };

  type wxSessionBySessionIdDeleteParams = {
    /** sessionId */
    sessionId: string;
  };

  type wxSessionBySessionIdGetParams = {
    /** sessionId */
    sessionId: string;
  };

  type wxSubscribeCancelByIdGetParams = {
    /** id */
    id: number;
  };

  type WxSubscribeDto = {
    appId?: string;
    attachment?: string;
    bizId?: string;
    createTime?: string;
    creatorId?: number;
    id?: number;
    openId?: string;
    qrcodeUuid?: string;
    subscribeType?: 'RpaNotify';
    teamId?: number;
    valid?: boolean;
  };

  type wxSubscribeGetByIdGetParams = {
    /** id */
    id: number;
  };

  type wxSubscribeQrcodeResultGetParams = {
    /** uuid */
    uuid: string;
  };
}
