// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 添加云账号 POST /api/customer/cloud */
export async function customerCloudPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.customerCloudPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultOperatingCloudDto>('/api/customer/cloud', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询某个云账号 GET /api/customer/cloud/${param0} */
export async function customerCloudByCloudIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.customerCloudByCloudIdGetParams,
  options?: { [key: string]: any },
) {
  const { cloudId: param0, ...queryParams } = params;
  return request<API.WebResultCustomerCloudVo>(`/api/customer/cloud/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改云账号 PUT /api/customer/cloud/${param0} */
export async function customerCloudByCloudIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.customerCloudByCloudIdPutParams,
  options?: { [key: string]: any },
) {
  const { cloudId: param0, ...queryParams } = params;
  return request<API.WebResultOperatingCloudDto>(`/api/customer/cloud/${param0}`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 删除云账号 DELETE /api/customer/cloud/${param0} */
export async function customerCloudByCloudIdDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.customerCloudByCloudIdDeleteParams,
  options?: { [key: string]: any },
) {
  const { cloudId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/customer/cloud/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改云账号的密钥对 PUT /api/customer/cloud/${param0}/keypair */
export async function customerCloudByCloudIdKeypairPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.customerCloudByCloudIdKeypairPutParams,
  options?: { [key: string]: any },
) {
  const { cloudId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/customer/cloud/${param0}/keypair`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 测试云账号AK GET /api/customer/cloud/check */
export async function customerCloudCheckGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.customerCloudCheckGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultAccountInfo>('/api/customer/cloud/check', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询云账号 GET /api/customer/cloud/page */
export async function customerCloudPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.customerCloudPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultCustomerCloudVo>('/api/customer/cloud/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
