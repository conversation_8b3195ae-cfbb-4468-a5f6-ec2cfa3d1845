import { Typography } from 'antd';
import Placeholder from '@/components/Common/Placeholder';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import type { ProColumnType } from '@ant-design/pro-table';
import SortDropdown, { useOrder } from '@/components/Sort/SortDropdown';
import _ from 'lodash';
import { getColumnByDataIndex } from '@/components/SortableTransfer/TableColumnsSettingModalTrigger';
import useResizableColumns from '@/hooks/useResizableColumns';
import { ProductItem } from '@/pages/Order/components/utils';
import ProductDetailModal from '@/pages/ProductManage/components/ProductDetailModal';
import { GhostModalCaller } from '@/mixins/modal';
import type { SampleRequestColumn } from '@/pages/Sample/components/useSampleRequestColumnsMeta';
import { useSampleRequestColumnsMeta } from '@/pages/Sample/components/useSampleRequestColumnsMeta';
import CreatorHandleCell from '@/pages/TikTok/Live/components/CreatorHandleCell';
import { dateFormat } from '@/utils/utils';
import { getCreatorStatus, getNumberDom } from '@/pages/TikTok/utils/utils';
import {
  SampleRequestProgressCell,
  StatusColumns,
} from '@/pages/TikTok/Live/components/SampleRequestCard';
import { ShopNodeById } from '@/components/Common/ShopNode';
import { TagMoreDropdown } from '@/components/Common/MoreDropdown';
import { generateTags } from '@/pages/Creator/utils';
import { SampleApproveActions, SampleDeleteAction } from '@/pages/Sample/components/utils';
import { history } from 'umi';
import { getCurrentTeamId } from '@/hooks/useCurrentTeam';
import CreatorDetailModal from '@/pages/TikTok/Live/components/CreatorDetailModal';
import CopyableText from '@/components/Common/CopyableText';
import I18N from '@/i18n';

function useSampleRequestColumns(options: {
  onChange: (reset?: boolean) => void;
  shopId?: number;
}) {
  const { onChange, shopId } = options;
  const { order, changeOrder } = useOrder(
    {
      key: 'applyTime',
      ascend: false,
    },
    `sample_request_table_order_V20250724`,
  );
  const { columns: activeColumns, meta, update } = useSampleRequestColumnsMeta(shopId);
  const flag = useRef(false);
  useEffect(() => {
    if (flag.current) {
      onChange();
    }
  }, [onChange, order]);
  useEffect(() => {
    flag.current = true;
  }, [order]);
  const getColumn = useCallback(
    (
      dataIndex: SampleRequestColumn,
    ): ProColumnType<API.TkshopSampleRequestAuditVo> & {
      sortable?: { order?: number };
      description?: string;
    } => {
      const target = getColumnByDataIndex(dataIndex, meta) || { title: dataIndex };
      const { title, description, sortable, width, resizable, disabled, valueType } = target;

      const base = {
        dataIndex,
        title,
        description,
        ellipsis: false,
        sortable,
        width,
        resizable,
        disabled,
      };
      switch (dataIndex) {
        case 'productName':
          return {
            ...base,
            render(_dom, record) {
              const { product } = record;
              return (
                <div style={{ display: 'flex', alignItems: 'center', overflow: 'hidden', gap: 8 }}>
                  <ProductItem
                    data={product}
                    onLinkClick={() => {
                      GhostModalCaller(<ProductDetailModal data={product} />);
                    }}
                  />
                </div>
              );
            },
          };
        case 'shopName': {
          return {
            ...base,
            render(_dom, record) {
              const { shopId: _shopId } = record;
              return (
                <ShopNodeById
                  onClick={() => {
                    history.push(`/team/${getCurrentTeamId()}/sample/${_shopId}/`);
                  }}
                  id={_shopId!}
                />
              );
            },
          };
        }
        case 'commissionRate': {
          return {
            ...base,
            valueType: 'percent',
          };
        }
        case 'productPrice': {
          return {
            ...base,
            render(_dom, record) {
              const { product } = record;
              if (!product) {
                return <Placeholder />;
              }
              const { price, priceUnit } = product;
              return getNumberDom(price, priceUnit);
            },
          };
        }
        case 'gmv': {
          return {
            ...base,
            render(_dom, record) {
              const { creator } = record;
              if (!creator) {
                return <Placeholder />;
              }
              return getNumberDom(creator.gmv, creator.unit);
            },
          };
        }
        case 'followerCnt': {
          return {
            ...base,
            render(_dom, record) {
              const { creator } = record;
              if (!creator) {
                return <Placeholder />;
              }
              return getNumberDom(creator.followerCnt);
            },
          };
        }
        case 'itemsSold': {
          return {
            ...base,
            render(_dom, record) {
              const { creator } = record;
              if (!creator) {
                return <Placeholder />;
              }
              return getNumberDom(creator.itemsSold);
            },
          };
        }
        case 'creatorCategoryList': {
          return {
            ...base,
            render(_dom, record) {
              const { creator } = record;
              if (!creator) {
                return <Placeholder />;
              }
              return generateTags(creator.categoryList);
            },
          };
        }
        case 'creatorAlias': {
          return {
            ...base,
            render(_dom, record) {
              const { creator } = record;
              if (!creator?.alias) {
                return <Placeholder />;
              }
              return (
                <Typography.Text ellipsis={{ tooltip: creator.alias }}>
                  {creator.alias}
                </Typography.Text>
              );
            },
          };
        }
        case 'creatorRemark': {
          return {
            ...base,
            render(_dom, record) {
              const { creator } = record;
              if (!creator?.remark) {
                return <Placeholder />;
              }
              return (
                <Typography.Text ellipsis={{ tooltip: creator.remark }}>
                  {creator.remark}
                </Typography.Text>
              );
            },
          };
        }
        case 'creatorTags': {
          return {
            ...base,
            render(_dom, record) {
              const { creator } = record;
              if (!creator) {
                return <Placeholder />;
              }
              return <TagMoreDropdown tags={creator.tags} />;
            },
          };
        }
        case 'creator': {
          return {
            ...base,
            render(_dom, record) {
              const { creator } = record;
              if (!creator) {
                return <Placeholder />;
              }
              return (
                <CreatorHandleCell
                  refer={'store'}
                  onUpdate={() => {
                    onChange(false);
                  }}
                  creator={creator!}
                />
              );
            },
          };
        }

        case 'applyTime':
        case 'lastSyncTime':
          return {
            ...base,
            render(_text, record) {
              const time = record[dataIndex];
              return time ? dateFormat(time) : <Placeholder />;
            },
          };
        case 'creatorStatus': {
          return {
            ...base,
            render(_text, record) {
              const { creator } = record;
              if (!creator) {
                return <Placeholder />;
              }
              return getCreatorStatus(creator);
            },
          };
        }
        case 'status': {
          return {
            ...base,
            title: <StatusColumns progress={false} />,
            render(_text, record) {
              return (
                <SampleRequestProgressCell
                  data={record}
                  onDetail={(type) => {
                    GhostModalCaller(
                      <CreatorDetailModal
                        data={record.creator}
                        applyId={record.applyId}
                        tab={type}
                        onUpdate={() => {
                          onChange(false);
                        }}
                      />,
                    );
                  }}
                />
              );
            },
          };
        }
        case 'applyId': {
          return {
            ...base,
            render(_text, record) {
              const { applyId } = record;
              return (
                <CopyableText type={I18N.t('索样记录ID')} text={applyId}>
                  {applyId}
                </CopyableText>
              );
            },
          };
        }
        case 'option': {
          return {
            ...base,
            render(_text, record) {
              return (
                <div
                  style={{
                    display: 'flex',
                    gap: 8,
                    flexWrap: 'nowrap',
                    justifyContent: 'space-between',
                  }}
                >
                  {record.status === 'ToReview' ? (
                    <SampleApproveActions data={record} onUpdate={onChange} />
                  ) : (
                    <span />
                  )}
                  <SampleDeleteAction
                    data={record}
                    onUpdate={() => {
                      onChange();
                    }}
                  />
                </div>
              );
            },
          };
        }
        default:
          return {
            ...base,
            render(_text, record) {
              if (!record) {
                return <Placeholder />;
              }
              const _inner = record[dataIndex];
              if (_.isNil(_inner)) {
                return <Placeholder />;
              }
              return (
                <Typography.Text
                  ellipsis={{
                    tooltip: _inner,
                  }}
                >
                  {_inner}
                </Typography.Text>
              );
            },
          };
      }
    },
    [meta, onChange],
  );
  const keysShow: SampleRequestColumn[] = useMemo(() => {
    return activeColumns.filter((key) => {
      return _.findIndex(meta, (i) => i.dataIndex === key) !== -1;
    });
  }, [meta, activeColumns]);
  const _columns = useMemo(() => {
    return keysShow.map((key) => {
      return getColumn(key);
    });
  }, [getColumn, keysShow]);
  const sorter = useMemo(() => {
    const sorts = meta
      .filter((i) => {
        return !!i.sortable && !i.filedType;
      })
      .map((i) => {
        return {
          key: i.dataIndex as unknown as string,
          label: i.title as unknown as string,
        };
      });
    return <SortDropdown list={sorts} order={order} onChange={changeOrder} />;
  }, [meta, order, changeOrder]);
  const { columns, tableWidth, header, isInitialized } = useResizableColumns({
    fixWidth: 32,
    columns: _columns,
    order,
    changeOrder,
    scope: 'tkshop_sample_request_table',
  });
  return {
    columns,
    tableWidth,
    header,
    sorter,
    activeColumns,
    meta,
    update,
    isInitialized,
    getSortParams() {
      return {
        sortField: order.key,
        sortOrder: order.ascend ? 'asc' : 'desc',
      };
    },
    changeOrder,
  };
}
export default useSampleRequestColumns;
