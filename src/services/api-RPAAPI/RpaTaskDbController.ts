// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** appendToSet PUT /api/rpa/task/db/appendToSet/${param0} */
export async function rpaTaskDbAppendToSetByKeyPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskDbAppendToSetByKeyPutParams,
  body: string,
  options?: { [key: string]: any },
) {
  const { key: param0, ...queryParams } = params;
  return request<API.WebResultint>(`/api/rpa/task/db/appendToSet/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...queryParams,
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除一个Key DELETE /api/rpa/task/db/del/${param0} */
export async function rpaTaskDbDelByKeyDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskDbDelByKeyDeleteParams,
  options?: { [key: string]: any },
) {
  const { key: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/rpa/task/db/del/${param0}`, {
    method: 'DELETE',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取一个Key GET /api/rpa/task/db/get/${param0} */
export async function rpaTaskDbGetByKeyGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskDbGetByKeyGetParams,
  options?: { [key: string]: any },
) {
  const { key: param0, ...queryParams } = params;
  return request<API.WebResultstring>(`/api/rpa/task/db/get/${param0}`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取已有key列表 GET /api/rpa/task/db/list */
export async function rpaTaskDbListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskDbListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListstring>('/api/rpa/task/db/list', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 写入一个Key PUT /api/rpa/task/db/put/${param0} */
export async function rpaTaskDbPutByKeyPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskDbPutByKeyPutParams,
  body: string,
  options?: { [key: string]: any },
) {
  const { key: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/rpa/task/db/put/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...queryParams,
    },
    data: body,
    ...(options || {}),
  });
}
