// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取活跃会话 GET /api/dashboard/activeSessions */
export async function dashboardActiveSessionsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.dashboardActiveSessionsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultActiveShopSessionVo>('/api/dashboard/activeSessions', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查看绑定手机号的奖励 GET /api/dashboard/bindPhoneReward */
export async function dashboardBindPhoneRewardGet(options?: { [key: string]: any }) {
  return request<API.WebResultGiftCardPackDetailVo>('/api/dashboard/bindPhoneReward', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 查看绑定手机号和微信的奖励 GET /api/dashboard/bindRewardObjects */
export async function dashboardBindRewardObjectsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListGiftCardPackDetailVo>('/api/dashboard/bindRewardObjects', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 查看绑定微信的奖励 GET /api/dashboard/bindWechatReward */
export async function dashboardBindWechatRewardGet(options?: { [key: string]: any }) {
  return request<API.WebResultGiftCardPackDetailVo>('/api/dashboard/bindWechatReward', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 关闭所有会话 GET /api/dashboard/closeAllSessions */
export async function dashboardCloseAllSessionsGet(options?: { [key: string]: any }) {
  return request<API.WebResult>('/api/dashboard/closeAllSessions', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 费用概览 GET /api/dashboard/feeSummary */
export async function dashboardFeeSummaryGet(options?: { [key: string]: any }) {
  return request<API.WebResultFeeSummaryVo>('/api/dashboard/feeSummary', {
    method: 'GET',
    ...(options || {}),
  });
}

/** IP统计信息 GET /api/dashboard/ipStat */
export async function dashboardIpStatGet(options?: { [key: string]: any }) {
  return request<API.WebResultTeamIpStatVo>('/api/dashboard/ipStat', {
    method: 'GET',
    ...(options || {}),
  });
}

/** IP统计信息V2 GET /api/dashboard/ipStatV2 */
export async function dashboardIpStatV2Get(options?: { [key: string]: any }) {
  return request<API.WebResultTeamIpStatVoV2>('/api/dashboard/ipStatV2', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取用户登录信息 GET /api/dashboard/lastLogin */
export async function dashboardLastLoginGet(options?: { [key: string]: any }) {
  return request<API.WebResultUserActiveDayVo>('/api/dashboard/lastLogin', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 团队成员（按最近活动排列） GET /api/dashboard/lastUserActivity */
export async function dashboardLastUserActivityGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.dashboardLastUserActivityGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultUserLastActivityVo>('/api/dashboard/lastUserActivity', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 标记用户1天内绑定手机号赠送花瓣 PUT /api/dashboard/markPhoneReward */
export async function dashboardMarkPhoneRewardPut(options?: { [key: string]: any }) {
  return request<API.WebResult>('/api/dashboard/markPhoneReward', {
    method: 'PUT',
    ...(options || {}),
  });
}

/** 标记用户1天内绑定微信赠送花瓣 PUT /api/dashboard/markWechatReward */
export async function dashboardMarkWechatRewardPut(options?: { [key: string]: any }) {
  return request<API.WebResult>('/api/dashboard/markWechatReward', {
    method: 'PUT',
    ...(options || {}),
  });
}

/** 获取我最近访问的分身列表 GET /api/dashboard/myShopSessionLastAccess */
export async function dashboardMyShopSessionLastAccessGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.dashboardMyShopSessionLastAccessGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListShopSessionStatVo>('/api/dashboard/myShopSessionLastAccess', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取即将执行的RPA计划 GET /api/dashboard/pendingRpaPlans */
export async function dashboardPendingRpaPlansGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.dashboardPendingRpaPlansGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListPendingRpaPlanVo>('/api/dashboard/pendingRpaPlans', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取RPA流程统计信息 GET /api/dashboard/rpaFlowStat */
export async function dashboardRpaFlowStatGet(options?: { [key: string]: any }) {
  return request<API.WebResultRpaFlowStatVo>('/api/dashboard/rpaFlowStat', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取RPA流程统计信息V2 GET /api/dashboard/rpaFlowStatV2 */
export async function dashboardRpaFlowStatV2Get(options?: { [key: string]: any }) {
  return request<API.WebResultRpaFlowStatVoV2>('/api/dashboard/rpaFlowStatV2', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取RPA计划列表 GET /api/dashboard/rpaPlans */
export async function dashboardRpaPlansGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.dashboardRpaPlansGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListPendingRpaPlanVo>('/api/dashboard/rpaPlans', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 最近rpaTask GET /api/dashboard/rpaTaskList */
export async function dashboardRpaTaskListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.dashboardRpaTaskListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListRpaSimpleHisVo>('/api/dashboard/rpaTaskList', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取团队分身和手机数量 GET /api/dashboard/shopAndPhoneStat */
export async function dashboardShopAndPhoneStatGet(options?: { [key: string]: any }) {
  return request<API.WebResultShopAndPhoneStat>('/api/dashboard/shopAndPhoneStat', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取团队分身按平台分布情况 GET /api/dashboard/shopPlatformStat */
export async function dashboardShopPlatformStatGet(options?: { [key: string]: any }) {
  return request<API.WebResultListShopPlatformStatVo>('/api/dashboard/shopPlatformStat', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取团队最近访问的分身列表 GET /api/dashboard/teamShopSessionLastAccess */
export async function dashboardTeamShopSessionLastAccessGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.dashboardTeamShopSessionLastAccessGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListShopSessionStatVo>('/api/dashboard/teamShopSessionLastAccess', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
