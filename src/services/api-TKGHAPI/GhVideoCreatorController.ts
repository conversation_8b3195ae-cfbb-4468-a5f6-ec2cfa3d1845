// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 查询达人详情 GET /api/gh/video/creator/${param0} */
export async function ghVideoCreatorByCreatorIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghVideoCreatorByCreatorIdGetParams,
  options?: { [key: string]: any },
) {
  const { creatorId: param0, ...queryParams } = params;
  return request<API.WebResultGhVideoCreatorDetailVo>(`/api/gh/video/creator/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 认领达人 PUT /api/gh/video/creator/acquire */
export async function ghVideoCreatorAcquirePut(
  body: API.GhCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/video/creator/acquire', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分配达人 PUT /api/gh/video/creator/allocate */
export async function ghVideoCreatorAllocatePut(
  body: API.GhCreatorAllocateRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/video/creator/allocate', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取TK达人头像 GET /api/gh/video/creator/avatar */
export async function ghVideoCreatorAvatarGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghVideoCreatorAvatarGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/gh/video/creator/avatar', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 取消分配 PUT /api/gh/video/creator/cancelAllocate */
export async function ghVideoCreatorCancelAllocatePut(
  body: API.GhCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/video/creator/cancelAllocate', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 统计团队达人的数量 GET /api/gh/video/creator/count */
export async function ghVideoCreatorCountGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghVideoCreatorCountGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultlong>('/api/gh/video/creator/count', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 删除达人 POST /api/gh/video/creator/delete */
export async function ghVideoCreatorDeletePost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghVideoCreatorDeletePostParams,
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/video/creator/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除达人（按查询） POST /api/gh/video/creator/deleteByQuery */
export async function ghVideoCreatorDeleteByQueryPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghVideoCreatorDeleteByQueryPostParams,
  body: API.FindGhVideoCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/video/creator/deleteByQuery', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询团队达人V2（不返回总数） GET /api/gh/video/creator/page */
export async function ghVideoCreatorPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghVideoCreatorPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultGhVideoCreatorDetailVo>('/api/gh/video/creator/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询我认领的达人 GET /api/gh/video/creator/pageResponsible */
export async function ghVideoCreatorPageResponsibleGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghVideoCreatorPageResponsibleGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultGhVideoCreatorDetailVo>(
    '/api/gh/video/creator/pageResponsible',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 恢复达人 POST /api/gh/video/creator/recover */
export async function ghVideoCreatorRecoverPost(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/video/creator/recover', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取TK达人区域 GET /api/gh/video/creator/regions */
export async function ghVideoCreatorRegionsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListstring>('/api/gh/video/creator/regions', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 取消认领达人 PUT /api/gh/video/creator/release */
export async function ghVideoCreatorReleasePut(
  body: API.GhCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/video/creator/release', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** getResponsibleUserList GET /api/gh/video/creator/responsibleUserList */
export async function ghVideoCreatorResponsibleUserListGet(options?: { [key: string]: any }) {
  return request<API.WebResultListlong>('/api/gh/video/creator/responsibleUserList', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 同步TK达人头像 POST /api/gh/video/creator/syncAvatar */
export async function ghVideoCreatorSyncAvatarPost(
  body: API.SyncCreatorAvatarRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/video/creator/syncAvatar', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量同步（创建）TK达人 POST /api/gh/video/creator/syncBatch */
export async function ghVideoCreatorSyncBatchPost(
  body: API.GhVideoCreatorDocumentBatch,
  options?: { [key: string]: any },
) {
  return request<API.WebResultSyncBatchResult>('/api/gh/video/creator/syncBatch', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量更新达人是否关注后私信 PUT /api/gh/video/creator/updateMessageStatus */
export async function ghVideoCreatorUpdateMessageStatusPut(
  body: API.GhCreatorUpdateMessageStatusRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/video/creator/updateMessageStatus', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量更新达人状态 PUT /api/gh/video/creator/updateStatus */
export async function ghVideoCreatorUpdateStatusPut(
  body: API.GhCreatorUpdateStatusRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListGhCreatorDto>('/api/gh/video/creator/updateStatus', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 达人可导出的字段元信息 GET /api/gh/video/fields */
export async function ghVideoFieldsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTkCreatorFiledVo>('/api/gh/video/fields', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 绩效统计 GET /api/gh/video/statPerformance */
export async function ghVideoStatPerformanceGet(options?: { [key: string]: any }) {
  return request<API.WebResultListGhCreatorPerformanceStatVo>('/api/gh/video/statPerformance', {
    method: 'GET',
    ...(options || {}),
  });
}
