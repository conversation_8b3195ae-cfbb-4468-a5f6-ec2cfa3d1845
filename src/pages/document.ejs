<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
            name="keywords"
            content="花漾灵动,指纹浏览器,登录,注册,跨境电商安全运营平台,多店铺防关联安全运营平台,社交媒体推广平台"
    />
    <meta
            name="description"
            content="花漾灵动登录与注册，花漾灵动是业界领先的防关联多重分身指纹浏览器，致力于解决多分身安全运营问题，其基于浏览器指纹和纯净IP地址技术，提供了多店铺防关联安全运营与社交媒体推广解决方案。"
    />
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <title>花漾灵动</title>
    <link rel="icon" href="<%= context.config.publicPath +'favicon.ico'%>" type="image/x-icon" />
    <style type="text/css">
      :root {
        --titlebar-area-height: calc(env(titlebar-area-height, 30px) + 2px);
      }
      #window-title-bar {
        position: relative;
        left: env(titlebar-area-x, 0);
        top: env(titlebar-area-y, 0);
        width: env(titlebar-area-width, 100%);
        height: var(--titlebar-area-height);
        app-region: drag;
        -webkit-app-region: drag;
        display: none;
        align-items: center;
        z-index: 1001;
      }
      #window-title-bar.visible {
        display: flex;
      }
      #window-title-bar-text-wrap {
        display: inline-flex;
        align-items: center;
        user-select: none;
      }
      #window-title-bar-text-wrap.center {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        margin-left: env(-titlebar-area-x, 0);
      }
      #window-title-bar-buttons {
        height: 100%;
        display: inline-flex;
        margin-left: auto;
        align-items: center;
        app-region: no-drag;
        -webkit-app-region: no-drag;
      }
      #window-title-bar-lang-selector {
        height: 100%;
      }
      #window-title-bar-lang-selector > a.ant-dropdown-trigger {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        width: 45px;
      }
      #window-title-bar-custom-buttons {
        height: 100%;
      }
      .window-title-btn {
        position: relative;
        display: flex;
        width: 45px;
        height: 100%;
        justify-content: center;
        align-items: center;
        transition: all 0.2s;
      }
      .window-title-btn.hide {
        display: none;
      }
      .window-title-btn.active {
        color: #1890ff;
      }
      .window-title-btn:hover {
        background-color: #eeeeee;
      }
      .window-title-btn .tooltip {
        display: none;
        visibility: hidden;
        opacity: 0;
        transition: opacity 3s ease;
        position: absolute;
        top: var(--titlebar-area-height);
        left: 50%;
        transform: translateX(-50%);
        padding-top: 5px;
      }
      .window-title-btn .tooltip .tooltip-inner {
        padding: 6px 10px;
        border-radius: 4px;
        background-color: #3b79f4;
        color: white;
        font-size: 12px;
        white-space: nowrap;
      }
      .window-title-btn .tooltip a {
        color: white;
        text-decoration: underline;
      }
      .window-title-btn .tooltip:before {
        content: "";
        position: absolute;
        bottom:  calc(100% - 5px);
        left: 50%;
        transform: translateX(-50%);
        border-style: solid;
        border-width: 0 4px 4px 4px;
        border-color: transparent transparent #3b79f4 transparent;
      }
      .window-title-btn:hover .tooltip {
        display: block;
        visibility: visible;
        opacity: 1;
        transition-delay: 0s;
      }
      .tooltip.align-right {
        right: 0;
        left: auto;
        transform: translateX(0);
      }
      .tooltip.align-right:before {
        left: auto;
        right: 19px;
        transform: translateX(0);
      }
    </style>
    <script type="text/javascript">
      serverData = "SERVER_DATA";
      try {
        serverData = JSON.parse(serverData);
        if (serverData["huayoung-language"]) {
          localStorage.setItem("huayoung-language", serverData["huayoung-language"]);
        }
      } catch (e) {
        serverData = {};
      }
    </script>
</head>
<body>
<noscript>您的浏览器不支持Javascript，请下载花漾灵动（https://www.szdamai.com/）或使用Chrome浏览器（https://www.google.cn/intl/zh-CN/chrome/）</noscript>
<div id="window-title-bar">
    <div id="window-title-bar-text-wrap">
        <img src="<%= context.config.publicPath +'logo-16.png'%>" alt="logo" style="width: 16px; margin-left: 8px; margin-right: 4px" />
        <div id="window-title-bar-title">花漾灵动</div>
    </div>
    <div id="window-title-bar-buttons">
        <div id="window-title-bar-lang-selector"></div>
        <div id="window-title-bar-help-button" class="window-title-btn">
            <svg t="1703730083908" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10054" width="16" height="16"><path d="M512 64a448 448 0 1 1 0 896A448 448 0 0 1 512 64z m0 64a384 384 0 1 0 0 768A384 384 0 0 0 512 128z m0 576a38.4 38.4 0 1 1 0 76.8 38.4 38.4 0 0 1 0-76.8z m0-447.616a155.328 155.328 0 0 1 155.2 155.2c0 68.352-52.48 121.728-105.408 143.296a29.056 29.056 0 0 0-17.792 27.008V640h-64v-58.112c0-35.904 20.096-67.072 49.856-82.624l7.616-3.584c37.248-15.168 65.728-49.664 65.728-84.096 0-50.24-40.96-91.2-91.2-91.2s-91.2 40.96-91.2 91.2h-64A155.328 155.328 0 0 1 512 256.384z" fill="currentColor" fill-opacity=".9" p-id="10055"></path></svg>
            <div class="tooltip">
                <div id="window-title-bar-help-tooltips" class="tooltip-inner">
                    <div><a href="https://www.szdamai.com/help/faqs" target="_blank" id="window-title-bar-help-tooltips-faq">常见问题</a></div>
                    <div><a href="https://www.szdamai.com/service/course/bootstrap" target="_blank" id="window-title-bar-help-tooltips-course">视频教程</a></div>
                </div>
            </div>
        </div>
        <div id="window-title-bar-custom-buttons"></div>
        <div id="window-title-bar-pin-button" class="window-title-btn">
            <svg t="1703730754353" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9767" width="16" height="16"><path d="M320 704a64 64 0 0 1-64-64v-44.608a64 64 0 0 1 10.752-35.52l106.496-159.744A64 64 0 0 0 384 364.608V282.496a64 64 0 0 0-18.752-45.248l-26.496-26.496A64 64 0 0 1 320 165.504V128a64 64 0 0 1 64-64h320a64 64 0 0 1 64 64v37.504a64 64 0 0 1-18.752 45.248l-26.496 26.496a64 64 0 0 0-18.752 45.248v82.112a64 64 0 0 0 10.752 35.52l106.496 159.744a64 64 0 0 1 10.752 35.52V640a64 64 0 0 1-64 64H576v256H512v-256H320z m384-576H384v37.504L410.496 192A128 128 0 0 1 448 282.496v82.112a128 128 0 0 1-21.504 71.04L320 595.392V640h448v-44.608l-106.496-159.744A128 128 0 0 1 640 364.608V282.496A128 128 0 0 1 677.504 192L704 165.504V128z" fill="currentColor" p-id="9768"></path></svg>
            <div class="tooltip">
                <div class="tooltip-inner">置顶</div>
            </div>
        </div>
    </div>
</div>
<div id="root">
    <style>
      html,
      body,
      #root {
        height: 100%;
        margin: 0;
        padding: 0;
      }
    </style>
    <div
            style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
        "
    >
        <img src="<%= context.config.publicPath +'logo-loading.gif'%>" alt="logo" style="width: 128px" />
    </div>
</div>
<script>
  if (window.showCustomWindowTitleBar) {
    setInterval(() => {
      document.querySelector('#window-title-bar-title').innerText = document.title;
    }, 500);
    document.querySelector('#window-title-bar').classList.add('visible');
    // 给 body 增加 class 类
    document.body.classList.add('custom-window-title-bar');
    if (window.hideWindowTitleBarHelpBtn) {
      document.querySelector('#window-title-bar-help-button')?.classList.add('hide');
    }
    if (window.hideWindowTitleBarPinBtn) {
      document.querySelector('#window-title-bar-pin-button')?.classList.add('hide');
    }
    const isCn = (window.ipcRenderer.sendSync('get-sys-pres', {}).appLang || 'zh-CN').startsWith('zh');
    document.querySelector('#window-title-bar-title').innerHTML = isCn ? '花漾灵动' : 'HuaYoung';
    document.querySelector('#window-title-bar-help-tooltips-faq').innerHTML = isCn ? '常见问题' : 'FAQ';
    document.querySelector('#window-title-bar-help-tooltips-course').innerHTML = isCn ? '视频教程' : 'Video Courses';
    document.querySelector('#window-title-bar-pin-button .tooltip-inner').innerHTML = isCn ? '置顶' : 'Pin';
    if (window.navigator.windowControlsOverlay.getTitlebarAreaRect().x > 10) {
      document.querySelector('#window-title-bar-text-wrap').classList.add('center');
      document.querySelector('#window-title-bar-text-wrap').style.marginLeft = `-${window.navigator.windowControlsOverlay.getTitlebarAreaRect().x / 2}px`;
      document.querySelector('#window-title-bar-pin-button .tooltip').classList.add('align-right');
    }
    if (navigator.userAgentData.platform === 'macOS') {
      document.querySelector(':root')?.style.setProperty('--titlebar-area-height', `${window.navigator.windowControlsOverlay.getTitlebarAreaRect().height}px`);
    }
    document.querySelector('#window-title-bar-help-tooltips').addEventListener('click', (e) => e.stopPropagation());
    const helpBtn = document.querySelector('#window-title-bar-help-button');
    const pinBtn = document.querySelector('#window-title-bar-pin-button');
    helpBtn.addEventListener('click', () => {
      window.ipcRenderer.send('asynchronous-message', {
        event: 'open-external-url',
        data: {
          url: 'https://www.szdamai.com/help/faqs'
        }
      });
    });
    pinBtn.addEventListener('click', () => {
      const isActive = pinBtn.classList.contains('active');
      if (isActive) {
        pinBtn.classList.remove('active');
      } else {
        pinBtn.classList.add('active');
      }
      window.ipcRenderer.invoke('window-action', {
        action: isActive ? 'unpin' : 'pin'
      });
      pinBtn.querySelector('.tooltip-inner').innerText = isActive ? `${isCn ? '置顶' : 'Pin'}` : `${isCn ? '取消置顶' : 'Unpin'}`;
    });
  } else {
    document.querySelector('#window-title-bar')?.remove();
  }
</script>
</body>
</html>
