// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** armCloudStatusNotify POST /api/shop/app_mobile/armCloudStatusNotify */
export async function shopAppMobileArmCloudStatusNotifyPost(
  body: string,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/app_mobile/armCloudStatusNotify', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** baiduMobileCallback POST /api/shop/app_mobile/baiduCallback/${param0} */
export async function shopAppMobileBaiduCallbackByActionPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopAppMobileBaiduCallbackByActionPostParams,
  body: string,
  options?: { [key: string]: any },
) {
  const { action: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/shop/app_mobile/baiduCallback/${param0}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...queryParams,
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取当前团队已经绑定到前设备的云手机code列表 GET /api/shop/app_mobile/findBoundCloudCodes */
export async function shopAppMobileFindBoundCloudCodesGet(options?: { [key: string]: any }) {
  return request<API.WebResultListstring>('/api/shop/app_mobile/findBoundCloudCodes', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 通过udid获取一个手机的信息 GET /api/shop/app_mobile/findMobileDtoByCode */
export async function shopAppMobileFindMobileDtoByCodeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopAppMobileFindMobileDtoByCodeGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTeamMobileDto>('/api/shop/app_mobile/findMobileDtoByCode', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取当前团队未导入过的云手机 GET /api/shop/app_mobile/findNotBindArmCloudIns */
export async function shopAppMobileFindNotBindArmCloudInsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListCloudMobileVo>('/api/shop/app_mobile/findNotBindArmCloudIns', {
    method: 'GET',
    ...(options || {}),
  });
}

/** findPadCodeByAdbAddress GET /api/shop/app_mobile/findPadCodeByAdbAddress */
export async function shopAppMobileFindPadCodeByAdbAddressGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopAppMobileFindPadCodeByAdbAddressGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/shop/app_mobile/findPadCodeByAdbAddress', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** findWifiUdidsByDevice GET /api/shop/app_mobile/findWifiUdidsByDevice */
export async function shopAppMobileFindWifiUdidsByDeviceGet(options?: { [key: string]: any }) {
  return request<API.WebResultListstring>('/api/shop/app_mobile/findWifiUdidsByDevice', {
    method: 'GET',
    ...(options || {}),
  });
}

/** mobileAiChat POST /api/shop/app_mobile/mobileAiChat */
export async function shopAppMobileMobileAiChatPost(
  body: API.MobileAiRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/shop/app_mobile/mobileAiChat', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** getCloudAdbAddress PUT /api/shop/app_mobile/toggleArmCloudAdb */
export async function shopAppMobileToggleArmCloudAdbPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopAppMobileToggleArmCloudAdbPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCloudAdbAddress>('/api/shop/app_mobile/toggleArmCloudAdb', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 通过code(udid)更新手机的在线状态，要求能获取到deviceId PUT /api/shop/app_mobile/updateStatus */
export async function shopAppMobileUpdateStatusPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopAppMobileUpdateStatusPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/app_mobile/updateStatus', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
