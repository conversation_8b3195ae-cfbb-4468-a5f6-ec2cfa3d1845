// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 打开分身会话V3 POST /api/shop/${param0}/openSession */
export async function shopByShopIdOpenSessionPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopByShopIdOpenSessionPostParams,
  body: API.OpenSessionRequest,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultSessionTokenVo>(`/api/shop/${param0}/openSession`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 刷新Channel会话Token V3 GET /api/shop/session/channelToken */
export async function shopSessionChannelTokenGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSessionChannelTokenGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultSessionChannelTokenVo>('/api/shop/session/channelToken', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 添加IP权重 POST /api/shop/session/ipWeight */
export async function shopSessionIpWeightPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSessionIpWeightPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/shop/session/ipWeight', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 更新通道的前置代理信息 POST /api/shop/session/reportTunnelInfo */
export async function shopSessionReportTunnelInfoPost(
  body: API.ReportSessionChannelTunnelRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/session/reportTunnelInfo', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 切换前置代理前调用 POST /api/shop/session/switchTunnel */
export async function shopSessionSwitchTunnelPost(
  body: API.ReportSessionChannelTunnelRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/session/switchTunnel', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
