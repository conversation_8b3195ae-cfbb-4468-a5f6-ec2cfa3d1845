// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 查询分身的配置信息 GET /api/shop/${param0}/config */
export async function shopByIdConfigGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopByIdConfigGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultListShopConfigDto>(`/api/shop/${param0}/config`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 删除分身的配置信息 DELETE /api/shop/${param0}/config */
export async function shopByIdConfigDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopByIdConfigDeleteParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultListShopConfigDto>(`/api/shop/${param0}/config`, {
    method: 'DELETE',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 批量修改分身的配置信息 PUT /api/shop/config */
export async function shopConfigPut(body: API.ShopConfigRequest, options?: { [key: string]: any }) {
  return request<API.WebResult>('/api/shop/config', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除分身的配置信息 PUT /api/shop/config/delete */
export async function shopConfigDeletePut(
  body: API.ShopConfigPrefixRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/config/delete', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
