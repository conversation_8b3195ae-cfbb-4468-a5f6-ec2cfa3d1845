import { Button, Layout, Space } from 'antd';
import { useRouteMatch } from 'umi';
import { useMemo, useRef, useState } from 'react';
import styled from 'styled-components';
import _ from 'lodash';
import { StyledCenterTabs, StyledTableWrapper } from '@/style/styled';
import SampleRequestTable from '@/pages/Sample/components/SampleRequestTable';
import SampleToViewByShopTable from '@/pages/Sample/components/SampleToViewByShopTable';
import SampleRequestShopsToReviewTable from '@/pages/Sample/components/SampleRequestShopsToReviewTable';
import { useShopBriefById } from '@/components/Common/ShopNode';
import IconFontIcon from '@/components/Common/IconFontIcon';
import I18N from '@/i18n';
import { GhostModalCaller } from '@/mixins/modal';
import SampleRequestPolicy from '@/pages/Sample/components/SampleRequestPolicy';
import { triggerShopTask } from '@/pages/Shop/components/utils';
import { GlobalHeaderAction } from '@/utils/pageUtils';
import type { ActionType } from '@ant-design/pro-table';

const StyledHeader = styled.div`
  padding-left: 0 !important;
  position: relative;
  && {
    .ant-tabs {
      position: absolute;
      right: 0;
      left: 0;
      z-index: 2;
      width: 300px;
      margin: 0 auto;
      .ant-tabs-nav {
        margin-bottom: 0;
      }
      .ant-tabs-tab {
        padding: 14px 12px;
      }
    }
  }
`;

const Index = () => {
  const match = useRouteMatch(['/team/:teamId/sample/:shopId?']);
  const shopId = useMemo(() => {
    const _shopId = Number(match?.params?.shopId);
    if (_.isFinite(_shopId)) {
      return _shopId;
    }
    return undefined;
  }, [match?.params?.shopId]);
  const shopDetail = useShopBriefById(shopId);
  const actionRef = useRef<ActionType>();
  const requestActionRef = useRef<ActionType>();
  const toViewsActionRef = useRef<ActionType>();
  const [activeKey, setActiveKey] = useState<'ToReview' | 'All'>('ToReview');

  return (
    <Layout.Content>
      <GlobalHeaderAction.Emit>
        <Space size={32}>
          <Button
            ghost
            style={{ background: 'none' }}
            icon={<IconFontIcon iconName={'shuaxin_24'} />}
            onClick={() => {
              if (activeKey === 'All') {
                // 历史索样记录
                requestActionRef.current?.reload();
              } else {
                if (shopId) {
                  //待审批的店铺索样记录
                  actionRef.current?.reload();
                } else {
                  //待审批的店铺
                  toViewsActionRef.current?.reload();
                }
              }
            }}
          >
            {I18N.t('刷新')}
          </Button>
          <Button
            ghost
            style={{ background: 'none' }}
            icon={<IconFontIcon iconName={'fangwencelve_24'} />}
            onClick={() => {
              GhostModalCaller(<SampleRequestPolicy />);
            }}
          >
            {I18N.t('自动审批策略')}
          </Button>
          <Button
            ghost
            hidden={activeKey !== 'ToReview' || !shopId}
            style={{ background: 'none' }}
            icon={<IconFontIcon iconName={'duzhanshifangwen_24'} />}
            onClick={() => {
              if (shopDetail) {
                triggerShopTask([shopDetail], 'TS_SyncSampleCreator');
              }
            }}
          >
            {I18N.t('索样记录同步')}
          </Button>
        </Space>
      </GlobalHeaderAction.Emit>
      <StyledHeader style={{ borderBottom: 'none' }} className="header">
        <StyledCenterTabs
          activeKey={activeKey}
          onChange={setActiveKey}
          items={[
            {
              label: '索样审批',
              key: 'ToReview',
              style: {
                width: 140,
              },
            },
            {
              label: '索样跟踪',
              key: 'All',
              style: {
                width: 140,
              },
            },
          ]}
        />
      </StyledHeader>
      <StyledTableWrapper className="main">
        <SampleRequestTable
          ref={requestActionRef}
          style={{ display: activeKey === 'All' ? 'flex' : 'none' }}
        />
        {shopId ? (
          <SampleToViewByShopTable
            ref={actionRef}
            style={{ display: activeKey === 'ToReview' ? 'flex' : 'none' }}
            shopId={shopId}
          />
        ) : (
          <SampleRequestShopsToReviewTable
            ref={toViewsActionRef}
            style={{ display: activeKey === 'ToReview' ? 'flex' : 'none' }}
          />
        )}
      </StyledTableWrapper>
    </Layout.Content>
  );
};
export default Index;
