// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：
import * as AccountController from './AccountController';
import * as AlipayTeamVerifyController from './AlipayTeamVerifyController';
import * as CorsAccountController from './CorsAccountController';
import * as DemoTeamController from './DemoTeamController';
import * as DepartmentController from './DepartmentController';
import * as DeviceRegisterController from './DeviceRegisterController';
import * as FavoriteController from './FavoriteController';
import * as FunctionController from './FunctionController';
import * as KrShopController from './KrShopController';
import * as LoginController from './LoginController';
import * as LoginDeviceController from './LoginDeviceController';
import * as TeamMemberController from './TeamMemberController';
import * as OAuth2Controller from './OAuth2Controller';
import * as OpsStrategyController from './OpsStrategyController';
import * as OpsWebhookController from './OpsWebhookController';
import * as RegisterController from './RegisterController';
import * as ResourceController from './ResourceController';
import * as SupportController from './SupportController';
import * as TeamController from './TeamController';
import * as TeamFavoriteController from './TeamFavoriteController';
import * as TeamSettingsController from './TeamSettingsController';
import * as TeamVerifyController from './TeamVerifyController';
import * as UserController from './UserController';
import * as UserPromotionController from './UserPromotionController';
import * as UserSettingsController from './UserSettingsController';
export default {
  AccountController,
  AlipayTeamVerifyController,
  CorsAccountController,
  DemoTeamController,
  DepartmentController,
  DeviceRegisterController,
  FavoriteController,
  FunctionController,
  KrShopController,
  LoginController,
  LoginDeviceController,
  TeamMemberController,
  OAuth2Controller,
  OpsStrategyController,
  OpsWebhookController,
  RegisterController,
  ResourceController,
  SupportController,
  TeamController,
  TeamFavoriteController,
  TeamSettingsController,
  TeamVerifyController,
  UserController,
  UserPromotionController,
  UserSettingsController,
};
