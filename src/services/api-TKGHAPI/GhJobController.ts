// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 创建主播关注任务 PUT /api/gh/jobs/accountFollow */
export async function ghJobsAccountFollowPut(
  body: API.AccountFollowRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/gh/jobs/accountFollow', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 手机养号 PUT /api/gh/jobs/accountMaintenance */
export async function ghJobsAccountMaintenancePut(
  body: API.AccountMaintenanceRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/jobs/accountMaintenance', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量取消公会任务 DELETE /api/gh/jobs/batchCancelJobs */
export async function ghJobsBatchCancelJobsDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghJobsBatchCancelJobsDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/jobs/batchCancelJobs', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 取消当前团队所有公会任务 DELETE /api/gh/jobs/cancelAllJobs */
export async function ghJobsCancelAllJobsDelete(
  body: API.CancelJobsFilter,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/jobs/cancelAllJobs', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 取消一个公会任务 DELETE /api/gh/jobs/cancelJob */
export async function ghJobsCancelJobDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghJobsCancelJobDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/jobs/cancelJob', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取任务详情 GET /api/gh/jobs/jobDetail */
export async function ghJobsJobDetailGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghJobsJobDetailGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultGhJobDetailVo>('/api/gh/jobs/jobDetail', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 按条件查询历史公会任务 GET /api/gh/jobs/listJobs/history */
export async function ghJobsListJobsHistoryGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghJobsListJobsHistoryGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultGhJobDto>('/api/gh/jobs/listJobs/history', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 按条件查询待执行公会任务 GET /api/gh/jobs/listJobs/pending */
export async function ghJobsListJobsPendingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghJobsListJobsPendingGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultGhJobDto>('/api/gh/jobs/listJobs/pending', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询任务池的分身（或mobile account） GET /api/gh/jobs/listShops */
export async function ghJobsListShopsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghJobsListShopsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListIdNameVo>('/api/gh/jobs/listShops', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** queryCreatingInfo GET /api/gh/jobs/queryCreatingInfo */
export async function ghJobsQueryCreatingInfoGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghJobsQueryCreatingInfoGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultJobCreatingInfo>('/api/gh/jobs/queryCreatingInfo', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建手机账号可用性检查任务 PUT /api/gh/jobs/sendAccountHealthCheck */
export async function ghJobsSendAccountHealthCheckPut(
  body: API.AccountHealthCheckRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/jobs/sendAccountHealthCheck', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 发送邀约建联信息 PUT /api/gh/jobs/sendInvitePM */
export async function ghJobsSendInvitePmPut(
  body: API.SendInvitePMRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/gh/jobs/sendInvitePM', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 同步kakao聊天记录 POST /api/gh/jobs/sendKakaoMessage */
export async function ghJobsSendKakaoMessagePost(
  body: API.SendKakaoMessageRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/jobs/sendKakaoMessage', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 发送回复私信消息 PUT /api/gh/jobs/sendReplyPM */
export async function ghJobsSendReplyPmPut(
  body: API.SendReplyPMRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/jobs/sendReplyPM', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 发送用户卡片 PUT /api/gh/jobs/sendUserCard */
export async function ghJobsSendUserCardPut(
  body: API.SendUserCardRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/jobs/sendUserCard', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建分享视频的任务 PUT /api/gh/jobs/shareVideo */
export async function ghJobsShareVideoPut(
  body: API.ShareVideoRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/gh/jobs/shareVideo', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 对团队所有的未签约的达人进行私信同步 PUT /api/gh/jobs/syncAllPmRequest */
export async function ghJobsSyncAllPmRequestPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghJobsSyncAllPmRequestPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/jobs/syncAllPmRequest', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 同步kakao聊天记录 PUT /api/gh/jobs/syncKakaoChats */
export async function ghJobsSyncKakaoChatsPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghJobsSyncKakaoChatsPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/jobs/syncKakaoChats', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 同步kakao联系人 PUT /api/gh/jobs/syncKakaoContacts */
export async function ghJobsSyncKakaoContactsPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghJobsSyncKakaoContactsPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/jobs/syncKakaoContacts', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 同步kakao联系人到手机 PUT /api/gh/jobs/syncKaokaoContactsToPhone */
export async function ghJobsSyncKaokaoContactsToPhonePut(
  body: API.SyncKaokaoContactsToPhoneRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/jobs/syncKaokaoContactsToPhone', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 选中达人私信同步 PUT /api/gh/jobs/syncPMRequest */
export async function ghJobsSyncPmRequestPut(
  body: API.SyncPMRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/jobs/syncPMRequest', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 流程执行时获取任务 GET /api/gh/jobs/task/fetchJob */
export async function ghJobsTaskFetchJobGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghJobsTaskFetchJobGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultGhJobDto>('/api/gh/jobs/task/fetchJob', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 汇报ghJob执行结果 PUT /api/gh/jobs/task/reportJob */
export async function ghJobsTaskReportJobPut(
  body: API.ReportGhJobRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/jobs/task/reportJob', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新公会达人信息 PUT /api/gh/jobs/updateCreatorInfo */
export async function ghJobsUpdateCreatorInfoPut(
  body: API.UpdateGhCreatorInfoRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/gh/jobs/updateCreatorInfo', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
