// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取演示团队信息 GET /api/demo/team */
export async function demoTeamGet(options?: { [key: string]: any }) {
  return request<API.WebResultTeamDto>('/api/demo/team', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 当前用户加入演示团队（如果未加入） POST /api/demo/team/checkAndJoin */
export async function demoTeamCheckAndJoinPost(options?: { [key: string]: any }) {
  return request<API.WebResultCheckAndJoinDemoTeamResult>('/api/demo/team/checkAndJoin', {
    method: 'POST',
    ...(options || {}),
  });
}
