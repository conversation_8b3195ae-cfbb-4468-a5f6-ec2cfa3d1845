// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** alipayCallback GET /api/team/verify/alipay/callback */
export async function teamVerifyAlipayCallbackGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamVerifyAlipayCallbackGetParams,
  options?: { [key: string]: any },
) {
  return request<any>('/api/team/verify/alipay/callback', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 录入用户实名信息预咨询 POST /api/team/verify/alipay/preconsult */
export async function teamVerifyAlipayPreconsultPost(
  body: API.AlipayPreconsultRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultAlipayPreconsultResult>('/api/team/verify/alipay/preconsult', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询认证结果 只有在发起认证2小时内才能查询结果 GET /api/team/verify/alipay/result/${param0} */
export async function teamVerifyAlipayResultByVerifyIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamVerifyAlipayResultByVerifyIdGetParams,
  options?: { [key: string]: any },
) {
  const { verifyId: param0, ...queryParams } = params;
  return request<API.WebResultAlipayVerifyResultVo>(`/api/team/verify/alipay/result/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}
