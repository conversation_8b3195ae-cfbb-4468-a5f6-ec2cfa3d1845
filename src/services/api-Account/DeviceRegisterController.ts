// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 设备注册 POST /api/device/register */
export async function deviceRegisterPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deviceRegisterPostParams,
  body: string,
  options?: { [key: string]: any },
) {
  return request<API.WebResultHyRuntimeDto>('/api/device/register', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 汇报真实IP GET /api/device/reportClientIp */
export async function deviceReportClientIpGet(options?: { [key: string]: any }) {
  return request<API.WebResult>('/api/device/reportClientIp', {
    method: 'GET',
    ...(options || {}),
  });
}
