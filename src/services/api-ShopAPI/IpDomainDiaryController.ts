// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取团队是否支持纯净度检查 GET /api/ipdd/ipPurenessCheck */
export async function ipddIpPurenessCheckGet(options?: { [key: string]: any }) {
  return request<API.WebResultboolean>('/api/ipdd/ipPurenessCheck', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 查询ip访问域名的日记 GET /api/ipdd/list */
export async function ipddListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipddListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultIpDomainDiaryDto>('/api/ipdd/list', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 记录一条日记 POST /api/ipdd/record */
export async function ipddRecordPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipddRecordPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultboolean>('/api/ipdd/record', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
