import I18N from '@/i18n';
import Selector from '@/components/Common/Selector/index';
import { Checkbox, Form, Menu } from 'antd';
import type { CSSProperties } from 'react';
import { useState } from 'react';
import { useSystemConfig } from '@/hooks/useSystemConfig';
import { OptionPlaceholder } from '@/components/Common/Placeholder';
import styled from 'styled-components';
import _ from 'lodash';

export const ConversionRateType = {
  good: I18N.t('优秀'),
  normal: I18N.t('正常'),
  bad: I18N.t('较差'),
  abnormal: I18N.t('异常'),
};
const StyledMenu = styled(Menu)`
  && {
    padding-top: 4px !important;
    padding-bottom: 4px !important;
    .ant-menu-item {
      height: 32px !important;
      margin: 0 !important;
      padding: 0 !important;
      line-height: 32px !important;
    }
    .ant-menu-title-content {
      height: 100%;
      > div {
        display: flex;
        gap: 8px;
        align-items: center;
        height: 100%;
        padding: 5px 12px;
        &:hover {
          background: #f5f5f5;
        }
      }
    }
  }
`;
const ConversionRateSelector = (props: {
  style?: CSSProperties;
  onChange?: (val: (keyof typeof ConversionRateType)[] | undefined) => void;
  value?: (keyof typeof ConversionRateType)[];
}) => {
  const { value = [], onChange } = props;
  const { data } = useSystemConfig();
  const [visible, setVisible] = useState(false);

  return (
    <Form.Item noStyle shouldUpdate>
      {() => {
        return (
          <>
            <Selector
              mode={'multiple'}
              dropdownMatchSelectWidth={false}
              showPlaceholderOption
              value={value?.length > 0 ? [value[0]] : value}
              maxTagCount={1}
              onChange={() => {
                onChange?.(undefined);
                setVisible(false);
              }}
              tagRender={() => {
                let text = undefined;
                if (value.length > 0) {
                  if (value?.length === 1) {
                    // 找到对应的label
                    text = ConversionRateType[value[0]];
                  } else {
                    text = I18N.t(`指定区间范围`);
                  }
                }
                return (
                  <div
                    style={{
                      paddingLeft: 7,
                      overflow: 'hidden',
                      height: '100%',
                      display: 'flex',
                      alignItems: 'center',
                    }}
                  >
                    <OptionPlaceholder type={'ordersPm'} text={text} />
                  </div>
                );
              }}
              placeholder={<OptionPlaceholder type={'ordersPm'} />}
              open={visible}
              onDropdownVisibleChange={(v) => {
                setVisible(v);
              }}
              dropdownRender={() => {
                const _options = Object.entries(ConversionRateType).map(([key, label]) => {
                  return {
                    value: key,
                    label,
                  };
                });
                return (
                  <StyledMenu
                    selectable={false}
                    items={_options.map((item) => {
                      const checked = value?.includes(item.value);
                      let suffix;
                      if (item.value === 'good') {
                        suffix = <span>&gt; {data?.ordersPmQueryVo?.goodRate}</span>;
                      } else if (item.value === 'normal') {
                        suffix = (
                          <span>
                            {data?.ordersPmQueryVo?.badRate} - {data?.ordersPmQueryVo?.goodRate}
                          </span>
                        );
                      } else if (item.value === 'bad') {
                        suffix = <span>&lt; {data?.ordersPmQueryVo?.badRate}</span>;
                      }

                      return {
                        key: item.value,
                        label: (
                          <div
                            onClick={() => {
                              let list: string[] = [];
                              if (checked) {
                                list = _.without(value, item.value);
                              } else {
                                list = [...value, item.value];
                              }
                              if (list.length === _options.length || list.length === 0) {
                                onChange?.(undefined);
                                setVisible(false);
                              } else {
                                onChange?.(list);
                              }
                            }}
                          >
                            <Checkbox checked={checked} />
                            <span>
                              {item.label} {suffix}
                            </span>
                          </div>
                        ),
                      };
                    })}
                  />
                );
              }}
            />
          </>
        );
      }}
    </Form.Item>
  );
};

export default ConversionRateSelector;
