// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 将rpa包时券绑定到设备 PUT /api/rpa/fees/bindVoucher/${param0} */
export async function rpaFeesBindVoucherByRpaVoucherIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaFeesBindVoucherByRpaVoucherIdPutParams,
  options?: { [key: string]: any },
) {
  const { rpaVoucherId: param0, ...queryParams } = params;
  return request<API.WebResultRpaVoucherVo>(`/api/rpa/fees/bindVoucher/${param0}`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 购买rpa包时券 POST /api/rpa/fees/buyRpaVouchers */
export async function rpaFeesBuyRpaVouchersPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaFeesBuyRpaVouchersPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCreateOrderResponse>('/api/rpa/fees/buyRpaVouchers', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取某个rpa包时券(绑定的设备)上正跑着的task个数 GET /api/rpa/fees/checkRunningTaskOnVoucher/${param0} */
export async function rpaFeesCheckRunningTaskOnVoucherByRpaVoucherIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaFeesCheckRunningTaskOnVoucherByRpaVoucherIdGetParams,
  options?: { [key: string]: any },
) {
  const { rpaVoucherId: param0, ...queryParams } = params;
  return request<API.WebResultint>(`/api/rpa/fees/checkRunningTaskOnVoucher/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 批量删除流程任务卡 DELETE /api/rpa/fees/deleteVouchers */
export async function rpaFeesDeleteVouchersDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaFeesDeleteVouchersDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/rpa/fees/deleteVouchers', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** rpa包时卡批量续费 POST /api/rpa/fees/renewRpaVouchers */
export async function rpaFeesRenewRpaVouchersPost(
  body: API.RenewRpaVoucherRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCreateOrderResponse>('/api/rpa/fees/renewRpaVouchers', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量切换rpa包时卡自动续费 PUT /api/rpa/fees/toggleRpaVoucherAutoRenew */
export async function rpaFeesToggleRpaVoucherAutoRenewPut(
  body: API.ToggleRpaVoucherAutoRenewRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/rpa/fees/toggleRpaVoucherAutoRenew', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 将rpa包时券解绑 PUT /api/rpa/fees/unbindVoucher/${param0} */
export async function rpaFeesUnbindVoucherByRpaVoucherIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaFeesUnbindVoucherByRpaVoucherIdPutParams,
  options?: { [key: string]: any },
) {
  const { rpaVoucherId: param0, ...queryParams } = params;
  return request<API.WebResultRpaVoucherVo>(`/api/rpa/fees/unbindVoucher/${param0}`, {
    method: 'PUT',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取某一个rpa包时券详情 GET /api/rpa/fees/voucher/${param0} */
export async function rpaFeesVoucherByRpaVoucherIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaFeesVoucherByRpaVoucherIdGetParams,
  options?: { [key: string]: any },
) {
  const { rpaVoucherId: param0, ...queryParams } = params;
  return request<API.WebResultRpaVoucherVo>(`/api/rpa/fees/voucher/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取流程任务卡绑定历史 GET /api/rpa/fees/voucherBindHis/${param0} */
export async function rpaFeesVoucherBindHisByRpaVoucherIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaFeesVoucherBindHisByRpaVoucherIdGetParams,
  options?: { [key: string]: any },
) {
  const { rpaVoucherId: param0, ...queryParams } = params;
  return request<API.WebResultListRpaVoucherHisVo>(`/api/rpa/fees/voucherBindHis/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 查询当前团队下rpa包时券列表 GET /api/rpa/fees/vouchers */
export async function rpaFeesVouchersGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaFeesVouchersGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultRpaVoucherVo>('/api/rpa/fees/vouchers', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
