// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 根据最佳实践创建一个计划链 POST /api/tkshop/plan/applyBestPractice */
export async function tkshopPlanApplyBestPracticePost(
  body: API.ApplyBestPracticeRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/plan/applyBestPractice', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建一个触发计划 POST /api/tkshop/plan/createFollowingPlan */
export async function tkshopPlanCreateFollowingPlanPost(
  body: API.CreateTkshopFollowingPlanRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/plan/createFollowingPlan', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 往tkshop计划编排里添加一个店铺 POST /api/tkshop/plan/createGroup */
export async function tkshopPlanCreateGroupPost(
  body: API.AddShopToPlanLayoutRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/plan/createGroup', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建一个tkshop计划编排 POST /api/tkshop/plan/createLayout */
export async function tkshopPlanCreateLayoutPost(
  body: API.CreatePlanChainLayoutRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/plan/createLayout', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建一个定时计划 POST /api/tkshop/plan/createTimingPlan */
export async function tkshopPlanCreateTimingPlanPost(
  body: API.chuangjianyigetkshopdingshijihua,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/plan/createTimingPlan', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除编排计划下的一个店铺，会同时删除该分身下的所有计划链 DELETE /api/tkshop/plan/deleteGroup */
export async function tkshopPlanDeleteGroupDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopPlanDeleteGroupDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/plan/deleteGroup', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 删除一个计划编排，会删除该编排下所有的计划 DELETE /api/tkshop/plan/deleteLayout */
export async function tkshopPlanDeleteLayoutDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopPlanDeleteLayoutDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/plan/deleteLayout', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 删除一个计划。会导致链上该计划的后续计划全部删除 DELETE /api/tkshop/plan/deletePlan */
export async function tkshopPlanDeletePlanDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopPlanDeletePlanDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/plan/deletePlan', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 执行一个group里的所有timing计划 PUT /api/tkshop/plan/executeAllPlansInGroup */
export async function tkshopPlanExecuteAllPlansInGroupPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopPlanExecuteAllPlansInGroupPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/plan/executeAllPlansInGroup', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 执行一个layout里的所有timing计划 PUT /api/tkshop/plan/executeAllPlansInLayout */
export async function tkshopPlanExecuteAllPlansInLayoutPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopPlanExecuteAllPlansInLayoutPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/plan/executeAllPlansInLayout', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取单个tkshop计划编排信息 GET /api/tkshop/plan/getLayout */
export async function tkshopPlanGetLayoutGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopPlanGetLayoutGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkshopPlanChainLayoutVo>('/api/tkshop/plan/getLayout', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取某个计划分组下的计划链信息 GET /api/tkshop/plan/loadGroupChains */
export async function tkshopPlanLoadGroupChainsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopPlanLoadGroupChainsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListPlanGroupChain>('/api/tkshop/plan/loadGroupChains', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取某个tkshop计划编的店铺信息，每个店铺的多条计划链称作一个分组 GET /api/tkshop/plan/loadLayoutGroups */
export async function tkshopPlanLoadLayoutGroupsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopPlanLoadLayoutGroupsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTkshopPlanChainGroupVo>('/api/tkshop/plan/loadLayoutGroups', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取tkshop计划编排列表 GET /api/tkshop/plan/loadLayouts */
export async function tkshopPlanLoadLayoutsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopPlanLoadLayoutsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTkshopPlanChainLayoutVo>('/api/tkshop/plan/loadLayouts', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 立即执行一个计划 PUT /api/tkshop/plan/runPlan */
export async function tkshopPlanRunPlanPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopPlanRunPlanPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/plan/runPlan', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 切换一个计划编排的开启状态，关闭之后会导致该编排里所有计划都不调度 PUT /api/tkshop/plan/toggleLayoutEnabled */
export async function tkshopPlanToggleLayoutEnabledPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopPlanToggleLayoutEnabledPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/plan/toggleLayoutEnabled', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 切换一个计划的开启状态 PUT /api/tkshop/plan/togglePlanEnabled */
export async function tkshopPlanTogglePlanEnabledPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopPlanTogglePlanEnabledPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/plan/togglePlanEnabled', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修改计划所属设备 POST /api/tkshop/plan/updateLayoutDevice */
export async function tkshopPlanUpdateLayoutDevicePost(
  body: API.UpdateLayoutDeviceRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/plan/updateLayoutDevice', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更改一个计划编排的名称 PUT /api/tkshop/plan/updateLayoutName */
export async function tkshopPlanUpdateLayoutNamePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopPlanUpdateLayoutNamePutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/plan/updateLayoutName', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修改一个计划的执行时间（自动计划）/ 触发间隔（触发计划），计划参数 POST /api/tkshop/plan/updatePlan */
export async function tkshopPlanUpdatePlanPost(
  body: API.UpdatePlanRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/plan/updatePlan', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
