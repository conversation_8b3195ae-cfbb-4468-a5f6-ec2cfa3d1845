// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 根据查询条件过滤所有中转 GET /api/transit/all */
export async function transitAllGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.transitAllGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTransitDto>('/api/transit/all', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 根据信息过滤团队接入点分组（仅供前端） GET /api/transit/groupList */
export async function transitGroupListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.transitGroupListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTransitGroupVo>('/api/transit/groupList', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取团队所有接入点分组（供客户端使用） GET /api/transit/groups */
export async function transitGroupsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.transitGroupsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTransitGroupVo>('/api/transit/groups', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取所有中转 GET /api/transit/list */
export async function transitListGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTransitWithLocationVo>('/api/transit/list', {
    method: 'GET',
    ...(options || {}),
  });
}
