// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 达人自动化标签配置 GET /api/tkshop/settings/creatorExpiredAutoTags */
export async function tkshopSettingsCreatorExpiredAutoTagsGet(options?: { [key: string]: any }) {
  return request<API.WebResultTkshopCreatorExpiredAutoTags>(
    '/api/tkshop/settings/creatorExpiredAutoTags',
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/** 配置达人自动化标签 PUT /api/tkshop/settings/creatorExpiredAutoTags */
export async function tkshopSettingsCreatorExpiredAutoTagsPut(
  body: API.TkshopCreatorExpiredAutoTags,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/settings/creatorExpiredAutoTags', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取浏览器执行策略 GET /api/tkshop/settings/getRpaBrowserPolicy */
export async function tkshopSettingsGetRpaBrowserPolicyGet(options?: { [key: string]: any }) {
  return request<API.WebResultGhRpaBrowserPolicy>('/api/tkshop/settings/getRpaBrowserPolicy', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取用户登录设备的浏览器流程并发数（仅限tkshop触发的任务） GET /api/tkshop/settings/getTSDeviceBrowserConcurrent */
export async function tkshopSettingsGetTsDeviceBrowserConcurrentGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopSettingsGetTSDeviceBrowserConcurrentGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/tkshop/settings/getTSDeviceBrowserConcurrent', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取同步达人基本信息的配置 GET /api/tkshop/settings/getTSSyncCreatorPolicy */
export async function tkshopSettingsGetTsSyncCreatorPolicyGet(options?: { [key: string]: any }) {
  return request<API.WebResultTSSyncCreatorPolicy>('/api/tkshop/settings/getTSSyncCreatorPolicy', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 设置用户登录设备的浏览器流程并发数（仅限tkshop触发的任务） PUT /api/tkshop/settings/setTSDeviceBrowserConcurrent */
export async function tkshopSettingsSetTsDeviceBrowserConcurrentPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopSettingsSetTSDeviceBrowserConcurrentPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/settings/setTSDeviceBrowserConcurrent', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 设置浏览器执行策略 PUT /api/tkshop/settings/updateRpaBrowserPolicy */
export async function tkshopSettingsUpdateRpaBrowserPolicyPut(
  body: API.GhRpaBrowserPolicy,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/settings/updateRpaBrowserPolicy', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新同步达人基本信息的配置 PUT /api/tkshop/settings/updateTSSyncCreatorPolicy */
export async function tkshopSettingsUpdateTsSyncCreatorPolicyPut(
  body: API.TSSyncCreatorPolicy,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/settings/updateTSSyncCreatorPolicy', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
