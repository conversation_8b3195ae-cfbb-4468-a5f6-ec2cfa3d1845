// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取单个plan详情，其账号信息是分页返回的 GET /api/rpa/plan/${param0} */
export async function rpaPlanByRpaPlanIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanByRpaPlanIdGetParams,
  options?: { [key: string]: any },
) {
  const { rpaPlanId: param0, ...queryParams } = params;
  return request<API.WebResultRpaPlanVo>(`/api/rpa/plan/${param0}`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 删除一个流程计划 DELETE /api/rpa/plan/${param0} */
export async function rpaPlanByRpaPlanIdDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanByRpaPlanIdDeleteParams,
  options?: { [key: string]: any },
) {
  const { rpaPlanId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/rpa/plan/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 复制一个流程计划 POST /api/rpa/plan/${param0}/copy */
export async function rpaPlanByRpaPlanIdCopyPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanByRpaPlanIdCopyPostParams,
  body: API.CopyPlanRequest,
  options?: { [key: string]: any },
) {
  const { rpaPlanId: param0, ...queryParams } = params;
  return request<API.WebResultRpaPlanVo>(`/api/rpa/plan/${param0}/copy`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 获取plan的调度列表 GET /api/rpa/plan/${param0}/findPlanSchedulers */
export async function rpaPlanByRpaPlanIdFindPlanSchedulersGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanByRpaPlanIdFindPlanSchedulersGetParams,
  options?: { [key: string]: any },
) {
  const { rpaPlanId: param0, ...queryParams } = params;
  return request<API.WebResultListstring>(`/api/rpa/plan/${param0}/findPlanSchedulers`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取plan的参数文件签名url(如果有的话) GET /api/rpa/plan/${param0}/getParamFileSignUrl */
export async function rpaPlanByRpaPlanIdGetParamFileSignUrlGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanByRpaPlanIdGetParamFileSignUrlGetParams,
  options?: { [key: string]: any },
) {
  const { rpaPlanId: param0, ...queryParams } = params;
  return request<API.WebResultstring>(`/api/rpa/plan/${param0}/getParamFileSignUrl`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取plan最近n个执行历史简易信息 GET /api/rpa/plan/${param0}/his */
export async function rpaPlanByRpaPlanIdHisGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanByRpaPlanIdHisGetParams,
  options?: { [key: string]: any },
) {
  const { rpaPlanId: param0, ...queryParams } = params;
  return request<API.WebResultListRpaSimpleHisVo>(`/api/rpa/plan/${param0}/his`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 编辑流程计划所有属性 POST /api/rpa/plan/${param0}/modify */
export async function rpaPlanByRpaPlanIdModifyPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanByRpaPlanIdModifyPostParams,
  body: API.CreatePlanRequest,
  options?: { [key: string]: any },
) {
  const { rpaPlanId: param0, ...queryParams } = params;
  return request<API.WebResultRpaPlanVo>(`/api/rpa/plan/${param0}/modify`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 获取plan的参数（已经按账号展开） GET /api/rpa/plan/${param0}/params */
export async function rpaPlanByRpaPlanIdParamsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanByRpaPlanIdParamsGetParams,
  options?: { [key: string]: any },
) {
  const { rpaPlanId: param0, ...queryParams } = params;
  return request<API.WebResultMaplong>(`/api/rpa/plan/${param0}/params`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** markPaused POST /api/rpa/plan/${param0}/pause */
export async function rpaPlanByRpaPlanIdPausePost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanByRpaPlanIdPausePostParams,
  options?: { [key: string]: any },
) {
  const { rpaPlanId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/rpa/plan/${param0}/pause`, {
    method: 'POST',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 切换是否开启自动调度 PUT /api/rpa/plan/${param0}/toggle/schedule */
export async function rpaPlanByRpaPlanIdToggleSchedulePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanByRpaPlanIdToggleSchedulePutParams,
  options?: { [key: string]: any },
) {
  const { rpaPlanId: param0, ...queryParams } = params;
  return request<API.WebResultRpaPlanVo>(`/api/rpa/plan/${param0}/toggle/schedule`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 编辑流程计划基本属性 POST /api/rpa/plan/${param0}/update/basic */
export async function rpaPlanByRpaPlanIdUpdateBasicPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanByRpaPlanIdUpdateBasicPostParams,
  body: API.UpdatePlanBasicRequest,
  options?: { [key: string]: any },
) {
  const { rpaPlanId: param0, ...queryParams } = params;
  return request<API.WebResultRpaPlanVo>(`/api/rpa/plan/${param0}/update/basic`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 编辑流程计划的变量 POST /api/rpa/plan/${param0}/update/params */
export async function rpaPlanByRpaPlanIdUpdateParamsPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanByRpaPlanIdUpdateParamsPostParams,
  body: API.UpdatePlanParamsRequest,
  options?: { [key: string]: any },
) {
  const { rpaPlanId: param0, ...queryParams } = params;
  return request<API.WebResultRpaPlanVo>(`/api/rpa/plan/${param0}/update/params`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 编辑流程计划的执行策略 POST /api/rpa/plan/${param0}/update/policy */
export async function rpaPlanByRpaPlanIdUpdatePolicyPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanByRpaPlanIdUpdatePolicyPostParams,
  body: API.UpdatePlanPolicyRequest,
  options?: { [key: string]: any },
) {
  const { rpaPlanId: param0, ...queryParams } = params;
  return request<API.WebResultRpaPlanVo>(`/api/rpa/plan/${param0}/update/policy`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 编辑流程计划账号列表 POST /api/rpa/plan/${param0}/update/shops */
export async function rpaPlanByRpaPlanIdUpdateShopsPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanByRpaPlanIdUpdateShopsPostParams,
  body: API.UpdatePlanShopsRequest,
  options?: { [key: string]: any },
) {
  const { rpaPlanId: param0, ...queryParams } = params;
  return request<API.WebResultRpaPlanVo>(`/api/rpa/plan/${param0}/update/shops`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 获取某个流程的历史版本记录，可用来回滚到某个特定版本 PUT /api/rpa/plan/${param0}/updateToLatestFlowVersion */
export async function rpaPlanByRpaPlanIdUpdateToLatestFlowVersionPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanByRpaPlanIdUpdateToLatestFlowVersionPutParams,
  options?: { [key: string]: any },
) {
  const { rpaPlanId: param0, ...queryParams } = params;
  return request<API.WebResultRpaPlanVo>(`/api/rpa/plan/${param0}/updateToLatestFlowVersion`, {
    method: 'PUT',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取团队内所有计划指定的设备列表（不包括指定'任意设备的'） GET /api/rpa/plan/allPlanDevices */
export async function rpaPlanAllPlanDevicesGet(options?: { [key: string]: any }) {
  return request<API.WebResultListLoginDeviceDto>('/api/rpa/plan/allPlanDevices', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 批量切换是否开启自动调度 GET /api/rpa/plan/batchToggleEnableSchedule */
export async function rpaPlanBatchToggleEnableScheduleGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanBatchToggleEnableScheduleGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/rpa/plan/batchToggleEnableSchedule', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 校验某个计划名称（在分组内）是否存在 GET /api/rpa/plan/checkNameExists */
export async function rpaPlanCheckNameExistsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanCheckNameExistsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultboolean>('/api/rpa/plan/checkNameExists', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建一个流程计划 POST /api/rpa/plan/create */
export async function rpaPlanCreatePost(
  body: API.CreatePlanRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultRpaPlanVo>('/api/rpa/plan/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除流程计划 DELETE /api/rpa/plan/deletePlans */
export async function rpaPlanDeletePlansDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanDeletePlansDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultRpaPlanExports>('/api/rpa/plan/deletePlans', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 导出流程计划 GET /api/rpa/plan/exportPlans */
export async function rpaPlanExportPlansGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanExportPlansGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultRpaPlanExports>('/api/rpa/plan/exportPlans', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 导入流程计划，其中计划的flowId已经根据suggestion或者用户设置改变了 POST /api/rpa/plan/importPlans */
export async function rpaPlanImportPlansPost(
  body: Record<string, any>[],
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/rpa/plan/importPlans', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页获取task plan列表 GET /api/rpa/plan/list */
export async function rpaPlanListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultRpaPlanVo>('/api/rpa/plan/list', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取task plan列表V2（TK子系统） cronExpression会转换成目标时区下的星期；zonedTime是目标时区下的时间 GET /api/rpa/plan/list2 */
export async function rpaPlanList2Get(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanList2GetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListRpaPlanVo>('/api/rpa/plan/list2', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 手动执行一个流程计划，在真实执行机器上调用该接口 POST /api/rpa/plan/manualRunPlan */
export async function rpaPlanManualRunPlanPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanManualRunPlanPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultRpaTaskVo>('/api/rpa/plan/manualRunPlan', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 手动触发执行一个流程计划，在发起执行的机器上调用该接口 POST /api/rpa/plan/manualTriggerPlan/${param0} */
export async function rpaPlanManualTriggerPlanByRpaPlanIdPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanManualTriggerPlanByRpaPlanIdPostParams,
  body: API.RunPlanRequest,
  options?: { [key: string]: any },
) {
  const { rpaPlanId: param0, ...queryParams } = params;
  return request<API.WebResultRpaTaskVo>(`/api/rpa/plan/manualTriggerPlan/${param0}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 根据名称获取rpaPlan可用的名称 GET /api/rpa/plan/prepareName */
export async function rpaPlanPrepareNameGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanPrepareNameGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/rpa/plan/prepareName', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 调度执行一个流程计划，在真实执行机器上调用该接口 POST /api/rpa/plan/run/${param0} */
export async function rpaPlanRunByRpaPlanIdPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanRunByRpaPlanIdPostParams,
  body: API.RunPlanRequest,
  options?: { [key: string]: any },
) {
  const { rpaPlanId: param0, ...queryParams } = params;
  return request<API.WebResultRpaTaskVo>(`/api/rpa/plan/run/${param0}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...queryParams,
    },
    data: body,
    ...(options || {}),
  });
}

/** 导入流程计划时用到，由原始计划的流程给出建议导入计划的时候应该使用什么流程 GET /api/rpa/plan/suggestImportPlanFlows */
export async function rpaPlanSuggestImportPlanFlowsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanSuggestImportPlanFlowsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMaplong>('/api/rpa/plan/suggestImportPlanFlows', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
