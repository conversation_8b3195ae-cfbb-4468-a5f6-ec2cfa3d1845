declare namespace API {
  type accountAvatarByFileGetParams = {
    /** file */
    file: string;
  };

  type accountByUserIdAvatarGetParams = {
    /** userId */
    userId: number;
  };

  type accountCheckEmailOrPhoneGetParams = {
    /** areaCode */
    areaCode?: string;
    /** account */
    account: string;
  };

  type accountCheckInviteCodeGetParams = {
    /** inviteCode */
    inviteCode: string;
  };

  type accountDeviceByDeviceIdGetParams = {
    /** deviceId */
    deviceId: string;
  };

  type accountDeviceByDeviceIdLogoutDeleteParams = {
    /** deviceId */
    deviceId: number;
  };

  type accountDeviceKickoffPutParams = {
    /** sessionId */
    sessionId?: string;
    /** self */
    self?: boolean;
  };

  type accountDeviceRemainLoginPutParams = {
    /** appRemainLogin */
    appRemainLogin: boolean;
  };

  type accountDevicesGetParams = {
    /** 是否在线，不设置返回全部 */
    online?: boolean;
    /** sortField */
    sortField?: 'hostName' | 'id' | 'lastActiveTime' | 'lastLoginTime';
    /** sortOrder */
    sortOrder?: 'asc' | 'desc';
  };

  type accountEmailPutParams = {
    /** 邮箱 */
    email: string;
    /** 验证码 */
    verifyCode: string;
  };

  type accountLoginByCodePostParams = {
    /** areaCode */
    areaCode?: string;
    /** account */
    account: string;
    /** verifyCode */
    verifyCode: string;
    /** rememberMe */
    rememberMe?: boolean;
  };

  type accountLoginPostParams = {
    /** account */
    account: string;
    /** password */
    password: string;
    /** rememberMe */
    rememberMe?: boolean;
  };

  type accountNicknameAndSignaturePutParams = {
    /** nickname */
    nickname: string;
    /** signature */
    signature: string;
  };

  type accountNotifyWeixinBindByTeamIdPutParams = {
    /** teamId */
    teamId: number;
  };

  type accountPhonePutParams = {
    /** areaCode */
    areaCode?: string;
    /** 手机号 */
    phone: string;
    /** 验证码 */
    verifyCode: string;
  };

  type accountPreLoginGetParams = {
    /** account */
    account: string;
  };

  type accountRegisterPostParams = {
    /** 邀请码，非必需 */
    inviteCode?: string;
    /** areaCode */
    areaCode?: string;
    /** 账号（手机号或邮箱） */
    account: string;
    /** 验证码 */
    verifyCode: string;
  };

  type accountSetPwdAndNicknamePostParams = {
    /** 昵称 */
    nickname: string;
    /** updateToken */
    updateToken: string;
    /** password */
    password: string;
  };

  type accountSetPwdWhenEmptyPostParams = {
    /** password */
    password: string;
  };

  type accountUpdatePwdByVerifyCodePostParams = {
    /** areaCode */
    areaCode?: string;
    /** 账号 */
    account: string;
    /** 密码 */
    password: string;
    /** 验证码 */
    verifyCode: string;
  };

  type accountUpdatePwdPostParams = {
    /** 原始登录密码 */
    originPassword?: string;
    /** 新的登录密码 */
    curPassword: string;
  };

  type accountVerifyCodeGetParams = {
    /** areaCode */
    areaCode?: string;
    /** 账号（手机号或邮箱） */
    account: string;
    /** 进行的操作 */
    action:
      | 'bindEmail'
      | 'download'
      | 'findPassword'
      | 'joinTeam'
      | 'login'
      | 'mobile3e'
      | 'partnerApply'
      | 'register'
      | 'resetPhone'
      | 'testing'
      | 'twoFa';
    /** captcha */
    captcha: string;
  };

  type AddFavorateRequest = {
    resourceItems?: FavoriteItem[];
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
  };

  type AlipayPreconsultRequest = {
    /** 姓名 */
    certName?: string;
    /** 身份证号 */
    certNo?: string;
    /** 支付宝登录账号，可选 */
    loginId?: string;
    /** 支付宝绑定手机号，可选 */
    mobile?: string;
  };

  type AlipayPreconsultResult = {
    url?: string;
    verifyId?: string;
  };

  type AlipayVerifyResultVo = {
    done?: boolean;
    message?: string;
    responseObj?: Record<string, any>;
    success?: boolean;
  };

  type AuditDetailVo = {
    /** 申请人 */
    applyUser?: UserVo;
    applyUserId?: number;
    applyUserNickname?: string;
    auditTime?: string;
    bizId?: number;
    createTime?: string;
    /** 处理人 */
    handleUser?: UserVo;
    handleUserId?: number;
    handleUserNickname?: string;
    id?: number;
    paramContent?: string;
    remark?: string;
    status?: 'APPROVED' | 'CANCEL' | 'NEW' | 'NOT_PASS';
    teamId?: number;
    teamName?: string;
    type?:
      | 'JOIN_TEAM'
      | 'NEW_DEVICE'
      | 'RECEIVE_SHOP'
      | 'RECEIVE_SHOPS'
      | 'SHARED_MOBILES'
      | 'SHARED_SHOP'
      | 'SHARED_SHOPS'
      | 'TRANSFER_SHOP';
  };

  type AuditResultVo = {
    /** 已完成总数 */
    done?: number;
    page?: PageResultAuditDetailVo;
    /** 待审批总数 */
    pending?: number;
  };

  type BizLinkDto = {
    bizData?: string;
    bizType?: 'InviteJoinTeam';
    createTime?: string;
    creatorId?: number;
    creatorName?: string;
    expireSeconds?: number;
    id?: number;
    lastAccessTime?: string;
    linkStatus?: 'Expired' | 'InValid' | 'Init' | 'Used' | 'Valid';
    oneoff?: boolean;
    targetEmail?: string;
    targetUser?: number;
    teamId?: number;
    teamName?: string;
    token?: string;
    url?: string;
  };

  type CheckAndJoinDemoTeamResult = {
    demoTeam?: TeamDto;
    joinStatus?: 'ALREADY_IN_AUDIT' | 'ALREADY_IN_TEAM' | 'JOIN_SUCCESS' | 'WAIT_AUDIT';
  };

  type CommonIdsRequest = {
    ids?: number[];
  };

  type Company4ElementVo = {
    /** 企业名称 */
    entname?: string;
    /** 企业法人身份证号 */
    idcard?: string;
    /** 企业法人姓名 */
    name?: string;
    /** 企业工商注册号/统一信用代码 */
    regno?: string;
  };

  type ConfirmInviteJoinTeamResult = {
    link?: BizLinkDto;
    loginResult?: LoginResultVo;
  };

  type CreateOpsStrategyGroupRequest = {
    description?: string;
    items?: OpsStrategyItemVo[];
    name?: string;
  };

  type departmentByDepartmentIdDeleteParams = {
    /** departmentId */
    departmentId: number;
  };

  type departmentByDepartmentIdGetParams = {
    /** departmentId */
    departmentId: number;
  };

  type departmentByDepartmentIdGrantedResourcesGetParams = {
    /** departmentId */
    departmentId: number;
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
  };

  type departmentByDepartmentIdGrantResourcesPutParams = {
    /** departmentId */
    departmentId: number;
  };

  type departmentByDepartmentIdMemberPostParams = {
    /** departmentId */
    departmentId: number;
  };

  type departmentByDepartmentIdMembersGetParams = {
    /** departmentId */
    departmentId: number;
  };

  type departmentByDepartmentIdPutParams = {
    /** departmentId */
    departmentId: number;
    /** name */
    name?: string;
  };

  type departmentByIdInvitingCodeGetParams = {
    /** id */
    id: number;
  };

  type departmentByIdInvitingEnabledPutParams = {
    /** id */
    id: number;
    /** invitingEnabled */
    invitingEnabled: boolean;
  };

  type departmentByIdRefreshInvitingCodePutParams = {
    /** id */
    id: number;
  };

  type departmentByIdResourceIdsGetParams = {
    /** id */
    id: number;
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
  };

  type DepartmentDto = {
    createTime?: string;
    hidden?: boolean;
    id?: number;
    invitingAuditEnabled?: boolean;
    invitingEnabled?: boolean;
    name?: string;
    parentId?: number;
    sortNumber?: number;
    teamId?: number;
  };

  type departmentMemberByUserIdPutParams = {
    /** userId */
    userId: number;
  };

  type DepartmentMemberMoveParamVo = {
    departmentId?: number;
    userIdList?: number[];
  };

  type DepartmentMemberUpdateParamVo = {
    departmentIdList?: number[];
    nickname?: string;
    roleCode?: 'boss' | 'manager' | 'staff' | 'superadmin';
  };

  type DepartmentParamVo = {
    name?: string;
    parentId?: number;
  };

  type departmentTreeGetParams = {
    /** 是否包含成员，默认：false */
    includeMembers?: boolean;
  };

  type DepartmentTreeNode = {
    /** 下级部门 */
    childList?: any[];
    /** 部门ID */
    id?: number;
    /** 成员数 */
    memberCount?: number;
    /** 部门成员 */
    members?: MemberVo[];
    /** 部门名称 */
    name?: string;
    /** 上级部门ID */
    parentId?: number;
    /** 排序数 */
    sortNumber?: number;
  };

  type DevicePlatformVo = {
    /** 是否运行这个平台访问 */
    enabled?: boolean;
    /** 不为空时，表示允许的最低版本 */
    minVersion?: string;
    /** 平台 */
    platform?: 'android' | 'linux' | 'macos' | 'unknown' | 'web' | 'windows' | 'windows7';
  };

  type deviceRegisterPostParams = {
    /** sign */
    sign: string;
  };

  type FavoriteDetailVo = {
    collectTime?: string;
    collector?: number;
    id?: number;
    name?: string;
    resourceId?: number;
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    size?: number;
    target?: Record<string, any>;
    teamId?: number;
    url?: string;
  };

  type FavoriteDto = {
    collectTime?: string;
    collector?: number;
    id?: number;
    name?: string;
    resourceId?: number;
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    size?: number;
    teamId?: number;
    url?: string;
  };

  type FavoriteItem = {
    resourceId?: number;
    resourceName?: string;
    size?: number;
  };

  type favoritesByTypeByResourceTypeGetParams = {
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
  };

  type favoritesDeletePostParams = {
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
  };

  type favoritesDetailByTypeByResourceTypeGetParams = {
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type favoritesPageGetParams = {
    /** resourceType */
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    /** collectTimeFrom */
    collectTimeFrom?: string;
    /** collectTimeTo */
    collectTimeTo?: string;
    /** query */
    query?: string;
    /** sortField */
    sortField?: 'collectTime' | 'id' | 'name' | 'size';
    /** sortOrder */
    sortOrder?: 'asc' | 'desc';
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type FunctionConfig = {
    granted?: string[];
    revoked?: string[];
  };

  type FunctionMetaVo = {
    /** 权限信息 */
    function?: FunctionVo;
    /** 每个角色的配置信息 */
    roleConfigList?: FunctionRoleConfig[];
  };

  type FunctionRoleConfig = {
    /** 是否允许自定义 */
    customGrantEnabled?: boolean;
    /** 当前是否授权 */
    customGranted?: boolean;
    /** 出厂默认值 */
    defaultGranted?: boolean;
    /** 角色信息 */
    role?: 'boss' | 'manager' | 'staff' | 'superadmin';
  };

  type FunctionVo = {
    /** 描述 */
    description?: string;
    descriptionEn?: string;
    /** ID */
    id?:
      | 'DISK_SPACE_MANAGER'
      | 'DISK_TEAM_MANAGER'
      | 'DISK_USER_MANAGER'
      | 'EXPENSE_MANAGE'
      | 'EXPENSE_VIEW'
      | 'EXTENSION_LIST'
      | 'EXTENSION_MANAGER'
      | 'FINGERPRINT_CONFIG'
      | 'FINGERPRINT_LIST'
      | 'FINGERPRINT_MANAGER'
      | 'IP_CONFIG'
      | 'IP_LIST'
      | 'IP_MANAGE'
      | 'KOL_LIST'
      | 'KOL_MANAGE'
      | 'MOBILE_CONFIG'
      | 'MOBILE_IMPORT_DELETE'
      | 'OPERATE_LOG_GET_IP'
      | 'OPERATE_LOG_GET_LOGIN'
      | 'OPERATE_LOG_GET_SHOP'
      | 'OPERATE_LOG_GET_TEAM_MANAGE'
      | 'OPERATE_LOG_MANAGE_SHOP'
      | 'RPA_CARD_MANAGER'
      | 'RPA_CREATE_DELETE'
      | 'RPA_LIST'
      | 'RPA_OPEN_API'
      | 'RPA_PLAN'
      | 'RPA_RUN'
      | 'SHOP_AUTHORIZE'
      | 'SHOP_BIND_IP_MANAGE'
      | 'SHOP_CONFIG'
      | 'SHOP_FINGERPRINT_MANAGE'
      | 'SHOP_IMPORT_DELETE'
      | 'TEAM_AUDIT'
      | 'TEAM_CRITICAL_MANAGE'
      | 'TEAM_MANAGE'
      | 'TEAM_RESOURCE_MANAGE'
      | 'TEAM_VIEW_MEMBER'
      | 'TKSHOP_BUYER_MANAGER'
      | 'TKSHOP_CREATOR_ALLOCATE'
      | 'TKSHOP_CREATOR_MANAGER'
      | 'TKSHOP_GLOBAL_CREATOR_MANAGER'
      | 'TKSHOP_MANAGE'
      | 'TK_PACK_MANAGE'
      | 'TK_流程计划编排'
      | 'TK_达人管理'
      | 'TK_达人运营'
      | 'TK_达人邀约'
      | 'TK_运营辅助';
    /** 模块名称 */
    module?: string;
    /** 权限项名称 */
    name?: string;
    nameEn?: string;
    /** 排序号 */
    sortNumber?: number;
  };

  type GbsConfig = {
    password?: string;
    tenantId?: string;
    userId?: string;
  };

  type GiftCardPackDetailVo = {
    cardCount?: number;
    createTime?: string;
    giftCardType?: 'Credit' | 'RpaVoucher';
    id?: number;
    name?: string;
    packItems?: GiftCardPackItemDetailVo[];
    partnerId?: number;
    /** 剩余可用礼品卡数量 */
    remainCount?: number;
    remarks?: string;
    serialNumber?: string;
    /** 是否在有效期内（根据createTime & validDays计算出来的） */
    valid?: boolean;
    validDays?: number;
  };

  type GiftCardPackItemDetailVo = {
    activatedTeamId?: number;
    activatedTeamName?: string;
    activeTime?: string;
    amount?: number;
    cardNumber?: string;
    cardPackId?: number;
    cardPassword?: string;
    createTime?: string;
    disabled?: boolean;
    /** 当为rpa礼品卡时，表示该卡有几个 periodUnit 月/周 */
    duration?: number;
    expireDate?: string;
    giftCardType?: 'Credit' | 'RpaVoucher';
    goodsId?: number;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    orderItemId?: number;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    remarks?: string;
    /** 是否在有效期内（根据createTime & validDays计算出来的） */
    valid?: boolean;
  };

  type GrantDepartmentResourcesRequest = {
    /** 资源ID，不设置是清空授权 */
    resourceIds?: number[];
    /** 资源类型，必填 */
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
  };

  type GrantInfo = {
    /** 授权的部门 */
    departments?: DepartmentDto[];
    /** 授权给的用户 */
    users?: MemberVo[];
  };

  type GrantOpsStrategyItem = {
    mappingType?: 'department' | 'role' | 'user';
    targetId?: number;
  };

  type GrantOpsStrategyRequest = {
    cleanFirst?: boolean;
    items?: GrantOpsStrategyItem[];
  };

  type GrantShopsParamVo = {
    shopIds?: number[];
    teamId?: number;
    userId?: number;
  };

  type HyRuntimeDto = {
    cpu?: number;
    createTime?: string;
    deviceId?: string;
    hostname?: string;
    id?: number;
    identifier?: string;
    lastActiveTime?: string;
    lastRemoteIp?: string;
    mem?: number;
    osName?: string;
    remark?: string;
    remoteIp?: string;
    secretKey?: string;
    status?: 'DELETED' | 'READY' | 'STOPPED';
    version?: string;
  };

  type InviteInfoVo = {
    /** 是否已经在团队中 */
    alreadyInTeam?: boolean;
    /** 审核状态 */
    auditStatus?: 'APPROVED' | 'CANCEL' | 'NEW' | 'NOT_PASS';
    /** 团队创建者信息 */
    creator?: UserVo;
    /** 部门名称 */
    departmentName?: string;
    /** 团队ID */
    teamId?: number;
    /** 团队名称 */
    teamName?: string;
  };

  type InviteJoinTeamCheckResult = {
    link?: BizLinkDto;
    status?: 'AccountLogin' | 'AccountNotExists' | 'AccountNotLogin' | 'AccountNotMatch';
  };

  type InviteLinkVo = {
    /** 创建时间 */
    createTime?: string;
    /** 过期时间 */
    expireTime?: string;
    /** id */
    id?: number;
    /** 链接状态 */
    linkStatus?: string;
    /** token */
    token?: string;
  };

  type IpDataDto = {
    city?: string;
    country?: string;
    countryCode?: string;
    district?: string;
    id?: number;
    ip?: string;
    isp?: string;
    locationId?: number;
    province?: string;
    revisedLocationId?: number;
    revisedProvider?: string;
    tag?: string;
    updateTime?: string;
    zip?: string;
    zone?: string;
  };

  type JoinTeamParamVo = {
    account?: string;
    areaCode?: string;
    inviteCode?: string;
    teamId?: number;
    verifyCode?: string;
  };

  type JoinTeamResultVo = {
    joinTeamStatus?: 'ALREADY_IN_AUDIT' | 'ALREADY_IN_TEAM' | 'JOIN_SUCCESS' | 'WAIT_AUDIT';
    jwt?: string;
    jwtExpireDate?: string;
  };

  type JwtResultVo = {
    jwt?: string;
    jwtExpireTime?: string;
    jwtId?: string;
  };

  type LadderPriceRange = {
    /** 阶梯折扣百分比（=原价*amount/100） */
    amount?: number;
    /** 超过特定数量 */
    threshold?: number;
  };

  type linkInviteJoinTeamByTokenConfirmPostParams = {
    /** token */
    token: string;
    /** password */
    password?: string;
  };

  type linkInviteJoinTeamByTokenGetParams = {
    /** token */
    token: string;
  };

  type linkInviteJoinTeamPostParams = {
    departmentId?: number;
    emailList?: string[];
    roleCode?: 'boss' | 'manager' | 'staff' | 'superadmin';
  };

  type LoginDeviceDto = {
    appId?: string;
    appVersion?: string;
    clientIp?: string;
    clientLocation?: number;
    cpus?: number;
    createTime?: string;
    deviceId?: string;
    deviceType?: 'App' | 'Browser' | 'Extension' | 'HYRuntime' | 'RpaExecutor';
    domestic?: boolean;
    hostName?: string;
    id?: number;
    ipDataId?: number;
    lastActiveTime?: string;
    lastCity?: string;
    lastLogTime?: string;
    lastRemoteIp?: string;
    lastUserId?: number;
    logUrl?: string;
    mem?: number;
    online?: boolean;
    osName?: string;
    userAgent?: string;
    version?: string;
  };

  type LoginDeviceLocationVo = {
    appId?: string;
    appVersion?: string;
    clientIp?: string;
    clientLocation?: number;
    cpus?: number;
    createTime?: string;
    deviceId?: string;
    deviceType?: 'App' | 'Browser' | 'Extension' | 'HYRuntime' | 'RpaExecutor';
    domestic?: boolean;
    hostName?: string;
    id?: number;
    ipData?: IpDataDto;
    ipDataId?: number;
    lastActiveTime?: string;
    lastCity?: string;
    lastLogTime?: string;
    lastRemoteIp?: string;
    lastUserId?: number;
    logUrl?: string;
    mem?: number;
    online?: boolean;
    osName?: string;
    userAgent?: string;
    version?: string;
  };

  type LoginDeviceSessionVo = {
    current?: boolean;
    deviceId?: string;
    remoteIp?: string;
    sessionId?: string;
  };

  type LoginDeviceTransitDto = {
    deviceId?: number;
    id?: number;
    lastProbeTime?: string;
    probeCode?: number;
    probeError?: string;
    probeOut?: string;
    remoteIp?: string;
    status?: 'Available' | 'Pending' | 'Unavailable';
    testingTime?: number;
    transitId?: number;
  };

  type LoginDeviceTransitItem = {
    /** 探测结果代码 */
    probeCode?: number;
    /** 探测时的错误信息 */
    probeError?: string;
    /** 输出信息 */
    probeOut?: string;
    /** 探测到的remoteIp */
    remoteIp?: string;
    /** 状态 */
    status?: 'Available' | 'Pending' | 'Unavailable';
    /** 探测总耗时 */
    testingTime?: number;
    /** 接入点ID */
    transitId?: number;
  };

  type LoginDeviceTransitRequest = {
    transitItems?: LoginDeviceTransitItem[];
  };

  type LoginResultVo = {
    account?: string;
    jwt?: string;
    jwtExpireTime?: string;
    jwtId?: string;
    /** 是否需要验证码 */
    needCaptcha?: boolean;
    nickname?: string;
    userId?: number;
    userType?: 'NORMAL' | 'PARTNER' | 'SHADOW';
  };

  type memberAuthDevicePutParams = {
    /** userId */
    userId: number;
    /** deviceId */
    deviceId: number;
    /** allowed */
    allowed: boolean;
  };

  type memberByUserIdDevicesGetParams = {
    /** userId */
    userId: number;
    /** sortField */
    sortField?: 'hostName' | 'id' | 'lastActiveTime' | 'lastLoginTime';
    /** sortOrder */
    sortOrder?: 'asc' | 'desc';
  };

  type memberDeviceLogoutDeleteParams = {
    /** deviceId */
    deviceId: number;
  };

  type memberPageGetParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** sortFiled */
    sortFiled?: 'last_login_time' | 'online';
    /** sortOrder */
    sortOrder?: 'asc' | 'desc';
  };

  type MemberVo = {
    /** 账号 */
    account?: string;
    avatar?: string;
    createTime?: string;
    /** 邮箱 */
    email?: string;
    gender?: 'FEMALE' | 'MALE' | 'UNSPECIFIC';
    id?: number;
    nickname?: string;
    partnerId?: number;
    /** 手机 */
    phone?: string;
    residentCity?: string;
    roleCode?: 'boss' | 'manager' | 'staff' | 'superadmin';
    signature?: string;
    status?: 'ACTIVE' | 'BLOCK' | 'DELETED' | 'INACTIVE';
    teamNickname?: string;
    tenant?: number;
    userType?: 'NORMAL' | 'PARTNER' | 'SHADOW';
    weixin?: string;
  };

  type Mobile3ElementResult = {
    charged?: boolean;
    matched?: boolean;
    message?: string;
    rawResponse?: Record<string, any>;
    success?: boolean;
  };

  type Mobile3ElementVo = {
    areaCode?: string;
    certName?: string;
    certNo?: string;
    mobile?: string;
    /** 手机验证码 */
    verifyCode?: string;
  };

  type oauthByOauthTypeGetParams = {
    /** oauthType */
    oauthType: string;
    /** state */
    state: string;
  };

  type oauthCallbackByOauthTypeGetParams = {
    /** oauthType */
    oauthType: string;
  };

  type oauthCancelByUuidGetParams = {
    /** uuid */
    uuid: string;
  };

  type oauthGetOAuthUrlGetParams = {
    /** oauthType */
    oauthType:
      | 'ALIYUN'
      | 'DINGTALK'
      | 'FACEBOOK'
      | 'GOOGLE'
      | 'KAKAO'
      | 'QQ'
      | 'WEIBO'
      | 'WEIXIN'
      | 'WORK_WEIXIN'
      | 'WX_MINPROGRAM';
    /** action */
    action: 'bind' | 'login';
    /** rememberMe */
    rememberMe?: boolean;
  };

  type OAuthProcessInfo = {
    action?: 'bind' | 'login';
    done?: boolean;
    error?: string;
    jwt?: string;
    oauthType?:
      | 'ALIYUN'
      | 'DINGTALK'
      | 'FACEBOOK'
      | 'GOOGLE'
      | 'KAKAO'
      | 'QQ'
      | 'WEIBO'
      | 'WEIXIN'
      | 'WORK_WEIXIN'
      | 'WX_MINPROGRAM';
    sessionId?: string;
    success?: boolean;
    url?: string;
    userId?: number;
    uuid?: string;
  };

  type oauthQrcodeByUuidGetParams = {
    /** uuid */
    uuid: string;
  };

  type oauthRByOauthTypeGetParams = {
    /** oauthType */
    oauthType: string;
    /** state */
    state: string;
  };

  type oauthResultByUuidGetParams = {
    /** uuid */
    uuid: string;
  };

  type OpenAccountDto = {
    appId?: string;
    avatar?: string;
    city?: string;
    corpId?: number;
    createTime?: string;
    email?: string;
    extra?: string;
    gender?: 'FEMALE' | 'MALE' | 'UNSPECIFIC';
    home?: string;
    id?: number;
    locale?: string;
    location?: string;
    nickname?: string;
    oauthType?:
      | 'ALIYUN'
      | 'DINGTALK'
      | 'FACEBOOK'
      | 'GOOGLE'
      | 'KAKAO'
      | 'QQ'
      | 'WEIBO'
      | 'WEIXIN'
      | 'WORK_WEIXIN'
      | 'WX_MINPROGRAM';
    openId?: string;
    phone?: string;
    province?: string;
    subscribe?: number;
    thirdAppUserId?: string;
    unionId?: string;
    userId?: number;
    valid?: boolean;
  };

  type opsGrantByUserPutParams = {
    /** userId */
    userId: number;
    /** groupId */
    groupId?: number;
  };

  type opsGroupByIdDeleteParams = {
    /** id */
    id: number;
  };

  type opsGroupByIdGetParams = {
    /** id */
    id: number;
  };

  type opsGroupByIdGrantPutParams = {
    /** id */
    id: number;
  };

  type opsGroupByIdItemPutParams = {
    /** id */
    id: number;
  };

  type opsGroupByIdPutParams = {
    /** id */
    id: number;
    /** name */
    name: string;
    /** description */
    description?: string;
  };

  type opsGroupByIdStrategyPutParams = {
    /** id */
    id: number;
  };

  type opsGroupPostParams = {
    createTime?: string;
    creator?: number;
    description?: string;
    id?: number;
    name?: string;
    teamId?: number;
  };

  type opsGroupsGetParams = {
    pageNum?: number;
    pageSize?: number;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
  };

  type OpsStrategyGroupDto = {
    createTime?: string;
    creator?: number;
    description?: string;
    id?: number;
    name?: string;
    teamId?: number;
  };

  type OpsStrategyGroupVo = {
    createTime?: string;
    creator?: number;
    description?: string;
    grantedUserIds?: number[];
    id?: number;
    items?: OpsStrategyItemVo[];
    name?: string;
    teamId?: number;
  };

  type OpsStrategyItemVo = {
    config?: Record<string, any>;
    opsStrategy?: 'device_auth_type' | 'login_time';
  };

  type OpStatVo = {
    /** 分身数量 */
    accountCount?: number;
    /** 用户数 */
    userCount?: number;
  };

  type PageResultAuditDetailVo = {
    current?: number;
    list?: AuditDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultFavoriteDetailVo = {
    current?: number;
    list?: FavoriteDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultFavoriteDto = {
    current?: number;
    list?: FavoriteDto[];
    pageSize?: number;
    total?: number;
  };

  type PageResultOpsStrategyGroupVo = {
    current?: number;
    list?: OpsStrategyGroupVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTeamFavoriteDetailVo = {
    current?: number;
    list?: TeamFavoriteDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultTeamFavoriteDto = {
    current?: number;
    list?: TeamFavoriteDto[];
    pageSize?: number;
    total?: number;
  };

  type PageResultUserActivityDeviceVo = {
    current?: number;
    list?: UserActivityDeviceVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultUserDetailVo = {
    current?: number;
    list?: UserDetailVo[];
    pageSize?: number;
    total?: number;
  };

  type PageResultUserPromotedRecordVo = {
    current?: number;
    list?: UserPromotedRecordVo[];
    pageSize?: number;
    total?: number;
  };

  type PartnerDto = {
    bankAccount?: string;
    bankName?: string;
    bankNo?: string;
    contactName?: string;
    contactPhone?: string;
    createTime?: string;
    fullName?: string;
    id?: number;
    managerId?: number;
    oemSupport?: boolean;
    openapiSupport?: boolean;
    organizedTeamAccountQuota?: number;
    organizedTeamUserQuota?: number;
    password?: string;
    role?: 'Broker' | 'Organizer';
    shortName?: string;
    status?: 'ACTIVE' | 'BLOCK' | 'DELETED' | 'INACTIVE';
    teamId?: number;
    userId?: number;
  };

  type PasswordPolicyVo = {
    /** 是否自动更新密码并通知（设置有效期时） */
    autoReset?: boolean;
    /** 包含任意字符 */
    containsAny?: boolean;
    /** 最大长度 */
    maxSize?: number;
    /** 最小长度 */
    minSize?: number;
    /** 必须包含字母 */
    mustContainsLetter?: boolean;
    /** 必须包含数字 */
    mustContainsNumber?: boolean;
    /** 必须包含特殊字符 */
    mustContainsSpecial?: boolean;
    /** 不能与前若干次重复 */
    nonRepeatCount?: number;
    /** 密码有效期(-1为永久有效） */
    validSeconds?: number;
  };

  type pByCodeGetParams = {
    /** code */
    code: string;
  };

  type PreLoginVo = {
    /** 是否需要验证码 */
    needCaptcha?: boolean;
  };

  type ProductConfigVo = {
    /** 公钥test */
    publicKeyPEM?: string;
  };

  type promotionGainRewardPostParams = {
    /** id */
    id: number;
  };

  type promotionGetRewardObjectGetParams = {
    /** id */
    id: number;
  };

  type promotionPromotedUsersGetParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type RegisterAccountVo = {
    loginResultVo?: LoginResultVo;
    updateToken?: string;
    user?: UserVo;
  };

  type RegisterConfig = {
    needInvite?: boolean;
  };

  type RemoteLocationVo = {
    city?: string;
    location?: string;
  };

  type resourceCheckAuthorizedGetParams = {
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    /** resourceId */
    resourceId: number;
  };

  type resourceGrantByResourceTypeByResourceIdGetParams = {
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    /** resourceId */
    resourceId: number;
  };

  type ResourceGrantRequest = {
    cleanFirst?: boolean;
    departmentIdList?: number[];
    resourceIds?: number[];
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    teamId?: number;
    userIds?: number[];
  };

  type RoleVo = {
    code?: 'boss' | 'manager' | 'staff' | 'superadmin';
    description?: string;
    id?: number;
    name?: string;
    teamId?: number;
    type?: 'build_in' | 'custom';
  };

  type SetRoleFunctionRequest = {
    codes?: ('boss' | 'manager' | 'staff' | 'superadmin')[];
    grantedFunctions?: (
      | 'DISK_SPACE_MANAGER'
      | 'DISK_TEAM_MANAGER'
      | 'DISK_USER_MANAGER'
      | 'EXPENSE_MANAGE'
      | 'EXPENSE_VIEW'
      | 'EXTENSION_LIST'
      | 'EXTENSION_MANAGER'
      | 'FINGERPRINT_CONFIG'
      | 'FINGERPRINT_LIST'
      | 'FINGERPRINT_MANAGER'
      | 'IP_CONFIG'
      | 'IP_LIST'
      | 'IP_MANAGE'
      | 'KOL_LIST'
      | 'KOL_MANAGE'
      | 'MOBILE_CONFIG'
      | 'MOBILE_IMPORT_DELETE'
      | 'OPERATE_LOG_GET_IP'
      | 'OPERATE_LOG_GET_LOGIN'
      | 'OPERATE_LOG_GET_SHOP'
      | 'OPERATE_LOG_GET_TEAM_MANAGE'
      | 'OPERATE_LOG_MANAGE_SHOP'
      | 'RPA_CARD_MANAGER'
      | 'RPA_CREATE_DELETE'
      | 'RPA_LIST'
      | 'RPA_OPEN_API'
      | 'RPA_PLAN'
      | 'RPA_RUN'
      | 'SHOP_AUTHORIZE'
      | 'SHOP_BIND_IP_MANAGE'
      | 'SHOP_CONFIG'
      | 'SHOP_FINGERPRINT_MANAGE'
      | 'SHOP_IMPORT_DELETE'
      | 'TEAM_AUDIT'
      | 'TEAM_CRITICAL_MANAGE'
      | 'TEAM_MANAGE'
      | 'TEAM_RESOURCE_MANAGE'
      | 'TEAM_VIEW_MEMBER'
      | 'TKSHOP_BUYER_MANAGER'
      | 'TKSHOP_CREATOR_ALLOCATE'
      | 'TKSHOP_CREATOR_MANAGER'
      | 'TKSHOP_GLOBAL_CREATOR_MANAGER'
      | 'TKSHOP_MANAGE'
      | 'TK_PACK_MANAGE'
      | 'TK_流程计划编排'
      | 'TK_达人管理'
      | 'TK_达人运营'
      | 'TK_达人邀约'
      | 'TK_运营辅助'
    )[];
    revokedFunctions?: (
      | 'DISK_SPACE_MANAGER'
      | 'DISK_TEAM_MANAGER'
      | 'DISK_USER_MANAGER'
      | 'EXPENSE_MANAGE'
      | 'EXPENSE_VIEW'
      | 'EXTENSION_LIST'
      | 'EXTENSION_MANAGER'
      | 'FINGERPRINT_CONFIG'
      | 'FINGERPRINT_LIST'
      | 'FINGERPRINT_MANAGER'
      | 'IP_CONFIG'
      | 'IP_LIST'
      | 'IP_MANAGE'
      | 'KOL_LIST'
      | 'KOL_MANAGE'
      | 'MOBILE_CONFIG'
      | 'MOBILE_IMPORT_DELETE'
      | 'OPERATE_LOG_GET_IP'
      | 'OPERATE_LOG_GET_LOGIN'
      | 'OPERATE_LOG_GET_SHOP'
      | 'OPERATE_LOG_GET_TEAM_MANAGE'
      | 'OPERATE_LOG_MANAGE_SHOP'
      | 'RPA_CARD_MANAGER'
      | 'RPA_CREATE_DELETE'
      | 'RPA_LIST'
      | 'RPA_OPEN_API'
      | 'RPA_PLAN'
      | 'RPA_RUN'
      | 'SHOP_AUTHORIZE'
      | 'SHOP_BIND_IP_MANAGE'
      | 'SHOP_CONFIG'
      | 'SHOP_FINGERPRINT_MANAGE'
      | 'SHOP_IMPORT_DELETE'
      | 'TEAM_AUDIT'
      | 'TEAM_CRITICAL_MANAGE'
      | 'TEAM_MANAGE'
      | 'TEAM_RESOURCE_MANAGE'
      | 'TEAM_VIEW_MEMBER'
      | 'TKSHOP_BUYER_MANAGER'
      | 'TKSHOP_CREATOR_ALLOCATE'
      | 'TKSHOP_CREATOR_MANAGER'
      | 'TKSHOP_GLOBAL_CREATOR_MANAGER'
      | 'TKSHOP_MANAGE'
      | 'TK_PACK_MANAGE'
      | 'TK_流程计划编排'
      | 'TK_达人管理'
      | 'TK_达人运营'
      | 'TK_达人邀约'
      | 'TK_运营辅助'
    )[];
  };

  type ShopDto = {
    /** 账户账号 */
    account?: string;
    /** 是否允许用户自行安装插件 */
    allowExtension?: boolean;
    /** 会话是否允许监视 */
    allowMonitor?: boolean;
    /** 是否允许跳过敏感操作 */
    allowSkip?: boolean;
    /** 是否自动代填 */
    autoFill?: boolean;
    bookmarkBar?: string;
    coordinateId?: string;
    /** 创建时间 */
    createTime?: string;
    /** 创建者 */
    creatorId?: number;
    deleteTime?: string;
    deleting?: boolean;
    /** 描述 */
    description?: string;
    domainPolicy?: 'Blacklist' | 'None' | 'Whitelist';
    /** 是否独占访问 */
    exclusive?: boolean;
    extension?: 'both' | 'extension' | 'huayang';
    extraProp?: string;
    /** 绑定的指纹Id */
    fingerprintId?: number;
    /** 绑定的指纹模板Id（只有无状态账号才允许绑定指纹模板） */
    fingerprintTemplateId?: number;
    frontUrl?: string;
    googleTranslateSpeed?: boolean;
    homePage?: string;
    homePageSites?: string;
    /** id */
    id?: number;
    imageForbiddenSize?: number;
    intranetEnabled?: boolean;
    ipSwitchCheckInterval?: number;
    ipSwitchStrategy?: 'Abort' | 'Alert' | 'Off';
    lastAccessTime?: string;
    lastAccessUser?: number;
    lastSyncTime?: string;
    loginStatus?: 'Offline' | 'Online' | 'Unknown';
    /** 任务栏分身标记文字 */
    markCode?: string;
    /** 任务栏分身标记背景颜色 */
    markCodeBg?: number;
    monitorPerception?: boolean;
    /** 账户名称 */
    name?: string;
    nameBookmarkEnabled?: boolean;
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    /** 经营品类 */
    operatingCategory?:
      | '医药保健'
      | '图书文具'
      | '宠物用品'
      | '家具建材'
      | '家电电器'
      | '工业用品'
      | '户外运动'
      | '手机数码'
      | '手表眼镜'
      | '护肤美妆'
      | '母婴玩具'
      | '汽车配件'
      | '生活家居'
      | '电商其他'
      | '电脑平板'
      | '艺术珠宝'
      | '花园聚会'
      | '计生情趣'
      | '软件程序'
      | '鞋服箱包'
      | '音乐影视'
      | '食品生鲜'
      | '鲜花绿植';
    parentShopId?: number;
    /** 密码 */
    password?: string;
    /** 平台ID */
    platformId?: number;
    privateAddress?: string;
    privateTitle?: string;
    recordPerception?: boolean;
    recordPolicy?: 'Chosen' | 'Disabled' | 'Forced';
    requireIp?: boolean;
    resourcePolicy?: number;
    securityPolicyEnabled?: boolean;
    securityPolicyUpdateTime?: string;
    sharePolicyId?: string;
    shopDataSize?: number;
    shopNo?: string;
    stateless?: boolean;
    statelessChangeFp?: boolean;
    statelessSyncPolicy?: number;
    syncPolicy?: number;
    /** 团队ID */
    teamId?: number;
    trafficAlertStrategy?: 'Abort' | 'Off';
    trafficAlertThreshold?: number;
    trafficSaving?: boolean;
    type?: 'Global' | 'Local' | 'None';
    webSecurity?: boolean;
  };

  type SortedDepartmentVo = {
    /** 当前同级部门ID（按顺序） */
    departmentIds?: number[];
    /** 上级部门ID */
    parentId?: number;
  };

  type supportRequestPostParams = {
    /** timezone */
    timezone?: string;
    /** lang */
    lang?: string;
    /** clientTimestamp */
    clientTimestamp?: number;
  };

  type teamAuditByAuditIdGetParams = {
    /** auditId */
    auditId: number;
  };

  type teamAuditByAuditIdPassPutParams = {
    /** auditId */
    auditId: number;
    /** 团队昵称 */
    teamNickname?: string;
    /** 默认角色code */
    roleCode?: 'boss' | 'manager' | 'staff' | 'superadmin';
    /** 部门ID，不设置时放在根部门（用逗号连接） */
    departmentIds?: string;
  };

  type teamAuditByAuditIdRejectPutParams = {
    /** auditId */
    auditId: number;
    /** reason */
    reason?: string;
  };

  type teamAuditsGetParams = {
    /** teamId */
    teamId: number;
    /** auditType */
    auditType?:
      | 'JOIN_TEAM'
      | 'NEW_DEVICE'
      | 'RECEIVE_SHOP'
      | 'RECEIVE_SHOPS'
      | 'SHARED_MOBILES'
      | 'SHARED_SHOP'
      | 'SHARED_SHOPS'
      | 'TRANSFER_SHOP';
    /** auditStatus */
    auditStatus?: 'APPROVED' | 'CANCEL' | 'NEW' | 'NOT_PASS';
    /** 是否已完成 */
    done?: boolean;
    /** 昵称或手机号搜索关键字 */
    word?: string;
    /** pageNum */
    pageNum: number;
    /** pageSize */
    pageSize: number;
  };

  type teamAvatarByFileGetParams = {
    /** file */
    file: string;
  };

  type teamByTeamIdAvatarGetParams = {
    /** teamId */
    teamId: number;
  };

  type teamByTeamIdAvatarPostParams = {
    /** teamId */
    teamId: number;
  };

  type teamByTeamIdConfigPutParams = {
    /** teamId */
    teamId: number;
    /** inviteEnable */
    inviteEnable: boolean;
    /** inviteAuditEnable */
    inviteAuditEnable: boolean;
  };

  type teamByTeamIdContactByPhoneByPhoneGetParams = {
    /** teamId */
    teamId: number;
    /** areaCode */
    areaCode?: string;
    /** phone */
    phone: string;
  };

  type teamByTeamIdDissolvePutParams = {
    /** teamId */
    teamId: number;
  };

  type teamByTeamIdFavoritePutParams = {
    /** teamId */
    teamId: number;
    /** favorite */
    favorite: boolean;
  };

  type teamByTeamIdGetParams = {
    /** 团队ID */
    teamId: number;
  };

  type teamByTeamIdInviteInfoGetParams = {
    /** teamId */
    teamId: number;
  };

  type teamByTeamIdPutParams = {
    /** teamId */
    teamId: number;
    /** teamName */
    teamName: string;
  };

  type teamByTeamIdRefreshInviteCodeGetParams = {
    /** teamId */
    teamId: number;
  };

  type teamByTeamIdStatGetParams = {
    /** teamId */
    teamId: number;
  };

  type teamByTeamIdTransferPutParams = {
    /** teamId */
    teamId: number;
    /** toUserId */
    toUserId: number;
  };

  type teamByTeamIdVerifiedInfoGetParams = {
    /** 团队ID */
    teamId: number;
  };

  type TeamConfigDto = {
    alreadyInTeam?: boolean;
    deleteShopFp?: boolean;
    id?: number;
    inviteAuditEnable?: boolean;
    inviteCode?: string;
    inviteEnable?: boolean;
    recordKeepDays?: number;
    rpaKeepDays?: number;
    storageAlertCredit?: number;
    teamId?: number;
  };

  type TeamDetailVo = {
    avatar?: string;
    consoleDesc?: string;
    createTime?: string;
    creatorId?: number;
    creatorName?: string;
    deleteTime?: string;
    domesticCloudEnabled?: boolean;
    fingerprintCount?: number;
    id?: number;
    invalidTime?: string;
    inviteCode?: number;
    ipCount?: number;
    memberCount?: number;
    name?: string;
    /** 订单数量 */
    orderCount?: number;
    overseaCloudEnabled?: boolean;
    paid?: boolean;
    partner?: PartnerDto;
    /** 伙伴管理者编辑的描述 */
    partnerDesc?: string;
    partnerId?: number;
    payTime?: string;
    repurchaseTime?: string;
    repurchased?: boolean;
    rpaTaskCount?: number;
    shopCount?: number;
    status?: 'Blocked' | 'Deleted' | 'Pending' | 'Ready';
    teamBalance?: number;
    /** 剩余花瓣 */
    teamCredit?: number;
    teamType?: 'crs' | 'gh' | 'krShop' | 'normal' | 'partner' | 'plugin' | 'tk' | 'tkshop';
    tenantId?: number;
    testing?: boolean;
    tkTeamRelation?: TkTeamRelationDto;
    validateTime?: string;
    validated?: boolean;
    verified?: boolean;
  };

  type TeamDeviceConfig = {
    /** 版本限制 */
    versionControls?: DevicePlatformVo[];
  };

  type TeamDto = {
    avatar?: string;
    createTime?: string;
    creatorId?: number;
    deleteTime?: string;
    domesticCloudEnabled?: boolean;
    id?: number;
    invalidTime?: string;
    inviteCode?: number;
    name?: string;
    overseaCloudEnabled?: boolean;
    paid?: boolean;
    partnerId?: number;
    payTime?: string;
    repurchaseTime?: string;
    repurchased?: boolean;
    status?: 'Blocked' | 'Deleted' | 'Pending' | 'Ready';
    teamType?: 'crs' | 'gh' | 'krShop' | 'normal' | 'partner' | 'plugin' | 'tk' | 'tkshop';
    tenantId?: number;
    testing?: boolean;
    validateTime?: string;
    validated?: boolean;
    verified?: boolean;
  };

  type TeamFavoriteDetailVo = {
    collectTime?: string;
    collector?: number;
    id?: number;
    name?: string;
    resourceId?: number;
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    size?: number;
    target?: Record<string, any>;
    teamId?: number;
    url?: string;
  };

  type TeamFavoriteDto = {
    collectTime?: string;
    collector?: number;
    id?: number;
    name?: string;
    resourceId?: number;
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    size?: number;
    teamId?: number;
    url?: string;
  };

  type teamFavoritesByTypeByResourceTypeGetParams = {
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
  };

  type teamFavoritesCheckByTypeByResourceTypePostParams = {
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
  };

  type teamFavoritesDeletePostParams = {
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
  };

  type teamFavoritesDetailByTypeByResourceTypeGetParams = {
    /** resourceType */
    resourceType:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type teamFavoritesPageGetParams = {
    /** resourceType */
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    /** collectTimeFrom */
    collectTimeFrom?: string;
    /** collectTimeTo */
    collectTimeTo?: string;
    /** query */
    query?: string;
    /** sortField */
    sortField?: 'collectTime' | 'id' | 'name' | 'size';
    /** sortOrder */
    sortOrder?: 'asc' | 'desc';
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type TeamFunctionConfigVo = {
    deducting?: boolean;
    enabled?: boolean;
    functionDesc?: string;
    functionName?:
      | 'IpQuotaTraffic'
      | 'IpTransitTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteCloud'
      | 'RpaExecuteLocal'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendSms';
    officialPrice?: number;
    price?: number;
    subKey?: string;
    subKeyInfo?: Record<string, any>;
    subKeyLabel?: string;
  };

  type teamInviteInfoByInviteCodeGetParams = {
    /** inviteCode */
    inviteCode: string;
  };

  type teamInviteLinkGetParams = {
    /** 团队ID */
    teamId: number;
  };

  type TeamInviteVo = {
    /** ID */
    id?: number;
    /** 是否开启团队邀请审核 */
    inviteAuditEnable?: boolean;
    /** 个人邀请码 */
    inviteCode?: string;
    /** 是否开启团队邀请 */
    inviteEnable?: boolean;
    /** 团队ID */
    teamId?: number;
  };

  type teamJoinListGetParams = {
    /** status */
    status?: 'Blocked' | 'Deleted' | 'Pending' | 'Ready';
    /** teamType */
    teamType?: 'crs' | 'gh' | 'krShop' | 'normal' | 'partner' | 'plugin' | 'tk' | 'tkshop';
  };

  type teamLoginTeamByTeamIdPutParams = {
    /** teamId */
    teamId: number;
  };

  type teamMemberConfigGetParams = {
    /** key */
    key: string;
  };

  type teamPostParams = {
    /** 团队名称 */
    teamName: string;
    /** teamType */
    teamType?: 'crs' | 'gh' | 'krShop' | 'normal' | 'partner' | 'plugin' | 'tk' | 'tkshop';
  };

  type teamQuotaDetailGetParams = {
    /** quotaName */
    quotaName:
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota';
  };

  type TeamQuotaDetailVo = {
    /** 团队免费配额，null表示使用官方默认，-1表示无限 */
    freeQuota?: number;
    /** 阶梯价格 */
    ladderPrices?: LadderPriceRange[];
    /** 需要付费 */
    needPay?: boolean;
    /** 官方单价（花瓣/天），0表示免费使用 */
    price?: number;
    /** （最大）配额(-1表示不限制） */
    quota?: number;
    /** 配额描述 */
    quotaDesc?: string;
    /** 配额名称 */
    quotaName?:
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota';
    /** 已用配额 */
    used?: number;
  };

  type TeamQuotaVo = {
    /** 团队免费配额，null表示使用官方默认，-1表示无限 */
    freeQuota?: number;
    /** 阶梯价格 */
    ladderPrices?: LadderPriceRange[];
    /** 官方单价（花瓣/天），0表示免费使用 */
    price?: number;
    /** （最大）配额(-1表示不限制） */
    quota?: number;
    /** 配额描述 */
    quotaDesc?: string;
    /** 配额名称 */
    quotaName?:
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota';
  };

  type teamRemainQuotaGetParams = {
    /** quotaName */
    quotaName:
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota';
  };

  type TeamRoleConfig = {
    roleFunctionMap?: Record<string, any>;
  };

  type teamRoleFunctionPutParams = {
    /** code */
    code: 'boss' | 'manager' | 'staff' | 'superadmin';
    /** customGranted */
    customGranted: boolean;
    /** function */
    function:
      | 'DISK_SPACE_MANAGER'
      | 'DISK_TEAM_MANAGER'
      | 'DISK_USER_MANAGER'
      | 'EXPENSE_MANAGE'
      | 'EXPENSE_VIEW'
      | 'EXTENSION_LIST'
      | 'EXTENSION_MANAGER'
      | 'FINGERPRINT_CONFIG'
      | 'FINGERPRINT_LIST'
      | 'FINGERPRINT_MANAGER'
      | 'IP_CONFIG'
      | 'IP_LIST'
      | 'IP_MANAGE'
      | 'KOL_LIST'
      | 'KOL_MANAGE'
      | 'MOBILE_CONFIG'
      | 'MOBILE_IMPORT_DELETE'
      | 'OPERATE_LOG_GET_IP'
      | 'OPERATE_LOG_GET_LOGIN'
      | 'OPERATE_LOG_GET_SHOP'
      | 'OPERATE_LOG_GET_TEAM_MANAGE'
      | 'OPERATE_LOG_MANAGE_SHOP'
      | 'RPA_CARD_MANAGER'
      | 'RPA_CREATE_DELETE'
      | 'RPA_LIST'
      | 'RPA_OPEN_API'
      | 'RPA_PLAN'
      | 'RPA_RUN'
      | 'SHOP_AUTHORIZE'
      | 'SHOP_BIND_IP_MANAGE'
      | 'SHOP_CONFIG'
      | 'SHOP_FINGERPRINT_MANAGE'
      | 'SHOP_IMPORT_DELETE'
      | 'TEAM_AUDIT'
      | 'TEAM_CRITICAL_MANAGE'
      | 'TEAM_MANAGE'
      | 'TEAM_RESOURCE_MANAGE'
      | 'TEAM_VIEW_MEMBER'
      | 'TKSHOP_BUYER_MANAGER'
      | 'TKSHOP_CREATOR_ALLOCATE'
      | 'TKSHOP_CREATOR_MANAGER'
      | 'TKSHOP_GLOBAL_CREATOR_MANAGER'
      | 'TKSHOP_MANAGE'
      | 'TK_PACK_MANAGE'
      | 'TK_流程计划编排'
      | 'TK_达人管理'
      | 'TK_达人运营'
      | 'TK_达人邀约'
      | 'TK_运营辅助';
  };

  type teamSettingsBrowserDebugPutParams = {
    /** browserDebug */
    browserDebug: string;
  };

  type teamSettingsFingerUpgradePutParams = {
    /** fingerUpgrade */
    fingerUpgrade: string;
  };

  type teamSettingsLanguagePutParams = {
    /** language */
    language: string;
  };

  type teamSettingsSessionRequireIpPutParams = {
    /** sessionRequireIp */
    sessionRequireIp: boolean;
  };

  type teamSettingsShopCodeStatusPutParams = {
    /** codeStatus */
    codeStatus: string;
  };

  type teamSettingsTeamConfigDeleteShopFpByDeleteShopFpPutParams = {
    /** deleteShopFp */
    deleteShopFp: boolean;
  };

  type TeamStatInfo = {
    creatorName?: string;
    ipCount?: number;
    memberCount?: number;
    /** 订单数量 */
    orderCount?: number;
    rpaTaskCount?: number;
    shopCount?: number;
    teamBalance?: number;
    /** 剩余花瓣 */
    teamCredit?: number;
  };

  type TeamSystemConfig = {
    /** 自有IP允许使用接入点 */
    enableTransit?: boolean;
    /** 通过插件绑定店铺的权限配置:自动授权 */
    extensionShopAutoGrant?: boolean;
    /** 发送邮件等时是否启用内容健康扫描 */
    greenScan?: boolean;
    /** 尽量使用EIP的方式来创建IP */
    ipCreateFromEip?: boolean;
    /** 导入动态IP命名规范 */
    ipNameSpecDynamic?: string;
    /** 导入平台IP命名规范 */
    ipNameSpecPlatform?: string;
    /** 导入静态IP命名规范 */
    ipNameSpecStatic?: string;
    /** IP纯净度检查 */
    ipPurenessCheck?: boolean;
    /** 创建IP时，遇到重复IP，重试次数 */
    ipUniqueCreateRetry?: number;
    /** 平台IP唯一性检查月数，=0不检查 */
    ipUniqueMonth?: number;
    /** 本地打开会话监听的地址 */
    sessionProxyHost?: string;
    /** TKshop隐式运行 */
    tkShopHeadless?: boolean;
  };

  type teamUserByUserIdShopTransferPutParams = {
    /** userId */
    userId: number;
  };

  type teamUserByUserIdStatusPutParams = {
    /** userId */
    userId: number;
    /** status */
    status: 'ACTIVE' | 'BLOCK' | 'DELETED' | 'INACTIVE';
  };

  type teamVerifyAlipayCallbackGetParams = {
    /** app_id */
    app_id: string;
    /** cert_verify_id */
    cert_verify_id: string;
    /** auth_code */
    auth_code: string;
  };

  type teamVerifyAlipayResultByVerifyIdGetParams = {
    /** verifyId */
    verifyId: string;
  };

  type TeamVerifyDto = {
    certName?: string;
    certNo?: string;
    certType?: 'BusinessLicense' | 'IdCard';
    charged?: boolean;
    enterpriseName?: string;
    enterpriseNo?: string;
    extraInfo?: string;
    id?: number;
    invalidTime?: string;
    legalIdCard?: string;
    legalName?: string;
    mobile?: string;
    operator?: number;
    pass?: boolean;
    remark?: string;
    teamId?: number;
    valid?: boolean;
    verifyProvider?: string;
    verifyTime?: string;
  };

  type TeamWithRoleVo = {
    avatar?: string;
    createTime?: string;
    creatorId?: number;
    deleteTime?: string;
    domesticCloudEnabled?: boolean;
    favorite?: boolean;
    id?: number;
    invalidTime?: string;
    inviteCode?: number;
    joinTime?: string;
    name?: string;
    overseaCloudEnabled?: boolean;
    paid?: boolean;
    partnerId?: number;
    payTime?: string;
    repurchaseTime?: string;
    repurchased?: boolean;
    roleCode?: string;
    status?: 'Blocked' | 'Deleted' | 'Pending' | 'Ready';
    teamType?: 'crs' | 'gh' | 'krShop' | 'normal' | 'partner' | 'plugin' | 'tk' | 'tkshop';
    /** 用户在团队内的状态 */
    teamUserStatus?: 'ACTIVE' | 'BLOCK' | 'DELETED' | 'INACTIVE';
    tenantId?: number;
    testing?: boolean;
    validateTime?: string;
    validated?: boolean;
    verified?: boolean;
  };

  type TkTeamRelationDto = {
    createTime?: string;
    dataTeamId?: number;
    id?: number;
    operationTeamId?: number;
    tkRole?: 'TkClient' | 'TkData' | 'TkOperation';
  };

  type UpdateDepartmentOrderParamVo = {
    /** 被移动顺序的上级部门列表 */
    sortedDepartments?: SortedDepartmentVo[];
  };

  type UpdateMemberConfigRequest = {
    /** 配置信息，为null时，删除配置 */
    config?: Record<string, any>;
    /** 配置Key */
    key?: string;
  };

  type UpdateOpsStrategyItemRequest = {
    items?: OpsStrategyItemVo[];
  };

  type UserActivityDeviceVo = {
    appRemainLogin?: boolean;
    authorizedDeviceIds?: number[];
    avatar?: string;
    continuousLogin?: number;
    devices?: LoginDeviceLocationVo[];
    domestic?: boolean;
    id?: number;
    lastCity?: string;
    lastLoginTime?: string;
    lastLogoutTime?: string;
    lastRemoteIps?: string;
    loginCount?: number;
    nickname?: string;
    online?: boolean;
    onlineTotalTime?: number;
    roleCode?: 'boss' | 'manager' | 'staff' | 'superadmin';
    strategyGroup?: OpsStrategyGroupVo;
    vitality?: number;
  };

  type UserActivityDto = {
    appRemainLogin?: boolean;
    continuousLogin?: number;
    domestic?: boolean;
    id?: number;
    lastCity?: string;
    lastLoginTime?: string;
    lastLogoutTime?: string;
    lastRemoteIps?: string;
    loginCount?: number;
    online?: boolean;
    onlineTotalTime?: number;
    vitality?: number;
  };

  type userByTeamByTeamIdUsersGetParams = {
    /** teamId */
    teamId: number;
    /** pageNum */
    pageNum: number;
    /** pageSize */
    pageSize: number;
    /** departmentId */
    departmentId?: number;
    /** searchWord */
    searchWord?: string;
    /** 是否排除当前用户 */
    excludeMyself?: boolean;
  };

  type userByUserIdFunctionsGetParams = {
    /** userId */
    userId: number;
  };

  type userByUserIdFunctionsPutParams = {
    /** userId */
    userId: number;
    /** 权限code列表，以逗号分隔 */
    functions: string;
  };

  type userByUserIdFunctionsV2PutParams = {
    /** userId */
    userId: number;
    /** 权限code列表，以逗号分隔 */
    grantedFunctions: string;
    /** 权限code列表，以逗号分隔 */
    revokedFunctions: string;
  };

  type userByUserIdGetParams = {
    /** userId */
    userId: number;
  };

  type userByUserIdGrantedFunctionsGetParams = {
    /** userId */
    userId: number;
  };

  type userByUserIdGrantListGetParams = {
    /** userId */
    userId: number;
  };

  type userByUserIdGrantPutParams = {
    /** userId */
    userId: number;
    /** function */
    function:
      | 'DISK_SPACE_MANAGER'
      | 'DISK_TEAM_MANAGER'
      | 'DISK_USER_MANAGER'
      | 'EXPENSE_MANAGE'
      | 'EXPENSE_VIEW'
      | 'EXTENSION_LIST'
      | 'EXTENSION_MANAGER'
      | 'FINGERPRINT_CONFIG'
      | 'FINGERPRINT_LIST'
      | 'FINGERPRINT_MANAGER'
      | 'IP_CONFIG'
      | 'IP_LIST'
      | 'IP_MANAGE'
      | 'KOL_LIST'
      | 'KOL_MANAGE'
      | 'MOBILE_CONFIG'
      | 'MOBILE_IMPORT_DELETE'
      | 'OPERATE_LOG_GET_IP'
      | 'OPERATE_LOG_GET_LOGIN'
      | 'OPERATE_LOG_GET_SHOP'
      | 'OPERATE_LOG_GET_TEAM_MANAGE'
      | 'OPERATE_LOG_MANAGE_SHOP'
      | 'RPA_CARD_MANAGER'
      | 'RPA_CREATE_DELETE'
      | 'RPA_LIST'
      | 'RPA_OPEN_API'
      | 'RPA_PLAN'
      | 'RPA_RUN'
      | 'SHOP_AUTHORIZE'
      | 'SHOP_BIND_IP_MANAGE'
      | 'SHOP_CONFIG'
      | 'SHOP_FINGERPRINT_MANAGE'
      | 'SHOP_IMPORT_DELETE'
      | 'TEAM_AUDIT'
      | 'TEAM_CRITICAL_MANAGE'
      | 'TEAM_MANAGE'
      | 'TEAM_RESOURCE_MANAGE'
      | 'TEAM_VIEW_MEMBER'
      | 'TKSHOP_BUYER_MANAGER'
      | 'TKSHOP_CREATOR_ALLOCATE'
      | 'TKSHOP_CREATOR_MANAGER'
      | 'TKSHOP_GLOBAL_CREATOR_MANAGER'
      | 'TKSHOP_MANAGE'
      | 'TK_PACK_MANAGE'
      | 'TK_流程计划编排'
      | 'TK_达人管理'
      | 'TK_达人运营'
      | 'TK_达人邀约'
      | 'TK_运营辅助';
    /** granted */
    granted?: boolean;
  };

  type userByUserIdKickOutPutParams = {
    /** userId */
    userId: number;
  };

  type userByUserIdRoleGetParams = {
    /** userId */
    userId: number;
  };

  type userByUserIdRolePutParams = {
    /** userId */
    userId: number;
    /** role */
    role: 'boss' | 'manager' | 'staff' | 'superadmin';
  };

  type userByUserIdTeamNicknameGetParams = {
    /** userId */
    userId: number;
  };

  type userByUserIdTeamNicknamePutParams = {
    /** userId */
    userId: number;
    /** nickname */
    nickname: string;
  };

  type UserDepartmentVo = {
    grantShopList?: ShopDto[];
    grantShopNumber?: number;
    id?: number;
    nickname?: string;
    role?: string;
  };

  type UserDetailVo = {
    /** 账号 */
    account?: string;
    avatar?: string;
    createTime?: string;
    /** 邮箱 */
    email?: string;
    gender?: 'FEMALE' | 'MALE' | 'UNSPECIFIC';
    id?: number;
    nickname?: string;
    partnerId?: number;
    /** 手机 */
    phone?: string;
    residentCity?: string;
    resourceGranted?: boolean;
    /** 所属角色 */
    role?: RoleVo;
    /** 授权的账户数量 */
    shopCount?: number;
    signature?: string;
    status?: 'ACTIVE' | 'BLOCK' | 'DELETED' | 'INACTIVE';
    /** 用户在团队的状态 */
    teamUserStatus?: 'ACTIVE' | 'BLOCK' | 'DELETED' | 'INACTIVE';
    tenant?: number;
    userType?: 'NORMAL' | 'PARTNER' | 'SHADOW';
    /** 是否可以查看详情 */
    viewDetail?: boolean;
    weixin?: string;
  };

  type UserFunctionDto = {
    functionCode?:
      | 'DISK_SPACE_MANAGER'
      | 'DISK_TEAM_MANAGER'
      | 'DISK_USER_MANAGER'
      | 'EXPENSE_MANAGE'
      | 'EXPENSE_VIEW'
      | 'EXTENSION_LIST'
      | 'EXTENSION_MANAGER'
      | 'FINGERPRINT_CONFIG'
      | 'FINGERPRINT_LIST'
      | 'FINGERPRINT_MANAGER'
      | 'IP_CONFIG'
      | 'IP_LIST'
      | 'IP_MANAGE'
      | 'KOL_LIST'
      | 'KOL_MANAGE'
      | 'MOBILE_CONFIG'
      | 'MOBILE_IMPORT_DELETE'
      | 'OPERATE_LOG_GET_IP'
      | 'OPERATE_LOG_GET_LOGIN'
      | 'OPERATE_LOG_GET_SHOP'
      | 'OPERATE_LOG_GET_TEAM_MANAGE'
      | 'OPERATE_LOG_MANAGE_SHOP'
      | 'RPA_CARD_MANAGER'
      | 'RPA_CREATE_DELETE'
      | 'RPA_LIST'
      | 'RPA_OPEN_API'
      | 'RPA_PLAN'
      | 'RPA_RUN'
      | 'SHOP_AUTHORIZE'
      | 'SHOP_BIND_IP_MANAGE'
      | 'SHOP_CONFIG'
      | 'SHOP_FINGERPRINT_MANAGE'
      | 'SHOP_IMPORT_DELETE'
      | 'TEAM_AUDIT'
      | 'TEAM_CRITICAL_MANAGE'
      | 'TEAM_MANAGE'
      | 'TEAM_RESOURCE_MANAGE'
      | 'TEAM_VIEW_MEMBER'
      | 'TKSHOP_BUYER_MANAGER'
      | 'TKSHOP_CREATOR_ALLOCATE'
      | 'TKSHOP_CREATOR_MANAGER'
      | 'TKSHOP_GLOBAL_CREATOR_MANAGER'
      | 'TKSHOP_MANAGE'
      | 'TK_PACK_MANAGE'
      | 'TK_流程计划编排'
      | 'TK_达人管理'
      | 'TK_达人运营'
      | 'TK_达人邀约'
      | 'TK_运营辅助';
    granted?: boolean;
    id?: number;
    teamId?: number;
    userId?: number;
  };

  type UserFunctionMetaVo = {
    /** 权限信息 */
    function?: FunctionVo;
    /** 当前是否已经授权 */
    granted?: boolean;
    /** 每个角色的配置信息 */
    roleConfigList?: FunctionRoleConfig[];
  };

  type UserLoginDeviceVo = {
    appId?: string;
    appVersion?: string;
    clientIp?: string;
    clientLocation?: number;
    cpus?: number;
    createTime?: string;
    currentDevice?: boolean;
    deviceId?: string;
    deviceType?: 'App' | 'Browser' | 'Extension' | 'HYRuntime' | 'RpaExecutor';
    domestic?: boolean;
    hostName?: string;
    id?: number;
    ipData?: IpDataDto;
    ipDataId?: number;
    lastActiveTime?: string;
    lastCity?: string;
    lastLogTime?: string;
    lastLoginTime?: string;
    lastRemoteIp?: string;
    lastUserId?: number;
    logUrl?: string;
    mem?: number;
    online?: boolean;
    osName?: string;
    scope?: 'Console' | 'Ipp' | 'Openapi' | 'Partner' | 'Portal';
    userAgent?: string;
    userId?: number;
    version?: string;
  };

  type userMemberGrantListGetParams = {
    /** pageNum */
    pageNum: number;
    /** pageSize */
    pageSize: number;
    /** departmentId */
    departmentId?: number;
    /** searchWord */
    searchWord?: string;
    /** resourceType */
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    /** resourceId */
    resourceId?: number;
    /** 是否排除当前用户 */
    excludeMyself?: boolean;
  };

  type UserPromoInfo = {
    code?: string;
    id?: number;
    phoneRewardId?: number;
    promoUserId?: number;
    /** 总奖励数量 */
    totalRewardAmount?: number;
    url?: string;
    wechatRewardId?: number;
  };

  type UserPromotedRecordVo = {
    createTime?: string;
    id?: number;
    nickname?: string;
    promoUserId?: number;
    promotedUserId?: number;
    rewardAmount?: number;
    rewardId?: number;
    rewardTime?: string;
  };

  type userSettingsLanguagePutParams = {
    /** language */
    language: string;
  };

  type UserShopTransferParamVo = {
    /** 是否复制权限授权记录 */
    copyGrant?: boolean;
    shopIdList?: number[];
    toUserId?: number;
  };

  type userTeamNicknamePutParams = {
    /** teamNickname */
    teamNickname: string;
  };

  type UserTeamVo = {
    /** 账号 */
    account?: string;
    avatar?: string;
    createTime?: string;
    /** 部门 */
    departmentList?: DepartmentDto[];
    /** 邮箱 */
    email?: string;
    gender?: 'FEMALE' | 'MALE' | 'UNSPECIFIC';
    id?: number;
    /** 加入时间 */
    joinTime?: string;
    nickname?: string;
    /** 绑定的开放账户 */
    openAccounts?: OpenAccountDto[];
    partnerId?: number;
    /** 手机 */
    phone?: string;
    residentCity?: string;
    /** 角色 */
    role?: 'boss' | 'manager' | 'staff' | 'superadmin';
    signature?: string;
    status?: 'ACTIVE' | 'BLOCK' | 'DELETED' | 'INACTIVE';
    tenant?: number;
    userType?: 'NORMAL' | 'PARTNER' | 'SHADOW';
    weixin?: string;
  };

  type UserVo = {
    /** 账号 */
    account?: string;
    avatar?: string;
    createTime?: string;
    /** 邮箱 */
    email?: string;
    gender?: 'FEMALE' | 'MALE' | 'UNSPECIFIC';
    id?: number;
    nickname?: string;
    partnerId?: number;
    /** 手机 */
    phone?: string;
    residentCity?: string;
    signature?: string;
    status?: 'ACTIVE' | 'BLOCK' | 'DELETED' | 'INACTIVE';
    tenant?: number;
    userType?: 'NORMAL' | 'PARTNER' | 'SHADOW';
    weixin?: string;
  };

  type WebResult = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultAlipayPreconsultResult = {
    code?: number;
    data?: AlipayPreconsultResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultAlipayVerifyResultVo = {
    code?: number;
    data?: AlipayVerifyResultVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultAuditDetailVo = {
    code?: number;
    data?: AuditDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultAuditResultVo = {
    code?: number;
    data?: AuditResultVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultboolean = {
    code?: number;
    data?: boolean;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCheckAndJoinDemoTeamResult = {
    code?: number;
    data?: CheckAndJoinDemoTeamResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultConfirmInviteJoinTeamResult = {
    code?: number;
    data?: ConfirmInviteJoinTeamResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultDepartmentDto = {
    code?: number;
    data?: DepartmentDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGbsConfig = {
    code?: number;
    data?: GbsConfig;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGiftCardPackDetailVo = {
    code?: number;
    data?: GiftCardPackDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGrantInfo = {
    code?: number;
    data?: GrantInfo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultHyRuntimeDto = {
    code?: number;
    data?: HyRuntimeDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultint = {
    code?: number;
    data?: number;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultInviteInfoVo = {
    code?: number;
    data?: InviteInfoVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultInviteJoinTeamCheckResult = {
    code?: number;
    data?: InviteJoinTeamCheckResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultInviteLinkVo = {
    code?: number;
    data?: InviteLinkVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultJoinTeamResultVo = {
    code?: number;
    data?: JoinTeamResultVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultJwtResultVo = {
    code?: number;
    data?: JwtResultVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListDepartmentTreeNode = {
    code?: number;
    data?: DepartmentTreeNode[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListFavoriteDto = {
    code?: number;
    data?: FavoriteDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListFunctionMetaVo = {
    code?: number;
    data?: FunctionMetaVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListFunctionVo = {
    code?: number;
    data?: FunctionVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListLoginDeviceDto = {
    code?: number;
    data?: LoginDeviceDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListLoginDeviceSessionVo = {
    code?: number;
    data?: LoginDeviceSessionVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListLoginDeviceTransitDto = {
    code?: number;
    data?: LoginDeviceTransitDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListlong = {
    code?: number;
    data?: number[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTeamFavoriteDto = {
    code?: number;
    data?: TeamFavoriteDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTeamFunctionConfigVo = {
    code?: number;
    data?: TeamFunctionConfigVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTeamQuotaVo = {
    code?: number;
    data?: TeamQuotaVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTeamWithRoleVo = {
    code?: number;
    data?: TeamWithRoleVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListUserActivityDeviceVo = {
    code?: number;
    data?: UserActivityDeviceVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListUserFunctionDto = {
    code?: number;
    data?: UserFunctionDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListUserFunctionMetaVo = {
    code?: number;
    data?: UserFunctionMetaVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListUserLoginDeviceVo = {
    code?: number;
    data?: UserLoginDeviceVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultLoginDeviceLocationVo = {
    code?: number;
    data?: LoginDeviceLocationVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultLoginResultVo = {
    code?: number;
    data?: LoginResultVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMaplong = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultMobile3ElementResult = {
    code?: number;
    data?: Mobile3ElementResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultOAuthProcessInfo = {
    code?: number;
    data?: OAuthProcessInfo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultOpsStrategyGroupDto = {
    code?: number;
    data?: OpsStrategyGroupDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultOpsStrategyGroupVo = {
    code?: number;
    data?: OpsStrategyGroupVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultOpStatVo = {
    code?: number;
    data?: OpStatVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultFavoriteDetailVo = {
    code?: number;
    data?: PageResultFavoriteDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultFavoriteDto = {
    code?: number;
    data?: PageResultFavoriteDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultOpsStrategyGroupVo = {
    code?: number;
    data?: PageResultOpsStrategyGroupVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTeamFavoriteDetailVo = {
    code?: number;
    data?: PageResultTeamFavoriteDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTeamFavoriteDto = {
    code?: number;
    data?: PageResultTeamFavoriteDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultUserActivityDeviceVo = {
    code?: number;
    data?: PageResultUserActivityDeviceVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultUserDetailVo = {
    code?: number;
    data?: PageResultUserDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultUserPromotedRecordVo = {
    code?: number;
    data?: PageResultUserPromotedRecordVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRegisterAccountVo = {
    code?: number;
    data?: RegisterAccountVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRegisterConfig = {
    code?: number;
    data?: RegisterConfig;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRoleVo = {
    code?: number;
    data?: RoleVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultstring = {
    code?: number;
    data?: string;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamConfigDto = {
    code?: number;
    data?: TeamConfigDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamDetailVo = {
    code?: number;
    data?: TeamDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamDeviceConfig = {
    code?: number;
    data?: TeamDeviceConfig;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamDto = {
    code?: number;
    data?: TeamDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamInviteVo = {
    code?: number;
    data?: TeamInviteVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamQuotaDetailVo = {
    code?: number;
    data?: TeamQuotaDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamRoleConfig = {
    code?: number;
    data?: TeamRoleConfig;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamStatInfo = {
    code?: number;
    data?: TeamStatInfo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamSystemConfig = {
    code?: number;
    data?: TeamSystemConfig;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamVerifyDto = {
    code?: number;
    data?: TeamVerifyDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultUserDepartmentVo = {
    code?: number;
    data?: UserDepartmentVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultUserDetailVo = {
    code?: number;
    data?: UserDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultUserPromoInfo = {
    code?: number;
    data?: UserPromoInfo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultUserTeamVo = {
    code?: number;
    data?: UserTeamVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };
}
