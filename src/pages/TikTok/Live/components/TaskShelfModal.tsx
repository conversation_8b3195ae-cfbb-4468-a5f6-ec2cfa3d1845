import I18N from '@/i18n';
import { Badge, Button, ConfigProvider, Empty, message, Space, Tooltip, Typography } from 'antd';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { scrollProTableOptionFn } from '@/mixins/table';
import { dateFormat } from '@/utils/utils';
import { useOrder } from '@/components/Sort/SortDropdown';
import SortTitle from '@/components/Sort/SortTitle';
import Placeholder from '@/components/Common/Placeholder';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { TkShopCreatorById } from '@/components/Common/TkShopCreator';
import { GhostModalCaller } from '@/mixins/modal';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { TaskDetail } from '@/pages/TaskHistory';
import DMConfirm from '@/components/Common/DMConfirm';
import { useRequest } from '@@/plugin-request/request';
import { useAnimation, useTaskPoolAddAnimation } from '@/hooks/interactions';
import DMModal from '@/components/Common/Modal/DMModal';
import buttonStyles from '@/style/button.less';
import SelectDeviceModal from '@/pages/RpaFlows/components/SelectDeviceModal';
import styled from 'styled-components';
import {
  tkshopTaskDrawerBatchDeletePost,
  tkshopTaskDrawerPost,
  tkshopTaskDrawersGet,
} from '@/services/api-TKShopAPI/TkshopTaskDrawerController';
import EventEmitter from 'events';
import { ShopNodeById } from '@/components/Common/ShopNode';
import MobileAccountNodeById from '@/components/Common/MobileAccountNode';
import { tkshopJobsSendDrawerToJobsPost } from '@/services/api-TKShopAPI/TkShopJobController';
import { useDrag } from '@use-gesture/react';
import { useLocalStorageState } from 'ahooks';
import { getTaskParamsColumn } from '@/pages/Task/utils';
import ColoursIcon from '@/components/Common/ColoursIcon';
import { TkShopBuyerById } from '@/components/Common/TkShopBuyer';
import { useVT } from 'virtualizedtableforantd4';
import _ from 'lodash';
import { StyledTableWrapper } from '@/style/styled';
import FunctionButton from '@/components/Common/FunctionButton';
import Functions from '@/constants/Functions';

export const TaskShelfJobType = {
  TS_IMChatByFilter: I18N.t('发送站内消息（指定筛选条件）'),
  TS_IMChatByHandle: I18N.t('发送站内消息（指定达人列表）'),
  TS_SampleApprove: I18N.t('索样审批流程'),
  TS_SendLine: I18N.t('发送Line消息'),
  TS_SendWhatsApp: I18N.t('发送Whatsapp消息'),
  TS_SendFacebook: I18N.t('发送Facebook消息'),
  TS_SendZalo: I18N.t('发送Zalo消息'),
  TS_SendViber: I18N.t('发送Viber消息'),
  TS_SyncCreator: I18N.t('达人基础信息更新'),
  TS_SyncSampleCreator: I18N.t('索样记录同步'),
  TS_SyncShopInfo: I18N.t('店铺信息同步'),
  TS_TargetPlanByFilter: I18N.t('定向邀约（指定筛选条件）'),
  TS_TargetPlanByHandle: I18N.t('定向邀约（指定达人列表）'),
  TS_LoginCheck: I18N.t('店铺登录状态检查'),
  TS_SendEmail: I18N.t('发送电子邮件'),
  TS_SendBuyerIMChat: I18N.t('发送站内消息（买家）'),
  TS_DemandPayment: I18N.t('未付款订单催付'),
  TS_TargetPlanClear: I18N.t('清理定向邀约计划'),
  TS_AddFriends: I18N.t('添加好友'),
  TS_AddContacts: I18N.t('添加联系人'),
  TS_SyncProducts: I18N.t('同步店铺商品'),
  TS_SyncVideos: I18N.t('视频基础信息更新'),
  TS_SyncOrders: I18N.t('同步店铺订单'),
  TS_VideoADCode: I18N.t('提取投流码'),
};
export const TaskShelfJobIcon = {
  TS_IMChatByFilter: 'shejiaopingtai_24',
  TS_IMChatByHandle: 'shejiaopingtai_24',
  TS_SampleApprove: 'daishenpi_24',
  TS_SendLine: 'Line_24',
  TS_SendWhatsApp: 'Whatsapp_24',
  TS_SendFacebook: 'FbMessenger_24',
  TS_SendZalo: 'Zalo_24',
  TS_SendViber: 'Viber_24',
  TS_SyncCreator: 'jibenxinxi_24',
  TS_SyncSampleCreator: 'duzhanshifangwen_24',
  TS_SyncShopInfo: 'xinxitongbu_24',
  TS_TargetPlanByFilter: 'dingxiangyaoyue_24',
  TS_TargetPlanByHandle: 'dingxiangyaoyue_24',
  TS_LoginCheck: 'dianpu_24',
  TS_SendEmail: 'xiaoxi_24',
  TS_SendBuyerIMChat: 'shejiaopingtai_24',
  TS_DemandPayment: 'xuyaoxufei_24',
  TS_TargetPlanClear: 'qingchu_24',
  TS_AddFriends: 'daishouquandianpu_24',
  TS_AddContacts: 'tuandui_24',
  TS_SyncProducts: 'goumai_24',
  TS_SyncVideos: 'duanshipin_24',
  TS_SyncOrders: 'wuliufuwu_24',
  TS_VideoADCode: 'AD_24',
};

export function getTaskShelfJobIcon(
  taskType: keyof typeof TaskShelfJobType,
  options?: {
    iconType?: 'iconfont' | 'svg';
    nodeType?: 'html' | 'react';
    size?: number;
  },
): any {
  const { iconType, nodeType, size } = _.assign(
    {
      iconType: 'svg',
      nodeType: 'react',
      size: 16,
    },
    options,
  );
  const icon = TaskShelfJobIcon[taskType] || 'renwuchi_24';
  if (iconType === 'iconfont') {
    if (nodeType === 'html') {
      return `<a style="font-size: inherit" class="dm-iconFontIcon iconfont icon-${icon}"></a>`;
    } else {
      return <IconFontIcon size={size} iconName={icon} />;
    }
  }
  if (nodeType === 'html') {
    return `<svg class='dm-colours-icon' style="margin-right: 4px;"><use xlink:href='#icon-${icon}' /></svg>`;
  }
  return <ColoursIcon size={size} className={icon} />;
}
const StyledImage = styled.div`
  position: relative;
  .ant-empty-img-simple-g,
  .ant-empty-img-simple-ellipse {
    stroke: #0f7cf4;
  }
  .ant-empty-img-simple-g {
    > path:nth-of-type(1) {
      fill: white;
    }
  }
  .ant-badge {
    position: absolute;
    top: -10px;
    left: 32px;
    z-index: 1;
    margin: auto;
    transform: translateX(-50%);
  }
`;
const taskShelfEvent = new EventEmitter();
export function triggerUpdate() {
  taskShelfEvent.emit('update');
}
export function useTaskShelfUpdate(fn: () => any | void) {
  useEffect(() => {
    taskShelfEvent.addListener('update', fn);
    return () => {
      taskShelfEvent.removeListener('update', fn);
    };
  }, [fn]);
}

const TaskShelfModal = () => {
  const [visible, changeVisible] = useState(true);
  const { play } = useTaskPoolAddAnimation();
  const { order, changeOrder } = useOrder(
    {
      key: 'create_time',
      ascend: false,
    },
    'task_shelf_v20250120',
  );
  const actionRef = useRef<ActionType>();
  const initFlag = useRef(false);
  const [total, setTotal] = useState(0);
  const [selected, changeSelected] = useState<number[]>([]);
  const [vt] = useVT(() => {
    return {
      scroll: {
        y: 550,
      },
    };
  }, []);
  const { run: cancel } = useRequest(
    async (ids: number[]) => {
      await tkshopTaskDrawerBatchDeletePost({
        ids,
      });
    },
    {
      manual: true,
      onSuccess() {
        actionRef.current?.reloadAndRest?.();
      },
    },
  );

  const columns: ProColumns<API.TkshopTaskDrawerDto>[] = useMemo(() => {
    return [
      {
        title: I18N.t('任务类型'),
        dataIndex: 'jobType',
        render(_text, record) {
          const { taskType } = record;
          const text = TaskShelfJobType[taskType!];
          return (
            <Typography.Link
              onClick={() => {
                GhostModalCaller(<TaskDetail job={record} alive />);
              }}
            >
              <Space align={'center'} size={4}>
                <Typography.Link>{getTaskShelfJobIcon(taskType!)}</Typography.Link>
                <span>{text}</span>
              </Space>
            </Typography.Link>
          );
        },
        width: 260,
      },
      {
        title: I18N.t('目标达人'),
        dataIndex: 'creator',
        width: 160,
        render(_text, record) {
          const { creatorId, taskType, parameter } = record;
          if (taskType === 'TS_SendBuyerIMChat') {
            try {
              if (JSON.parse(parameter).advanceSettings.taskType === 'TS_VideoADCode') {
                return <TkShopCreatorById id={creatorId} />;
              }
            } catch (e) {}
            return <TkShopBuyerById id={creatorId} />;
          }
          if (creatorId) {
            return <TkShopCreatorById id={creatorId} />;
          }
          return <Placeholder />;
        },
      },

      {
        title: I18N.t('分身/手机账号'),
        width: 140,
        dataIndex: 'shopId',
        ellipsis: true,
        render(_text, record) {
          const { rpaType, accountId } = record;
          if (!accountId) {
            return <Placeholder />;
          }
          if (rpaType === 'Browser') {
            return <ShopNodeById id={accountId} />;
          }
          return <MobileAccountNodeById id={accountId} />;
        },
      },
      {
        title: I18N.t('主要参数'),
        dataIndex: 'params',
        width: 160,
        ellipsis: true,
        renderText(_text, record) {
          return getTaskParamsColumn(record, 'TkshopTaskDrawerDto');
        },
      },
      {
        width: 120,
        title: (
          <SortTitle
            label={I18N.t('创建时间')}
            order={order}
            onSort={changeOrder}
            orderKey={'create_time'}
          />
        ),
        dataIndex: 'create_time',
        render(_text, record) {
          const { createTime } = record;
          return createTime ? dateFormat(new Date(createTime!), 'MM-DD HH:mm:ss') : <Placeholder />;
        },
      },
    ];
  }, [changeOrder, order]);
  return (
    <DMModal
      width={960}
      bodyStyle={{ paddingBottom: 0 }}
      open={visible}
      footer={null}
      onCancel={() => {
        changeVisible(false);
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
        <StyledImage>
          <Badge
            overflowCount={99}
            size={'small'}
            color={total > 0 ? 'red' : undefined}
            count={total}
            showZero={false}
          />
          {Empty.PRESENTED_IMAGE_SIMPLE}
        </StyledImage>
        <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
          <div style={{ fontSize: 16 }}>{I18N.t('我的任务抽屉')}</div>
          <Typography.Text type={'secondary'}>
            {I18N.t(
              '一些小的、片断性的操作可暂存到任务抽屉里，再将其批量组装成若干个流程，以提升执行效率',
            )}
          </Typography.Text>
        </div>
      </div>
      <ConfigProvider>
        <StyledTableWrapper height={42} style={{ height: 600, marginTop: 16 }}>
          <ProTable<API.GhJobDto>
            actionRef={actionRef}
            params={{ order }}
            rowSelection={{
              selectedRowKeys: selected,
              onChange(selectedRowKeys) {
                changeSelected(selectedRowKeys);
              },
            }}
            components={vt}
            request={async (_params) => {
              const { current, pageSize } = _params;
              triggerUpdate();
              return await tkshopTaskDrawersGet({
                pageNum: current,
                pageSize,
              }).then((res) => {
                const _list = res.data?.list || [];
                if (!initFlag.current) {
                  initFlag.current = true;
                  changeSelected(
                    _list.map((item) => {
                      return item.id!;
                    }),
                  );
                } else {
                  const _selected: any[] = [];
                  // 已选中的要删除不在list的
                  selected.forEach((id) => {
                    if (_list.findIndex((item) => item.id === id) !== -1) {
                      _selected.push(id);
                    }
                  });
                  changeSelected(_selected);
                }
                setTotal(res.data?.total || 0);
                return {
                  data: _list,
                  total: res.data?.total || 0,
                };
              });
            }}
            {...scrollProTableOptionFn({
              pageId: 'task-shelf-list',
              pagination: {
                pageSize: 200,
                renderTotal() {
                  return (
                    <Typography.Text type={'secondary'} style={{ marginRight: -8 }}>
                      {I18N.t('最大容量 {{max}}，已有 {{total}} 笔任务，', {
                        max: '2000',
                        total: total?.toLocaleString(),
                      })}
                    </Typography.Text>
                  );
                },
              },
              alwaysShowFooter: true,
              footer: () => {
                return (
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}
                  >
                    <Tooltip
                      title={!selected?.length ? I18N.t('请选中后操作') : undefined}
                      placement={'topLeft'}
                    >
                      <Space>
                        <FunctionButton
                          code={[Functions.RPA_RUN, Functions.RPA_LIST]}
                          className={buttonStyles.successBtn}
                          onClick={(e) => {
                            GhostModalCaller(
                              <SelectDeviceModal
                                onSubmit={async (devices) => {
                                  if (devices.length) {
                                    await tkshopJobsSendDrawerToJobsPost({
                                      drawerJobIds: selected,
                                      deviceId: devices[0].deviceId,
                                    });
                                    play(e);
                                    actionRef.current?.reloadAndRest?.();
                                    changeVisible(false);
                                  }
                                }}
                              />,
                              'SelectDeviceModal',
                            );
                          }}
                          icon={<IconFontIcon iconName={'Close-Circle_24'} />}
                          disabled={!selected.length}
                        >
                          {I18N.t('批量执行')}
                        </FunctionButton>
                        <Button
                          disabled={!selected.length}
                          onClick={() => {
                            DMConfirm({
                              width: 460,
                              title: I18N.t('确定要取消选中的任务吗？'),
                              onOk() {
                                cancel(selected);
                                message.success(I18N.t('取消成功'));
                              },
                            });
                          }}
                          danger
                          icon={<IconFontIcon iconName={'Close-Circle_24'} />}
                        >
                          {I18N.t('取消任务')}
                        </Button>
                      </Space>
                    </Tooltip>
                  </div>
                );
              },
            })}
            columns={columns}
          />
        </StyledTableWrapper>
      </ConfigProvider>
    </DMModal>
  );
};
export default TaskShelfModal;

const StyledButton = styled.div`
  position: fixed;
  right: 48px;
  cursor: pointer;
  &:hover {
    filter: drop-shadow(0 0 12px rgba(0, 0, 0, 0.2));
  }
  .ant-empty-img-simple-g,
  .ant-empty-img-simple-ellipse {
    stroke: #0f7cf4;
  }
  .ant-badge {
    position: absolute;
    top: -10px;
    left: 32px;
    z-index: 1;
    margin: auto;
    transform: translateX(-50%);
  }
`;
export function TaskShelfFloatButton() {
  const { run, data } = useRequest(
    () => {
      return tkshopTaskDrawersGet({
        pageNum: 1,
        pageSize: 1,
      });
    },
    {
      formatResult(res) {
        return res.data?.total;
      },
      pollingInterval: 5000,
      debounceInterval: 500,
    },
  );
  const [bottom, setBottom] = useLocalStorageState('TaskShelfButtonBottom', 60);
  const bind = useDrag(
    (state) => {
      setBottom((prev) => {
        const _val = prev - state.delta[1];
        if (_val < 30) {
          return 30;
        }
        if (window.innerHeight - _val < 150) {
          return window.innerHeight - 150;
        }
        return _val;
      });
    },
    {
      delay: 150,
      axis: 'y',
      filterTaps: true,
    },
  );
  useTaskShelfUpdate(run);
  return (
    <StyledButton
      {...bind()}
      style={{
        bottom,
      }}
      id={'task-shelf-float-button'}
      onClick={() => {
        GhostModalCaller(<TaskShelfModal />);
      }}
    >
      <Badge
        overflowCount={99}
        size={'small'}
        color={data ? 'red' : undefined}
        count={data}
        showZero={false}
      />
      <StyledImage>{Empty.PRESENTED_IMAGE_SIMPLE}</StyledImage>
    </StyledButton>
  );
}
function generateTaskItemKey(items: API.AddTaskDrawerItem[]) {
  return items.map((item) => {
    const { taskType, parameter, creatorId, accountId } = item;
    let taskKey;
    try {
      const json = JSON.parse(parameter || '{}');
      switch (taskType) {
        case 'TS_SampleApprove':
          taskKey = `${taskType}_${json.applyId}`;
          break;
        case 'TS_SyncCreator':
          taskKey = `${taskType}_${creatorId}_${accountId}`;
          break;
        case 'TS_SyncVideos':
          taskKey = `${taskType}_${json?.videoIds?.join(',')}`;
          break;
        default:
          taskKey = undefined;
      }
    } catch (e) {
      console.log(e);
    }

    return {
      ...item,
      taskKey,
    };
  });
}
export function useAddTask() {
  const { play } = useAnimation({
    target: '#task-shelf-float-button',
    vars: (rect) => {
      return {
        left: rect.x,
        top: rect.y - rect.height,
        opacity: 0.5,
        delay: 0,
        duration: 1.5,
        scale: 0.25,
        ease: 'power2.out',
      };
    },
  });
  const [loading, setLoading] = useState(false);
  return {
    run: async (body: API.AddTaskDrawerRequest, e: React.MouseEvent) => {
      if (loading) {
        return;
      }
      const taskType = body?.items?.[0]?.taskType;
      setLoading(true);
      return tkshopTaskDrawerPost({
        items: generateTaskItemKey(body.items!),
      })
        .then((res) => {
          const icon = getTaskShelfJobIcon(taskType!, {
            nodeType: 'html',
          });
          if (taskType === 'TS_SampleApprove' && res?.data?.length === body.items?.length) {
            triggerUpdate();
            setLoading(false);
          } else {
            play(e, icon).then(() => {
              triggerUpdate();
              setLoading(false);
            });
          }

          return res;
        })
        .catch(() => {
          setLoading(false);
        });
    },
    loading,
  };
}
