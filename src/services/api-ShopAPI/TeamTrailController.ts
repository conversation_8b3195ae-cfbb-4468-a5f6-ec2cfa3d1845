// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 查询IP创建的那一条日志（由于历史ip没有相应的日志，所以返回的可能为空） GET /api/trails/${param0}/findIpCreateTrail */
export async function trailsByIpIdFindIpCreateTrailGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.trailsByIpIdFindIpCreateTrailGetParams,
  options?: { [key: string]: any },
) {
  const { ipId: param0, ...queryParams } = params;
  return request<API.WebResultOperateLogsVo>(`/api/trails/${param0}/findIpCreateTrail`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 查询账户导入的那一条日志（由于历史账户没有相应的日志，所以返回的可能为空） GET /api/trails/${param0}/findShopCreateTrail */
export async function trailsByShopIdFindShopCreateTrailGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.trailsByShopIdFindShopCreateTrailGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultOperateLogsVo>(`/api/trails/${param0}/findShopCreateTrail`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 查询IP轨迹 GET /api/trails/findIpTrails */
export async function trailsFindIpTrailsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.trailsFindIpTrailsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultOperateLogsVo>('/api/trails/findIpTrails', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询当前用户IP轨迹 GET /api/trails/findMyIpTrails */
export async function trailsFindMyIpTrailsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.trailsFindMyIpTrailsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultOperateLogsVo>('/api/trails/findMyIpTrails', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询当前用户账户轨迹 GET /api/trails/findMyShopTrails */
export async function trailsFindMyShopTrailsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.trailsFindMyShopTrailsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultOperateLogsVo>('/api/trails/findMyShopTrails', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询账户轨迹 GET /api/trails/findShopTrails */
export async function trailsFindShopTrailsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.trailsFindShopTrailsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultOperateLogsVo>('/api/trails/findShopTrails', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
