// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取指定团队TK参数 GET /api/tk/params */
export async function tkParamsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTkParamVo>('/api/tk/params', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 修改团队TK参数 PUT /api/tk/tk-param */
export async function tkTkParamPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkTkParamPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkParamDto>('/api/tk/tk-param', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
