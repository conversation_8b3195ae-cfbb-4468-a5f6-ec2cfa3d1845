// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** IP池批量导入代理IP POST /api/ipp/${param0}/importAsync */
export async function ippByIppIdImportAsyncPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ippByIppIdImportAsyncPostParams,
  body: API.CreateIppIpRequest,
  options?: { [key: string]: any },
) {
  const { ippId: param0, ...queryParams } = params;
  return request<API.WebResultTaskDto>(`/api/ipp/${param0}/importAsync`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询IP池IP列表 GET /api/ipp/${param0}/ips */
export async function ippByIppIdIpsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ippByIppIdIpsGetParams,
  options?: { [key: string]: any },
) {
  const { ippId: param0, ...queryParams } = params;
  return request<API.WebResultPageResultIppIpDetailVo>(`/api/ipp/${param0}/ips`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 解析IP的xlsx POST /api/ipp/parseIpExcel */
export async function ippParseIpExcelPost(body: {}, file?: File, options?: { [key: string]: any }) {
  const formData = new FormData();

  if (file) {
    formData.append('file', file);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as any)[ele];

    if (item !== undefined && item !== null) {
      if (typeof item === 'object' && !(item instanceof File)) {
        if (item instanceof Array) {
          item.forEach((f) => formData.append(ele, f || ''));
        } else {
          formData.append(ele, JSON.stringify(item));
        }
      } else {
        formData.append(ele, item);
      }
    }
  });

  return request<API.WebResultListExcelIppIPImportVo>('/api/ipp/parseIpExcel', {
    method: 'POST',
    data: formData,
    requestType: 'form',
    ...(options || {}),
  });
}

/** 批量引用团队IP POST /api/ipp/pool/${param0}/teamIps */
export async function ippPoolByIppIdTeamIpsPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ippPoolByIppIdTeamIpsPostParams,
  options?: { [key: string]: any },
) {
  const { ippId: param0, ...queryParams } = params;
  return request<API.WebResultListIppIpDto>(`/api/ipp/pool/${param0}/teamIps`, {
    method: 'POST',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 分页查询IP池详情 GET /api/ipp/pool/pageDetail */
export async function ippPoolPageDetailGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ippPoolPageDetailGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultIppPoolVo>('/api/ipp/pool/pageDetail', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
