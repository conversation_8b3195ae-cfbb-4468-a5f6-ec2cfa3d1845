// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 创建AK POST /api/openak/ak */
export async function openakAkPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.openakAkPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultOpenapiAkDto>('/api/openak/ak', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查看AK详情 GET /api/openak/ak/${param0} */
export async function openakAkByAkIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.openakAkByAkIdGetParams,
  options?: { [key: string]: any },
) {
  const { akId: param0, ...queryParams } = params;
  return request<API.WebResultOpenapiAkDto>(`/api/openak/ak/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改AK状态 PUT /api/openak/ak/${param0}/status */
export async function openakAkByAkIdStatusPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.openakAkByAkIdStatusPutParams,
  options?: { [key: string]: any },
) {
  const { akId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/openak/ak/${param0}/status`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 查询AK GET /api/openak/ak/page */
export async function openakAkPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.openakAkPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultOpenapiAkDto>('/api/openak/ak/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取当前团队openapi配置 GET /api/openak/openapi/config */
export async function openakOpenapiConfigGet(options?: { [key: string]: any }) {
  return request<API.WebResultTeamOpenapiDto>('/api/openak/openapi/config', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取当前团队在当前周期内使用Openapi次数 GET /api/openak/openapi/currentRate */
export async function openakOpenapiCurrentRateGet(options?: { [key: string]: any }) {
  return request<API.WebResultlong>('/api/openak/openapi/currentRate', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 启用/禁用 Open API PUT /api/openak/openapi/open */
export async function openakOpenapiOpenPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.openakOpenapiOpenPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/openak/openapi/open', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
