import I18N from '@/i18n';
import { Tooltip, Typography } from 'antd';
import ColoursIcon from '@/components/Common/ColoursIcon';
import { useMemo, useState } from 'react';
import type { GhScheduleType } from '@/pages/Setting/utils';
import Placeholder from '@/components/Common/Placeholder';
import { useGetShopBriefById } from '@/components/Common/ShopNode';
import { useRequest } from '@@/plugin-request/request';
import { useOpenMobile } from '@/hooks/interactions';
import { AreaIcon } from '@/components/Common/LocationCascader';
import PlatformCateIcon from '@/components/Common/PlatformCateIcon';
import { getMobileAccount } from '@/components/Common/MobileAccountNode';
import openShopBrowser from '@/pages/Shop/components/utils';

const TikTokAccountMap = {
  GhBackstage: I18N.t('经纪人'),
  Mobile: I18N.t('手机账号'),
  Normal: I18N.t('普通账号'),
  Official: I18N.t('官方号'),
};

export const TikTokAccountTypeNode = (props: {
  id?: number;
  type?: GhScheduleType;
  mode?: 'text' | 'iconLabel' | 'iconText' | 'both';
  name?: string;
  handle?: string;
}) => {
  const { id, type, mode = 'both', name, handle } = props;
  const { run: openMobile } = useOpenMobile();

  const getShopAccount = useGetShopBriefById();
  const [data, setData] = useState<API.ShopBriefVo | API.MobileAccountVo>();
  const icon = useMemo(() => {
    switch (type) {
      case 'Mobile':
        if (data?.platform?.typeName) {
          return <PlatformCateIcon size={16} platformName={data?.platform?.typeName} />;
        }
        return <ColoursIcon size={16} className={'yidongwangluoIP_24'} />;
      case 'Normal':
        if (data?.platform?.area) {
          return <AreaIcon size={16} area={data?.platform?.area} />;
        }
        return <ColoursIcon size={16} className={'Chrome_24'} />;
      default:
        return <ColoursIcon size={16} className={'Chrome_24'} />;
    }
  }, [data?.platform?.area, data?.platform?.typeName, type]);
  useRequest(
    async () => {
      if (!id) {
        return;
      }
      if (type === 'Mobile') {
        getMobileAccount(id).then((res) => {
          setData(res!);
        });
      } else {
        getShopAccount(id).then((res) => {
          setData(res!);
        });
      }
    },
    {
      refreshDeps: [id],
    },
  );
  const { run: onClick, loading } = useRequest(
    async () => {
      if (loading || !data) {
        return;
      }
      if (type === 'Mobile') {
        await openMobile({
          id: data.id!,
          type: 'account',
        });
      } else {
        await openShopBrowser(data!);
      }
    },
    {
      manual: true,
    },
  );
  const label = useMemo(() => {
    if (name) {
      return name;
    }
    if (type === 'Mobile') {
      return data?.username;
    }
    return data?.name;
  }, [data?.name, data?.username, name, type]);
  const prefix = useMemo(() => {
    if (mode !== 'text' && type) {
      return (
        <>
          <span style={{ flex: '0 0 16px' }}>{icon}</span>
          {mode !== 'iconText' && (
            <>
              <span style={{ minWidth: `${TikTokAccountMap[type].length}em` }}>
                {TikTokAccountMap[type]}
              </span>
              <span style={{ minWidth: '1em', textAlign: 'left', paddingLeft: 2 }}>:</span>
            </>
          )}
        </>
      );
    }
    return false;
  }, [icon, mode, type]);
  const suffix = useMemo(() => {
    if (mode === 'iconLabel') {
      return false;
    }
    if (label && id) {
      return (
        <Tooltip
          placement={'topLeft'}
          title={
            type === 'Mobile' && data
              ? () => {
                  return (
                    <div
                      style={{
                        display: 'flex',
                        gap: 8,
                        flexDirection: 'column',
                        overflow: 'hidden',
                      }}
                    >
                      <Typography.Text style={{ color: 'inherit' }} ellipsis>
                        {I18N.t('手机：')}
                        {data?.mobileName || '--'}
                      </Typography.Text>
                      <Typography.Text style={{ color: 'inherit' }} ellipsis>
                        {I18N.t('账号名称：')}
                        {label}
                      </Typography.Text>
                    </div>
                  );
                }
              : undefined
          }
        >
          <Typography.Link style={{ flex: 1, overflow: 'hidden' }} onClick={onClick} ellipsis>
            {label}
          </Typography.Link>
        </Tooltip>
      );
    }
    return <Placeholder />;
  }, [data, id, label, mode, onClick, type]);
  return (
    <div style={{ display: 'flex', gap: 4, overflow: 'hidden', alignItems: 'center' }}>
      {prefix}
      {suffix}
    </div>
  );
};
