// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 添加分身到快照策略 POST /api/shop/snapshot/plan/addShopsToPlan */
export async function shopSnapshotPlanAddShopsToPlanPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSnapshotPlanAddShopsToPlanPostParams,
  body: number[],
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/snapshot/plan/addShopsToPlan', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建一个新的快照策略 POST /api/shop/snapshot/plan/create */
export async function shopSnapshotPlanCreatePost(
  body: API.CreateShopSnapshotPlanRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/snapshot/plan/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除一个快照策略 DELETE /api/shop/snapshot/plan/delete */
export async function shopSnapshotPlanDeleteDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSnapshotPlanDeleteDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/snapshot/plan/delete', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 从快照策略中删除分身 DELETE /api/shop/snapshot/plan/deleteShopFromPlan */
export async function shopSnapshotPlanDeleteShopFromPlanDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSnapshotPlanDeleteShopFromPlanDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/snapshot/plan/deleteShopFromPlan', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取团队的分身快照策略列表 GET /api/shop/snapshot/plan/list */
export async function shopSnapshotPlanListGet(options?: { [key: string]: any }) {
  return request<API.WebResultListShopSnapshotPlanVo>('/api/shop/snapshot/plan/list', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取未加入到任何快照策略的分身信息 GET /api/shop/snapshot/plan/listNotAddedShops */
export async function shopSnapshotPlanListNotAddedShopsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListShopDetailVo>('/api/shop/snapshot/plan/listNotAddedShops', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取某个快照策略的分身信息 GET /api/shop/snapshot/plan/listPlanShops */
export async function shopSnapshotPlanListPlanShopsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopSnapshotPlanListPlanShopsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListShopDetailVo>('/api/shop/snapshot/plan/listPlanShops', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取未加入到任何快照策略的分身信息 POST /api/shop/snapshot/plan/pageNotAddedShops */
export async function shopSnapshotPlanPageNotAddedShopsPost(
  body: API.PageShopRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultShopDetailVo>('/api/shop/snapshot/plan/pageNotAddedShops', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取某个快照策略的分身信息 POST /api/shop/snapshot/plan/pagePlanShops */
export async function shopSnapshotPlanPagePlanShopsPost(
  body: API.PagePlanShopRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultShopDetailVo>('/api/shop/snapshot/plan/pagePlanShops', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新一个新的快照策略 POST /api/shop/snapshot/plan/update */
export async function shopSnapshotPlanUpdatePost(
  body: API.UpdateShopSnapshotPlanRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/snapshot/plan/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
