// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 查询当前用户可用的Openapi AK列表 GET /api/tk/akList */
export async function tkAkListGet(options?: { [key: string]: any }) {
  return request<API.WebResultListOpenapiAkDto>('/api/tk/akList', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 计算购买TK套餐订单的价格 POST /api/tk/calcBuyTkPackOrderPrice */
export async function tkCalcBuyTkPackOrderPricePost(
  body: API.CreateBuyTkPackRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCalcPriceResponse>('/api/tk/calcBuyTkPackOrderPrice', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 计算续费TK套餐订单的价格 POST /api/tk/calcRenewTkPackPrice */
export async function tkCalcRenewTkPackPricePost(
  body: API.CreateRenewTkPackRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCalcPriceResponse>('/api/tk/calcRenewTkPackPrice', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 确认已交付 PUT /api/tk/confirmDelivered */
export async function tkConfirmDeliveredPut(options?: { [key: string]: any }) {
  return request<API.WebResult>('/api/tk/confirmDelivered', {
    method: 'PUT',
    ...(options || {}),
  });
}

/** 创建购买TK套餐订单 POST /api/tk/createBuyTkPackOrder */
export async function tkCreateBuyTkPackOrderPost(
  body: API.CreateBuyTkPackRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCreateBuyTkPackOrderResponse>('/api/tk/createBuyTkPackOrder', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建续费TK套餐订单 POST /api/tk/createRenewTkPackOrder */
export async function tkCreateRenewTkPackOrderPost(
  body: API.CreateRenewTkPackRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCreateOrderResponse>('/api/tk/createRenewTkPackOrder', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取折扣信息 GET /api/tk/discountInfo */
export async function tkDiscountInfoGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkDiscountInfoGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultDiscountsDto>('/api/tk/discountInfo', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询TK所有流程 GET /api/tk/flows */
export async function tkFlowsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkFlowsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTkFlowDetailVo>('/api/tk/flows', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 根据子系统类型，查询流程分组 GET /api/tk/groups/${param0} */
export async function tkGroupsBySubSystemTypeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkGroupsBySubSystemTypeGetParams,
  options?: { [key: string]: any },
) {
  const { subSystemType: param0, ...queryParams } = params;
  return request<API.WebResultListTkFlowGroupVo>(`/api/tk/groups/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 是否购买的试用版 GET /api/tk/hasTrialOrder */
export async function tkHasTrialOrderGet(options?: { [key: string]: any }) {
  return request<API.WebResultboolean>('/api/tk/hasTrialOrder', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 用当前AK创建签名的token，用户访问持有当前AK的第三方系统 请在request payload中传入需要签名的信息。系统默认会添加userId和teamId（有传入时）放入签名体。请注意request payload不能超过500字符 POST /api/tk/isv-token */
export async function tkIsvTokenPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkIsvTokenPostParams,
  body: Record<string, any>,
  options?: { [key: string]: any },
) {
  return request<API.WebResultAkAccessToken>('/api/tk/isv-token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询团队订单列表 GET /api/tk/orders */
export async function tkOrdersGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkOrdersGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultOrderDetailVo>('/api/tk/orders', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取套餐参数类型 GET /api/tk/pack/paramDef */
export async function tkPackParamDefGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTkPackParamVo>('/api/tk/pack/paramDef', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取当前团队套餐参数 GET /api/tk/pack/params */
export async function tkPackParamsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTkPackParamDto>('/api/tk/pack/params', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 根据子系统类型，查询流程套餐 GET /api/tk/packs/${param0} */
export async function tkPacksBySubSystemTypeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkPacksBySubSystemTypeGetParams,
  options?: { [key: string]: any },
) {
  const { subSystemType: param0, ...queryParams } = params;
  return request<API.WebResultListTkPackVo>(`/api/tk/packs/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 根据优惠码查询流程套餐 GET /api/tk/packsWithDistributionCode/${param0} */
export async function tkPacksWithDistributionCodeBySubSystemTypeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkPacksWithDistributionCodeBySubSystemTypeGetParams,
  options?: { [key: string]: any },
) {
  const { subSystemType: param0, ...queryParams } = params;
  return request<API.WebResultTkPacksWithDistribution>(
    `/api/tk/packsWithDistributionCode/${param0}`,
    {
      method: 'GET',
      params: {
        ...queryParams,
      },
      ...(options || {}),
    },
  );
}

/** 根据推荐码查询流程套餐 GET /api/tk/packWithDistributionCode */
export async function tkPackWithDistributionCodeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkPackWithDistributionCodeGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkPackWithDistributionVo>('/api/tk/packWithDistributionCode', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 返回系统可赠送IP列表 GET /api/tk/presentIps */
export async function tkPresentIpsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTkPresentIpVo>('/api/tk/presentIps', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 查询当前团队状态 GET /api/tk/systemStatus */
export async function tkSystemStatusGet(options?: { [key: string]: any }) {
  return request<API.WebResultTkSystemStatusVo>('/api/tk/systemStatus', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取已购买套餐 GET /api/tk/teamPacks */
export async function tkTeamPacksGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTkTeamPackVo>('/api/tk/teamPacks', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 将当前TK团队降级为普通团队 PUT /api/tk/tkDowngrade */
export async function tkTkDowngradePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkTkDowngradePutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tk/tkDowngrade', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
