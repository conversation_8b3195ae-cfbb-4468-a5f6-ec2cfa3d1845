// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取实例登录账户密码（或ssh key） GET /api/eip/instance/${param0}/accessInfo */
export async function eipInstanceByIdAccessInfoGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.eipInstanceByIdAccessInfoGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultAccountCredentialVo>(`/api/eip/instance/${param0}/accessInfo`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** Aws Windows解密Windows密码 GET /api/eip/instance/${param0}/decryptWindowsPassword */
export async function eipInstanceByIdDecryptWindowsPasswordGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.eipInstanceByIdDecryptWindowsPasswordGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultstring>(`/api/eip/instance/${param0}/decryptWindowsPassword`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}
