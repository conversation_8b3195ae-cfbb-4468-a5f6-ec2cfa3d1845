import { useEffect, useState } from 'react';
import { SkipErrorNotifyOption } from '@/utils/utils';
import EventEmitter from 'events';
import useCurrentTeam from '@/hooks/useCurrentTeam';
import {
  tkshopTeamConfigGet,
  tkshopTeamConfigPut,
} from '@/services/api-TKShopAPI/TkshopSystemController';

export type SystemConfig = API.TeamTkshopConfig;

const UPDATE_SYMBOL = Symbol('UPDATE');
const eventEmitter = new EventEmitter();

let _config: API.TeamTkshopConfig;
let fetchPromise: Promise<API.TeamTkshopConfig> | null = null; // 请求锁

export const fetchSystemConfig = () => {
  if (fetchPromise) return fetchPromise;
  fetchPromise = tkshopTeamConfigGet(SkipErrorNotifyOption)
    .then(async (res) => {
      _config = res.data!;
      eventEmitter.emit(UPDATE_SYMBOL, _config);
      return _config;
    })
    .finally(() => {
      fetchPromise = null;
    });
  return fetchPromise;
};
export function useSystemConfig() {
  const [config, setConfig] = useState(_config);
  const team = useCurrentTeam();
  useEffect(() => {
    if (team?.id) {
      fetchSystemConfig();
    }
    eventEmitter.on(UPDATE_SYMBOL, setConfig);
    return () => {
      eventEmitter.off(UPDATE_SYMBOL, setConfig);
    };
  }, [team?.id]);

  return {
    data: config,
    update: async (json: SystemConfig) => {
      await tkshopTeamConfigPut(json);
      fetchPromise = null; // 清空锁，确保fetchSystemConfig会重新请求
      fetchSystemConfig();
    },
  };
}
