// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 创建团队功能屏蔽 POST /api/team/settings/shopPolicies/createBlockElement */
export async function teamSettingsShopPoliciesCreateBlockElementPost(
  body: API.CreateBlockElementRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultBlockElementVo>(
    '/api/team/settings/shopPolicies/createBlockElement',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 删除团队功能屏蔽 DELETE /api/team/settings/shopPolicies/deleteBlockElements */
export async function teamSettingsShopPoliciesDeleteBlockElementsDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamSettingsShopPoliciesDeleteBlockElementsDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/team/settings/shopPolicies/deleteBlockElements', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取团队绕过代理域名 GET /api/team/settings/shopPolicies/directDomains */
export async function teamSettingsShopPoliciesDirectDomainsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListDirectDomainsDto>(
    '/api/team/settings/shopPolicies/directDomains',
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/** 编辑团队功能屏蔽 POST /api/team/settings/shopPolicies/editBlockElement */
export async function teamSettingsShopPoliciesEditBlockElementPost(
  body: API.EditBlockElementRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultBlockElementVo>('/api/team/settings/shopPolicies/editBlockElement', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取团队功能屏蔽列表 GET /api/team/settings/shopPolicies/teamBlockElements */
export async function teamSettingsShopPoliciesTeamBlockElementsGet(options?: {
  [key: string]: any;
}) {
  return request<API.WebResultListBlockElementVo>(
    '/api/team/settings/shopPolicies/teamBlockElements',
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/** 批量设置团队绕过代理域名 POST /api/team/settings/shopPolicies/updateDirectDomains */
export async function teamSettingsShopPoliciesUpdateDirectDomainsPost(
  body: API.UpdateTeamDirectDomainsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/team/settings/shopPolicies/updateDirectDomains', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
