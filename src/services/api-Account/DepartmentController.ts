// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 添加部门 POST /api/department/ */
export async function departmentPost(
  body: API.DepartmentParamVo,
  options?: { [key: string]: any },
) {
  return request<API.WebResultDepartmentDto>('/api/department/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询部门 GET /api/department/${param0} */
export async function departmentByDepartmentIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.departmentByDepartmentIdGetParams,
  options?: { [key: string]: any },
) {
  const { departmentId: param0, ...queryParams } = params;
  return request<API.WebResultDepartmentDto>(`/api/department/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改部门信息 PUT /api/department/${param0} */
export async function departmentByDepartmentIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.departmentByDepartmentIdPutParams,
  options?: { [key: string]: any },
) {
  const { departmentId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/department/${param0}`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 删除部门 DELETE /api/department/${param0} */
export async function departmentByDepartmentIdDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.departmentByDepartmentIdDeleteParams,
  options?: { [key: string]: any },
) {
  const { departmentId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/department/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 给部门授权特定资源权限（更新不是追加） GET /api/department/${param0}/grantedResources */
export async function departmentByDepartmentIdGrantedResourcesGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.departmentByDepartmentIdGrantedResourcesGetParams,
  options?: { [key: string]: any },
) {
  const { departmentId: param0, ...queryParams } = params;
  return request<API.WebResultListlong>(`/api/department/${param0}/grantedResources`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 给部门授权特定资源权限（更新不是追加） PUT /api/department/${param0}/grantResources */
export async function departmentByDepartmentIdGrantResourcesPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.departmentByDepartmentIdGrantResourcesPutParams,
  body: API.GrantDepartmentResourcesRequest,
  options?: { [key: string]: any },
) {
  const { departmentId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/department/${param0}/grantResources`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 查看部门邀请码 GET /api/department/${param0}/invitingCode */
export async function departmentByIdInvitingCodeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.departmentByIdInvitingCodeGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultstring>(`/api/department/${param0}/invitingCode`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 开启/关闭部门邀约 PUT /api/department/${param0}/invitingEnabled */
export async function departmentByIdInvitingEnabledPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.departmentByIdInvitingEnabledPutParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/department/${param0}/invitingEnabled`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 添加部门成员 POST /api/department/${param0}/member */
export async function departmentByDepartmentIdMemberPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.departmentByDepartmentIdMemberPostParams,
  body: number[],
  options?: { [key: string]: any },
) {
  const { departmentId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/department/${param0}/member`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 列出部门成员 GET /api/department/${param0}/members */
export async function departmentByDepartmentIdMembersGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.departmentByDepartmentIdMembersGetParams,
  options?: { [key: string]: any },
) {
  const { departmentId: param0, ...queryParams } = params;
  return request<API.WebResultUserDepartmentVo>(`/api/department/${param0}/members`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 刷新部门邀请码 PUT /api/department/${param0}/refreshInvitingCode */
export async function departmentByIdRefreshInvitingCodePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.departmentByIdRefreshInvitingCodePutParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/department/${param0}/refreshInvitingCode`, {
    method: 'PUT',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取部门授权资源ID列表 GET /api/department/${param0}/resourceIds */
export async function departmentByIdResourceIdsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.departmentByIdResourceIdsGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultListlong>(`/api/department/${param0}/resourceIds`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 编辑成员 PUT /api/department/member/${param0} */
export async function departmentMemberByUserIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.departmentMemberByUserIdPutParams,
  body: API.DepartmentMemberUpdateParamVo,
  options?: { [key: string]: any },
) {
  const { userId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/department/member/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 移动成员 PUT /api/department/member/move */
export async function departmentMemberMovePut(
  body: API.DepartmentMemberMoveParamVo,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/department/member/move', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 移动部门顺序 PUT /api/department/orders */
export async function departmentOrdersPut(
  body: API.UpdateDepartmentOrderParamVo,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/department/orders', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 列出部门树,根据当前用户拥有的最大部门权限 GET /api/department/tree */
export async function departmentTreeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.departmentTreeGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListDepartmentTreeNode>('/api/department/tree', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
