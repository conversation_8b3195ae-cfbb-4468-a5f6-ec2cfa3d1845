// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 取消监听某个事件 PUT /api/ajax-event/batch-un-listener */
export async function ajaxEventBatchUnListenerPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ajaxEventBatchUnListenerPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/ajax-event/batch-un-listener', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 监听某个事件 PUT /api/ajax-event/batchListener */
export async function ajaxEventBatchListenerPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ajaxEventBatchListenerPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/ajax-event/batchListener', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 监听某个事件 PUT /api/ajax-event/listener */
export async function ajaxEventListenerPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ajaxEventListenerPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/ajax-event/listener', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 定时轮循某个页面的服务器推送事件 GET /api/ajax-event/query */
export async function ajaxEventQueryGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ajaxEventQueryGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListAjaxEventVo>('/api/ajax-event/query', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 取消监听某个事件 PUT /api/ajax-event/un-listener */
export async function ajaxEventUnListenerPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ajaxEventUnListenerPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/ajax-event/un-listener', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
