// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 批量更新默认路由 PUT /api/shop/defaultRule/routers */
export async function shopDefaultRuleRoutersPut(
  body: API.BatchUpdateShopDefaultRouterRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/defaultRule/routers', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量更新账户的路由 PUT /api/shop/routers */
export async function shopRoutersPut(
  body: API.BatchUpdateShopRouterRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/routers', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
