// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取一个task的基本信息 GET /api/rpa/task/${param0} */
export async function rpaTaskByRpaTaskIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskByRpaTaskIdGetParams,
  options?: { [key: string]: any },
) {
  const { rpaTaskId: param0, ...queryParams } = params;
  return request<API.WebResultRpaTaskVo>(`/api/rpa/task/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取一个taskItem的变量值定义 GET /api/rpa/task/${param0}/${param1}/params */
export async function rpaTaskByRpaTaskIdByRpaTaskItemIdParamsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskByRpaTaskIdByRpaTaskItemIdParamsGetParams,
  options?: { [key: string]: any },
) {
  const { rpaTaskId: param0, rpaTaskItemId: param1, ...queryParams } = params;
  return request<API.WebResultMapstring>(`/api/rpa/task/${param0}/${param1}/params`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除任务item文件，可指定具体文件 DELETE /api/rpa/task/${param0}/${param1}/removeTaskItemFiles */
export async function rpaTaskByRpaTaskIdByTaskItemIdRemoveTaskItemFilesDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskByRpaTaskIdByTaskItemIdRemoveTaskItemFilesDeleteParams,
  options?: { [key: string]: any },
) {
  const { rpaTaskId: param0, taskItemId: param1, ...queryParams } = params;
  return request<API.WebResult>(`/api/rpa/task/${param0}/${param1}/removeTaskItemFiles`, {
    method: 'DELETE',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 将某个正在等待调度的rpa task取消调度（大多因为rpa云端执行器） POST /api/rpa/task/${param0}/cancelTaskScheduling */
export async function rpaTaskByRpaTaskIdCancelTaskSchedulingPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskByRpaTaskIdCancelTaskSchedulingPostParams,
  options?: { [key: string]: any },
) {
  const { rpaTaskId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/rpa/task/${param0}/cancelTaskScheduling`, {
    method: 'POST',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取一个task的环境变量 GET /api/rpa/task/${param0}/environments */
export async function rpaTaskByRpaTaskIdEnvironmentsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskByRpaTaskIdEnvironmentsGetParams,
  options?: { [key: string]: any },
) {
  const { rpaTaskId: param0, ...queryParams } = params;
  return request<API.WebResultRpaTaskReport>(`/api/rpa/task/${param0}/environments`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取一个预览任务的分身的信息 GET /api/rpa/task/${param0}/findPreviewTaskShopInfo */
export async function rpaTaskByShopIdFindPreviewTaskShopInfoGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskByShopIdFindPreviewTaskShopInfoGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultListRpaShopInfo>(`/api/rpa/task/${param0}/findPreviewTaskShopInfo`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取一个task item打开了哪些会话 GET /api/rpa/task/${param0}/findRpaSessions */
export async function rpaTaskByRpaTaskItemIdFindRpaSessionsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskByRpaTaskItemIdFindRpaSessionsGetParams,
  options?: { [key: string]: any },
) {
  const { rpaTaskItemId: param0, ...queryParams } = params;
  return request<API.WebResultListlong>(`/api/rpa/task/${param0}/findRpaSessions`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 强制结束一个任务 PUT /api/rpa/task/${param0}/forceEnd */
export async function rpaTaskByRpaTaskIdForceEndPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskByRpaTaskIdForceEndPutParams,
  options?: { [key: string]: any },
) {
  const { rpaTaskId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/rpa/task/${param0}/forceEnd`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取一个task的输入变量值 GET /api/rpa/task/${param0}/getTaskPredefineParams */
export async function rpaTaskByRpaTaskIdGetTaskPredefineParamsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskByRpaTaskIdGetTaskPredefineParamsGetParams,
  options?: { [key: string]: any },
) {
  const { rpaTaskId: param0, ...queryParams } = params;
  return request<API.WebResultMaplong>(`/api/rpa/task/${param0}/getTaskPredefineParams`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取一个task的item列表 GET /api/rpa/task/${param0}/items */
export async function rpaTaskByRpaTaskIdItemsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskByRpaTaskIdItemsGetParams,
  options?: { [key: string]: any },
) {
  const { rpaTaskId: param0, ...queryParams } = params;
  return request<API.WebResultListRpaTaskItemVo>(`/api/rpa/task/${param0}/items`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 切换任务文件的lock状态 PUT /api/rpa/task/${param0}/lockFile */
export async function rpaTaskByRpaTaskIdLockFilePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskByRpaTaskIdLockFilePutParams,
  options?: { [key: string]: any },
) {
  const { rpaTaskId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/rpa/task/${param0}/lockFile`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 任务跑完后要调用该接口 PUT /api/rpa/task/${param0}/markTaskEnd */
export async function rpaTaskByRpaTaskIdMarkTaskEndPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskByRpaTaskIdMarkTaskEndPutParams,
  options?: { [key: string]: any },
) {
  const { rpaTaskId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/rpa/task/${param0}/markTaskEnd`, {
    method: 'PUT',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除任务文件 (会清除整个task的文件) DELETE /api/rpa/task/${param0}/removeTaskFiles */
export async function rpaTaskByRpaTaskIdRemoveTaskFilesDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskByRpaTaskIdRemoveTaskFilesDeleteParams,
  options?: { [key: string]: any },
) {
  const { rpaTaskId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/rpa/task/${param0}/removeTaskFiles`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取一个task的所有的分身或者是手机信息(根据rpaType的不同而定) GET /api/rpa/task/${param0}/shopsInfo */
export async function rpaTaskByRpaTaskIdShopsInfoGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskByRpaTaskIdShopsInfoGetParams,
  options?: { [key: string]: any },
) {
  const { rpaTaskId: param0, ...queryParams } = params;
  return request<API.WebResultListAbstractRpaShopInfo>(`/api/rpa/task/${param0}/shopsInfo`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 为会rpa生成上传sts token GET /api/rpa/task/${param0}/signature */
export async function rpaTaskByRpaTaskItemIdSignatureGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskByRpaTaskItemIdSignatureGetParams,
  options?: { [key: string]: any },
) {
  const { rpaTaskItemId: param0, ...queryParams } = params;
  return request<API.WebResultStsPostSignature>(`/api/rpa/task/${param0}/signature`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 通知服务器更新某个文件的大小（如日志文件大小变化了） PUT /api/rpa/task/${param0}/updateFileSize */
export async function rpaTaskByRpaTaskFileIdUpdateFileSizePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskByRpaTaskFileIdUpdateFileSizePutParams,
  options?: { [key: string]: any },
) {
  const { rpaTaskFileId: param0, ...queryParams } = params;
  return request<API.WebResultRpaTaskFileVo>(`/api/rpa/task/${param0}/updateFileSize`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 切换流程的暂停状态 PUT /api/rpa/task/changePauseStatus/${param0} */
export async function rpaTaskChangePauseStatusByRpaTaskIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskChangePauseStatusByRpaTaskIdPutParams,
  options?: { [key: string]: any },
) {
  const { rpaTaskId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/rpa/task/changePauseStatus/${param0}`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 根据bizCode(+shopId)统计未结束的流程任务数量 GET /api/rpa/task/countNotEndByBizCode */
export async function rpaTaskCountNotEndByBizCodeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskCountNotEndByBizCodeGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/rpa/task/countNotEndByBizCode', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取团队总task个数 GET /api/rpa/task/countTasks */
export async function rpaTaskCountTasksGet(options?: { [key: string]: any }) {
  return request<API.WebResultint>('/api/rpa/task/countTasks', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取某个流程的插件列表 GET /api/rpa/task/detectRpaPlugins */
export async function rpaTaskDetectRpaPluginsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskDetectRpaPluginsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListstring>('/api/rpa/task/detectRpaPlugins', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取当前用户登录设备 GET /api/rpa/task/devices */
export async function rpaTaskDevicesGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskDevicesGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListRpaRunDeviceVo>('/api/rpa/task/devices', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 触发一个rpa事件链的执行 PUT /api/rpa/task/dispatchRpaEvent */
export async function rpaTaskDispatchRpaEventPut(
  body: API.DispatchRpaEventRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/rpa/task/dispatchRpaEvent', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 执行初始子流程时，设置最终要执行哪些分身 PUT /api/rpa/task/dynamicSetTaskItems */
export async function rpaTaskDynamicSetTaskItemsPut(
  body: API.DynamicSetTaskItemRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListRpaTaskItemVo>('/api/rpa/task/dynamicSetTaskItems', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取单个RpaTaskItem的信息 GET /api/rpa/task/findItem/${param0} */
export async function rpaTaskFindItemByRpaTaskItemIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskFindItemByRpaTaskItemIdGetParams,
  options?: { [key: string]: any },
) {
  const { rpaTaskItemId: param0, ...queryParams } = params;
  return request<API.WebResultRpaTaskItemVo>(`/api/rpa/task/findItem/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取一个预览任务的Mobile的信息 GET /api/rpa/task/findPreviewTaskMobileInfo */
export async function rpaTaskFindPreviewTaskMobileInfoGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskFindPreviewTaskMobileInfoGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListRpaMobileInfo>('/api/rpa/task/findPreviewTaskMobileInfo', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** rpa.api使用，根据平台类型获取shop(已对当前用户授权) GET /api/rpa/task/findShopsByPlatform */
export async function rpaTaskFindShopsByPlatformGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskFindShopsByPlatformGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListRpaShopInfo>('/api/rpa/task/findShopsByPlatform', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** rpa.api使用，根据标签名获取shop(已对当前用户授权) GET /api/rpa/task/findShopsByTagName */
export async function rpaTaskFindShopsByTagNameGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskFindShopsByTagNameGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListRpaShopInfo>('/api/rpa/task/findShopsByTagName', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 按照bizCode强制结束所有任务 PUT /api/rpa/task/forceEndByBizCode */
export async function rpaTaskForceEndByBizCodePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskForceEndByBizCodePutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/rpa/task/forceEndByBizCode', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 生成鼠标移动轨迹 GET /api/rpa/task/geneMouseTracks */
export async function rpaTaskGeneMouseTracksGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskGeneMouseTracksGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTrack>('/api/rpa/task/geneMouseTracks', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 根据bizCode(+shopId)获取最后一条taskItem GET /api/rpa/task/getLatestTaskItem */
export async function rpaTaskGetLatestTaskItemGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskGetLatestTaskItemGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultRpaTaskItemVo>('/api/rpa/task/getLatestTaskItem', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取webrtc stun服务器信息 GET /api/rpa/task/getStunInfo */
export async function rpaTaskGetStunInfoGet(options?: { [key: string]: any }) {
  return request<API.WebResultStunInfo>('/api/rpa/task/getStunInfo', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 任务心跳(旧版本客户端才会用到) PUT /api/rpa/task/heartbeat/${param0} */
export async function rpaTaskHeartbeatByRpaTaskIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskHeartbeatByRpaTaskIdPutParams,
  options?: { [key: string]: any },
) {
  const { rpaTaskId: param0, ...queryParams } = params;
  return request<API.WebResultstring>(`/api/rpa/task/heartbeat/${param0}`, {
    method: 'PUT',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 任务心跳 PUT /api/rpa/task/heartbeat/v2/${param0} */
export async function rpaTaskHeartbeatV2ByRpaTaskIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskHeartbeatV2ByRpaTaskIdPutParams,
  body: API.RpaTaskHeartbeatRequest,
  options?: { [key: string]: any },
) {
  const { rpaTaskId: param0, ...queryParams } = params;
  return request<API.WebResultstring>(`/api/rpa/task/heartbeat/v2/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 获取某个item的文件列表 GET /api/rpa/task/item/${param0}/files */
export async function rpaTaskItemByRpaTaskItemIdFilesGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskItemByRpaTaskItemIdFilesGetParams,
  options?: { [key: string]: any },
) {
  const { rpaTaskItemId: param0, ...queryParams } = params;
  return request<API.WebResultListRpaTaskFileVo>(`/api/rpa/task/item/${param0}/files`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取某个item的log文件信息(因为日志文件记录是标记执行的时候就生成了) GET /api/rpa/task/item/${param0}/getLogFileInfo */
export async function rpaTaskItemByRpaTaskItemIdGetLogFileInfoGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskItemByRpaTaskItemIdGetLogFileInfoGetParams,
  options?: { [key: string]: any },
) {
  const { rpaTaskItemId: param0, ...queryParams } = params;
  return request<API.WebResultRpaTaskFileVo>(`/api/rpa/task/item/${param0}/getLogFileInfo`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取某个账号对应账号的密码 GET /api/rpa/task/item/${param0}/passwords */
export async function rpaTaskItemByShopIdPasswordsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskItemByShopIdPasswordsGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultListRpaShopPasswordVo>(`/api/rpa/task/item/${param0}/passwords`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取某个item的执行过的nodes GET /api/rpa/task/item/${param0}/ranNodes */
export async function rpaTaskItemByRpaTaskItemIdRanNodesGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskItemByRpaTaskItemIdRanNodesGetParams,
  options?: { [key: string]: any },
) {
  const { rpaTaskItemId: param0, ...queryParams } = params;
  return request<API.WebResultListRpaTaskNodeVo>(`/api/rpa/task/item/${param0}/ranNodes`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 往某个item日志上追加内容，直接写oss的，要控制调用频率 PUT /api/rpa/task/item/appendLog */
export async function rpaTaskItemAppendLogPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskItemAppendLogPutParams,
  body: string,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/rpa/task/item/appendLog', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页获取task列表 GET /api/rpa/task/list */
export async function rpaTaskListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultRpaTaskVo>('/api/rpa/task/list', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 执行一个流程task，在发起执行的机器上调用该接口 POST /api/rpa/task/manualTriggerTask */
export async function rpaTaskManualTriggerTaskPost(
  body: API.NewRpaTaskRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultRpaTaskVo>('/api/rpa/task/manualTriggerTask', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 标记某个item取消执行 POST /api/rpa/task/markItemCancelled */
export async function rpaTaskMarkItemCancelledPost(
  body: API.MarkRpaItemCancelledRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultRpaTaskItemVo>('/api/rpa/task/markItemCancelled', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 标记某个item执行结束 POST /api/rpa/task/markItemEnd */
export async function rpaTaskMarkItemEndPost(
  body: API.MarkRpaItemEndRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultRpaTaskItemVo>('/api/rpa/task/markItemEnd', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 通知客户端所有的账号items都已经完成了 POST /api/rpa/task/notifyAllItemsEnd */
export async function rpaTaskNotifyAllItemsEndPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskNotifyAllItemsEndPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/rpa/task/notifyAllItemsEnd', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 为了避免歧义，请改用 /notifyAllItemsEnd，本接口保留只为了兼容旧客户端 POST /api/rpa/task/notifyAllShopsEnd */
export async function rpaTaskNotifyAllShopsEndPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskNotifyAllShopsEndPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/rpa/task/notifyAllShopsEnd', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取用于用户自定义邮箱服务器发邮件的邮箱列表 POST /api/rpa/task/notifyEmails */
export async function rpaTaskNotifyEmailsPost(
  body: API.SendNotifyUsers,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListstring>('/api/rpa/task/notifyEmails', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 通知服务器记录上传了一个文件 PUT /api/rpa/task/notifyFileAdded */
export async function rpaTaskNotifyFileAddedPut(
  body: API.NotifyFileAddedRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultRpaTaskFileVo>('/api/rpa/task/notifyFileAdded', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 文件操作后通知服务器记录文件的添加 PUT /api/rpa/task/notifyOssFileAdded */
export async function rpaTaskNotifyOssFileAddedPut(
  body: API.NotifyOssFileAddedRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/rpa/task/notifyOssFileAdded', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 将相应的店铺id解析出来 GET /api/rpa/task/parseToShopIds */
export async function rpaTaskParseToShopIdsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskParseToShopIdsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListlong>('/api/rpa/task/parseToShopIds', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 汇报rpa打开了一个会话 POST /api/rpa/task/reportOpenShopSession */
export async function rpaTaskReportOpenShopSessionPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskReportOpenShopSessionPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/rpa/task/reportOpenShopSession', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 开始执行一个流程item POST /api/rpa/task/runItem */
export async function rpaTaskRunItemPost(
  body: API.RunTaskItemRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultRpaTaskItemVo>('/api/rpa/task/runItem', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建一个流程task，在真实执行机器上调用该接口 POST /api/rpa/task/runTask */
export async function rpaTaskRunTaskPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskRunTaskPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultRpaTaskVo>('/api/rpa/task/runTask', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 发送消息节点 POST /api/rpa/task/sendNotify */
export async function rpaTaskSendNotifyPost(
  body: API.SendNotifyRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultSendNotifyResponse>('/api/rpa/task/sendNotify', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 为某个网盘文件生成签名读取链接 GET /api/rpa/task/signDiskFile */
export async function rpaTaskSignDiskFileGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskSignDiskFileGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/rpa/task/signDiskFile', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 为某个rpa task file生成签名读取链接 GET /api/rpa/task/signFile/${param0}/rpaTaskFileId */
export async function rpaTaskSignFileByRpaTaskItemIdRpaTaskFileIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskSignFileByRpaTaskItemIdRpaTaskFileIdGetParams,
  options?: { [key: string]: any },
) {
  const { rpaTaskItemId: param0, rpaTaskFileId: param1, ...queryParams } = params;
  return request<API.WebResultstring>(`/api/rpa/task/signFile/${param0}/rpaTaskFileId`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 直接通过bizCode执行一个流程，但不生成rpa_task记录，用于执行一些小动作如打开手机后直接切换到某个tk账号 POST /api/rpa/task/silentTriggerFlowByBizCode */
export async function rpaTaskSilentTriggerFlowByBizCodePost(
  body: API.TriggerFlowByBizCodeRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/rpa/task/silentTriggerFlowByBizCode', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 在网盘和rpa工作目录之间同步文件。 PUT /api/rpa/task/syncFiles */
export async function rpaTaskSyncFilesPut(
  body: API.SyncFilesRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/rpa/task/syncFiles', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
