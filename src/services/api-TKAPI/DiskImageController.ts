// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 通用图片存储接口 POST /api/tk/image/upload */
export async function tkImageUploadPost(
  body: API.UploadGeneralImageRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tk/image/upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
