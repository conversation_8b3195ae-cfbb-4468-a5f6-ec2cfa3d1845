// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 激活子团队 POST /api/gh/subTeam */
export async function ghSubTeamPost(body: API.KolSubTeamVo, options?: { [key: string]: any }) {
  return request<API.WebResultKolSubTeamDto>('/api/gh/subTeam', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改子团队 PUT /api/gh/subTeam/${param0} */
export async function ghSubTeamBySubTeamIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSubTeamBySubTeamIdPutParams,
  body: API.KolUpdateSubTeamRequest,
  options?: { [key: string]: any },
) {
  const { subTeamId: param0, ...queryParams } = params;
  return request<API.WebResultKolSubTeamDto>(`/api/gh/subTeam/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 修改子团队有效性 PUT /api/gh/subTeam/${param0}/valid */
export async function ghSubTeamBySubTeamIdValidPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSubTeamBySubTeamIdValidPutParams,
  options?: { [key: string]: any },
) {
  const { subTeamId: param0, ...queryParams } = params;
  return request<API.WebResultKolSubTeamDto>(`/api/gh/subTeam/${param0}/valid`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 查询子团队 GET /api/gh/subTeam/page */
export async function ghSubTeamPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSubTeamPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultKolSubTeamDetailVo>('/api/gh/subTeam/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
