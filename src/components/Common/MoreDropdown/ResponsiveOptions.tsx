import styled from 'styled-components';
import type { ReactNode } from 'react';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Tooltip, Dropdown } from 'antd';
import IconFontIcon from '@/components/Common/IconFontIcon';
import colors from '@/style/color.less';
import { debounce } from 'lodash';

const StyledContainer = styled.div<{ gap?: number }>`
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  gap: ${(props) => props.gap || 8}px;
`;

export const ResponsiveOptions = (props: {
  itemWidth: number;
  moreBtnWidth: number;
  gap: number;
  items: {
    key: string;
    label: ReactNode;
    tooltip?: ReactNode;
    icon: ReactNode;
    onClick: (e: React.MouseEvent) => void;
  }[];
}) => {
  const { itemWidth, gap, items, moreBtnWidth = 30 } = props;
  const [ellipsis, setEllipsis] = useState(false);
  const ref = useRef<HTMLDivElement>();
  const [maxCount, setMaxCount] = useState(items.length);

  useEffect(() => {
    const observer = new ResizeObserver(
      debounce((entries) => {
        const containerWidth = entries[0].contentRect.width;
        // 计算所有项目完全显示需要的总宽度
        const totalItemsWidth = items.length * itemWidth + (items.length - 1) * gap;
        // 判断是否溢出
        if (totalItemsWidth <= containerWidth) {
          // 不溢出，显示所有项目
          setEllipsis(false);
          setMaxCount(items.length);
        } else {
          // 溢出，需要显示"更多"按钮
          setEllipsis(true);
          // 计算可用于显示项目的宽度（减去"更多"按钮和其前面的gap）
          const availableWidth = containerWidth - moreBtnWidth - gap;
          // 计算能显示的项目数量
          // availableWidth = n * itemWidth + (n-1) * gap
          // 解得：n = (availableWidth + gap) / (itemWidth + gap)
          const _count = Math.floor((availableWidth + gap) / (itemWidth + gap));
          // 确保至少为0，最多为总项目数-1（因为有"更多"按钮）
          setMaxCount(Math.max(0, Math.min(_count, items.length - 1)));
        }
      }, 100),
    );
    if (ref.current) {
      observer.observe(ref.current);
    }
    return () => {
      observer.disconnect();
    };
  }, [itemWidth, gap, moreBtnWidth, items.length]);
  const nodes = useMemo(() => {
    // 确定要显示的选项数量
    const displayCount = ellipsis ? maxCount : items.length;

    // 获取要显示的选项
    const displayItems = items.slice(0, displayCount);

    return displayItems.map((item) => (
      <Tooltip key={item.key} title={item.tooltip || item.label} placement="top">
        <div
          style={{
            display: 'inline-block',
            cursor: 'pointer',
            color: colors.primaryColor,
          }}
          onClick={item.onClick}
        >
          {item.icon}
        </div>
      </Tooltip>
    ));
  }, [items, maxCount, ellipsis]);
  const more = useMemo(() => {
    // 如果没有溢出，不显示更多按钮
    if (!ellipsis || maxCount >= items.length) {
      return null;
    }

    // 获取溢出的选项（从 maxCount 开始到结尾）
    const overflowItems = items.slice(maxCount);

    // 构建 Dropdown 的菜单项
    const menuItems = overflowItems.map((item) => ({
      key: item.key,
      label: (
        <div
          style={{
            display: 'inline-flex',
            gap: 4,
            alignItems: 'center',
            color: colors.primaryColor,
          }}
        >
          {item.icon}
          {item.label}
        </div>
      ),
      onClick: item.onClick,
    }));

    return (
      <Dropdown menu={{ items: menuItems }} trigger={['hover']}>
        <span
          style={{
            display: 'inline-block',
            cursor: 'pointer',
            color: colors.primaryColor,
          }}
        >
          <IconFontIcon iconName="gengduo_24" />
        </span>
      </Dropdown>
    );
  }, [items, maxCount, ellipsis]);

  return (
    <div style={{ overflow: 'hidden' }} ref={ref}>
      <StyledContainer>
        {nodes}
        {more}
      </StyledContainer>
    </div>
  );
};
