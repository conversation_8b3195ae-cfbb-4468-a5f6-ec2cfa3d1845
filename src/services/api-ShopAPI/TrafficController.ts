// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 汇报流量 注意，仅在tunnelType=transit和jump时不需要汇报，其他情况都汇报 POST /api/traffic/byChannelSession/${param0} */
export async function trafficByChannelSessionByChannelSessionIdPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.trafficByChannelSessionByChannelSessionIdPostParams,
  options?: { [key: string]: any },
) {
  const { channelSessionId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/traffic/byChannelSession/${param0}`, {
    method: 'POST',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取IP剩余流量概览 GET /api/traffic/ipTrafficRemainSummary */
export async function trafficIpTrafficRemainSummaryGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.trafficIpTrafficRemainSummaryGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultIpTrafficVo>('/api/traffic/ipTrafficRemainSummary', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
