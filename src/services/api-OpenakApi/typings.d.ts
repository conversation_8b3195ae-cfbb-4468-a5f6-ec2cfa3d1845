declare namespace API {
  type openakAkByAkIdGetParams = {
    /** akId */
    akId: number;
  };

  type openakAkByAkIdStatusPutParams = {
    /** akId */
    akId: number;
    /** status */
    status: 'Deleted' | 'Disabled' | 'Enabled';
  };

  type openakAkPageGetParams = {
    /** creator */
    creator?: number;
    /** userId */
    userId?: number;
    /** id */
    id?: number;
    /** accessKeyId */
    accessKeyId?: string;
    /** status */
    status?: 'Deleted' | 'Disabled' | 'Enabled';
    /** createTimeFrom */
    createTimeFrom?: string;
    /** createTimeTo */
    createTimeTo?: string;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type openakAkPostParams = {
    /** userId */
    userId: number;
  };

  type openakOpenapiOpenPutParams = {
    /** enabled */
    enabled: boolean;
  };

  type OpenapiAkDto = {
    accessKeyId?: string;
    accessKeySecret?: string;
    akType?: 'Global' | 'Partner' | 'PortalTeamMember';
    createTime?: string;
    creator?: number;
    id?: number;
    status?: 'Deleted' | 'Disabled' | 'Enabled';
    teamId?: number;
    updateTime?: string;
    userId?: number;
  };

  type PageResultOpenapiAkDto = {
    current?: number;
    list?: OpenapiAkDto[];
    pageSize?: number;
    total?: number;
  };

  type TeamOpenapiDto = {
    enabled?: boolean;
    id?: number;
    rate?: number;
    rateControl?: boolean;
    rateType?: 'day' | 'hour' | 'minute' | 'month' | 'second' | 'week' | 'year';
  };

  type WebResult = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultlong = {
    code?: number;
    data?: number;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultOpenapiAkDto = {
    code?: number;
    data?: OpenapiAkDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultOpenapiAkDto = {
    code?: number;
    data?: PageResultOpenapiAkDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTeamOpenapiDto = {
    code?: number;
    data?: TeamOpenapiDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };
}
