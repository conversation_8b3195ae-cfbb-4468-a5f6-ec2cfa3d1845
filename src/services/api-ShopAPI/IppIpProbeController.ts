// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 探测IP池IP与中转之间的连接情况（返回remoteIp） GET /api/poolip/${param0}/probe */
export async function poolipByIppIpIdProbeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.poolipByIppIpIdProbeGetParams,
  options?: { [key: string]: any },
) {
  const { ippIpId: param0, ...queryParams } = params;
  return request<API.WebResultProxyProbeWithRemoteIp>(`/api/poolip/${param0}/probe`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}
