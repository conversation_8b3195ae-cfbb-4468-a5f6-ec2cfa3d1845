declare namespace API {
  type CreditConfig = {
    initPresentAmount?: number;
    inviteUserAmount?: number;
    phoneRewardedAmount?: number;
    promotedPresentAmount?: number;
    sessionAbortRemainCredit?: number;
    sessionAlertRemainCredit?: number;
    speedLimit1?: number;
    speedLimit2?: number;
    wechatRewardedAmount?: number;
  };

  type DiscountsVo = {
    /** 赠送数量或折扣百分比或阶梯折扣百分比 */
    amount?: number;
    /** 打折code */
    discountCode?: string;
    /** 打折还是赠送 */
    discountType?: 'Discount' | 'LadderPrice' | 'Present';
    /** 周期或数量单位 */
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    /** 备注 */
    remarks?: string;
    /** 期数或数量 */
    threshold?: number;
  };

  type goodsByGoodsIdCheckStockGetParams = {
    /** goodsId */
    goodsId: number;
  };

  type GoodsDto = {
    arch?: string;
    bandwidth?: number;
    buyoutPrice?: number;
    city?: string;
    cost?: number;
    countryCode?: string;
    cpu?: number;
    currency?: 'CREDIT' | 'RMB' | 'USD';
    dayCost?: number;
    dayPrice?: number;
    dayTraffic?: number;
    description?: string;
    disk?: number;
    diskCategory?: string;
    dynamic?: boolean;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    initialQuantity?: number;
    instanceType?: string;
    ipv6?: boolean;
    listPrice?: number;
    mem?: number;
    name?: string;
    networkType?: 'cloudIdc' | 'mobile' | 'proxyIdc' | 'residential' | 'unknown' | 'unknownIdc';
    onSale?: 'Disabled' | 'Offline' | 'Online';
    perfLevel?:
      | 'CostEffective'
      | 'HighlyConcurrent'
      | 'LargeTraffic'
      | 'None'
      | 'RemoteLogin'
      | 'UnlimitedTraffic';
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    pipeType?: 'None' | 'Proxy' | 'Tunnel' | 'TunnelFailToProxy';
    platform?: 'android' | 'linux' | 'macos' | 'unknown' | 'web' | 'windows' | 'windows7';
    price?: number;
    provider?: string;
    region?: string;
    remoteLogin?: boolean;
    resourceId?: number;
    sortNo?: number;
    tcpfp?: boolean;
    traffic?: number;
    trafficCurrency?: 'CREDIT' | 'RMB' | 'USD';
    trafficPrice?: number;
    updateTime?: string;
    weekCost?: number;
    weekPrice?: number;
    weekTraffic?: number;
  };

  type goodsIpCountrywideConfigPutParams = {
    countryCodes?: string[];
  };

  type goodsIpRegionsByFilterPostParams = {
    /** category */
    category?: 'daily' | 'manyAccount' | 'rdp' | 'sensitive';
  };

  type goodsIpRegionsGetParams = {
    /** 可以查询多种商品类型，用逗号连接 */
    goodsType?: string;
    /** 可以查询多种网络类型，用逗号连接 */
    networkType?: string;
    /** 是否动态 */
    dynamic?: boolean;
  };

  type GoodsPriceVo = {
    creditConfig?: CreditConfig;
    ipGoodsPriceRegions?: IpGoodsPriceRegionVo[];
    rpaExecute?: GoodsDto;
    rpaMobile?: GoodsWithDiscountDto;
    rpaVouchers?: GoodsDto[];
    shop?: GoodsWithDiscountDto;
    storage?: GoodsWithDiscountDto;
    teamMember?: GoodsWithDiscountDto;
    teamMobile?: GoodsWithDiscountDto;
    transitTraffic?: GoodsDto;
  };

  type GoodsProviderDto = {
    description?: string;
    homeUrl?: string;
    id?: number;
    name?: string;
    pipeType?: 'None' | 'Proxy' | 'Tunnel' | 'TunnelFailToProxy';
    promoUrl?: string;
    provider?: string;
    rangeType?: 'Domestic' | 'Global' | 'Oversea';
    sticky?: boolean;
  };

  type goodsProviderInfoPutParams = {
    /** provider */
    provider: string;
    /** description */
    description: string;
    /** homeUrl */
    homeUrl?: string;
    /** promoUrl */
    promoUrl?: string;
    /** rangeType */
    rangeType?: 'Domestic' | 'Global' | 'Oversea';
  };

  type GoodsUsabilityVo = {
    arch?: string;
    bandwidth?: number;
    buyoutPrice?: number;
    city?: string;
    cost?: number;
    countryCode?: string;
    cpu?: number;
    currency?: 'CREDIT' | 'RMB' | 'USD';
    dayCost?: number;
    dayPrice?: number;
    dayTraffic?: number;
    description?: string;
    disk?: number;
    diskCategory?: string;
    dynamic?: boolean;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    initialQuantity?: number;
    instanceType?: string;
    ipv6?: boolean;
    listPrice?: number;
    mem?: number;
    name?: string;
    networkType?: 'cloudIdc' | 'mobile' | 'proxyIdc' | 'residential' | 'unknown' | 'unknownIdc';
    onSale?: 'Disabled' | 'Offline' | 'Online';
    perfLevel?:
      | 'CostEffective'
      | 'HighlyConcurrent'
      | 'LargeTraffic'
      | 'None'
      | 'RemoteLogin'
      | 'UnlimitedTraffic';
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    pipeType?: 'None' | 'Proxy' | 'Tunnel' | 'TunnelFailToProxy';
    platform?: 'android' | 'linux' | 'macos' | 'unknown' | 'web' | 'windows' | 'windows7';
    platformUsabilityList?: PlatformUsabilityVo[];
    price?: number;
    provider?: string;
    region?: string;
    remoteLogin?: boolean;
    resourceId?: number;
    sortNo?: number;
    tcpfp?: boolean;
    traffic?: number;
    trafficCurrency?: 'CREDIT' | 'RMB' | 'USD';
    trafficPrice?: number;
    updateTime?: string;
    weekCost?: number;
    weekPrice?: number;
    weekTraffic?: number;
  };

  type GoodsWithDiscountDto = {
    arch?: string;
    bandwidth?: number;
    buyoutPrice?: number;
    city?: string;
    cost?: number;
    countryCode?: string;
    cpu?: number;
    currency?: 'CREDIT' | 'RMB' | 'USD';
    dayCost?: number;
    dayPrice?: number;
    dayTraffic?: number;
    description?: string;
    discounts?: DiscountsVo[];
    disk?: number;
    diskCategory?: string;
    dynamic?: boolean;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    initialQuantity?: number;
    instanceType?: string;
    ipv6?: boolean;
    listPrice?: number;
    mem?: number;
    name?: string;
    networkType?: 'cloudIdc' | 'mobile' | 'proxyIdc' | 'residential' | 'unknown' | 'unknownIdc';
    onSale?: 'Disabled' | 'Offline' | 'Online';
    perfLevel?:
      | 'CostEffective'
      | 'HighlyConcurrent'
      | 'LargeTraffic'
      | 'None'
      | 'RemoteLogin'
      | 'UnlimitedTraffic';
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    pipeType?: 'None' | 'Proxy' | 'Tunnel' | 'TunnelFailToProxy';
    platform?: 'android' | 'linux' | 'macos' | 'unknown' | 'web' | 'windows' | 'windows7';
    price?: number;
    provider?: string;
    region?: string;
    remoteLogin?: boolean;
    resourceId?: number;
    sortNo?: number;
    tcpfp?: boolean;
    traffic?: number;
    trafficCurrency?: 'CREDIT' | 'RMB' | 'USD';
    trafficPrice?: number;
    updateTime?: string;
    weekCost?: number;
    weekPrice?: number;
    weekTraffic?: number;
  };

  type IpGoodsCountrywideConfig = {
    countryCodes?: string[];
  };

  type IpGoodsFilterVo = {
    city?: string;
    countryCode?: string;
    dynamic?: boolean;
    excludedCity?: string;
    goodsTypes?: (
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher'
    )[];
    ipv6?: boolean;
    networkTypes?: (
      | 'cloudIdc'
      | 'mobile'
      | 'proxyIdc'
      | 'residential'
      | 'unknown'
      | 'unknownIdc'
    )[];
    perfLevels?: (
      | 'CostEffective'
      | 'HighlyConcurrent'
      | 'LargeTraffic'
      | 'None'
      | 'RemoteLogin'
      | 'UnlimitedTraffic'
    )[];
    platforms?: ('android' | 'linux' | 'macos' | 'unknown' | 'web' | 'windows' | 'windows7')[];
    priceRanges?: PriceRangeVo[];
    provider?: string;
    region?: string;
  };

  type IpGoodsPriceItem = {
    monthPrice?: number;
    networkType?: 'cloudIdc' | 'mobile' | 'proxyIdc' | 'residential' | 'unknown' | 'unknownIdc';
    weekPrice?: number;
  };

  type IpGoodsPriceRegionVo = {
    /** IP区域 */
    area?: '中东' | '中国' | '亚太' | '北美' | '南极洲' | '南美' | '欧洲' | '非洲';
    areaEn?: string;
    /** 城市 */
    city?: string;
    cityEn?: string;
    /** 国家 */
    country?: string;
    /** 国家代码 */
    countryCode?: string;
    /** 国家英文 */
    countryEn?: string;
    goodsList?: IpGoodsPriceItem[];
    /** 排序字段 */
    orderNo?: number;
  };

  type IpRegionVo = {
    /** IP区域 */
    area?: '中东' | '中国' | '亚太' | '北美' | '南极洲' | '南美' | '欧洲' | '非洲';
    areaEn?: string;
    /** 城市 */
    city?: string;
    cityEn?: string;
    /** 国家 */
    country?: string;
    /** 国家代码 */
    countryCode?: string;
    /** 国家英文 */
    countryEn?: string;
    /** 当前区域商品列表 */
    goods?: GoodsUsabilityVo[];
    /** 种类 */
    importType?: 'Platform' | 'User';
    /** 排序字段 */
    orderNo?: number;
    /** 供应商 */
    provider?:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    /** 区域ID */
    region?: string;
  };

  type PlatformUsabilityVo = {
    area?:
      | 'Argentina'
      | 'Australia'
      | 'Austria'
      | 'Belarus'
      | 'Belgium'
      | 'Bolivia'
      | 'Brazil'
      | 'Canada'
      | 'Chile'
      | 'China'
      | 'Colombia'
      | 'Costa_Rica'
      | 'Dominican'
      | 'Ecuador'
      | 'Egypt'
      | 'France'
      | 'Germany'
      | 'Global'
      | 'Guatemala'
      | 'Honduras'
      | 'HongKong'
      | 'India'
      | 'Indonesia'
      | 'Ireland'
      | 'Israel'
      | 'Italy'
      | 'Japan'
      | 'Kazakhstan'
      | 'Korea'
      | 'Malaysia'
      | 'Mexico'
      | 'Netherlands'
      | 'Nicaragua'
      | 'Panama'
      | 'Paraguay'
      | 'Peru'
      | 'Philippines'
      | 'Poland'
      | 'Portuguese'
      | 'Puerto_Rico'
      | 'Russia'
      | 'Salvador'
      | 'Saudi_Arabia'
      | 'Singapore'
      | 'Spain'
      | 'Sweden'
      | 'Switzerland'
      | 'Taiwan'
      | 'Thailand'
      | 'Turkey'
      | 'United_Arab_Emirates'
      | 'United_Kingdom'
      | 'United_States'
      | 'Uruguay'
      | 'Venezuela'
      | 'Vietnam';
    platformType?: string;
    usable?: boolean;
  };

  type PriceRangeVo = {
    from?: number;
    period?: 'Month' | 'Week';
    to?: number;
  };

  type RegionVo = {
    /** IP区域 */
    area?: '中东' | '中国' | '亚太' | '北美' | '南极洲' | '南美' | '欧洲' | '非洲';
    areaEn?: string;
    /** 城市 */
    city?: string;
    cityEn?: string;
    /** 国家 */
    country?: string;
    /** 国家代码 */
    countryCode?: string;
    /** 国家英文 */
    countryEn?: string;
    /** 排序字段 */
    orderNo?: number;
  };

  type webhookIpRegionsByFilterPostParams = {
    /** category */
    category?: 'daily' | 'manyAccount' | 'rdp' | 'sensitive';
  };

  type WebResult = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultCreditConfig = {
    code?: number;
    data?: CreditConfig;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultGoodsPriceVo = {
    code?: number;
    data?: GoodsPriceVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultIpGoodsCountrywideConfig = {
    code?: number;
    data?: IpGoodsCountrywideConfig;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListGoodsProviderDto = {
    code?: number;
    data?: GoodsProviderDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListIpRegionVo = {
    code?: number;
    data?: IpRegionVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListRegionVo = {
    code?: number;
    data?: RegionVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };
}
