// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 校验验证码 GET /api/captcha/check */
export async function captchaCheckGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.captchaCheckGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/captcha/check', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取captcha图片 GET /api/captcha/get */
export async function captchaGetGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.captchaGetGetParams,
  options?: { [key: string]: any },
) {
  return request<API.lang>('/api/captcha/get', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取captcha图片（base64格式） GET /api/captcha/getBase64 */
export async function captchaGetBase64Get(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.captchaGetBase64GetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCaptchaBase64Vo>('/api/captcha/getBase64', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取一个captcha token GET /api/captcha/token */
export async function captchaTokenGet(options?: { [key: string]: any }) {
  return request<API.WebResultCaptchaTokenVo>('/api/captcha/token', {
    method: 'GET',
    ...(options || {}),
  });
}
