// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 激活一张礼品卡 POST /api/payment/gift-card/activeAGiftCard */
export async function paymentGiftCardActiveAGiftCardPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.paymentGiftCardActiveAGiftCardPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultGiftCardPackItemVo>('/api/payment/gift-card/activeAGiftCard', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
