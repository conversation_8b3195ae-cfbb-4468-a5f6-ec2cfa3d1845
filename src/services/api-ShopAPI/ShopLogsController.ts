// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取一个ip流量扣花瓣相关的流量详情 GET /api/logs/findCreditTrafficLog */
export async function logsFindCreditTrafficLogGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.logsFindCreditTrafficLogGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultOperateLogsVo>('/api/logs/findCreditTrafficLog', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询操作日志 GET /api/logs/findOperateLogs */
export async function logsFindOperateLogsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.logsFindOperateLogsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultOperateLogsVo>('/api/logs/findOperateLogs', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取一个会话的流量详情 GET /api/logs/findSessionTrafficDetail */
export async function logsFindSessionTrafficDetailGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.logsFindSessionTrafficDetailGetParams,
  options?: { [key: string]: any },
) {
  return request<API.id>('/api/logs/findSessionTrafficDetail', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查看历史指纹配置详情 GET /api/logs/fingerConfig/${param0} */
export async function logsFingerConfigByConfigIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.logsFingerConfigByConfigIdGetParams,
  options?: { [key: string]: any },
) {
  const { configId: param0, ...queryParams } = params;
  return request<API.WebResultFingerprintConfigVo>(`/api/logs/fingerConfig/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 查询团队店铺访问日志 GET /api/logs/shopSessions */
export async function logsShopSessionsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.logsShopSessionsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultShopSessionLogVo>('/api/logs/shopSessions', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
