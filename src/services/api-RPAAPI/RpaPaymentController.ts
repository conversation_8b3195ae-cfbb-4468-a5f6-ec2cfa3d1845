// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 计算购买rpa包时券价格 GET /api/payment/calcBuyRpaVoucherPrice */
export async function paymentCalcBuyRpaVoucherPriceGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.paymentCalcBuyRpaVoucherPriceGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCalcPriceResponse>('/api/payment/calcBuyRpaVoucherPrice', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 计算续费rpa包时卡价格 GET /api/payment/calcRenewRpaVoucherPrice */
export async function paymentCalcRenewRpaVoucherPriceGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.paymentCalcRenewRpaVoucherPriceGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCalcRenewRpaVoucherResponse>(
    '/api/payment/calcRenewRpaVoucherPrice',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}
