// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 批量分配【按查询】 POST /api/gh/allocateByQuery */
export async function ghAllocateByQueryPost(
  body: API.AllocateTkCreatorByQueryRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/allocateByQuery', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量收藏或取消收藏 POST /api/gh/favoriteByQuery */
export async function ghFavoriteByQueryPost(
  body: API.FavoriteTkCreatorByQueryRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/favoriteByQuery', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 导入工会达人 POST /api/gh/import */
export async function ghImportPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghImportPostParams,
  body: API.GhCreatorImportRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListlong>('/api/gh/import', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** Excel导入达人 POST /api/gh/importExcel/${param0} */
export async function ghImportExcelByScenePost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghImportExcelByScenePostParams,
  body: {
    /** tags */
    tags?: string;
  },
  file?: File,
  options?: { [key: string]: any },
) {
  const { scene: param0, ...queryParams } = params;
  const formData = new FormData();

  if (file) {
    formData.append('file', file);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as any)[ele];

    if (item !== undefined && item !== null) {
      if (typeof item === 'object' && !(item instanceof File)) {
        if (item instanceof Array) {
          item.forEach((f) => formData.append(ele, f || ''));
        } else {
          formData.append(ele, JSON.stringify(item));
        }
      } else {
        formData.append(ele, item);
      }
    }
  });

  return request<API.WebResultListlong>(`/api/gh/importExcel/${param0}`, {
    method: 'POST',
    params: { ...queryParams },
    data: formData,
    requestType: 'form',
    ...(options || {}),
  });
}

/** 批量取消分配 POST /api/gh/releaseByQuery */
export async function ghReleaseByQueryPost(
  body: API.TkCreatorByQueryRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/releaseByQuery', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 绩效统计(3类用户相加） GET /api/gh/statPerformance */
export async function ghStatPerformanceGet(options?: { [key: string]: any }) {
  return request<API.WebResultListGhCreatorPerformanceStatVo>('/api/gh/statPerformance', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 数据统计 GET /api/gh/statRows */
export async function ghStatRowsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghStatRowsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListGhStatRowVo>('/api/gh/statRows', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 给指定达人打标签 POST /api/gh/tag */
export async function ghTagPost(body: API.GhTagByHandleRequest, options?: { [key: string]: any }) {
  return request<API.WebResult>('/api/gh/tag', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量打标签或取消标签 POST /api/gh/tagByQuery */
export async function ghTagByQueryPost(
  body: API.TagTkCreatorByQueryRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/tagByQuery', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 取消指定达人的特定标签 POST /api/gh/untag */
export async function ghUntagPost(
  body: API.GhTagByHandleRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/untag', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询达人详情 GET /api/gh/user/${param0} */
export async function ghUserByCreatorIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghUserByCreatorIdGetParams,
  options?: { [key: string]: any },
) {
  const { creatorId: param0, ...queryParams } = params;
  return request<API.WebResultGhUserDetailVo>(`/api/gh/user/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 认领达人 PUT /api/gh/user/acquire */
export async function ghUserAcquirePut(
  body: API.GhCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/user/acquire', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分配达人 PUT /api/gh/user/allocate */
export async function ghUserAllocatePut(
  body: API.GhCreatorAllocateRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/user/allocate', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取TK达人头像 GET /api/gh/user/avatar */
export async function ghUserAvatarGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghUserAvatarGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/gh/user/avatar', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 取消分配 PUT /api/gh/user/cancelAllocate */
export async function ghUserCancelAllocatePut(
  body: API.GhCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/user/cancelAllocate', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 统计团队达人的数量 GET /api/gh/user/count */
export async function ghUserCountGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghUserCountGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultlong>('/api/gh/user/count', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 删除达人 POST /api/gh/user/delete */
export async function ghUserDeletePost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghUserDeletePostParams,
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/user/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除达人（按查询） POST /api/gh/user/deleteByQuery */
export async function ghUserDeleteByQueryPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghUserDeleteByQueryPostParams,
  body: API.FindGhUserRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/user/deleteByQuery', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 达人可导出的字段元信息 GET /api/gh/user/fields */
export async function ghUserFieldsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTkCreatorFiledVo>('/api/gh/user/fields', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 分页查询团队达人V2（不返回总数） GET /api/gh/user/page */
export async function ghUserPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghUserPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultGhUserDetailVo>('/api/gh/user/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询我认领的达人 GET /api/gh/user/pageResponsible */
export async function ghUserPageResponsibleGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghUserPageResponsibleGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultGhUserDetailVo>('/api/gh/user/pageResponsible', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 恢复达人 POST /api/gh/user/recover */
export async function ghUserRecoverPost(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/user/recover', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取TK达人区域 GET /api/gh/user/regions */
export async function ghUserRegionsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListstring>('/api/gh/user/regions', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 取消认领达人 PUT /api/gh/user/release */
export async function ghUserReleasePut(
  body: API.GhCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/user/release', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** getResponsibleUserList GET /api/gh/user/responsibleUserList */
export async function ghUserResponsibleUserListGet(options?: { [key: string]: any }) {
  return request<API.WebResultListlong>('/api/gh/user/responsibleUserList', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 同步TK达人头像 POST /api/gh/user/syncAvatar */
export async function ghUserSyncAvatarPost(
  body: API.SyncCreatorAvatarRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/user/syncAvatar', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量同步（创建）TK达人 POST /api/gh/user/syncBatch */
export async function ghUserSyncBatchPost(
  body: API.GhUserDocumentBatch,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListGhUserDto>('/api/gh/user/syncBatch', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量更新达人是否关注后私信 PUT /api/gh/user/updateMessageStatus */
export async function ghUserUpdateMessageStatusPut(
  body: API.GhCreatorUpdateMessageStatusRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/user/updateMessageStatus', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量更新达人状态 PUT /api/gh/user/updateStatus */
export async function ghUserUpdateStatusPut(
  body: API.GhCreatorUpdateStatusRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListGhCreatorDto>('/api/gh/user/updateStatus', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 下载达人信息 GET /api/gh/v2/download/${param0} */
export async function ghV2DownloadByTokenGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghV2DownloadByTokenGetParams,
  options?: { [key: string]: any },
) {
  const { token: param0, ...queryParams } = params;
  return request<API.Resource>(`/api/gh/v2/download/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 下载达人信息【按查询】 GET /api/gh/v2/downloadByQuery/${param0} */
export async function ghV2DownloadByQueryByTokenGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghV2DownloadByQueryByTokenGetParams,
  options?: { [key: string]: any },
) {
  const { token: param0, ...queryParams } = params;
  return request<API.Resource>(`/api/gh/v2/downloadByQuery/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 下载达人handle信息 GET /api/gh/v2/exportHandle/${param0} */
export async function ghV2ExportHandleByTokenGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghV2ExportHandleByTokenGetParams,
  options?: { [key: string]: any },
) {
  const { token: param0, ...queryParams } = params;
  return request<API.WebResultstring>(`/api/gh/v2/exportHandle/${param0}`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 下载达人handle信息【按查询】 GET /api/gh/v2/exportHandleByQuery/${param0} */
export async function ghV2ExportHandleByQueryByTokenGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghV2ExportHandleByQueryByTokenGetParams,
  options?: { [key: string]: any },
) {
  const { token: param0, ...queryParams } = params;
  return request<API.WebResultstring>(`/api/gh/v2/exportHandleByQuery/${param0}`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 下载达人的token POST /api/gh/v2/exportToken */
export async function ghV2ExportTokenPost(
  body: API.ExportTkCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/gh/v2/exportToken', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 导出达人（按查询） POST /api/gh/v2/exportTokenByQuery */
export async function ghV2ExportTokenByQueryPost(
  body: API.ExportTkCreatorByQueryRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/gh/v2/exportTokenByQuery', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
