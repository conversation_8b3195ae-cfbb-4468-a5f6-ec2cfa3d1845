// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 解密一个Excel文件 POST /api/excel/decrypt */
export async function excelDecryptPost(
  body: {
    /** password */
    password: string;
  },
  file?: File,
  options?: { [key: string]: any },
) {
  const formData = new FormData();

  if (file) {
    formData.append('file', file);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as any)[ele];

    if (item !== undefined && item !== null) {
      if (typeof item === 'object' && !(item instanceof File)) {
        if (item instanceof Array) {
          item.forEach((f) => formData.append(ele, f || ''));
        } else {
          formData.append(ele, JSON.stringify(item));
        }
      } else {
        formData.append(ele, item);
      }
    }
  });

  return request<API.lang>('/api/excel/decrypt', {
    method: 'POST',
    data: formData,
    requestType: 'form',
    ...(options || {}),
  });
}

/** 将json转成excel POST /api/excel/gene */
export async function excelGenePost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.excelGenePostParams,
  body: string,
  options?: { [key: string]: any },
) {
  return request<any>('/api/excel/gene', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 解析excel文件 POST /api/excel/parse */
export async function excelParsePost(body: {}, file?: File, options?: { [key: string]: any }) {
  const formData = new FormData();

  if (file) {
    formData.append('file', file);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as any)[ele];

    if (item !== undefined && item !== null) {
      if (typeof item === 'object' && !(item instanceof File)) {
        if (item instanceof Array) {
          item.forEach((f) => formData.append(ele, f || ''));
        } else {
          formData.append(ele, JSON.stringify(item));
        }
      } else {
        formData.append(ele, item);
      }
    }
  });

  return request<API.WebResultCommonExcel>('/api/excel/parse', {
    method: 'POST',
    data: formData,
    requestType: 'form',
    ...(options || {}),
  });
}
