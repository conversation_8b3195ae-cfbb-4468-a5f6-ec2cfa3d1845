// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：
import * as GhCreatorController from './GhCreatorController';
import * as GhGifterController from './GhGifterController';
import * as GhInteractController from './GhInteractController';
import * as GhJobController from './GhJobController';
import * as GhJobPlanController from './GhJobPlanController';
import * as GhLiveController from './GhLiveController';
import * as GhLiveCreatorController from './GhLiveCreatorController';
import * as GhMessageController from './GhMessageController';
import * as GhSettingsController from './GhSettingsController';
import * as GhSpeechController from './GhSpeechController';
import * as GhUserController from './GhUserController';
import * as GhVideoCreatorController from './GhVideoCreatorController';
import * as KolCreatorController from './KolCreatorController';
import * as KolSubTeamController from './KolSubTeamController';
export default {
  GhCreatorController,
  GhGifterController,
  GhInteractController,
  GhJobController,
  GhJobPlanController,
  GhLiveController,
  GhLiveCreatorController,
  GhMessageController,
  GhSettingsController,
  GhSpeechController,
  GhUserController,
  GhVideoCreatorController,
  KolCreatorController,
  KolSubTeamController,
};
