// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 修改用户的策略组 PUT /api/ops/grantByUser */
export async function opsGrantByUserPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.opsGrantByUserPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/ops/grantByUser', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建策略分组 POST /api/ops/group */
export async function opsGroupPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.opsGroupPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultOpsStrategyGroupDto>('/api/ops/group', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取策略分组详情 GET /api/ops/group/${param0} */
export async function opsGroupByIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.opsGroupByIdGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultOpsStrategyGroupVo>(`/api/ops/group/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改策略分组名称和描述 PUT /api/ops/group/${param0} */
export async function opsGroupByIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.opsGroupByIdPutParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultOpsStrategyGroupDto>(`/api/ops/group/${param0}`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 删除策略分组 DELETE /api/ops/group/${param0} */
export async function opsGroupByIdDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.opsGroupByIdDeleteParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/ops/group/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 将目标添加到策略组 PUT /api/ops/group/${param0}/grant */
export async function opsGroupByIdGrantPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.opsGroupByIdGrantPutParams,
  body: API.GrantOpsStrategyRequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/ops/group/${param0}/grant`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 更新策略条目 PUT /api/ops/group/${param0}/item */
export async function opsGroupByIdItemPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.opsGroupByIdItemPutParams,
  body: API.OpsStrategyItemVo,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/ops/group/${param0}/item`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 更新策略 PUT /api/ops/group/${param0}/strategy */
export async function opsGroupByIdStrategyPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.opsGroupByIdStrategyPutParams,
  body: API.UpdateOpsStrategyItemRequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/ops/group/${param0}/strategy`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 分页获取策略分组 GET /api/ops/groups */
export async function opsGroupsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.opsGroupsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultOpsStrategyGroupVo>('/api/ops/groups', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建策略分组V2 POST /api/ops/groupV2 */
export async function opsGroupV2Post(
  body: API.CreateOpsStrategyGroupRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultOpsStrategyGroupDto>('/api/ops/groupV2', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
