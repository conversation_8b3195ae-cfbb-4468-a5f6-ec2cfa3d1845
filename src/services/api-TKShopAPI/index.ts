// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：
import * as TkShopJobController from './TkShopJobController';
import * as TkShopMobileAccountController from './TkShopMobileAccountController';
import * as TkShopPlanController from './TkShopPlanController';
import * as TkshopBuyerController from './TkshopBuyerController';
import * as TkshopCreatorController from './TkshopCreatorController';
import * as TkshopGlobalCreatorController from './TkshopGlobalCreatorController';
import * as TkshopInteractionController from './TkshopInteractionController';
import * as TkshopInvitationController from './TkshopInvitationController';
import * as TkshopLiveController from './TkshopLiveController';
import * as TkshopOrderController from './TkshopOrderController';
import * as TkshopProductController from './TkshopProductController';
import * as TkshopSampleRequestController from './TkshopSampleRequestController';
import * as TkshopSearchProfileController from './TkshopSearchProfileController';
import * as TkshopSettingController from './TkshopSettingController';
import * as TkshopShopController from './TkshopShopController';
import * as TkshopSystemController from './TkshopSystemController';
import * as TkshopTaskDrawerController from './TkshopTaskDrawerController';
import * as TkshopVideoController from './TkshopVideoController';
export default {
  TkShopJobController,
  TkShopMobileAccountController,
  TkShopPlanController,
  TkshopBuyerController,
  TkshopCreatorController,
  TkshopGlobalCreatorController,
  TkshopInteractionController,
  TkshopInvitationController,
  TkshopLiveController,
  TkshopOrderController,
  TkshopProductController,
  TkshopSampleRequestController,
  TkshopSearchProfileController,
  TkshopSettingController,
  TkshopShopController,
  TkshopSystemController,
  TkshopTaskDrawerController,
  TkshopVideoController,
};
