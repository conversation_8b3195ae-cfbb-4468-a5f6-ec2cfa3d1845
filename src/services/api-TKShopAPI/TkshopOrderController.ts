// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 查询某个tkshop买家的数据库里的最后一个订单的orderNo，返回空表示没有订单（应该不可能生） GET /api/tkshop/findBuyerLastOrderNo */
export async function tkshopFindBuyerLastOrderNoGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopFindBuyerLastOrderNoGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/tkshop/findBuyerLastOrderNo', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询某个tkshop买家的订单 GET /api/tkshop/findBuyerOrders */
export async function tkshopFindBuyerOrdersGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopFindBuyerOrdersGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTkshopOrderVo>('/api/tkshop/findBuyerOrders', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 统计商品数量和销量 POST /api/tkshop/order/calcProduct */
export async function tkshopOrderCalcProductPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopOrderCalcProductPostParams,
  body: API.PageTkshopOrderRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListOrderProductStatVo>('/api/tkshop/order/calcProduct', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 统计订单数量 POST /api/tkshop/order/count */
export async function tkshopOrderCountPost(
  body: API.PageTkshopOrderRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkshopOrderCountVo>('/api/tkshop/order/count', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询特定状态订单是否有手机号 GET /api/tkshop/order/hasNoPhoneList */
export async function tkshopOrderHasNoPhoneListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopOrderHasNoPhoneListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListstring>('/api/tkshop/order/hasNoPhoneList', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询特定状态订单是否有手机号 GET /api/tkshop/order/hasPhoneList */
export async function tkshopOrderHasPhoneListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopOrderHasPhoneListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTkshopOrderHasPhoneVo>('/api/tkshop/order/hasPhoneList', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 标记同步订单结束 GET /api/tkshop/order/markSyncDone */
export async function tkshopOrderMarkSyncDoneGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopOrderMarkSyncDoneGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/order/markSyncDone', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询订单 POST /api/tkshop/order/page */
export async function tkshopOrderPagePost(
  body: API.PageTkshopOrderRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkshopOrderVo>('/api/tkshop/order/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 统计订单 POST /api/tkshop/order/stat */
export async function tkshopOrderStatPost(
  body: API.PageTkshopOrderRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkshopOrderStatVo>('/api/tkshop/order/stat', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 按状态统计店铺数据 POST /api/tkshop/order/statusStat */
export async function tkshopOrderStatusStatPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopOrderStatusStatPostParams,
  body: API.PageTkshopOrderRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListOrderStatusStatVo>('/api/tkshop/order/statusStat', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 按状态统计单个店铺数据 POST /api/tkshop/order/statusStatOnShop/${param0} */
export async function tkshopOrderStatusStatOnShopByShopIdPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopOrderStatusStatOnShopByShopIdPostParams,
  body: API.PageTkshopOrderRequest,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultListOrderStatusStatVo>(
    `/api/tkshop/order/statusStatOnShop/${param0}`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      params: {
        ...queryParams,
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 同步订单 POST /api/tkshop/order/sync */
export async function tkshopOrderSyncPost(
  body: API.TkshopSyncOrderRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/order/sync', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 同步联盟订单 POST /api/tkshop/order/syncAffiliate */
export async function tkshopOrderSyncAffiliatePost(
  body: API.SyncAffiliateOrderRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/order/syncAffiliate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 同步无数据的联盟订单 POST /api/tkshop/order/syncAffiliateNoData */
export async function tkshopOrderSyncAffiliateNoDataPost(
  body: API.SyncAffiliateNoDataRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/order/syncAffiliateNoData', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取未同步完的联盟订单 GET /api/tkshop/order/unfinishedAffiliateOrders */
export async function tkshopOrderUnfinishedAffiliateOrdersGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopOrderUnfinishedAffiliateOrdersGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListstring>('/api/tkshop/order/unfinishedAffiliateOrders', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取未完成订单 GET /api/tkshop/order/unfinishedList */
export async function tkshopOrderUnfinishedListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopOrderUnfinishedListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListstring>('/api/tkshop/order/unfinishedList', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
