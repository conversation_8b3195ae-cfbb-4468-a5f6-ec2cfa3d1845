// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 查询当前系统所有账号地区列表 GET /api/meta/areas */
export async function metaAreasGet(options?: { [key: string]: any }) {
  return request<API.WebResultListAreaVo>('/api/meta/areas', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 特定平台数据 GET /api/meta/platform/${param0} */
export async function metaPlatformByIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.metaPlatformByIdGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultShopPlatformVo>(`/api/meta/platform/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 所有平台数据 GET /api/meta/platforms */
export async function metaPlatformsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListPlatformVo>('/api/meta/platforms', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 所有平台类型数据 GET /api/meta/platformTypes */
export async function metaPlatformTypesGet(options?: { [key: string]: any }) {
  return request<API.WebResultListPlatformTypeDto>('/api/meta/platformTypes', {
    method: 'GET',
    ...(options || {}),
  });
}
