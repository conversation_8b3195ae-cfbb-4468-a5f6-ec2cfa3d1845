declare namespace API {
  type CreateDocumentAttachmentRequest = {
    files?: DocumentAttachmentItem[];
  };

  type CreateDocumentRequest = {
    content?: string;
    status?: 'Deleted' | 'Draft' | 'Released';
    title?: string;
    type?: 'Email';
  };

  type DocumentAttachmentDto = {
    createTime?: string;
    documentId?: number;
    id?: number;
    path?: string;
    size?: number;
    teamId?: number;
    type?: string;
    url?: string;
  };

  type DocumentAttachmentItem = {
    /** 路径 */
    path?: string;
    /** 文件大小 */
    size?: number;
    /** 文件类型 */
    type?: string;
    /** 公共访问链接 */
    url?: string;
  };

  type DocumentBriefVo = {
    category?: string;
    createTime?: string;
    creatorId?: number;
    id?: number;
    link?: string;
    sortNo?: number;
    status?: 'Deleted' | 'Draft' | 'Released';
    teamId?: number;
    title?: string;
    type?: 'Email';
    updateTime?: string;
    updaterId?: number;
  };

  type documentByIdAttachmentPostParams = {
    /** id */
    id: number;
  };

  type documentByIdDeleteParams = {
    /** id */
    id: number;
  };

  type documentByIdGetParams = {
    /** id */
    id: number;
  };

  type documentByIdOssUploadTokenGetParams = {
    /** id */
    id: number;
  };

  type documentByIdPutParams = {
    /** id */
    id: number;
  };

  type documentByIdStatusPutParams = {
    /** id */
    id: number;
    /** status */
    status: 'Deleted' | 'Draft' | 'Released';
  };

  type DocumentDetailVo = {
    attachments?: DocumentAttachmentDto[];
    category?: string;
    content?: string;
    createTime?: string;
    creatorId?: number;
    id?: number;
    link?: string;
    sortNo?: number;
    status?: 'Deleted' | 'Draft' | 'Released';
    teamId?: number;
    title?: string;
    type?: 'Email';
    updateTime?: string;
    updaterId?: number;
  };

  type DocumentDto = {
    category?: string;
    content?: string;
    createTime?: string;
    creatorId?: number;
    id?: number;
    link?: string;
    sortNo?: number;
    status?: 'Deleted' | 'Draft' | 'Released';
    teamId?: number;
    title?: string;
    type?: 'Email';
    updateTime?: string;
    updaterId?: number;
  };

  type documentPageGetParams = {
    pageNum?: number;
    pageSize?: number;
    sortField?: string;
    sortOrder?: 'asc' | 'desc';
    status?: 'Deleted' | 'Draft' | 'Released';
    type?: 'Email';
  };

  type PageResultDocumentBriefVo = {
    current?: number;
    list?: DocumentBriefVo[];
    pageSize?: number;
    total?: number;
  };

  type StsPostSignature = {
    accessKeyId?: string;
    accessKeySecret?: string;
    bucketName?: string;
    expiration?: string;
    fileVal?: string;
    policy?: string;
    provider?: string;
    region?: string;
    securityToken?: string;
    serverTime?: string;
    url?: string;
  };

  type WebResult = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultDocumentDetailVo = {
    code?: number;
    data?: DocumentDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultDocumentDto = {
    code?: number;
    data?: DocumentDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultDocumentBriefVo = {
    code?: number;
    data?: PageResultDocumentBriefVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultStsPostSignature = {
    code?: number;
    data?: StsPostSignature;
    message?: string;
    requestId?: string;
    success?: boolean;
  };
}
