// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取客户端IP GET /api/meta/ip */
export async function metaIpGet(options?: { [key: string]: any }) {
  return request<string>('/api/meta/ip', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 添加ip归属地 POST /api/meta/ip/location */
export async function metaIpLocationPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.metaIpLocationPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultIpLocationDto>('/api/meta/ip/location', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询位置信息 GET /api/meta/ip/location/${param0} */
export async function metaIpLocationByLocationIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.metaIpLocationByLocationIdGetParams,
  options?: { [key: string]: any },
) {
  const { locationId: param0, ...queryParams } = params;
  return request<API.WebResultIpLocationDto>(`/api/meta/ip/location/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 查询位置信息层次 GET /api/meta/ip/location/${param0}/hierarchy */
export async function metaIpLocationByLocationIdHierarchyGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.metaIpLocationByLocationIdHierarchyGetParams,
  options?: { [key: string]: any },
) {
  const { locationId: param0, ...queryParams } = params;
  return request<API.WebResultListIpLocationDto>(`/api/meta/ip/location/${param0}/hierarchy`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 查询国家级别地理位置 GET /api/meta/ip/location/countries */
export async function metaIpLocationCountriesGet(options?: { [key: string]: any }) {
  return request<API.WebResultListIpLocationDto>('/api/meta/ip/location/countries', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 查询特定国家级别地理位置 GET /api/meta/ip/location/country */
export async function metaIpLocationCountryGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.metaIpLocationCountryGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultIpLocationDto>('/api/meta/ip/location/country', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 返回ip归属地列表（树结构） GET /api/meta/ip/locationTree */
export async function metaIpLocationTreeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.metaIpLocationTreeGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListProvinceDetailVo>('/api/meta/ip/locationTree', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取客户端IP（或指定IP）的位置信息 GET /api/meta/ip/myLocation */
export async function metaIpMyLocationGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.metaIpMyLocationGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultIpLocationVo>('/api/meta/ip/myLocation', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取IP库中的省份-城市列表 GET /api/meta/ip/provinceCityList */
export async function metaIpProvinceCityListGet(options?: { [key: string]: any }) {
  return request<API.WebResultListProvinceCityListVo>('/api/meta/ip/provinceCityList', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 搜索位置 匹配城市、省、国家 GET /api/meta/ip/search */
export async function metaIpSearchGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.metaIpSearchGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultIpLocationDto>('/api/meta/ip/search', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 搜索城市 可以不设置countryCode和provinceCode，只搜素city（匹配中英文） GET /api/meta/ip/searchCity */
export async function metaIpSearchCityGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.metaIpSearchCityGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListIpLocationDto>('/api/meta/ip/searchCity', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 根据countryCode搜索省 GET /api/meta/ip/searchProvince */
export async function metaIpSearchProvinceGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.metaIpSearchProvinceGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListProvinceVo>('/api/meta/ip/searchProvince', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取客户端IP（或指定IP）的位置信息 GET /api/meta/ip138 */
export async function metaIp138Get(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.metaIp138GetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultIpDataVo>('/api/meta/ip138', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
