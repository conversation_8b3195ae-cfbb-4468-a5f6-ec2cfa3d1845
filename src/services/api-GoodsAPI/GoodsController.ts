// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** checkGoodsStock GET /api/goods/${param0}/checkStock */
export async function goodsByGoodsIdCheckStockGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.goodsByGoodsIdCheckStockGetParams,
  options?: { [key: string]: any },
) {
  const { goodsId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/goods/${param0}/checkStock`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取花瓣配置 GET /api/goods/credit/config */
export async function goodsCreditConfigGet(options?: { [key: string]: any }) {
  return request<API.WebResultCreditConfig>('/api/goods/credit/config', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取IP售卖城市列表 GET /api/goods/ip/cities */
export async function goodsIpCitiesGet(options?: { [key: string]: any }) {
  return request<API.WebResultListRegionVo>('/api/goods/ip/cities', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取Ip商品按国家划分的配置 GET /api/goods/ip/countrywideConfig */
export async function goodsIpCountrywideConfigGet(options?: { [key: string]: any }) {
  return request<API.WebResultIpGoodsCountrywideConfig>('/api/goods/ip/countrywideConfig', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取Ip商品按国家划分的配置 PUT /api/goods/ip/countrywideConfig */
export async function goodsIpCountrywideConfigPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.goodsIpCountrywideConfigPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/goods/ip/countrywideConfig', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取IP方案及价格 GET /api/goods/ip/regions */
export async function goodsIpRegionsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.goodsIpRegionsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListIpRegionVo>('/api/goods/ip/regions', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 过滤IP商品列表 POST /api/goods/ip/regionsByFilter */
export async function goodsIpRegionsByFilterPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.goodsIpRegionsByFilterPostParams,
  body: API.IpGoodsFilterVo,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListIpRegionVo>('/api/goods/ip/regionsByFilter', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改IP供应商信息 PUT /api/goods/provider/info */
export async function goodsProviderInfoPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.goodsProviderInfoPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/goods/provider/info', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取商品供应商列表 GET /api/goods/providers */
export async function goodsProvidersGet(options?: { [key: string]: any }) {
  return request<API.WebResultListGoodsProviderDto>('/api/goods/providers', {
    method: 'GET',
    ...(options || {}),
  });
}
