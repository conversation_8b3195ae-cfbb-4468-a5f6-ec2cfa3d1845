// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：
import * as AppOnlyMobileController from './AppOnlyMobileController';
import * as AppVersionController from './AppVersionController';
import * as AppleDeveloperIosController from './AppleDeveloperIosController';
import * as BookmarksController from './BookmarksController';
import * as ClientLogController from './ClientLogController';
import * as DashboardController from './DashboardController';
import * as DeviceTunnelController from './DeviceTunnelController';
import * as DistributionCodeController from './DistributionCodeController';
import * as DwlController from './DwlController';
import * as ExcelController from './ExcelController';
import * as ExtensionController from './ExtensionController';
import * as FingerprintController from './FingerprintController';
import * as FingerprintTemplateController from './FingerprintTemplateController';
import * as IpController from './IpController';
import * as IpDomainDiaryController from './IpDomainDiaryController';
import * as IpGatewayProxyController from './IpGatewayProxyController';
import * as IpPaymentController from './IpPaymentController';
import * as IpProbeController from './IpProbeController';
import * as IppIpController from './IppIpController';
import * as IppIpProbeController from './IppIpProbeController';
import * as MobileAccountController from './MobileAccountController';
import * as MobileShareController from './MobileShareController';
import * as QCloudMobileController from './QCloudMobileController';
import * as RecordController from './RecordController';
import * as ShopChannelController from './ShopChannelController';
import * as ShopConfigController from './ShopConfigController';
import * as ShopController from './ShopController';
import * as ShopDataController from './ShopDataController';
import * as ShopFlowController from './ShopFlowController';
import * as ShopIpMonitorController from './ShopIpMonitorController';
import * as ShopLogsController from './ShopLogsController';
import * as ShopPoliciesController from './ShopPoliciesController';
import * as ShopSessionController from './ShopSessionController';
import * as ShopSessionV3Controller from './ShopSessionV3Controller';
import * as ShopSettingsController from './ShopSettingsController';
import * as ShopShareController from './ShopShareController';
import * as ShopShortcutController from './ShopShortcutController';
import * as ShopSnapshotController from './ShopSnapshotController';
import * as ShopSnapshotPlanController from './ShopSnapshotPlanController';
import * as StorageController from './StorageController';
import * as TagController from './TagController';
import * as TeamExtensionController from './TeamExtensionController';
import * as TeamMobileGroupController from './TeamMobileGroupController';
import * as TeamShopPoliciesController from './TeamShopPoliciesController';
import * as TeamTrailController from './TeamTrailController';
import * as TrafficController from './TrafficController';
import * as TrafficPackController from './TrafficPackController';
import * as TransitController from './TransitController';
import * as UpgradeController from './UpgradeController';
import * as ViewController from './ViewController';
import * as WebhookController from './WebhookController';
export default {
  AppOnlyMobileController,
  AppVersionController,
  AppleDeveloperIosController,
  BookmarksController,
  ClientLogController,
  DashboardController,
  DeviceTunnelController,
  DistributionCodeController,
  DwlController,
  ExcelController,
  ExtensionController,
  FingerprintController,
  FingerprintTemplateController,
  IpController,
  IpDomainDiaryController,
  IpGatewayProxyController,
  IpPaymentController,
  IpProbeController,
  IppIpController,
  IppIpProbeController,
  MobileAccountController,
  MobileShareController,
  QCloudMobileController,
  RecordController,
  ShopChannelController,
  ShopConfigController,
  ShopController,
  ShopDataController,
  ShopFlowController,
  ShopIpMonitorController,
  ShopLogsController,
  ShopPoliciesController,
  ShopSessionController,
  ShopSessionV3Controller,
  ShopSettingsController,
  ShopShareController,
  ShopShortcutController,
  ShopSnapshotController,
  ShopSnapshotPlanController,
  StorageController,
  TagController,
  TeamExtensionController,
  TeamMobileGroupController,
  TeamShopPoliciesController,
  TeamTrailController,
  TrafficController,
  TrafficPackController,
  TransitController,
  UpgradeController,
  ViewController,
  WebhookController,
};
