// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** IP详情 GET /api/ip/${param0} */
export async function ipByIpIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIpIdGetParams,
  options?: { [key: string]: any },
) {
  const { ipId: param0, ...queryParams } = params;
  return request<API.WebResultIpDetailAllVo>(`/api/ip/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除IP DELETE /api/ip/${param0} */
export async function ipByIpIdDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIpIdDeleteParams,
  options?: { [key: string]: any },
) {
  const { ipId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/ip/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 更新IP基本信息（城市、时区、语言、描述、经纬度） locationId未设置时，后端可能需要根据countryCode、provinceCode创建IpLocation PUT /api/ip/${param0}/basicInfo */
export async function ipByIpIdBasicInfoPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIpIdBasicInfoPutParams,
  options?: { [key: string]: any },
) {
  const { ipId: param0, ...queryParams } = params;
  return request<API.WebResultIpLocationDto>(`/api/ip/${param0}/basicInfo`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** ip详情页面将ip绑定到账户之前，先检查一下是否有警告信息 GET /api/ip/${param0}/bindHisInfo */
export async function ipByIpIdBindHisInfoGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIpIdBindHisInfoGetParams,
  options?: { [key: string]: any },
) {
  const { ipId: param0, ...queryParams } = params;
  return request<API.WebResultMapstring>(`/api/ip/${param0}/bindHisInfo`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 绑定账户历史 GET /api/ip/${param0}/bindHistory */
export async function ipByIpIdBindHistoryGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIpIdBindHistoryGetParams,
  options?: { [key: string]: any },
) {
  const { ipId: param0, ...queryParams } = params;
  return request<API.WebResultListIpBindDto>(`/api/ip/${param0}/bindHistory`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取ip绑定账户历史记录 GET /api/ip/${param0}/bindShopHistory */
export async function ipByIpIdBindShopHistoryGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIpIdBindShopHistoryGetParams,
  options?: { [key: string]: any },
) {
  const { ipId: param0, ...queryParams } = params;
  return request<API.WebResultListIpBindHistoryVo>(`/api/ip/${param0}/bindShopHistory`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改IP为按月续费 PUT /api/ip/${param0}/chargeByMonth */
export async function ipByIpIdChargeByMonthPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIpIdChargeByMonthPutParams,
  options?: { [key: string]: any },
) {
  const { ipId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/ip/${param0}/chargeByMonth`, {
    method: 'PUT',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取IP代理信息 GET /api/ip/${param0}/credConfig */
export async function ipByIdCredConfigGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIdCredConfigGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultIpSocksDto>(`/api/ip/${param0}/credConfig`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 修改IP的IP值 前端确保IP是正确的 PUT /api/ip/${param0}/ip */
export async function ipByIpIdIpPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIpIdIpPutParams,
  options?: { [key: string]: any },
) {
  const { ipId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/ip/${param0}/ip`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 更新IP的名称 PUT /api/ip/${param0}/name */
export async function ipByIpIdNamePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIpIdNamePutParams,
  options?: { [key: string]: any },
) {
  const { ipId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/ip/${param0}/name`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 修改IP的Provider和Dynamic 前端确保IP是正确的 PUT /api/ip/${param0}/providerAndDynamic */
export async function ipByIpIdProviderAndDynamicPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIpIdProviderAndDynamicPutParams,
  options?: { [key: string]: any },
) {
  const { ipId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/ip/${param0}/providerAndDynamic`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 重启平台IP PUT /api/ip/${param0}/reboot */
export async function ipByIdRebootPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIdRebootPutParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/ip/${param0}/reboot`, {
    method: 'PUT',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 重启平台IP-返回异步任务信息 PUT /api/ip/${param0}/rebootJob */
export async function ipByIdRebootJobPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIdRebootJobPutParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/ip/${param0}/rebootJob`, {
    method: 'PUT',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 重置IP PUT /api/ip/${param0}/refresh */
export async function ipByIdRefreshPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIdRefreshPutParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultTaskDto>(`/api/ip/${param0}/refresh`, {
    method: 'PUT',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取IP重置信息 GET /api/ip/${param0}/refreshInfo */
export async function ipByIdRefreshInfoGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIdRefreshInfoGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultIpRefreshInfo>(`/api/ip/${param0}/refreshInfo`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 尝试重启IP所在主机（5分钟内只能做一次） GET /api/ip/${param0}/restart */
export async function ipByIdRestartGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIdRestartGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/ip/${param0}/restart`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取IP的打开会话数 GET /api/ip/${param0}/sessionCount */
export async function ipByIpIdSessionCountGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIpIdSessionCountGetParams,
  options?: { [key: string]: any },
) {
  const { ipId: param0, ...queryParams } = params;
  return request<API.WebResultint>(`/api/ip/${param0}/sessionCount`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取IP的接入点分组 GET /api/ip/${param0}/transitGroups */
export async function ipByIdTransitGroupsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIdTransitGroupsGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultListIpTransitGroupVo>(`/api/ip/${param0}/transitGroups`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** IP与中转探测详情 GET /api/ip/${param0}/transits */
export async function ipByIpIdTransitsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIpIdTransitsGetParams,
  options?: { [key: string]: any },
) {
  const { ipId: param0, ...queryParams } = params;
  return request<API.WebResultListIpTransitDto>(`/api/ip/${param0}/transits`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取IP的加速通道配置 GET /api/ip/${param0}/tunnelGroups */
export async function ipByIdTunnelGroupsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByIdTunnelGroupsGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultTeamIpTunnelVo>(`/api/ip/${param0}/tunnelGroups`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 批量自动续费设置。该接口返回的预估费用是团队全部自动续费ip加起来的费用 PUT /api/ip/autoRenew */
export async function ipAutoRenewPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipAutoRenewPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultbigdecimal>('/api/ip/autoRenew', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 批量删除IP DELETE /api/ip/batchDelete */
export async function ipBatchDeleteDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipBatchDeleteDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/ip/batchDelete', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 批量删除IP V2 DELETE /api/ip/batchDeleteV2 */
export async function ipBatchDeleteV2Delete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipBatchDeleteV2DeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTaskDto>('/api/ip/batchDeleteV2', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 绑定账户 PUT /api/ip/bindShop */
export async function ipBindShopPut(body: API.IpBindShopParamVo, options?: { [key: string]: any }) {
  return request<API.WebResult>('/api/ip/bindShop', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 绑定账户V2 PUT /api/ip/bindShopV2 */
export async function ipBindShopV2Put(
  body: API.IpBindShopParamVoV2,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/ip/bindShopV2', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 购买新IP POST /api/ip/buyIps */
export async function ipBuyIpsPost(
  body: API.CreateBuyIpOrderRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCreateOrderResponse>('/api/ip/buyIps', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 购买用户自有云账户IP POST /api/ip/buyOperatingCloudIps */
export async function ipBuyOperatingCloudIpsPost(
  body: API.BuyOperatingCloudIpOrderRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCreateOrderResponse>('/api/ip/buyOperatingCloudIps', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 按类型（或视图）查询 GET /api/ip/byCategory/${param0} */
export async function ipByCategoryByCategoryGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipByCategoryByCategoryGetParams,
  options?: { [key: string]: any },
) {
  const { category: param0, ...queryParams } = params;
  return request<API.WebResultPageResultIpDetailVo>(`/api/ip/byCategory/${param0}`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 切换 PUT /api/ip/changeShopIp */
export async function ipChangeShopIpPut(
  body: API.IpBindShopChangeRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/ip/changeShopIp', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 判断名称是否存在 GET /api/ip/checkOrGenerateIpName */
export async function ipCheckOrGenerateIpNameGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipCheckOrGenerateIpNameGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/ip/checkOrGenerateIpName', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询团队导入IP数量 GET /api/ip/countByTeam */
export async function ipCountByTeamGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipCountByTeamGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/ip/countByTeam', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取团队所有IP的国家（地区）列表 GET /api/ip/countries */
export async function ipCountriesGet(options?: { [key: string]: any }) {
  return request<API.WebResultListCountryDto>('/api/ip/countries', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取自有云账户IP方案及价格 GET /api/ip/customerIpRegions/${param0} */
export async function ipCustomerIpRegionsByCloudIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipCustomerIpRegionsByCloudIdGetParams,
  options?: { [key: string]: any },
) {
  const { cloudId: param0, ...queryParams } = params;
  return request<API.WebResultCustomerIpInfo>(`/api/ip/customerIpRegions/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 根据规范全局重命名 PUT /api/ip/executeRenameBySpec */
export async function ipExecuteRenameBySpecPut(options?: { [key: string]: any }) {
  return request<API.WebResult>('/api/ip/executeRenameBySpec', {
    method: 'PUT',
    ...(options || {}),
  });
}

/** 生产名称示例 GET /api/ip/generateNameExample */
export async function ipGenerateNameExampleGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipGenerateNameExampleGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/ip/generateNameExample', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取所有IP ID列表 GET /api/ip/ids */
export async function ipIdsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListlong>('/api/ip/ids', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 批量导入代理IP（异步任务版） POST /api/ip/importV2 */
export async function ipImportV2Post(
  body: API.CreateProxyIpRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTaskDto>('/api/ip/importV2', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询可供账户绑定的ip列表 GET /api/ip/ipForShopBind/${param0} */
export async function ipIpForShopBindByShopIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipIpForShopBindByShopIdGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultListIpForShopBindVo>(`/api/ip/ipForShopBind/${param0}`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 修改名称前后缀以及打标签 PUT /api/ip/nameAndTag */
export async function ipNameAndTagPut(
  body: API.UpdateIpNameAndTagRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/ip/nameAndTag', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取IP命名规范配置 GET /api/ip/nameSpecConfig */
export async function ipNameSpecConfigGet(options?: { [key: string]: any }) {
  return request<API.WebResultIpNameSpecConfigVo>('/api/ip/nameSpecConfig', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 设置IP命名规范 POST /api/ip/nameSpecConfig */
export async function ipNameSpecConfigPost(
  body: API.IpNameSpecConfigVo,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/ip/nameSpecConfig', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 命名规范列表 GET /api/ip/nameSpecList */
export async function ipNameSpecListGet(options?: { [key: string]: any }) {
  return request<API.WebResultListIpNameSpecVo>('/api/ip/nameSpecList', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 查询IP列表 GET /api/ip/page */
export async function ipPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultIpDetailVo>('/api/ip/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 解析IP的xlsx POST /api/ip/parseIpExcel */
export async function ipParseIpExcelPost(body: {}, file?: File, options?: { [key: string]: any }) {
  const formData = new FormData();

  if (file) {
    formData.append('file', file);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as any)[ele];

    if (item !== undefined && item !== null) {
      if (typeof item === 'object' && !(item instanceof File)) {
        if (item instanceof Array) {
          item.forEach((f) => formData.append(ele, f || ''));
        } else {
          formData.append(ele, JSON.stringify(item));
        }
      } else {
        formData.append(ele, item);
      }
    }
  });

  return request<API.WebResultListExcelIpImportVo>('/api/ip/parseIpExcel', {
    method: 'POST',
    data: formData,
    requestType: 'form',
    ...(options || {}),
  });
}

/** 解析IP的txt POST /api/ip/parseIpTxt */
export async function ipParseIpTxtPost(
  body: Record<string, any>,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListExcelIpImportVo>('/api/ip/parseIpTxt', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 探测IP与中转（或门户）之间的连接情况（返回remoteIp） GET /api/ip/probeIp */
export async function ipProbeIpGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipProbeIpGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultProbeResult>('/api/ip/probeIp', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 探测代理IP的remoteIp GET /api/ip/probeProxy */
export async function ipProbeProxyGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipProbeProxyGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultProxyProbeWithRemoteIp>('/api/ip/probeProxy', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 探测remoteIp GET /api/ip/probeRemoteIp */
export async function ipProbeRemoteIpGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipProbeRemoteIpGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultProxyProbeWithRemoteIp>('/api/ip/probeRemoteIp', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 根据IP与中转连接情况更新IP状态 GET /api/ip/refreshIpStatusByIpTransit */
export async function ipRefreshIpStatusByIpTransitGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipRefreshIpStatusByIpTransitGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/ip/refreshIpStatusByIpTransit', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** ip批量续费 POST /api/ip/renewIps */
export async function ipRenewIpsPost(
  body: API.CreateIpRenewOrderRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCreateOrderResponse>('/api/ip/renewIps', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询联营IP GET /api/ip/sharing/page */
export async function ipSharingPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipSharingPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultSharingIpVo>('/api/ip/sharing/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 更新代理信息 PUT /api/ip/socks/${param0} */
export async function ipSocksByIpIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipSocksByIpIdPutParams,
  options?: { [key: string]: any },
) {
  const { ipId: param0, ...queryParams } = params;
  return request<API.WebResultIpSocksDto>(`/api/ip/socks/${param0}`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 探测代理IP PUT /api/ip/socks/probe */
export async function ipSocksProbePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipSocksProbePutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultProxyProbeWithRemoteIp>('/api/ip/socks/probe', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 添加用户自有代理ip V2 POST /api/ip/socks2 */
export async function ipSocks2Post(body: API.CreateProxyIpVo, options?: { [key: string]: any }) {
  return request<API.WebResultIpSocksDto>('/api/ip/socks2', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** getIpSources GET /api/ip/sources */
export async function ipSourcesGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipSourcesGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListstring>('/api/ip/sources', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取当前团队的花漾前置代理 GET /api/ip/third_frontend/getFrontendProxyUrl */
export async function ipThirdFrontendGetFrontendProxyUrlGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipThirdFrontendGetFrontendProxyUrlGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/ip/third_frontend/getFrontendProxyUrl', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 全量/批量更新IP的加速通道 PUT /api/ip/tunnels */
export async function ipTunnelsPut(
  body: API.UpdateTunnelsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/ip/tunnels', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 解绑账户 PUT /api/ip/unbindShop */
export async function ipUnbindShopPut(
  body: API.IpBindShopParamVo,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/ip/unbindShop', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
