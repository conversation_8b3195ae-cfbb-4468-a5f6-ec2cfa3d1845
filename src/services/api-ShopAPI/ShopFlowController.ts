// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取某个店铺绑定的流程 GET /api/shop/flow/${param0}/getFlows */
export async function shopFlowByShopIdGetFlowsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopFlowByShopIdGetFlowsGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultListShopRpaFlowDto>(`/api/shop/flow/${param0}/getFlows`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取某个店铺绑定的流程信息(包含发布到全局列表流程)，返回绑定信息，包含版本信息 GET /api/shop/flow/${param0}/getFlows/v20240118 */
export async function shopFlowByShopIdGetFlowsV20240118Get(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopFlowByShopIdGetFlowsV20240118GetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultListShopRpaFlowDto>(`/api/shop/flow/${param0}/getFlows/v20240118`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 清空团队全局内置到浏览器的流程列表 DELETE /api/shop/flow/clearGlobalList */
export async function shopFlowClearGlobalListDelete(options?: { [key: string]: any }) {
  return request<API.WebResult>('/api/shop/flow/clearGlobalList', {
    method: 'DELETE',
    ...(options || {}),
  });
}

/** 获取团队全局的分身流程列表 GET /api/shop/flow/findGlobalShopFlows */
export async function shopFlowFindGlobalShopFlowsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListShopRpaFlowDto>('/api/shop/flow/findGlobalShopFlows', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 发布到全局内置到浏览器列表 POST /api/shop/flow/publishFlowToGlobalList */
export async function shopFlowPublishFlowToGlobalListPost(
  body: number[],
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/flow/publishFlowToGlobalList', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 发布到浏览器分身 POST /api/shop/flow/publishFlowToShops */
export async function shopFlowPublishFlowToShopsPost(
  body: API.PublishFlowToShops,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/flow/publishFlowToShops', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 发布到全局内置到浏览器流程列表 DELETE /api/shop/flow/removeFromGlobalList */
export async function shopFlowRemoveFromGlobalListDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopFlowRemoveFromGlobalListDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/flow/removeFromGlobalList', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 保存分身绑定的流程 POST /api/shop/flow/saveFlows */
export async function shopFlowSaveFlowsPost(
  body: API.UpdateShopFlowsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/flow/saveFlows', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
