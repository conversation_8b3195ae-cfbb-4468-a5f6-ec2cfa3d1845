import type { ReactNode } from 'react';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { <PERSON><PERSON>, Button, Col, Row, Tooltip, Typography } from 'antd';
import styles from './selectDeviceModal.less';
import type { GhostModalWrapperComponentProps } from '@/mixins/modal';
import { GhostModalCaller } from '@/mixins/modal';
import DMModal from '@/components/Common/Modal/DMModal';
import { useRequest } from '@@/plugin-request/request';
import { rpaTaskDevicesGet } from '@/services/api-RPAAPI/RpaTaskController';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { checkClient, openPureClient } from '@/utils/pageUtils';
import { getJwt } from '@/utils/utils';
import I18N from '@/i18n';
import _ from 'lodash';
import { ProTable } from '@ant-design/pro-table';
import { scrollProTableOptionFn } from '@/mixins/table';
import { Device } from '@/pages/Setting/components/SelectDeviceModal';

type Props = GhostModalWrapperComponentProps & {
  selected?: string[];
  onSubmit: (devices: API.RpaRunDeviceVo[]) => Promise<void>;
  onCancel?: () => void;
  extension?: API.ShopBriefVo['extension'];
  title?: string;
  alert?: ReactNode;
  shopId?: number;
  onlineOnly?: boolean;
};

function useLocalDeviceId(manual?: boolean) {
  const timer = useRef<any>();
  const [port, setPort] = useState(0);
  const [error, setError] = useState(0);
  const clear = useCallback(() => {
    clearTimeout(timer.current);
  }, []);
  const { data: deviceId, run } = useRequest(
    async () => {
      let _port = port;
      if (!port) {
        _port = await checkClient(0).catch(() => {
          setError(404);
        });
        setPort(_port as number);
      }
      const jwtData = await window
        .fetch(`http://127.0.0.1:${_port}/checkJwt?jwt=${getJwt()}`)
        .then((rs) => rs.json());
      if (!jwtData.hasJwt) {
        setError(404);
        throw new Error();
      } else if (!jwtData.isSame) {
        setError(401);
        throw new Error();
      } else {
        const data = await window
          .fetch(`http://127.0.0.1:${_port}/getDeviceId`)
          .then((res) => res.json());
        setError(0);
        return {
          data: data?.deviceId,
        };
      }
    },
    {
      onSuccess(res) {
        if (res) {
          clearTimeout(timer.current);
        } else {
          timer.current = setTimeout(run, 3000);
        }
      },
      onError() {
        timer.current = setTimeout(run, 3000);
      },
      manual,
    },
  );
  useEffect(() => {
    return () => {
      clear();
    };
  }, [clear]);
  return {
    deviceId,
    error,
    port,
    run,
    clear,
  };
}

const SelectDeviceModal: React.FC<Props> = (props) => {
  const {
    selected,
    onSubmit,
    title = I18N.t('请选择要执行流程的设备'),
    onCancel,
    extension = 'huayang',
    modalProps,
    onlineOnly = false,
    shopId,
    alert,
  } = props;
  const [visible, setVisible] = useState(true);
  const { deviceId } = useLocalDeviceId();
  const onClose = useCallback(() => {
    setVisible(false);
    onCancel?.();
  }, [onCancel]);
  const [devices, setDevices] = useState<API.RpaRunDeviceVo[]>([]);
  const { run: submit, loading } = useRequest(
    async () => {
      await onSubmit(devices);
      setVisible(false);
    },
    {
      manual: true,
    },
  );

  const { data = [], loading: deviceLoading } = useRequest(() => rpaTaskDevicesGet({ shopId }), {
    formatResult: (res) =>
      res.data?.filter((item) => {
        if (extension === 'extension') {
          return item.deviceType === 'Extension';
        }
        if (extension === 'huayang') {
          return item.deviceType === 'App';
        }
        return item.deviceType === 'App' || item.deviceType === 'Extension';
      }),
  });
  const _sorted_list = useMemo(() => {
    return _.orderBy(
      data || [],
      [
        (item) => {
          return item.deviceId === deviceId || selected?.includes(item.deviceId!);
        },
        'online',
        'lastActiveTime',
      ],
      ['desc', 'desc', 'desc'],
    );
  }, [data, deviceId, selected]);

  useEffect(() => {
    if (!deviceLoading) {
      if (selected?.length) {
        const deviceVos = data.filter((vo) => {
          return (
            selected.findIndex((id) => {
              return id === vo.deviceId;
            }) !== -1
          );
        });
        setDevices(deviceVos);
      } else {
        const first_online = _.find(data, (i) => {
          if (deviceId) {
            return i.deviceId === deviceId && i.online!;
          }
          return i.online!;
        });
        if (first_online) {
          setDevices([first_online]);
        }
      }
    }
  }, [data, deviceId, deviceLoading, selected]);
  const alertNode = useMemo(() => {
    if (_.isNil(alert)) {
      return (
        <Alert
          showIcon
          message={I18N.t(
            `通过花漾浏览器管理的店铺，需要选择花漾客户端所在的设备（要求设备开机、花漾客户端打开且登录）；通过花漾TK插件管理的店铺，需要选保安装插件的浏览器已经打开`,
          )}
        />
      );
    }
    return <Alert showIcon message={alert} />;
  }, [alert]);

  return (
    <DMModal
      open={visible}
      title={title}
      width={800}
      onOk={submit}
      bodyStyle={{ display: 'flex', flexDirection: 'column', gap: 8, paddingTop: alert ? 12 : 16 }}
      onCancel={onClose}
      okButtonProps={{
        disabled: !devices.length || (onlineOnly && !_.some(devices, (d) => d.online)),
      }}
      confirmLoading={loading}
      {...modalProps}
    >
      {alertNode}
      <div style={{ height: 430, overflow: 'hidden' }}>
        <ProTable<API.RpaRunDeviceVo>
          loading={deviceLoading}
          {...scrollProTableOptionFn({})}
          size="middle"
          onRow={(record) => {
            return {
              style: {
                cursor: 'pointer',
              },
              onClick() {
                if (onlineOnly) {
                  if (record.online) {
                    setDevices([record]);
                  }
                } else {
                  setDevices([record]);
                }
              },
            };
          }}
          columns={[
            {
              title: '设备名称',
              dataIndex: 'hostName',
              render: (dom, record) => {
                return <Device data={record} iconSize={16} />;
              },
            },
            {
              title: '状态',
              width: 110,
              dataIndex: 'status',
              render: (dom, record) => {
                const { online, deviceId: d_id } = record;
                let suffix = '';
                if (d_id === deviceId) {
                  suffix = '（本机）';
                }
                if (!online) {
                  return <Typography.Text type={'secondary'}>{I18N.t('离线')}</Typography.Text>;
                }
                return (
                  <Typography.Text type={'success'}>
                    {I18N.t('在线')}
                    {suffix}
                  </Typography.Text>
                );
              },
            },
            {
              title: '设备标识',
              dataIndex: 'deviceId',
              width: 125,
              ellipsis: true,
            },
          ]}
          dataSource={_sorted_list}
          rowKey="deviceId"
          rowSelection={{
            type: 'radio',
            selectedRowKeys: devices.map((item) => {
              return item.deviceId!;
            }),
            getCheckboxProps: (record) => {
              return {
                disabled: !record.online && onlineOnly,
              };
            },
            onChange: (ids, rows) => {
              setDevices(rows);
            },
          }}
          pagination={false}
        />
      </div>
    </DMModal>
  );
};

type SelectDeviceProps = {
  value?: API.RpaRunDeviceVo;
  shopId?: number;
  extension?: API.ShopDetailVo['extension'];
  onChange?: (val?: API.RpaRunDeviceVo) => void;
  onlineOnly?: boolean;
};

export const SelectDeviceField = (props: SelectDeviceProps) => {
  const { value, extension = 'huayang', shopId, onChange, onlineOnly = true } = props;
  const { deviceId, error: errorCode, port } = useLocalDeviceId(extension === 'extension');
  const [error, setError] = useState<any>();
  const loadingRef = useRef(false);
  const ErrorMsg = useMemo(() => {
    return {
      404: I18N.t('客户端未打开'),
      401: I18N.t('用户身份不一致'),
    };
  }, []);
  const { run: reloadDevice, loading } = useRequest((target?: string) => {
    return rpaTaskDevicesGet({ shopId }).then((res) => {
      const list = (res?.data || []).filter((item) => {
        if (extension === 'extension') {
          return item.deviceType === 'Extension';
        }
        if (extension === 'huayang') {
          return item.deviceType === 'App';
        }
        return item.deviceType === 'App' || item.deviceType === 'Extension';
      });
      const online_one = list.find((item) => {
        return item.online;
      });
      if (!value) {
        if (target) {
          const _target = list.find((item) => {
            return item.deviceId === target;
          });
          if (_target) {
            onChange?.(_target);
          }
        } else {
          if (online_one) {
            onChange?.(online_one);
          }
        }
      }

      if (list.length == 0) {
        setError(
          extension === 'extension'
            ? '你还没有登录或安装花漾TK插件'
            : '你还没有登录或安装花漾客户端',
        );
      } else {
        if (!online_one) {
          setError(extension === 'extension' ? '花漾TK插件均不在线' : '花漾客户端均不在线');
        } else {
          setError(undefined);
        }
      }

      return {
        data: list,
      };
    });
  });
  useEffect(() => {
    if (deviceId) {
      setTimeout(() => {
        reloadDevice(deviceId);
      }, 1000);
    }
  }, [deviceId, reloadDevice]);

  const errMsgAction = useMemo(() => {
    if (error) {
      return false;
    }
    const loginByJwt = () => {
      const obj = { jwt: getJwt() };
      const queryStr = new URLSearchParams(obj).toString();
      window.fetch(`http://127.0.0.1:${port}/openUrl`, {
        method: 'post',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url: `?${queryStr}` }),
      });
    };
    if (port && (errorCode === 404 || errorCode === 401)) {
      return (
        <Typography.Link
          underline
          style={{ color: 'white' }}
          onClick={(e) => {
            e.stopPropagation();
            if (loadingRef.current) return;
            loadingRef.current = true;
            loginByJwt();
            setTimeout(() => (loadingRef.current = false), 3000);
          }}
        >
          {errorCode === 404 ? '立即登录' : '重新登录'}
        </Typography.Link>
      );
    }
    return (
      <Typography.Link
        underline
        style={{ color: 'white' }}
        onClick={(e) => {
          e.stopPropagation();
          const obj = { jwt: getJwt() };
          const queryStr = new URLSearchParams(obj).toString();
          openPureClient(`?${queryStr}`);
        }}
      >
        立即启动
      </Typography.Link>
    );
  }, [error, errorCode, port]);

  const getDeviceContent = useCallback(() => {
    if (value) {
      return (
        <Row
          style={{ width: '100%', overflow: 'hidden', marginRight: 8, fontSize: 14 }}
          wrap={false}
        >
          <Col flex={1}>
            <Device data={value} iconSize={16} />
          </Col>
        </Row>
      );
    }

    if (errorCode || error) {
      return (
        <Tooltip placement={'left'} title={errMsgAction}>
          <Typography.Text type="danger">
            <IconFontIcon iconName="jinggao_24" style={{ marginRight: 4 }} />
            {error || ErrorMsg[errorCode]}
          </Typography.Text>
        </Tooltip>
      );
    }
    return <span style={{ color: '#999' }}>请选择运行设备</span>;
  }, [value, errorCode, error, errMsgAction, ErrorMsg]);
  return (
    <Row
      wrap={false}
      onClick={() => {
        if (loading) {
          return;
        }
        GhostModalCaller(
          <SelectDeviceModal
            onlineOnly={onlineOnly}
            shopId={shopId}
            extension={extension}
            selected={value ? [value.deviceId!] : []}
            onSubmit={async (devices) => {
              const [rpaRunDeviceVo] = devices;
              onChange?.(rpaRunDeviceVo);
            }}
          />,
        );
      }}
    >
      <Col flex={1} className={styles.deviceInput}>
        {getDeviceContent()}
      </Col>
      <Col>
        <Button type="primary" disabled={loading} className={styles.deviceBtn}>
          选择
        </Button>
      </Col>
    </Row>
  );
};

export default SelectDeviceModal;
