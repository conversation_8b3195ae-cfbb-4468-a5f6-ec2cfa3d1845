// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** Proxy导入远程IP POST /api/proxy/remote/importIp */
export async function proxyRemoteImportIpPost(
  body: API.ReportIpRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultReportIpResult>('/api/proxy/remote/importIp', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取proxy元信息，包括中转列表和团队信息 GET /api/proxy/remote/metas */
export async function proxyRemoteMetasGet(options?: { [key: string]: any }) {
  return request<API.WebResultProxyMeta>('/api/proxy/remote/metas', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取远程IP GET /api/proxy/remote/remoteIp */
export async function proxyRemoteRemoteIpGet(options?: { [key: string]: any }) {
  return request<API.WebResultstring>('/api/proxy/remote/remoteIp', {
    method: 'GET',
    ...(options || {}),
  });
}

/** Proxy删除远程IP DELETE /api/proxy/remote/remoteIp */
export async function proxyRemoteRemoteIpDelete(
  body: API.ReportIpRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultReportIpResult>('/api/proxy/remote/remoteIp', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** Proxy汇报远程IP POST /api/proxy/remote/reportIp */
export async function proxyRemoteReportIpPost(
  body: API.ReportIpRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultReportIpResult>('/api/proxy/remote/reportIp', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据模版获取一个Proxy Token GET /api/proxy/remote/templateTunnelToken */
export async function proxyRemoteTemplateTunnelTokenGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.proxyRemoteTemplateTunnelTokenGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCheckCodeResult>('/api/proxy/remote/templateTunnelToken', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 根据接入码获取Proxy Token GET /api/proxy/remote/tunnelToken/byCode */
export async function proxyRemoteTunnelTokenByCodeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.proxyRemoteTunnelTokenByCodeGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCheckCodeResult>('/api/proxy/remote/tunnelToken/byCode', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
