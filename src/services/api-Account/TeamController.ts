// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 创建团队 POST /api/team/ */
export async function teamPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTeamDto>('/api/team/', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取指定团队详情 GET /api/team/${param0} */
export async function teamByTeamIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamByTeamIdGetParams,
  options?: { [key: string]: any },
) {
  const { teamId: param0, ...queryParams } = params;
  return request<API.WebResultTeamDetailVo>(`/api/team/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改团队信息 PUT /api/team/${param0} */
export async function teamByTeamIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamByTeamIdPutParams,
  options?: { [key: string]: any },
) {
  const { teamId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/team/${param0}`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取团队头像URL GET /api/team/${param0}/avatar */
export async function teamByTeamIdAvatarGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamByTeamIdAvatarGetParams,
  options?: { [key: string]: any },
) {
  const { teamId: param0, ...queryParams } = params;
  return request<API.WebResultstring>(`/api/team/${param0}/avatar`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 更新团队头像 POST /api/team/${param0}/avatar */
export async function teamByTeamIdAvatarPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamByTeamIdAvatarPostParams,
  body: Record<string, any>,
  options?: { [key: string]: any },
) {
  const { teamId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/team/${param0}/avatar`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 修改团队设置 PUT /api/team/${param0}/config */
export async function teamByTeamIdConfigPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamByTeamIdConfigPutParams,
  options?: { [key: string]: any },
) {
  const { teamId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/team/${param0}/config`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 查看其他团队的一个联系人信息 GET /api/team/${param0}/contactByPhone/${param1} */
export async function teamByTeamIdContactByPhoneByPhoneGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamByTeamIdContactByPhoneByPhoneGetParams,
  options?: { [key: string]: any },
) {
  const { teamId: param0, phone: param1, ...queryParams } = params;
  return request<API.WebResult>(`/api/team/${param0}/contactByPhone/${param1}`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 解散团队 PUT /api/team/${param0}/dissolve */
export async function teamByTeamIdDissolvePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamByTeamIdDissolvePutParams,
  options?: { [key: string]: any },
) {
  const { teamId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/team/${param0}/dissolve`, {
    method: 'PUT',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** （取消）收藏团队 PUT /api/team/${param0}/favorite */
export async function teamByTeamIdFavoritePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamByTeamIdFavoritePutParams,
  options?: { [key: string]: any },
) {
  const { teamId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/team/${param0}/favorite`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取邀请信息 GET /api/team/${param0}/inviteInfo */
export async function teamByTeamIdInviteInfoGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamByTeamIdInviteInfoGetParams,
  options?: { [key: string]: any },
) {
  const { teamId: param0, ...queryParams } = params;
  return request<API.WebResultTeamInviteVo>(`/api/team/${param0}/inviteInfo`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 刷新我的邀请链接 GET /api/team/${param0}/refreshInviteCode */
export async function teamByTeamIdRefreshInviteCodeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamByTeamIdRefreshInviteCodeGetParams,
  options?: { [key: string]: any },
) {
  const { teamId: param0, ...queryParams } = params;
  return request<API.WebResultstring>(`/api/team/${param0}/refreshInviteCode`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取指定团队统计信息 GET /api/team/${param0}/stat */
export async function teamByTeamIdStatGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamByTeamIdStatGetParams,
  options?: { [key: string]: any },
) {
  const { teamId: param0, ...queryParams } = params;
  return request<API.WebResultTeamStatInfo>(`/api/team/${param0}/stat`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 转移团队BOSS PUT /api/team/${param0}/transfer */
export async function teamByTeamIdTransferPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamByTeamIdTransferPutParams,
  options?: { [key: string]: any },
) {
  const { teamId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/team/${param0}/transfer`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 实名认证信息 GET /api/team/${param0}/verifiedInfo */
export async function teamByTeamIdVerifiedInfoGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamByTeamIdVerifiedInfoGetParams,
  options?: { [key: string]: any },
) {
  const { teamId: param0, ...queryParams } = params;
  return request<API.WebResultTeamVerifyDto>(`/api/team/${param0}/verifiedInfo`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取审核详情 GET /api/team/audit/${param0} */
export async function teamAuditByAuditIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamAuditByAuditIdGetParams,
  options?: { [key: string]: any },
) {
  const { auditId: param0, ...queryParams } = params;
  return request<API.WebResultAuditDetailVo>(`/api/team/audit/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 通过审批 PUT /api/team/audit/${param0}/pass */
export async function teamAuditByAuditIdPassPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamAuditByAuditIdPassPutParams,
  options?: { [key: string]: any },
) {
  const { auditId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/team/audit/${param0}/pass`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 拒绝审批 PUT /api/team/audit/${param0}/reject */
export async function teamAuditByAuditIdRejectPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamAuditByAuditIdRejectPutParams,
  options?: { [key: string]: any },
) {
  const { auditId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/team/audit/${param0}/reject`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取成员审核列表 GET /api/team/audits */
export async function teamAuditsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamAuditsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultAuditResultVo>('/api/team/audits', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查看团队头像 GET /api/team/avatar/${param0} */
export async function teamAvatarByFileGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamAvatarByFileGetParams,
  options?: { [key: string]: any },
) {
  const { file: param0, ...queryParams } = params;
  return request<Record<string, any>>(`/api/team/avatar/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取官方免费配额 GET /api/team/free-quotas */
export async function teamFreeQuotasGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTeamQuotaVo>('/api/team/free-quotas', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取指定团队功能配置详情 GET /api/team/function-configs */
export async function teamFunctionConfigsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTeamFunctionConfigVo>('/api/team/function-configs', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 根据邀请码获取邀请详情 GET /api/team/inviteInfo/${param0} */
export async function teamInviteInfoByInviteCodeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamInviteInfoByInviteCodeGetParams,
  options?: { [key: string]: any },
) {
  const { inviteCode: param0, ...queryParams } = params;
  return request<API.WebResultInviteInfoVo>(`/api/team/inviteInfo/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取邀请加入团队链接 GET /api/team/inviteLink */
export async function teamInviteLinkGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamInviteLinkGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultInviteLinkVo>('/api/team/inviteLink', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 加入团队 POST /api/team/join */
export async function teamJoinPost(body: API.JoinTeamParamVo, options?: { [key: string]: any }) {
  return request<API.WebResultJoinTeamResultVo>('/api/team/join', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取已经加入的团队列表 GET /api/team/joinList */
export async function teamJoinListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamJoinListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTeamWithRoleVo>('/api/team/joinList', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 队页面点进入团队按钮之后需要调用该接口 PUT /api/team/loginTeam/${param0} */
export async function teamLoginTeamByTeamIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamLoginTeamByTeamIdPutParams,
  options?: { [key: string]: any },
) {
  const { teamId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/team/loginTeam/${param0}`, {
    method: 'PUT',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取团队特定配额详情 GET /api/team/quotaDetail */
export async function teamQuotaDetailGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamQuotaDetailGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTeamQuotaDetailVo>('/api/team/quotaDetail', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取指定团队配额详情 GET /api/team/quotas */
export async function teamQuotasGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTeamQuotaVo>('/api/team/quotas', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取指定项目的剩余配额 GET /api/team/remainQuota */
export async function teamRemainQuotaGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamRemainQuotaGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/team/remainQuota', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 角色配置信息 GET /api/team/roleConfig */
export async function teamRoleConfigGet(options?: { [key: string]: any }) {
  return request<API.WebResultTeamRoleConfig>('/api/team/roleConfig', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 修订角色默认权限 PUT /api/team/roleFunction */
export async function teamRoleFunctionPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamRoleFunctionPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/team/roleFunction', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 批量修订角色默认权限 PUT /api/team/roleFunctionBatch */
export async function teamRoleFunctionBatchPut(
  body: API.SetRoleFunctionRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/team/roleFunctionBatch', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询团队的RPA限定设备 GET /api/team/rpaDevices */
export async function teamRpaDevicesGet(options?: { [key: string]: any }) {
  return request<API.WebResultListLoginDeviceDto>('/api/team/rpaDevices', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取团队系统配置 GET /api/team/systemConfig */
export async function teamSystemConfigGet(options?: { [key: string]: any }) {
  return request<API.WebResultTeamSystemConfig>('/api/team/systemConfig', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 交接账户 PUT /api/team/user/${param0}/shopTransfer */
export async function teamUserByUserIdShopTransferPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamUserByUserIdShopTransferPutParams,
  body: API.UserShopTransferParamVo,
  options?: { [key: string]: any },
) {
  const { userId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/team/user/${param0}/shopTransfer`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 更新成员状态 PUT /api/team/user/${param0}/status */
export async function teamUserByUserIdStatusPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.teamUserByUserIdStatusPutParams,
  options?: { [key: string]: any },
) {
  const { userId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/team/user/${param0}/status`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}
