// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 计算续费ip价格 GET /api/payment/calcRenewIpsPrice */
export async function paymentCalcRenewIpsPriceGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.paymentCalcRenewIpsPriceGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCalcRenewIpResponse>('/api/payment/calcRenewIpsPrice', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
