// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** getTransitInfos GET /api/tunnel/pingTransits */
export async function tunnelPingTransitsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tunnelPingTransitsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMap>('/api/tunnel/pingTransits', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
