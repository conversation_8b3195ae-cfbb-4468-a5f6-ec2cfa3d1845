// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取域名白名单 GET /api/meta/dwl */
export async function metaDwlGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.metaDwlGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListDomainWhitelistItem>('/api/meta/dwl', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询域名白名单申请 GET /api/meta/dwl/audit */
export async function metaDwlAuditGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.metaDwlAuditGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultDwlAuditVo>('/api/meta/dwl/audit', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 发起域名白名单申请 POST /api/meta/dwl/audit */
export async function metaDwlAuditPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.metaDwlAuditPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultDwlAuditDto>('/api/meta/dwl/audit', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询某个域名白名单申请 GET /api/meta/dwl/findDwlAudit/${param0} */
export async function metaDwlFindDwlAuditByAuditIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.metaDwlFindDwlAuditByAuditIdGetParams,
  options?: { [key: string]: any },
) {
  const { auditId: param0, ...queryParams } = params;
  return request<API.WebResultDwlAuditDto>(`/api/meta/dwl/findDwlAudit/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}
