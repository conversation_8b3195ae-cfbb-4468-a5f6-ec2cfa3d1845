// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 查询通信记录 GET /api/tk/communication/page */
export async function tkCommunicationPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkCommunicationPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkInteractionDetailVo>('/api/tk/communication/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询达人详情 GET /api/tk/creator/${param0} */
export async function tkCreatorByCreatorIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkCreatorByCreatorIdGetParams,
  options?: { [key: string]: any },
) {
  const { creatorId: param0, ...queryParams } = params;
  return request<API.WebResultTkCreatorDetailVo>(`/api/tk/creator/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 查询达人头像 GET /api/tk/creator/avatarByHandle */
export async function tkCreatorAvatarByHandleGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkCreatorAvatarByHandleGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/tk/creator/avatarByHandle', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询达人详情 GET /api/tk/creator/byHandle */
export async function tkCreatorByHandleGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkCreatorByHandleGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkCreatorDetailVo>('/api/tk/creator/byHandle', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 删除达人 DELETE /api/tk/creator/delete */
export async function tkCreatorDeleteDelete(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tk/creator/delete', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询团队达人 GET /api/tk/creator/page */
export async function tkCreatorPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkCreatorPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkCreatorDetailVo>('/api/tk/creator/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 同步（创建）TK达人 POST /api/tk/creator/sync */
export async function tkCreatorSyncPost(
  body: API.TkCreatorDocumentSyncRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkCreatorDto>('/api/tk/creator/sync', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 同步（创建）TK达人V2 POST /api/tk/creator/syncV2 */
export async function tkCreatorSyncV2Post(
  body: API.TkCreatorDocumentV2,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkCreatorDto>('/api/tk/creator/syncV2', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 是否与达人产生过交互 GET /api/tk/hasInteraction */
export async function tkHasInteractionGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkHasInteractionGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultboolean>('/api/tk/hasInteraction', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 是否与达人产生过交互（批量） POST /api/tk/hasInteractions */
export async function tkHasInteractionsPost(
  body: API.HasInteractionRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListHasInteractionVo>('/api/tk/hasInteractions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 添加交互记录 POST /api/tk/interaction */
export async function tkInteractionPost(
  body: API.TkInteractionVo,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tk/interaction', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询交互记录详情 GET /api/tk/interaction/${param0} */
export async function tkInteractionByIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkInteractionByIdGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultTkInteractionDetailVo>(`/api/tk/interaction/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 查询交互记录 GET /api/tk/interaction/page */
export async function tkInteractionPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkInteractionPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkInteractionDetailVo>('/api/tk/interaction/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 添加交互记录（批量） POST /api/tk/interactions */
export async function tkInteractionsPost(
  body: API.AddTkInteractionRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tk/interactions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询与达人最近产生过交互 GET /api/tk/latestInteraction */
export async function tkLatestInteractionGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkLatestInteractionGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkInteractionDetailVo>('/api/tk/latestInteraction', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
