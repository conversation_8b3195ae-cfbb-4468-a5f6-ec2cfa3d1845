// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 查询当前团队所有手机账号的地区列表 GET /api/shop/mobile/account/areas */
export async function shopMobileAccountAreasGet(options?: { [key: string]: any }) {
  return request<API.WebResultListAreaVo>('/api/shop/mobile/account/areas', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 批量删除手机账号 DELETE /api/shop/mobile/account/batchDelete */
export async function shopMobileAccountBatchDeleteDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileAccountBatchDeleteDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/account/batchDelete', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 批量设置账号分享用户卡片config PUT /api/shop/mobile/account/batchUpdateCardConfig */
export async function shopMobileAccountBatchUpdateCardConfigPut(
  body: API.BatchUpdateCardConfigRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/account/batchUpdateCardConfig', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量设置账号邀约config PUT /api/shop/mobile/account/batchUpdateInviteConfig */
export async function shopMobileAccountBatchUpdateInviteConfigPut(
  body: API.BatchUpdateInviteConfigRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/account/batchUpdateInviteConfig', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量设置账号养号config PUT /api/shop/mobile/account/batchUpdateMaintenanceConfig */
export async function shopMobileAccountBatchUpdateMaintenanceConfigPut(
  body: API.BatchUpdateMaintenanceConfigRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/account/batchUpdateMaintenanceConfig', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 添加一个手机账号 POST /api/shop/mobile/account/create */
export async function shopMobileAccountCreatePost(
  body: API.CreateMobileAccountRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMobileAccountVo>('/api/shop/mobile/account/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除一个手机账号 POST /api/shop/mobile/account/delete */
export async function shopMobileAccountDeletePost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileAccountDeletePostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/account/delete', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取某台手机的账号列表 GET /api/shop/mobile/account/findMobileAccounts */
export async function shopMobileAccountFindMobileAccountsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileAccountFindMobileAccountsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListMobileAccountVo>('/api/shop/mobile/account/findMobileAccounts', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取一个手机账号详情 GET /api/shop/mobile/account/get */
export async function shopMobileAccountGetGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileAccountGetGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMobileAccountVo>('/api/shop/mobile/account/get', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取账号信息，要求已授权 GET /api/shop/mobile/account/getAuthorizedAccount */
export async function shopMobileAccountGetAuthorizedAccountGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileAccountGetAuthorizedAccountGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMobileAccountVo>('/api/shop/mobile/account/getAuthorizedAccount', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取手机账号列表 GET /api/shop/mobile/account/list */
export async function shopMobileAccountListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileAccountListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultMobileAccountVo>('/api/shop/mobile/account/list', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取当前用户已授权的手机账号列表 GET /api/shop/mobile/account/listAuthorizedAccounts */
export async function shopMobileAccountListAuthorizedAccountsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListMobileAccountVo>(
    '/api/shop/mobile/account/listAuthorizedAccounts',
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/** 用来通知有哪些手机账号有新消息了 PUT /api/shop/mobile/account/notifyNewTkMessage */
export async function shopMobileAccountNotifyNewTkMessagePut(
  body: API.NotifyNewTkMessageRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/account/notifyNewTkMessage', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** rpa同步手机账号 POST /api/shop/mobile/account/rpaSyncMobileAccounts */
export async function shopMobileAccountRpaSyncMobileAccountsPost(
  body: API.RpaSyncMobileAccountsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/account/rpaSyncMobileAccounts', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 添加一个手机账号 POST /api/shop/mobile/account/update */
export async function shopMobileAccountUpdatePost(
  body: API.UpdateMobileAccountRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMobileAccountVo>('/api/shop/mobile/account/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
