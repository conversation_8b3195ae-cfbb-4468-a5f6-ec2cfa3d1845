// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取邮件触发器邮件接收地址 GET /api/rpa/trigger/email-receiver */
export async function rpaTriggerEmailReceiverGet(options?: { [key: string]: any }) {
  return request<API.WebResultstring>('/api/rpa/trigger/email-receiver', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 创建邮件触发器Token POST /api/rpa/trigger/emailToken */
export async function rpaTriggerEmailTokenPost(options?: { [key: string]: any }) {
  return request<API.WebResultstring>('/api/rpa/trigger/emailToken', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 触发一个文件触发器事件 POST /api/rpa/trigger/fileEvent */
export async function rpaTriggerFileEventPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTriggerFileEventPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultRpaFileTriggerEventVo>('/api/rpa/trigger/fileEvent', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取当前设备的文件触发器 GET /api/rpa/trigger/fileTriggers */
export async function rpaTriggerFileTriggersGet(options?: { [key: string]: any }) {
  return request<API.WebResultListRpaFileTriggerVo>('/api/rpa/trigger/fileTriggers', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 触发Http触发器 GET /api/rpa/trigger/hook */
export async function rpaTriggerHookGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTriggerHookGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultRpaHttpTriggerResponseVo>('/api/rpa/trigger/hook', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 触发Http触发器 POST /api/rpa/trigger/hook */
export async function rpaTriggerHookPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTriggerHookPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultRpaHttpTriggerResponseVo>('/api/rpa/trigger/hook', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询Rpa Http触发事件 GET /api/rpa/trigger/hook-event */
export async function rpaTriggerHookEventGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTriggerHookEventGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultRpaHttpTriggerResponseVo>('/api/rpa/trigger/hook-event', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建Http触发器Token POST /api/rpa/trigger/httpToken */
export async function rpaTriggerHttpTokenPost(options?: { [key: string]: any }) {
  return request<API.WebResultstring>('/api/rpa/trigger/httpToken', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 获取当前团队的消息触发器 GET /api/rpa/trigger/messageTriggers */
export async function rpaTriggerMessageTriggersGet(options?: { [key: string]: any }) {
  return request<API.WebResultListRpaMessageTriggerVo>('/api/rpa/trigger/messageTriggers', {
    method: 'GET',
    ...(options || {}),
  });
}
