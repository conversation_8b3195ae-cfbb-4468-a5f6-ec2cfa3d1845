// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 切换录像文件的lock状态 PUT /api/record/${param0}/lockRecord */
export async function recordBySessionIdLockRecordPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.recordBySessionIdLockRecordPutParams,
  options?: { [key: string]: any },
) {
  const { sessionId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/record/${param0}/lockRecord`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 删除会话录像文件 DELETE /api/record/${param0}/removeRecordFiles */
export async function recordBySessionIdRemoveRecordFilesDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.recordBySessionIdRemoveRecordFilesDeleteParams,
  options?: { [key: string]: any },
) {
  const { sessionId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/record/${param0}/removeRecordFiles`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除会话录像文件 POST /api/record/batchRemoveRecordFiles */
export async function recordBatchRemoveRecordFilesPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.recordBatchRemoveRecordFilesPostParams,
  body: string,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/record/batchRemoveRecordFiles', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 按时间范围清理录像文件 DELETE /api/record/recordFilesByTime */
export async function recordRecordFilesByTimeDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.recordRecordFilesByTimeDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/record/recordFilesByTime', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取会话的录像列表及监管列表 GET /api/record/session/${param0} */
export async function recordSessionBySessionIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.recordSessionBySessionIdGetParams,
  options?: { [key: string]: any },
) {
  const { sessionId: param0, ...queryParams } = params;
  return request<API.WebResultSessionTimeLineVo>(`/api/record/session/${param0}`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 为会话录像片段生成oss上传sts token GET /api/record/session/${param0}/signature */
export async function recordSessionBySessionIdSignatureGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.recordSessionBySessionIdSignatureGetParams,
  options?: { [key: string]: any },
) {
  const { sessionId: param0, ...queryParams } = params;
  return request<API.WebResultStsPostSignature>(`/api/record/session/${param0}/signature`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 创建一个新的录像片段 PUT /api/record/session/${param0}/slice */
export async function recordSessionBySessionIdSlicePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.recordSessionBySessionIdSlicePutParams,
  options?: { [key: string]: any },
) {
  const { sessionId: param0, ...queryParams } = params;
  return request<API.WebResultRecordSliceVo>(`/api/record/session/${param0}/slice`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 结束一个录像片段 PUT /api/record/slice/${param0}/end */
export async function recordSliceByRecordSliceIdEndPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.recordSliceByRecordSliceIdEndPutParams,
  options?: { [key: string]: any },
) {
  const { recordSliceId: param0, ...queryParams } = params;
  return request<API.WebResultRecordSliceVo>(`/api/record/slice/${param0}/end`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 为会话片段生成用于播放的签名url GET /api/record/slice/${param0}/getUrl */
export async function recordSliceByRecordSliceIdGetUrlGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.recordSliceByRecordSliceIdGetUrlGetParams,
  options?: { [key: string]: any },
) {
  const { recordSliceId: param0, ...queryParams } = params;
  return request<API.WebResultstring>(`/api/record/slice/${param0}/getUrl`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 录像过程中心跳 PUT /api/record/slice/${param0}/heartbeat */
export async function recordSliceByRecordSliceIdHeartbeatPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.recordSliceByRecordSliceIdHeartbeatPutParams,
  options?: { [key: string]: any },
) {
  const { recordSliceId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/record/slice/${param0}/heartbeat`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}
