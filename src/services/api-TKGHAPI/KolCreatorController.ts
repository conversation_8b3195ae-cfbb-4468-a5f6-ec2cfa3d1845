// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 批量删除国家库达人 POST /api/gh/pub/creator/deleteBatch */
export async function ghPubCreatorDeleteBatchPost(
  body: API.HandleRegionBatchRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/gh/pub/creator/deleteBatch', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取最早待更新的达人列表 GET /api/gh/pub/creator/earliestUpdatedCreators */
export async function ghPubCreatorEarliestUpdatedCreatorsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghPubCreatorEarliestUpdatedCreatorsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListCreatorBriefVo>('/api/gh/pub/creator/earliestUpdatedCreators', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 批量查询达人的区域（如果没找到，不会在返回的map中） creatorId=>region map POST /api/gh/pub/creator/regions */
export async function ghPubCreatorRegionsPost(
  body: API.CommonCreatorIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMapstring>('/api/gh/pub/creator/regions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 使用开放服务抓取tk达人的信息：faas-sgp1-18bc02ac.doserverless.co GET /api/gh/pub/creator/scrapTkUserProfile */
export async function ghPubCreatorScrapTkUserProfileGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghPubCreatorScrapTkUserProfileGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMap>('/api/gh/pub/creator/scrapTkUserProfile', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 批量同步（创建）TK国家库达人 POST /api/gh/pub/creator/syncBatch */
export async function ghPubCreatorSyncBatchPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghPubCreatorSyncBatchPostParams,
  body: API.GhCreatorDocumentBatch,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/pub/creator/syncBatch', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 给公共库达人批量打标签 POST /api/gh/pub/creator/tag */
export async function ghPubCreatorTagPost(
  body: API.KolTagRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/pub/creator/tag', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 清空带特定后缀的标签（更新所有团队） POST /api/gh/pub/creator/untagBySuffix */
export async function ghPubCreatorUntagBySuffixPost(
  body: API.KolUntagBySuffixRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/pub/creator/untagBySuffix', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
