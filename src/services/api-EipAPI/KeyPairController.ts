// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 创建密钥对 会返回未加密的公私钥字符串 POST /api/customer/keypair */
export async function customerKeypairPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.customerKeypairPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultKeyPairDto>('/api/customer/keypair', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询密钥对 GET /api/customer/keypair/${param0} */
export async function customerKeypairByKeyIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.customerKeypairByKeyIdGetParams,
  options?: { [key: string]: any },
) {
  const { keyId: param0, ...queryParams } = params;
  return request<API.WebResultKeyPairDto>(`/api/customer/keypair/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 验证私钥是否匹配 GET /api/customer/keypair/${param0}/check */
export async function customerKeypairByKeyIdCheckGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.customerKeypairByKeyIdCheckGetParams,
  options?: { [key: string]: any },
) {
  const { keyId: param0, ...queryParams } = params;
  return request<API.WebResultboolean>(`/api/customer/keypair/${param0}/check`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 上传公钥 POST /api/customer/keypair/byPubkey */
export async function customerKeypairByPubkeyPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.customerKeypairByPubkeyPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultKeyPairDto>('/api/customer/keypair/byPubkey', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
