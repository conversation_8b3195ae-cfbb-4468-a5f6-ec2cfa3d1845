// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 查询优惠码与当前商品信息 GET /api/distribution/codeWithGoods */
export async function distributionCodeWithGoodsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.distributionCodeWithGoodsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultDistributionCodeVo>('/api/distribution/codeWithGoods', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
