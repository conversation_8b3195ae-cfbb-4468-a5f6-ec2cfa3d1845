import type { ButtonProps } from 'antd';
import { Select } from 'antd';
import { Form } from 'antd';
import { <PERSON><PERSON>crumb, Button, Empty, Layout, Space, Tooltip, Input, Typography } from 'antd';
import { useCallback, useEffect, useLayoutEffect, useMemo, useState } from 'react';
import { useLocalStorageState } from 'ahooks';
import { GhostModalCaller } from '@/mixins/modal';
import { shopByCategoryByCategoryGet } from '@/services/api-ShopAPI/ShopController';
import ColoursIcon from '@/components/Common/ColoursIcon';
import styles from './style.less';
import DMConfirm from '@/components/Common/DMConfirm';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { GlobalHeaderAction } from '@/utils/pageUtils';
import MiddleSpin from '@/components/Common/MiddleSpin';
import SortDropdown, { useOrder } from '@/components/Sort/SortDropdown';
import CreateShopGuide from '@/pages/Shop/components/CreateShopGuide';
import TkShopSetting from '@/pages/Shop/components/TkShopSetting';
import { useAuthJudgeResult } from '@/components/Common/FunctionButton';
import Functions from '@/constants/Functions';
import { useLocation, history, useRequest } from 'umi';
import { trimValues } from '@/utils/utils';
import I18N from '@/i18n';
import TkShopTypeSelector from '@/components/Common/Selector/TkShopTypeSelector';
import { OptionPlaceholder } from '@/components/Common/Placeholder';
import styled from 'styled-components';
import TkRegionSelector from '@/components/Common/Selector/TkRegionSelector';
import DmPagination from '@/components/DmPagination';
import type { TkShopDetailVo } from '@/pages/Shop/components/utils';
import { isIpEmpty } from '@/pages/Shop/components/utils';
import HelpLink from '@/components/HelpLink';
import { getCurrentTeamId } from '@/hooks/useCurrentTeam';
import ShopTable from '@/pages/Shop/components/ShopTable';
import buttonStyles from '@/style/button.less';
import { tkshopShopPagePost } from '@/services/api-TKShopAPI/TkshopShopController';
import ShopCard, { StyledDiv } from '@/pages/Shop/components/ShopCard';
import ClientManager from '@/utils/ClientManager';
import LabelRow, { LabelRowContext } from '@/components/Common/LabelRow';
import { useCreateShopEntryModal } from '@/pages/Shop/components/CreateShopEntryModal';

const StyledLi = styled.div`
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  flex-wrap: nowrap;
  overflow: hidden;
  > span {
    flex: 0 0 20px;
  }
  > div {
    white-space: nowrap;
  }
`;
const Shop = (props: { route: any }) => {
  const { route } = props;
  const { pathname, query } = useLocation();
  const hasAuth = useAuthJudgeResult(Functions.SHOP_IMPORT_DELETE);
  const [pageSize, setPageSize] = useLocalStorageState('tkshop_shop_list_page_size', 150);
  const [current, setCurrent] = useState(1);
  const [total, setTotal] = useState(0);
  const [form] = Form.useForm();
  const [viewType, setViewType] = useLocalStorageState<'Card' | 'List'>(
    `tkshop_shop_list_${getCurrentTeamId()}_view_type`,
    'Card',
  );
  const [selectedShops, setSelectedShops] = useState<TkShopDetailVo[]>([]);
  const { order, changeOrder } = useOrder(
    {
      key: 'createTime',
      ascend: true,
    },
    'tkshop_list_v20250807',
  );
  const showCreateShopEntryModal = useCreateShopEntryModal();
  const { data, run, loading } = useRequest(
    async () => {
      const {
        platformId,
        shopType,
        query: keyword,
      } = trimValues(form.getFieldsValue()) as API.PageTkshopRequest;
      return await tkshopShopPagePost({
        shopType,
        sortField: order.key,
        sortOrder: order.ascend ? 'asc' : 'desc',
        platformId,
        pageSize,
        pageNum: current,
        query: keyword,
      });
    },
    {
      formatResult(res) {
        setTotal(res.data?.total || 0);
        return res.data?.list || [];
      },

      defaultLoading: true,
      manual: true,
    },
  );
  const _onChange = useCallback(
    (reset?: boolean) => {
      if (!reset) {
        run();
      } else {
        if (current == 1) {
          run();
        } else {
          setCurrent(1);
        }
      }
    },
    [current, run],
  );
  useEffect(() => {
    run();
  }, [order, run, pageSize, current]);
  const { run: openSessions, loading: opening } = useRequest(
    async () => {
      const valid_shops: TkShopDetailVo[] = [];
      const ip_empty_shops: TkShopDetailVo[] = [];
      const extension_shops: TkShopDetailVo[] = [];
      selectedShops?.forEach((item) => {
        if (item.extension === 'extension') {
          extension_shops.push(item);
        } else if (isIpEmpty(item)) {
          ip_empty_shops.push(item);
        } else {
          valid_shops.push(item);
        }
      });
      if (!valid_shops.length) {
        let content = (
          <div>
            <LabelRowContext.Provider value={{ labelMuted: false, labelWidth: 90 }}>
              {extension_shops.length > 0 && (
                <LabelRow label={I18N.t('其他浏览器')}>{extension_shops.length} 个</LabelRow>
              )}
              {ip_empty_shops.length > 0 && (
                <LabelRow label={I18N.t('未设置IP隔离')}>{ip_empty_shops.length} 个</LabelRow>
              )}
            </LabelRowContext.Provider>
          </div>
        );
        if (ip_empty_shops.length === selectedShops.length) {
          content = <div>无法打开未设置IP隔离的浏览器分身</div>;
        } else if (valid_shops.length === selectedShops.length) {
          content = <div>系统只能为您打开由花漾浏览器运营的店铺</div>;
        }
        DMConfirm({
          width: 460,
          title: I18N.t('无法打开选中的浏览器分身'),
          content,
          type: 'info',
        });
      } else {
        await ClientManager.executeRpc('openBrowsers', {
          shopIds: valid_shops.map((item) => item.id!),
        });
      }
    },
    {
      manual: true,
    },
  );
  const showCreateShopGuide = useCallback(() => {
    showCreateShopEntryModal({
      onFinish: () => {
        GhostModalCaller(<CreateShopGuide onUpdate={run} />);
      },
    });
  }, [run, showCreateShopEntryModal]);
  const setting = useCallback(
    (id: number) => {
      GhostModalCaller(<TkShopSetting onUpdate={run} id={id} />);
    },
    [run],
  );
  useLayoutEffect(() => {
    if (data && (query?.id || query.keyword)) {
      history.replace(pathname);
      if (query.id) {
        setting(query.id);
      }
    }
  }, [data, query, pathname, setting]);

  const addCard = useMemo(() => {
    return (
      <StyledDiv add style={{ flex: '0 0 300px' }} onClick={showCreateShopGuide}>
        <IconFontIcon iconName="tianjia_24" size={48} />
        <Typography.Link>添加TikTok店铺</Typography.Link>
      </StyledDiv>
    );
  }, [showCreateShopGuide]);
  const AddButton = useMemo(() => {
    return (ps: ButtonProps) => {
      return (
        <Button {...ps} onClick={showCreateShopGuide}>
          <Space>
            <IconFontIcon iconName={'tianjia_24'} />
            <span>添加TikTok店铺</span>
          </Space>
        </Button>
      );
    };
  }, [showCreateShopGuide]);
  const content = useMemo(() => {
    if (loading) {
      return <MiddleSpin />;
    }
    if (!data?.length) {
      if (!hasAuth) {
        return (
          <Empty
            style={{
              display: 'flex',
              position: 'absolute',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              left: 0,
              top: 0,
              right: 0,
              bottom: 0,
            }}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={'您没有TK店铺的授权，请联络您的团队管理员'}
          />
        );
      }
      return (
        <div
          style={{
            display: 'flex',
            position: 'absolute',
            flexDirection: 'column',
            left: 0,
            top: 0,
            right: 0,
            bottom: 0,
          }}
        >
          <div
            style={{
              display: 'flex',
              flex: 1,
              gap: 48,
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                gap: 24,
                alignContent: 'center',
                alignItems: 'center',
              }}
            >
              <ColoursIcon className={'dianpu_24'} size={84} />
              <AddButton type={'primary'} />
            </div>
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between',
                gap: 16,
              }}
            >
              <Typography.Title level={3}>添加您的TikTok店铺</Typography.Title>
              <div>{I18N.t('在使用 TikTok Shop 达人私域系统之前，您需要：')}</div>
              <StyledLi>
                <span>1.</span>
                <div style={{ flex: 1, overflow: 'hidden' }}>
                  <div>{I18N.t('添加您的 TikTok 店铺')}</div>
                </div>
              </StyledLi>
              <StyledLi>
                <span>2.</span>
                <div>
                  <div>{I18N.t('本土店：请导入您自己的 IP 地址，或者购买平台 IP')}</div>
                </div>
              </StyledLi>
              <StyledLi>
                <span>3.</span>
                <div>
                  <div>{I18N.t('跨境店：可以使用“本机 IP 直连”')}</div>
                </div>
              </StyledLi>
              <StyledLi>
                <span>4.</span>
                <div>
                  <div>{I18N.t('店铺设置好IP隔离后，请打开店铺对应的浏览器，完成登录')}</div>
                </div>
              </StyledLi>
              <div>
                更多信息请参考：
                <HelpLink href={'/tkshop2/buy#create'}>{I18N.t('快速入门')}</HelpLink>
              </div>
            </div>
          </div>
        </div>
      );
    }
    if (viewType === 'List') {
      return (
        <ShopTable
          selected={selectedShops}
          onSelect={setSelectedShops}
          order={order}
          changeOrder={changeOrder}
          dataSource={data}
          onChange={_onChange}
        />
      );
    }
    const list = (data ?? []).map((item) => {
      return (
        <ShopCard
          style={{ flex: '0 0 300px' }}
          key={item.id}
          shop={item}
          onSetting={setting}
          onUpdate={_onChange}
        />
      );
    });
    return (
      <div
        style={{
          padding: 16,
          overflow: 'visible',
          gap: 16,
          flexWrap: 'wrap',
          display: 'flex',
          alignContent: 'flex-start',
        }}
      >
        {list}
        {hasAuth && addCard}
      </div>
    );
  }, [
    AddButton,
    _onChange,
    addCard,
    changeOrder,
    data,
    hasAuth,
    loading,
    order,
    selectedShops,
    setting,
    viewType,
  ]);
  const sortDropdown = useMemo(() => {
    return (
      <SortDropdown
        list={[
          {
            key: 'createTime',
            label: '创建时间',
          },
          {
            key: 'name',
            label: '店铺名称',
          },
          {
            key: 'type',
            label: '店铺类型',
          },
          {
            key: 'healthStatus',
            label: '健康状态',
          },
          {
            key: 'healthScore',
            label: '店铺得分',
          },
          {
            key: 'lastSyncTime',
            label: '最近更新',
          },
        ]}
        order={order}
        onChange={changeOrder}
      />
    );
  }, [changeOrder, order]);

  return (
    <Layout.Content className={styles.shopPageContainer}>
      <GlobalHeaderAction.Emit>
        <Space size={32}>
          <Button
            ghost
            style={{ background: 'none' }}
            icon={<IconFontIcon iconName={'shuaxin_24'} />}
            onClick={() => {
              run();
            }}
          >
            <span>{I18N.t('刷新')}</span>
          </Button>
          {hasAuth && <AddButton ghost />}
        </Space>
      </GlobalHeaderAction.Emit>
      <div className={'header'}>
        <Breadcrumb>
          <Breadcrumb.Item>{route.name}</Breadcrumb.Item>
        </Breadcrumb>
        <Form form={form} style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
          <Form.Item noStyle name={'shopType'}>
            <TkShopTypeSelector style={{ width: 130 }} onChange={_onChange} />
          </Form.Item>
          <Form.Item noStyle name={'platformId'}>
            <TkRegionSelector
              allowClear
              placeholder={<OptionPlaceholder type={'site'} />}
              valuePropName={'id'}
              style={{ width: 130 }}
              onChange={_onChange}
            />
          </Form.Item>
          <Form.Item noStyle name={'query'} initialValue={query?.keyword || ''}>
            <Input.Search
              placeholder={I18N.t('依据名称或备注检索')}
              onSearch={(v) => {
                form.setFieldValue('query', v);
                _onChange();
              }}
              allowClear
              style={{ width: 200 }}
            />
          </Form.Item>
          <Form.Item noStyle shouldUpdate>
            <Select
              optionLabelProp="icon"
              dropdownMatchSelectWidth={false}
              value={viewType}
              onSelect={(value) => {
                setViewType(value);
              }}
              style={{ width: 68 }}
            >
              <Select.Option value="Card" icon={<IconFontIcon iconName="chanpin_24" />}>
                <Space size={4}>
                  <IconFontIcon iconName="chanpin_24" />
                  {I18N.t('详情')}
                </Space>
              </Select.Option>
              <Select.Option value="List" icon={<IconFontIcon iconName="liebiaoxingshi_24" />}>
                <Space size={4}>
                  <IconFontIcon iconName="liebiaoxingshi_24" />
                  {I18N.t('列表')}
                </Space>
              </Select.Option>
            </Select>
          </Form.Item>
          {sortDropdown}
        </Form>
      </div>
      <div className={'main'}>{content}</div>
      <div className={'footer'}>
        {viewType === 'List' ? (
          <Tooltip
            placement={'topLeft'}
            title={selectedShops.length === 0 ? '请至少选择一个店铺' : false}
          >
            <Space>
              <Button
                type={'primary'}
                ghost
                icon={<IconFontIcon iconName="fangwendianpu_24" />}
                loading={opening}
                className={selectedShops.length === 0 ? buttonStyles.disabled : ''}
                onClick={async () => {
                  if (selectedShops.length === 0) {
                    return;
                  }
                  openSessions();
                }}
              >
                <span>{I18N.t('访问店铺')}</span>
              </Button>
            </Space>
          </Tooltip>
        ) : (
          <span />
        )}
        <DmPagination
          loading={loading}
          onChange={(p, s) => {
            setCurrent(p);
            setPageSize(s);
          }}
          pageSize={pageSize}
          total={total}
          current={current}
        />
      </div>
      {/*<EntryStep />*/}
    </Layout.Content>
  );
};
export default Shop;
