// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 企业四要素认证 POST /api/team/verify/company4Element */
export async function teamVerifyCompany4ElementPost(
  body: API.Company4ElementVo,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/team/verify/company4Element', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 解除实名认证 GET /api/team/verify/invalidate */
export async function teamVerifyInvalidateGet(options?: { [key: string]: any }) {
  return request<API.WebResult>('/api/team/verify/invalidate', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 手机三要素认证 POST /api/team/verify/mobile3Element */
export async function teamVerifyMobile3ElementPost(
  body: API.Mobile3ElementVo,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMobile3ElementResult>('/api/team/verify/mobile3Element', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
