// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 删除一个团队自有插件 DELETE /api/extensions/team/${param0} */
export async function extensionsTeamByIdDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.extensionsTeamByIdDeleteParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/extensions/team/${param0}`, {
    method: 'DELETE',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 上传一个新的团队自有插件 POST /api/extensions/team/create */
export async function extensionsTeamCreatePost(
  body: {
    /** param */
    param: string;
  },
  file?: File,
  options?: { [key: string]: any },
) {
  const formData = new FormData();

  if (file) {
    formData.append('file', file);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as any)[ele];

    if (item !== undefined && item !== null) {
      if (typeof item === 'object' && !(item instanceof File)) {
        if (item instanceof Array) {
          item.forEach((f) => formData.append(ele, f || ''));
        } else {
          formData.append(ele, JSON.stringify(item));
        }
      } else {
        formData.append(ele, item);
      }
    }
  });

  return request<API.WebResultExtensionsVo>('/api/extensions/team/create', {
    method: 'POST',
    data: formData,
    requestType: 'form',
    ...(options || {}),
  });
}

/** 分页获取团队自有插件列表 GET /api/extensions/team/list */
export async function extensionsTeamListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.extensionsTeamListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultExtensionsVo>('/api/extensions/team/list', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 更新团队自有插件 POST /api/extensions/team/update */
export async function extensionsTeamUpdatePost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.extensionsTeamUpdatePostParams,
  body: {},
  file?: File,
  options?: { [key: string]: any },
) {
  const formData = new FormData();

  if (file) {
    formData.append('file', file);
  }

  Object.keys(body).forEach((ele) => {
    const item = (body as any)[ele];

    if (item !== undefined && item !== null) {
      if (typeof item === 'object' && !(item instanceof File)) {
        if (item instanceof Array) {
          item.forEach((f) => formData.append(ele, f || ''));
        } else {
          formData.append(ele, JSON.stringify(item));
        }
      } else {
        formData.append(ele, item);
      }
    }
  });

  return request<API.WebResultExtensionsVo>('/api/extensions/team/update', {
    method: 'POST',
    params: {
      ...params,
    },
    data: formData,
    requestType: 'form',
    ...(options || {}),
  });
}
