// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 第三方登录跳转入口 GET /api/oauth/${param0} */
export async function oauthByOauthTypeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.oauthByOauthTypeGetParams,
  options?: { [key: string]: any },
) {
  const { oauthType: param0, ...queryParams } = params;
  return request<Record<string, any>>(`/api/oauth/${param0}`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 第三方登录回调入口 GET /api/oauth/callback/${param0} */
export async function oauthCallbackByOauthTypeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.oauthCallbackByOauthTypeGetParams,
  options?: { [key: string]: any },
) {
  const { oauthType: param0, ...queryParams } = params;
  return request<Record<string, any>>(`/api/oauth/callback/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 取消这次登录 GET /api/oauth/cancel/${param0} */
export async function oauthCancelByUuidGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.oauthCancelByUuidGetParams,
  options?: { [key: string]: any },
) {
  const { uuid: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/oauth/cancel/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 从客户端获取一个浏览器登录或绑定链接和查询uuid GET /api/oauth/getOAuthUrl */
export async function oauthGetOAuthUrlGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.oauthGetOAuthUrlGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultOAuthProcessInfo>('/api/oauth/getOAuthUrl', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 根据uuid获取二维码版本的URL GET /api/oauth/qrcode/${param0} */
export async function oauthQrcodeByUuidGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.oauthQrcodeByUuidGetParams,
  options?: { [key: string]: any },
) {
  const { uuid: param0, ...queryParams } = params;
  return request<Record<string, any>>(`/api/oauth/qrcode/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 第三方登录跳转入口 GET /api/oauth/r/${param0} */
export async function oauthRByOauthTypeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.oauthRByOauthTypeGetParams,
  options?: { [key: string]: any },
) {
  const { oauthType: param0, ...queryParams } = params;
  return request<Record<string, any>>(`/api/oauth/r/${param0}`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 根据uuid查询浏览器的登录或绑定请求结果 GET /api/oauth/result/${param0} */
export async function oauthResultByUuidGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.oauthResultByUuidGetParams,
  options?: { [key: string]: any },
) {
  const { uuid: param0, ...queryParams } = params;
  return request<API.WebResultOAuthProcessInfo>(`/api/oauth/result/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}
