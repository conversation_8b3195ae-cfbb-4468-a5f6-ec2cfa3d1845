// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：
import * as DiskImageController from './DiskImageController';
import * as TkBuyerController from './TkBuyerController';
import * as TkChatController from './TkChatController';
import * as TkController from './TkController';
import * as TkCreatorController from './TkCreatorController';
import * as TkCreatorViewController from './TkCreatorViewController';
import * as TkDataCreatorController from './TkDataCreatorController';
import * as TkEmailController from './TkEmailController';
import * as TkMediaController from './TkMediaController';
import * as TkOpenAppController from './TkOpenAppController';
import * as TkOpenProductController from './TkOpenProductController';
import * as Tk<PERSON>penShopController from './TkOpenShopController';
import * as TkOperationController from './TkOperationController';
import * as TkParamController from './TkParamController';
import * as TkProductController from './TkProductController';
import * as TkPropController from './TkPropController';
import * as TkPublicController from './TkPublicController';
import * as TkSearchController from './TkSearchController';
import * as TkShopController from './TkShopController';
export default {
  DiskImageController,
  TkBuyerController,
  TkChatController,
  TkController,
  TkCreatorController,
  TkCreatorViewController,
  TkDataCreatorController,
  TkEmailController,
  TkMediaController,
  TkOpenAppController,
  TkOpenProductController,
  TkOpenShopController,
  TkOperationController,
  TkParamController,
  TkProductController,
  TkPropController,
  TkPublicController,
  TkSearchController,
  TkShopController,
};
