// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 计算购买ip价格 GET /api/webhook/payment/calcBuyIpPrice */
export async function webhookPaymentCalcBuyIpPriceGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.webhookPaymentCalcBuyIpPriceGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCalcPriceResponse>('/api/webhook/payment/calcBuyIpPrice', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取公有云IP通用折扣信息 GET /api/webhook/payment/cloudIpDiscounts */
export async function webhookPaymentCloudIpDiscountsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListDiscountsDto>('/api/webhook/payment/cloudIpDiscounts', {
    method: 'GET',
    ...(options || {}),
  });
}
