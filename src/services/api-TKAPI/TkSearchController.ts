// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 创建搜索任务 POST /api/tk/search/task */
export async function tkSearchTaskPost(
  body: API.CreateTkSearchTaskRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkSearchTaskDto>('/api/tk/search/task', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询任务详情 GET /api/tk/search/task/${param0}/detail */
export async function tkSearchTaskByIdDetailGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkSearchTaskByIdDetailGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultTkSearchTaskDetailVo>(`/api/tk/search/task/${param0}/detail`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 更新任务状态 PUT /api/tk/search/task/${param0}/status */
export async function tkSearchTaskByIdStatusPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkSearchTaskByIdStatusPutParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultTkSearchTaskDto>(`/api/tk/search/task/${param0}/status`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 分页查询任务 GET /api/tk/search/task/page */
export async function tkSearchTaskPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkSearchTaskPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkSearchTaskVo>('/api/tk/search/task/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 追加搜索记录 POST /api/tk/search/taskResult */
export async function tkSearchTaskResultPost(
  body: API.AddSearchTaskResultRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkSearchResultDto>('/api/tk/search/taskResult', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
