// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 修改产品的备注 PUT /api/tk/open/product/remark */
export async function tkOpenProductRemarkPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkOpenProductRemarkPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tk/open/product/remark', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 搜索产品列表 POST /api/tk/open/product/search */
export async function tkOpenProductSearchPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkOpenProductSearchPostParams,
  body: API.TkProductsSearchRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkProductsSearchResult>('/api/tk/open/product/search', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}
