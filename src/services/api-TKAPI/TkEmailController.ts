// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 添加邮件服务 POST /api/tk/email */
export async function tkEmailPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkEmailPostParams,
  body: API.TkEmailDto,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkEmailDto>('/api/tk/email', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改邮件服务 PUT /api/tk/email/${param0} */
export async function tkEmailByIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkEmailByIdPutParams,
  body: API.TkEmailDto,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultTkEmailDto>(`/api/tk/email/${param0}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...queryParams,
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除邮箱 DELETE /api/tk/email/${param0} */
export async function tkEmailByIdDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkEmailByIdDeleteParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/tk/email/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 过滤出有邮件任务正在调度或执行中的达人列表 POST /api/tk/email/filterPending */
export async function tkEmailFilterPendingPost(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListlong>('/api/tk/email/filterPending', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 申请执行邮件Job GET /api/tk/email/job/${param0} */
export async function tkEmailJobByIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkEmailJobByIdGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultTkEmailJobVo>(`/api/tk/email/job/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 更新发送邮件的结果 PUT /api/tk/email/job/${param0}/result */
export async function tkEmailJobByIdResultPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkEmailJobByIdResultPutParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/tk/email/job/${param0}/result`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 邮件配置列表 GET /api/tk/email/list */
export async function tkEmailListGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTkEmailDto>('/api/tk/email/list', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 探测邮件服务 POST /api/tk/email/probe */
export async function tkEmailProbePost(body: API.TkEmailDto, options?: { [key: string]: any }) {
  return request<API.WebResult>('/api/tk/email/probe', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建一个发送邮件任务 POST /api/tk/email/task */
export async function tkEmailTaskPost(
  body: API.CreateEmailTaskRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTaskDto>('/api/tk/email/task', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
