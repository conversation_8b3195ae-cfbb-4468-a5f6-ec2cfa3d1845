// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 查询带视频的达人 POST /api/tkshop/live/creator/page */
export async function tkshopLiveCreatorPagePost(
  body: API.PageVideoCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkshopCreatorProductVo>('/api/tkshop/live/creator/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 返回直播的详情 GET /api/tkshop/live/detail */
export async function tkshopLiveDetailGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopLiveDetailGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkshopLiveVo>('/api/tkshop/live/detail', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询直播 POST /api/tkshop/live/page */
export async function tkshopLivePagePost(
  body: API.PageLiveRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkshopLiveVo>('/api/tkshop/live/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新备注 PUT /api/tkshop/live/remark */
export async function tkshopLiveRemarkPut(
  body: API.UpdateRemarkRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/live/remark', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询带视频的店铺 GET /api/tkshop/live/shops */
export async function tkshopLiveShopsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopLiveShopsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListlong>('/api/tkshop/live/shops', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
