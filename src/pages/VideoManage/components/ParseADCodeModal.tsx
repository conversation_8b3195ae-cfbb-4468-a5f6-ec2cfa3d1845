import { useC<PERSON>back, useMemo, useRef, useState } from 'react';
import DMModal from '@/components/Common/Modal/DMModal';
import I18N from '@/i18n';
import { Button, Form, Space, Typography, Checkbox, InputNumber, Row, Col } from 'antd';
import DMFormItem, { DMFormItemContext } from '@/components/Common/DMFormItem';
import { useRequest } from '@@/plugin-request/request';
import {
  getTaskShelfJobIcon,
  TaskShelfJobType,
  useAddTask,
} from '@/pages/TikTok/Live/components/TaskShelfModal';
import { trimValues } from '@/utils/utils';
import type { GhostModalWrapperComponentProps } from '@/mixins/modal';
import { useTaskPoolAddAnimation } from '@/hooks/interactions';
import { tkshopJobsSendVideoAdCodePost } from '@/services/api-TKShopAPI/TkShopJobController';
import buttonStyles from '@/style/button.less';
import { SelectDeviceField } from '@/pages/RpaFlows/components/SelectDeviceModal';
import { SelectShopField } from '@/pages/RpaFlows/components/SelectShopModal';
import FlowReadmeAsidePanel, {
  FlowReadmeAsidePanelWidth,
} from '@/components/Common/MarkdownView/FlowReadmeAsidePanel';
import { showFunctionCodeAlert, useAuthJudgeCallback } from '@/components/Common/FunctionButton';
import Functions from '@/constants/Functions';
import CreatorAvatarAndName from '@/components/Common/CreatorAvatarAndName';
import _ from 'lodash';

const ParseADCodeModal = (
  props: GhostModalWrapperComponentProps & {
    selected: API.TkshopCreatorDto[];
    shop: API.ShopDetailVo;
  },
) => {
  const { selected, modalProps, shop: _shop } = props;
  const [visible, setVisible] = useState(true);
  const [form] = Form.useForm();
  const { play } = useTaskPoolAddAnimation();
  const add_to_task = useRef(false);
  const { run: add } = useAddTask();

  const renderLabel = useCallback(() => {
    if (selected.length === 0) {
      return <CreatorAvatarAndName creator={selected[0]} />;
    }
    return `${selected.length?.toLocaleString()} 个达人`;
  }, [selected]);
  const { run: addToTask, loading: adding } = useRequest(
    async (e: any) => {
      add_to_task.current = true;
      const values = await form.validateFields();
      const {
        shop,
        parse_creator_message,
        parse_buyer_message,
        creator_message_count,
        buyer_message_count,
      } = values;
      await add(
        {
          items: selected.map((item) => {
            return {
              rpaType: 'Browser',
              accountId: shop.id!,
              taskType: 'TS_VideoADCode',
              creatorId: item.id!,
              parameter: JSON.stringify({
                advanceSettings: {
                  cid: item.cid,
                  handle: item.handle,
                  parse_creator_message,
                  parse_buyer_message,
                  creator_message_count,
                  buyer_message_count,
                },
              }),
            };
          }),
        },
        e,
      );

      setVisible(false);
    },
    {
      manual: true,
    },
  );
  const hasAuth = useAuthJudgeCallback();
  const { run: submit, loading } = useRequest(
    async (e: any) => {
      add_to_task.current = false;
      const values = trimValues(await form.validateFields());
      const {
        device,
        shop,
        parse_creator_message,
        parse_buyer_message,
        creator_message_count,
        buyer_message_count,
      } = values;
      if (!hasAuth([Functions.RPA_RUN, Functions.RPA_LIST])) {
        showFunctionCodeAlert();
      } else {
        await tkshopJobsSendVideoAdCodePost({
          ghCreatorIds: selected.map((item) => item.id!),
          shopId: shop.id!,
          deviceId: device.deviceId!,
          advanceSettings: {
            parse_creator_message,
            parse_buyer_message,
            creator_message_count,
            buyer_message_count,
          },
        });
        play(e, 'TS_VideoADCode');

        setVisible(false);
      }
    },
    {
      manual: true,
    },
  );
  const footer = useMemo(() => {
    return (
      <Space>
        <Button onClick={addToTask} loading={adding} className={buttonStyles.successBtn}>
          {I18N.t('放到任务抽屉')}
        </Button>
        <Button onClick={submit} loading={loading} type={'primary'}>
          {I18N.t('立即执行')}
        </Button>
        <Button
          type={'default'}
          onClick={() => {
            setVisible(false);
          }}
        >
          {I18N.t('取消')}
        </Button>
      </Space>
    );
  }, [addToTask, adding, loading, submit]);

  return (
    <DMModal
      title={I18N.t('提取投流码')}
      bodyStyle={{ paddingBottom: 0 }}
      width={760 + FlowReadmeAsidePanelWidth}
      asideWrapperStyle={{
        flex: `0 0 ${FlowReadmeAsidePanelWidth}px`,
        height: 460,
      }}
      asidePanel={<FlowReadmeAsidePanel bizCode={'tkshop.TS_VideoADCode'} />}
      footer={footer}
      open={visible}
      onCancel={() => {
        setVisible(false);
      }}
      {...modalProps}
    >
      <DMFormItemContext.Provider value={{ disableLabelMuted: false, labelWidth: 90 }}>
        <Form
          requiredMark={false}
          form={form}
          style={{ display: 'flex', flexDirection: 'column', overflow: 'hidden', height: '100%' }}
        >
          <Row gutter={[8, 0]}>
            <Col span={12}>
              <DMFormItem
                label={I18N.t('店铺')}
                name={'shop'}
                initialValue={_shop}
                rules={[{ required: true, message: I18N.t('请选择浏览器分身') }]}
              >
                <SelectShopField />
              </DMFormItem>
            </Col>
            <Col span={12}>
              <DMFormItem
                label={I18N.t('运行设备')}
                name={'device'}
                rules={[
                  {
                    validator(_rule, value) {
                      if (value || add_to_task.current) {
                        return Promise.resolve();
                      }
                      return Promise.reject(I18N.t('请指定运行设备'));
                    },
                  },
                ]}
              >
                <SelectDeviceField ignoreDeviceType={['Extension']} />
              </DMFormItem>
            </Col>
            <Col span={12}>
              <DMFormItem label={I18N.t('指定达人')} shouldUpdate>
                {renderLabel()}
              </DMFormItem>
            </Col>
            <Col span={12}>
              <DMFormItem label={I18N.t('任务类型')}>
                <Space align={'center'} size={4}>
                  <Typography.Text>{getTaskShelfJobIcon('TS_VideoADCode')}</Typography.Text>
                  <span>{TaskShelfJobType.TS_VideoADCode}</span>
                </Space>
              </DMFormItem>
            </Col>
          </Row>
          <div
            style={{
              flex: 1,
              overflow: 'hidden',
              display: 'flex',
              justifyContent: 'flex-end',
              flexDirection: 'column',
            }}
          >
            <div style={{ display: 'flex', alignItems: 'flex-start', lineHeight: '32px' }}>
              <div style={{ flex: '0 0 310px', display: 'flex', gap: 4, alignItems: 'center' }}>
                <Form.Item noStyle shouldUpdate>
                  {(f) => {
                    const disabled =
                      f.getFieldValue('parse_creator_message') &&
                      !f.getFieldValue('parse_buyer_message');
                    return (
                      <Form.Item
                        noStyle
                        shouldUpdate
                        name={'parse_creator_message'}
                        valuePropName={'checked'}
                        initialValue={true}
                      >
                        <Checkbox disabled={disabled} />
                      </Form.Item>
                    );
                  }}
                </Form.Item>
                {I18N.t('分析所选达人的站内消息，从最近的')}
              </div>
              <Form.Item noStyle shouldUpdate>
                {(f) => {
                  const disabled = !f.getFieldValue('parse_creator_message');
                  return (
                    <Form.Item
                      name="creator_message_count"
                      initialValue={50}
                      rules={[
                        {
                          validator(rule, value) {
                            const val = _.trim(value);
                            if (!val && !disabled) {
                              return Promise.reject(I18N.t('不能为空'));
                            }
                            return Promise.resolve();
                          },
                        },
                      ]}
                    >
                      <InputNumber min={1} disabled={disabled} />
                    </Form.Item>
                  );
                }}
              </Form.Item>

              <div style={{ paddingLeft: 8 }}>{I18N.t('等消息记录中解析可能存在的投流码')}</div>
            </div>
            <div style={{ display: 'flex', alignItems: 'flex-start', lineHeight: '32px' }}>
              <div style={{ flex: '0 0 310px', display: 'flex', gap: 4, alignItems: 'center' }}>
                <Form.Item noStyle shouldUpdate>
                  {(f) => {
                    const disabled =
                      !f.getFieldValue('parse_creator_message') &&
                      f.getFieldValue('parse_buyer_message');
                    return (
                      <Form.Item
                        noStyle
                        shouldUpdate
                        name={'parse_buyer_message'}
                        valuePropName={'checked'}
                        initialValue={true}
                      >
                        <Checkbox disabled={disabled} />
                      </Form.Item>
                    );
                  }}
                </Form.Item>
                {I18N.t('分析所选达人作为买家的店铺私信，从最近的')}
              </div>
              <Form.Item noStyle shouldUpdate>
                {(f) => {
                  const disabled = !f.getFieldValue('parse_buyer_message');
                  return (
                    <Form.Item
                      name="buyer_message_count"
                      initialValue={50}
                      rules={[
                        {
                          validator(rule, value) {
                            const val = _.trim(value);
                            if (!val && !disabled) {
                              return Promise.reject(I18N.t('不能为空'));
                            }
                            return Promise.resolve();
                          },
                        },
                      ]}
                    >
                      <InputNumber min={1} disabled={disabled} />
                    </Form.Item>
                  );
                }}
              </Form.Item>
              <div style={{ paddingLeft: 8 }}>{I18N.t('等消息记录中解析可能存在的投流码')}</div>
            </div>
          </div>
        </Form>
      </DMFormItemContext.Provider>
    </DMModal>
  );
};
export default ParseADCodeModal;
