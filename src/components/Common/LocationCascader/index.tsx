import I18N from '@/i18n';
import { Badge, Dropdown, Menu, Typography } from 'antd';
import selectorStyles from '@/components/Common/Selector/index.less';
import type { FC } from 'react';
import { useCallback, useState } from 'react';
import { OptionPlaceholder } from '@/components/Common/Placeholder';
import type { SelectorProps } from '../Selector';
import Selector from '../Selector';
import { metaCountriesGet } from '@/services/api-MetaAPI/MetaGeoController';
import { useRequest } from 'umi';
import ColoursIcon from '@/components/Common/ColoursIcon';
import { isIconValid } from '@/utils/iconUtils';
import _ from 'lodash';
import { ipCountriesGet } from '@/services/api-ShopAPI/IpController';
import { ippCountriesGet } from '@/services/api-IpPoolAPI/IpPoolController';
import IpLocation from '@/components/IpLocation';

/**
 * 以IP归属地为参照，
 */
const HackIconMap = {
  Salvador: 'El_Salvador',
  Korea: 'South_Korea',
  HongKong: 'Hong_Kong',
  Portuguese: 'Portugal',
  Global: 'Global',
  Macau: 'Macao',
};

export function hasValidCountryFlag(area: string) {
  return HackIconMap[area] || isIconValid(area);
}
export function hackAreaIcon(area: string, defaultIcon: string) {
  if (HackIconMap[area]) {
    return HackIconMap[area];
  }
  if (!isIconValid(area)) {
    return defaultIcon;
  }
  return area;
}
export const AreaIcon = (props: {
  [x: string]: any;
  area: any;
  defaultIcon?: string;
  size?: number;
}) => {
  const { area, size = 16, defaultIcon = 'IPguishushiqu_24', ...others } = props;
  return <ColoursIcon size={size} className={hackAreaIcon(area, defaultIcon)} {...others} />;
};
export const transformLocation = (location: API.IpLocationDto) => {
  const {
    id,
    countryCode,
    provinceCode,
    country,
    countryEn,
    province,
    provinceEn,
    city,
    cityEn,
    level,
  } = location;
  if (!id) {
    return;
  }
  switch (level) {
    case 'Country':
      return {
        type: 'country',
        value: countryCode,
        id,
        title: (I18N.isCn() ? country : countryEn) || countryEn,
      };
    case 'Province':
      return {
        type: 'province',
        value: provinceCode,
        id,
        title: (I18N.isCn() ? province : provinceEn) || provinceEn || province,
      };
    case 'City':
      return {
        type: 'city',
        value: cityEn,
        id,
        title: (I18N.isCn() ? city : cityEn) || cityEn || city,
      };
    default:
      return;
  }
};

export function getInitialLocation(location: API.IpLocationDto) {
  const _location = location || {};
  const {
    level,
    countryCode,
    cityEn,
    provinceCode,
    id,
    country,
    countryEn,
    province,
    provinceEn,
    city,
  } = _location;
  const country_item = {
    type: 'country',
    value: countryCode,
    title: (I18N.isCn() ? country : countryEn) || countryEn,
  };
  const province_item = {
    type: 'province',
    value: provinceCode,
    title: (I18N.isCn() ? province : provinceEn) || provinceEn || province,
  };
  const city_item = {
    type: 'city',
    value: cityEn,
    id,
    title: (I18N.isCn() ? city : cityEn) || cityEn || city,
  };
  switch (level) {
    case 'District':
    case 'City':
      return [country_item, province_item, city_item];
    case 'Province':
      return [country_item, { province_item, id }];
    case 'Country':
    case 'Continent':
      return [{ ...country_item, id }];
    default:
      return [];
  }
}

export function getDynamicStrategyLocation(location: API.IpLocationDto) {
  return getInitialLocation(location);
}
export function getDynamicStrategyLevel(locs: { type: 'Country' | 'Province' | 'City' }[]) {
  return _.capitalize(_.last(locs)?.type || 'None');
}
export function getDynamicLocationId(locs: { id: number }[]) {
  return _.last(locs)?.id;
}

function useLocationMetaRequest(
  options: { type?: 'meta' | 'ip' | 'ipp'; valuePropName?: 'name' | 'code' | 'nameEn' } = {},
) {
  const { type = 'ip', valuePropName = 'name' } = options;
  return useRequest(
    () => {
      if (type === 'meta') {
        return metaCountriesGet({ includeLocation: true });
      }
      if (type === 'ipp') {
        return ippCountriesGet();
      }
      return ipCountriesGet();
    },
    {
      formatResult(res) {
        const list: API.CountryDto[] = [];
        let headList: API.CountryDto[] = [];
        res.data?.forEach((item) => {
          const { code } = item;
          if (SortHack.includes(code!)) {
            headList.push(item);
          } else {
            list.push(item);
          }
        });
        headList = _.sortBy(headList, ({ code }) => SortHack.indexOf(code!));

        return [...headList, ...list].map((item) => {
          const { nameEn, name } = item;
          return {
            label: <IpLocation location={{ countryEn: nameEn, country: name }} />,
            value: item[valuePropName],
            ...item,
          };
        });
      },
    },
  );
}

const SortHack = ['CN', 'HK', 'MO', 'TW', 'US'];
export const CuteLocationDropdown = (props: {
  value: string | any[];
  onChange: (value: any, option: any) => void;
  type?: 'meta' | 'ip' | 'ipp';
  valuePropName?: 'name' | 'code' | 'nameEn';
}) => {
  const { valuePropName = 'name', type = 'ip', onChange, value } = props;
  const { loading, data } = useLocationMetaRequest({ valuePropName, type });
  const [cuteState, setCuteState] = useState<string[]>(() => {
    return _.isArray(value) && value.length > 0 ? value : [];
  });
  const _onChange = useCallback(
    (info: { selectedKeys?: string[] } = {}) => {
      const { selectedKeys = [] } = info;
      let _value = selectedKeys;
      if (selectedKeys.length) {
        _value = [selectedKeys.pop()!];
      }
      setCuteState(_value);
      onChange?.(
        _value,
        _.filter(data, (item) => {
          return _value.includes(item.value);
        }),
      );
    },
    [data],
  );
  return (
    <Dropdown
      arrow={false}
      placement={'bottomLeft'}
      overlayClassName={selectorStyles.horizontalOverlay}
      overlayStyle={{ maxWidth: 420 }}
      overlay={
        <Menu
          multiple
          selectable
          selectedKeys={cuteState}
          onDeselect={_onChange}
          onSelect={_onChange}
        >
          {data?.map((item: any) => {
            return <Menu.Item key={item.value}>{item.label}</Menu.Item>;
          })}
        </Menu>
      }
    >
      <Badge dot={!!cuteState.length} size={'small'} showZero={false}>
        <Typography.Link disabled={loading}>
          <ColoursIcon disabled={loading} size={20} className="IPguishushiqu_24" />
        </Typography.Link>
      </Badge>
    </Dropdown>
  );
};
export const LocationSelector: FC<
  SelectorProps & { valuePropName?: string; type?: 'meta' | 'ipp' | 'ip' }
> = (props) => {
  const {
    showSearch = true,
    showPlaceholderOption = true,
    placeholder = <OptionPlaceholder type="location" />,
    valuePropName = 'name',
    type = 'ip',
    ...others
  } = props;
  const { loading, data } = useLocationMetaRequest({ valuePropName, type });

  return (
    <Selector
      showSearch={showSearch}
      virtual={false}
      horizontal
      loading={loading}
      options={data}
      showPlaceholderOption={showPlaceholderOption}
      placeholder={showPlaceholderOption ? placeholder : I18N.t('请选择归属地')}
      filterOption={(input, option: API.CountryDto & { value: string }) => {
        const { name, nameEn, value, code } = option;
        const searchVal = input.toLowerCase();
        // 从name, nameEn, value中查找
        return _.some([name, nameEn, value, code], (item) => {
          return item?.toLowerCase().includes(searchVal);
        });
      }}
      {...others}
    />
  );
};
