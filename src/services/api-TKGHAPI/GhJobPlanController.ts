// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 添加一个调度计划 POST /api/gh/plans/addPlan */
export async function ghPlansAddPlanPost(
  body: API.CreateGhJobPlanRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultGhJobPlanVo>('/api/gh/plans/addPlan', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 检查计划名称是否存在 GET /api/gh/plans/checkNameExists */
export async function ghPlansCheckNameExistsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghPlansCheckNameExistsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultboolean>('/api/gh/plans/checkNameExists', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 添加交互记录 DELETE /api/gh/plans/deletePlan */
export async function ghPlansDeletePlanDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghPlansDeletePlanDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/plans/deletePlan', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询计划列表 GET /api/gh/plans/plans */
export async function ghPlansPlansGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghPlansPlansGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListGhJobPlanVo>('/api/gh/plans/plans', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 执行一个任务 POST /api/gh/plans/runJob */
export async function ghPlansRunJobPost(
  body: API.CreateGhJobPlanRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/plans/runJob', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 切换计划的开启状态 PUT /api/gh/plans/togglePlanEnabled */
export async function ghPlansTogglePlanEnabledPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghPlansTogglePlanEnabledPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/plans/togglePlanEnabled', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** updatePlan POST /api/gh/plans/updatePlan */
export async function ghPlansUpdatePlanPost(
  body: API.UpdateGhJobPlanRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultGhJobPlanVo>('/api/gh/plans/updatePlan', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
