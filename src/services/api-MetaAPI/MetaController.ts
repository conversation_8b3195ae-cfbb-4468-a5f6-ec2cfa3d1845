// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取App版本配置信息 GET /api/meta/app/versionConfigs */
export async function metaAppVersionConfigsGet(options?: { [key: string]: any }) {
  return request<API.WebResultAppVersionConfig>('/api/meta/app/versionConfigs', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 支持的云厂商列表 GET /api/meta/cloud/providers */
export async function metaCloudProvidersGet(options?: { [key: string]: any }) {
  return request<API.WebResultListCloudProviderVo>('/api/meta/cloud/providers', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取云厂商的区域列表 GET /api/meta/cloud/regions */
export async function metaCloudRegionsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.metaCloudRegionsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListCloudProviderVo>('/api/meta/cloud/regions', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取花瓣类型信息 GET /api/meta/creditTypes */
export async function metaCreditTypesGet(options?: { [key: string]: any }) {
  return request<API.WebResultListCreditTypeVo>('/api/meta/creditTypes', {
    method: 'GET',
    ...(options || {}),
  });
}

/** getExchangeList GET /api/meta/exchange/list */
export async function metaExchangeListGet(options?: { [key: string]: any }) {
  return request<API.WebResultListExchangeDto>('/api/meta/exchange/list', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取货币转换汇率 GET /api/meta/exchange/rate */
export async function metaExchangeRateGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.metaExchangeRateGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultdouble>('/api/meta/exchange/rate', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询运营配置 GET /api/meta/sysOpConfig */
export async function metaSysOpConfigGet(options?: { [key: string]: any }) {
  return request<API.WebResultSystemOpConfig>('/api/meta/sysOpConfig', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取TK套餐参数类型 GET /api/meta/tk/pack/paramDef */
export async function metaTkPackParamDefGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTkPackParamVo>('/api/meta/tk/pack/paramDef', {
    method: 'GET',
    ...(options || {}),
  });
}
