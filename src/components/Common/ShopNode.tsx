import type { CSSProperties } from 'react';
import { useCallback, useMemo } from 'react';
import { useEffect, useState } from 'react';
import { shopByIdPlatformGet, shopByShopIdBriefGet } from '@/services/api-ShopAPI/ShopController';
import { Spin, Tooltip, Typography } from 'antd';
import { SkipErrorNotifyOption } from '@/utils/utils';
import colors from '@/style/color.less';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { RESOURCE_NOT_FOUND } from '@/constants/ErrorCode';
import { AreaIcon } from '@/components/Common/LocationCascader';
import Placeholder from '@/components/Common/Placeholder';

const ShopCache: Record<number, Promise<string>> = {};
const ShopByIdCache: Record<number, Promise<API.ShopBriefVo>> = {};
// 同步缓存，存储已解析的数据
const ShopByIdSyncCache: Record<number, API.ShopBriefVo | null> = {};

export function useShopPlatformCacheById(shopId: number, manual = true) {
  const [data, setData] = useState<string>();
  useEffect(() => {
    if (!Number.isNaN(Number(shopId)) && shopId) {
      if (!ShopCache[shopId]) {
        ShopCache[shopId] = shopByIdPlatformGet(
          {
            id: shopId!,
          },
          SkipErrorNotifyOption,
        )
          .then((res) => {
            return res.data!;
          })
          .catch((e) => {
            return 'Other';
          });
      }
      ShopCache[shopId].then((res) => {
        setData(res);
      });
    }
  }, [shopId]);
  return data;
}
export function useGetShopBriefById() {
  return useCallback((id: number) => {
    if (!ShopByIdCache[id]) {
      ShopByIdCache[id] = shopByShopIdBriefGet(
        {
          shopId: id,
        },
        SkipErrorNotifyOption,
      )
        .then((res) => {
          const data = res.data!;
          // 缓存到同步缓存中
          ShopByIdSyncCache[id] = data;
          return data;
        })
        .catch((e) => {
          const errorData = {
            code: e?.data?.code,
            id,
            error: true,
            message: e.message,
          } as API.ShopBriefVo;
          // 错误也缓存到同步缓存中
          ShopByIdSyncCache[id] = errorData;
          return errorData;
        });
    }
    return ShopByIdCache[id];
  }, []);
}

/**
 * 同步获取 shopBrief 的方法
 * @param id shopId
 * @returns shopBrief 数据或 null（如果正在请求中或未请求）
 */
export function getShopBriefByIdSync(id: number): API.ShopBriefVo | null {
  // 如果同步缓存中已有数据，直接返回
  if (ShopByIdSyncCache[id] !== undefined) {
    return ShopByIdSyncCache[id];
  }

  // 如果没有缓存，发起请求（但不等待结果）
  if (!ShopByIdCache[id]) {
    ShopByIdCache[id] = shopByShopIdBriefGet(
      {
        shopId: id,
      },
      SkipErrorNotifyOption,
    )
      .then((res) => {
        const data = res.data!;
        // 缓存到同步缓存中
        ShopByIdSyncCache[id] = data;
        return data;
      })
      .catch((e) => {
        const errorData = {
          code: e?.data?.code,
          id,
          error: true,
          message: e.message,
        } as API.ShopBriefVo;
        // 错误也缓存到同步缓存中
        ShopByIdSyncCache[id] = errorData;
        return errorData;
      });
  }

  // 返回 null 表示正在请求中
  return null;
}
export function useShopBriefById(shopId?: number) {
  const [data, setData] = useState<API.ShopBriefVo>();
  const getShopBriefById = useGetShopBriefById();
  useEffect(() => {
    if (!Number.isNaN(Number(shopId)) && shopId !== undefined) {
      getShopBriefById(shopId).then((res) => {
        setData(res!);
      });
    }
  }, [getShopBriefById, shopId]);
  return data;
}

export const ShopIdNode = (props: {
  shopId: number;
  shopName: string;
  style?: CSSProperties;
  className?: string;
  iconSize?: number;
}) => {
  const { shopId, style, className, iconSize, shopName } = props;
  const platform = useShopPlatformCacheById(shopId);

  if (!platform) {
    return <Spin size={'small'} />;
  }
  return (
    <div
      className={className}
      style={{
        display: 'flex',
        maxWidth: '100%',
        overflow: 'hidden',
        gap: 4,
        alignItems: 'center',
        ...style,
      }}
    >
      <span style={{ minWidth: iconSize }}>
        <AreaIcon area={platform?.area} size={iconSize} />
      </span>
      <Typography.Text ellipsis={{ tooltip: shopName }} style={{ flex: 1, color: 'inherit' }}>
        {shopName}
      </Typography.Text>
    </div>
  );
};
export const ShopNodeById = (props: {
  id: number;
  style?: CSSProperties;
  className?: string;
  iconSize?: number;
  children?: any;
  onClick?: (data: API.ShopBriefVo) => void;
  showTooltip?: boolean;
}) => {
  const {
    id,
    style = {},
    className,
    iconSize,
    showTooltip = true,
    children = null,
    onClick,
  } = props;
  const data = useShopBriefById(id);
  const clickable = useMemo(() => {
    return data && onClick;
  }, [data, onClick]);
  if (!data) {
    return <IconFontIcon iconName={'loading_24'} spin style={{ fontSize: iconSize }} />;
  }
  if (data?.error) {
    if (data.code === RESOURCE_NOT_FOUND) {
      // 资源不存在就不显示了
      return children;
    }
    return (
      <Tooltip title={data.message}>
        <IconFontIcon iconName={'baoqian_24'} size={iconSize} color={colors.primaryColor} />
      </Tooltip>
    );
  }
  const { platform, name } = data;

  return (
    <div
      className={className}
      style={{
        display: 'flex',
        maxWidth: '100%',
        overflow: 'hidden',
        gap: 4,
        alignItems: 'center',
      }}
    >
      <AreaIcon area={platform?.area!} size={iconSize} />
      <Typography.Text
        onClick={() => {
          if (clickable) {
            onClick?.(data);
          }
        }}
        ellipsis={{ tooltip: showTooltip ? name : false }}
        style={{
          flex: 1,
          color: clickable ? colors.primaryColor : 'inherit',
          cursor: clickable ? 'pointer' : 'default',
          ...style,
        }}
      >
        {name}
      </Typography.Text>
    </div>
  );
};
export function ShopDetailNode(props: {
  data: API.ShopDetailVo;
  iconSize?: number;
  style?: CSSProperties;
  className?: string;
  onClick?: (e: any) => any;
}) {
  const { data, className, style, iconSize, onClick } = props;
  const { platform, name } = data;
  const node = useMemo(() => {
    return (
      <Typography.Text
        onClick={(e) => {
          if (onClick) {
            onClick(e);
          }
        }}
        ellipsis={{ tooltip: name }}
        style={{
          flex: 1,
          color: onClick ? colors.primaryColor : 'inherit',
          cursor: onClick ? 'pointer' : 'inherit',
        }}
      >
        {name}
      </Typography.Text>
    );
  }, [name, onClick]);
  return (
    <div
      className={className}
      style={{
        display: 'flex',
        maxWidth: '100%',
        overflow: 'hidden',
        gap: 4,
        alignItems: 'center',
        ...style,
      }}
    >
      <span style={{ minWidth: iconSize }}>
        <AreaIcon area={platform?.area} size={iconSize} />
      </span>
      {node}
    </div>
  );
}
export const ShopNameLinkById = (props: {
  id: number;
  onClick?: (data: API.ShopBriefVo) => void;
  style?: CSSProperties;
  showTooltip?: boolean;
}) => {
  const { id, onClick, style, showTooltip = true } = props;
  const data = useShopBriefById(id);

  if (!data) {
    return <IconFontIcon iconName={'loading_24'} spin />;
  }
  if (data?.error) {
    if (data.code === RESOURCE_NOT_FOUND) {
      // 资源不存在就不显示了
      return <Placeholder>{id}</Placeholder>;
    }
    return (
      <Typography.Link type={'danger'} ellipsis>
        {data.message}
      </Typography.Link>
    );
  }
  return (
    <Typography.Text
      style={{ color: colors.primaryColor, cursor: 'pointer', ...(style || {}) }}
      onClick={() => {
        if (data && onClick) {
          onClick(data);
        }
      }}
      ellipsis={{
        tooltip: showTooltip ? data.name : false,
      }}
    >
      {data.name!}
    </Typography.Text>
  );
};
export default ShopIdNode;
