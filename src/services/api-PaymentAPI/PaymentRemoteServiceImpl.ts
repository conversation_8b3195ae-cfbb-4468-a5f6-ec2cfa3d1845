// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 重新生产某个未生产成功订单 POST /api/remote/payment/checkProduced */
export async function remotePaymentCheckProducedPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remotePaymentCheckProducedPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/remote/payment/checkProduced', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 检查某个订单是否可安全退款，如果不可安全退款则会返回非空值 GET /api/remote/payment/checkRefundAble */
export async function remotePaymentCheckRefundAbleGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remotePaymentCheckRefundAbleGetParams,
  options?: { [key: string]: any },
) {
  return request<string>('/api/remote/payment/checkRefundAble', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 通知银行支付订单打款失败 POST /api/remote/payment/notifyBankPayFailed */
export async function remotePaymentNotifyBankPayFailedPost(
  body: API.NotifyBankPayFailedRequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/remote/payment/notifyBankPayFailed', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 通知银行支付订单打款成功 POST /api/remote/payment/notifyBankPaySuccess */
export async function remotePaymentNotifyBankPaySuccessPost(
  body: API.NotifyBankPaySuccessRequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/remote/payment/notifyBankPaySuccess', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 通知一个offline订单创建了。主要用来给团队用户发订单待支付消息 POST /api/remote/payment/notifyOfflineOrderCreated */
export async function remotePaymentNotifyOfflineOrderCreatedPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remotePaymentNotifyOfflineOrderCreatedPostParams,
  options?: { [key: string]: any },
) {
  return request<any>('/api/remote/payment/notifyOfflineOrderCreated', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 订单退款 POST /api/remote/payment/refundOrder */
export async function remotePaymentRefundOrderPost(
  body: API.RefundRequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/remote/payment/refundOrder', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 重新生产某个未生产成功订单 POST /api/remote/payment/reProduceOrder */
export async function remotePaymentReProduceOrderPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remotePaymentReProduceOrderPostParams,
  options?: { [key: string]: any },
) {
  return request<any>('/api/remote/payment/reProduceOrder', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
