// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** authGet GET /api/wx/portal/${param0} */
export async function wxPortalByAppidGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.wxPortalByAppidGetParams,
  options?: { [key: string]: any },
) {
  const { appid: param0, ...queryParams } = params;
  return request<string>(`/api/wx/portal/${param0}`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** post POST /api/wx/portal/${param0} */
export async function wxPortalByAppidPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.wxPortalByAppidPostParams,
  body: string,
  options?: { [key: string]: any },
) {
  const { appid: param0, ...queryParams } = params;
  return request<string>(`/api/wx/portal/${param0}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...queryParams,
    },
    data: body,
    ...(options || {}),
  });
}
