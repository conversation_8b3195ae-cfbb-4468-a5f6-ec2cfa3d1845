// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取买家统计数据 GET /api/tkshop/shop/${param0}/buyerShopStat */
export async function tkshopShopByShopIdBuyerShopStatGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopShopByShopIdBuyerShopStatGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultTkshopBuyerShopStat>(`/api/tkshop/shop/${param0}/buyerShopStat`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 汇报TK店铺健康状态 PUT /api/tkshop/shop/${param0}/reportHealth */
export async function tkshopShopByShopIdReportHealthPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopShopByShopIdReportHealthPutParams,
  body: API.TkshopReportHealthRequest,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/tkshop/shop/${param0}/reportHealth`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 汇报TK店铺ID PUT /api/tkshop/shop/${param0}/reportTkShopNo */
export async function tkshopShopByIdReportTkShopNoPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopShopByIdReportTkShopNoPutParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/tkshop/shop/${param0}/reportTkShopNo`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取店铺最后同步时间 POST /api/tkshop/shop/getLastSyncTime */
export async function tkshopShopGetLastSyncTimePost(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMaplong>('/api/tkshop/shop/getLastSyncTime', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 加载tkshop店铺信息 POST /api/tkshop/shop/loadTkshopInfo */
export async function tkshopShopLoadTkshopInfoPost(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTkshopShopVo>('/api/tkshop/shop/loadTkshopInfo', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 标记同步店铺结束 GET /api/tkshop/shop/markSyncDone */
export async function tkshopShopMarkSyncDoneGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopShopMarkSyncDoneGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/shop/markSyncDone', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** TKshop首页分页查询店铺 POST /api/tkshop/shop/page */
export async function tkshopShopPagePost(
  body: API.PageTkshopRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultShopHealthVo>('/api/tkshop/shop/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
