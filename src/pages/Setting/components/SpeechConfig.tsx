import I18N from '@/i18n';
import { useCallback, useEffect, useMemo, useState } from 'react';
import ProTable from '@ant-design/pro-table';
import { message, Space, Tabs, Tooltip, Typography } from 'antd';
import {
  ghSpeechDeletePost,
  ghSpeechGroupByGroupIdDelete,
  ghSpeechGroupListGet,
  ghSpeechPageGet,
  ghSpeechUpdateSortNoPut,
} from '@/services/api-TKGHAPI/GhSpeechController';
import IconFontIcon from '@/components/Common/IconFontIcon';
import DMConfirm from '@/components/Common/DMConfirm';
import { scrollProTableOptionFn } from '@/mixins/table';
import styled from 'styled-components';
import type { SortableContainerProps, SortEnd } from 'react-sortable-hoc';
import { SortableContainer, SortableElement, SortableHandle } from 'react-sortable-hoc';
import { arrayMove } from '@dnd-kit/sortable';
import { useRequest } from '@@/plugin-request/request';
import { StyledSettingCard } from '@/pages/Setting/utils';
import _ from 'lodash';
import { GhostModalCaller } from '@/mixins/modal';
import ModifySpeechContentModal from './ModifySpeechContentModal';
import { getSpeechPrefix } from '@/pages/TikTok/components/SpeechGroupSelector';
import ModifySpeechGroupModal from './ModifySpeechGroupModal';

const DragHandle = SortableHandle((props: { index: any }) => {
  const { index } = props;
  return (
    <span style={{ display: 'flex', alignItems: 'center', gap: 4, cursor: 'move' }}>
      <IconFontIcon iconName={'menu'} />
      <span>{index + 1}</span>
    </span>
  );
});
const SortableItem = SortableElement((props: React.HTMLAttributes<HTMLTableRowElement>) => (
  <tr {...props} />
));
const SortableBody = SortableContainer((props: React.HTMLAttributes<HTMLTableSectionElement>) => (
  <tbody {...props} />
));
const TabStyled = styled(Tabs)`
  height: auto !important;
  background: #fafafa;
  flex: 0 0 145px !important;
  border-right: 1px solid #dddddd !important;
  .ant-tabs-tab {
    margin-top: 0 !important;
    padding: 0 !important;
    overflow: hidden;
  }
  .ant-tabs-tab-active {
    color: #0f7cf4 !important;
  }
  .tab-item {
    display: flex;
    gap: 4px;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 8px 0 16px;
    overflow: hidden;
    position: relative;
    &.add {
      justify-content: center;
    }
    .content {
      flex: 1;
      overflow: hidden;
      /* 默认状态下，content 可以占用 actions 的空间 */
      margin-right: 0;
      transition: margin-right 0.2s ease;
    }
    .actions {
      /* 为 actions 预留固定宽度，避免布局跳动 */
      width: 20px;
      flex-shrink: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.2s ease, visibility 0.2s ease;
      /* 使用绝对定位，避免影响 content 的布局 */
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
    }
    &:hover {
      .content {
        /* hover 时为 actions 让出空间 */
        margin-right: 24px;
      }
      .actions {
        opacity: 1;
        visibility: visible;
      }
    }
  }
  .ant-tabs-tab-btn {
    color: inherit !important;
  }
  .ant-tabs-nav-add {
    margin-left: 0 !important;
    color: #0f7cf4;
    background: transparent;
  }
  .ant-tabs-tab-btn,
  .ant-tabs-nav-add {
    width: 100% !important;
    height: 40px !important;
    padding: 0 !important;
    border: none !important;
  }
`;

// 即时消息目前是后端完成替换模板字符串
const SpeechConfig = (props: API.ghSpeechGroupListGetParams) => {
  const { speechType, scene } = props;
  const title = getSpeechPrefix(scene, speechType);
  const [groupId, setGroupId] = useState<number>();
  const {
    data: groups,
    loading: loadingGroup,
    run: fetchGroups,
  } = useRequest(
    (_groupId?: number) => {
      return ghSpeechGroupListGet({
        speechType,
        scene,
      }).then((res) => {
        const data = res?.data || [];
        const list = _.sortBy(data, 'createTime', 'asc');
        setGroupId((prev) => {
          let contain = false;
          const target_group = _groupId || prev;
          for (let i = 0; i < list.length; i++) {
            if (String(list[i].id) === String(target_group)) {
              contain = true;
              break;
            }
          }
          return contain ? target_group : list[0]?.id;
        });
        return { data: list };
      });
    },
    {
      defaultLoading: true,
    },
  );
  const {
    data: dataSource,
    run: fetchList,
    loading,
  } = useRequest(
    async (_group?: number) => {
      if (!_group) {
        return {
          data: [],
        };
      }
      return ghSpeechPageGet({
        pageSize: 99999,
        pageNum: 1,
        scene,
        speechType,
        groupId: _group,
      }).then((res) => {
        return {
          data: res.data?.list,
        };
      });
    },
    {
      initialData: [],
      manual: true,
      defaultLoading: true,
    },
  );
  useEffect(() => {
    fetchList(groupId);
  }, [fetchList, groupId]);
  const onSortEnd = useCallback(
    async ({ oldIndex, newIndex }: SortEnd) => {
      if (!dataSource?.length || !groupId) {
        return;
      }
      if (oldIndex !== newIndex) {
        const ids = arrayMove(dataSource, oldIndex, newIndex).map((i) => i.id!);
        await ghSpeechUpdateSortNoPut(
          {
            groupId,
          },
          {
            ids,
          },
        );
        fetchList(groupId!);
      }
    },
    [dataSource, fetchList, groupId],
  );
  const DraggableContainer = (_props: SortableContainerProps) => (
    <SortableBody
      lockAxis={'y'}
      distance={5}
      lockToContainerEdges
      useDragHandle
      helperClass="row-dragging"
      onSortEnd={onSortEnd}
      {..._props}
    />
  );
  const DraggableBodyRow: React.FC<any> = ({ className, style, ...restProps }) => {
    // function findIndex base on Table rowKey props and should always be a right array index
    const index = dataSource?.findIndex((x) => x.id === restProps['data-row-key']);
    return <SortableItem disabled={dataSource?.length === 1} index={index} {...restProps} />;
  };
  const items = useMemo(() => {
    return (
      groups?.map((group) => {
        const { id, name } = group;
        return {
          label: (
            <div className={'tab-item'}>
              <span className={'content'}>
                <Typography.Text style={{ color: 'inherit' }} ellipsis>
                  {name}
                </Typography.Text>
              </span>
              <Space className={'actions'}>
                <Typography.Link
                  onClick={(e) => {
                    e.stopPropagation();
                    GhostModalCaller(
                      <ModifySpeechGroupModal
                        showDelete={groups?.length > 1}
                        group={group}
                        onUpdate={() => {
                          fetchGroups();
                        }}
                      />,
                      'ModifySpeechGroupModal',
                    );
                  }}
                >
                  <IconFontIcon size={14} iconName={'shezhi_24'} />
                </Typography.Link>
              </Space>
            </div>
          ),
          key: String(id),
          closable: false,
        };
      }) || []
    );
  }, [fetchGroups, groups]);
  return (
    <StyledSettingCard hoverable id={`hash_${speechType}_${scene}`}>
      <div className="title">{title}</div>
      <div
        className={'main'}
        style={{
          display: 'flex',
          flexDirection: 'row',
          flexWrap: 'nowrap',
          alignItems: 'stretch',
          overflow: 'hidden',
        }}
      >
        <TabStyled
          addIcon={
            <div
              className={'tab-item add'}
              onClick={() => {
                GhostModalCaller(
                  <ModifySpeechGroupModal
                    group={{
                      bizScene: scene,
                      speechType,
                    }}
                    onUpdate={(id) => {
                      fetchGroups(id);
                    }}
                  />,
                  'ModifySpeechGroupModal',
                );
              }}
            >
              <IconFontIcon size={14} iconName={'tianjia_24'} />
              <span>新分组</span>
            </div>
          }
          type={'editable-card'}
          items={items}
          tabPosition={'left'}
          activeKey={groupId?.toString()}
          onChange={(key) => {
            setGroupId(key);
          }}
        />
        <div style={{ height: 300, overflow: 'hidden' }}>
          <ProTable
            dataSource={dataSource}
            loading={loading || loadingGroup}
            components={{
              body: {
                wrapper: DraggableContainer,
                row: DraggableBodyRow,
              },
            }}
            {...scrollProTableOptionFn({
              alwaysShowFooter: !!groupId,
              pagination: false,
              footer: () => {
                if (!groupId) {
                  return false;
                }
                return (
                  <div style={{ flex: 1, display: 'flex', justifyContent: 'flex-end' }}>
                    <Tooltip title={I18N.t('添加话术')}>
                      <Typography.Link
                        onClick={() => {
                          GhostModalCaller(
                            <ModifySpeechContentModal
                              data={{ groupId, bizScene: scene, speechType }}
                              onUpdate={() => {
                                fetchList(groupId);
                              }}
                            />,
                            'ModifySpeechContentModal',
                          );
                        }}
                      >
                        <Space size={4}>
                          <IconFontIcon size={14} iconName={'tianjia_24'} />
                          <span>新话术</span>
                        </Space>
                      </Typography.Link>
                    </Tooltip>
                  </div>
                );
              },
            })}
            columns={[
              {
                dataIndex: 'no',
                title: I18N.t('序号'),
                width: '60px',
                render(_text, record, index) {
                  return <DragHandle index={index} />;
                },
              },
              {
                dataIndex: 'name',
                width: '120px',
                title: I18N.t('名称'),
                ellipsis: true,
              },
              {
                dataIndex: 'content',
                title: I18N.t('话术内容'),
                ellipsis: true,
              },
              {
                title: I18N.t('操作'),
                valueType: 'option',
                onCell() {
                  return {
                    className: 'option-cell',
                  };
                },
                width: '80px',
                render(_text, record) {
                  const { id } = record;
                  return [
                    <Typography.Link
                      key={'edit'}
                      onClick={() => {
                        GhostModalCaller(
                          <ModifySpeechContentModal
                            data={record}
                            onUpdate={() => {
                              fetchList(groupId);
                            }}
                          />,
                          'ModifySpeechContentModal',
                        );
                      }}
                    >
                      {I18N.t('编辑')}
                    </Typography.Link>,
                    <Typography.Link
                      key={'delete'}
                      type={'danger'}
                      onClick={() => {
                        DMConfirm({
                          title: I18N.t('删除话术'),
                          content: I18N.t('确定删除该话术吗？'),
                          onOk: async () => {
                            await ghSpeechDeletePost({
                              ids: [id],
                            });
                            message.success(I18N.t('删除成功'));
                            fetchList(groupId);
                          },
                        });
                      }}
                    >
                      {I18N.t('删除')}
                    </Typography.Link>,
                  ];
                },
              },
            ]}
          />
        </div>
      </div>
    </StyledSettingCard>
  );
};
export default SpeechConfig;
