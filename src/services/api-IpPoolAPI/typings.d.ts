declare namespace API {
  type AllocIppIpResult = {
    /** 如果分配成功，则返回分配的ip池IP ID */
    ippIpId?: number;
    /** 如果需要生产IP，则用下面的规则去调用 */
    produceIpSpecVo?: ProduceIpSpecVo;
  };

  type ClientProduceIpResult = {
    /** http响应代码 */
    httpCode?: number;
    /** 生产IP时的 */
    produceIpSpecVo?: ProduceIpSpecVo;
    /** http响应流 */
    responseBody?: string;
    /** http响应头 */
    responseHeaders?: Record<string, any>;
  };

  type CountryDto = {
    code?: string;
    continentCode?: string;
    continentName?: string;
    continentNameEn?: string;
    geonameId?: number;
    id?: number;
    inEu?: boolean;
    name?: string;
    nameEn?: string;
    show?: boolean;
  };

  type CreateIpPoolRequest = {
    allocateStrategy?: 'ByLessTraffic' | 'ByLoad' | 'ByOrder' | 'ByRandom';
    /** 白名单连接的接入点 */
    connectTransitIds?: number[];
    countryCode?: string;
    description?: string;
    domestic?: boolean;
    /** 是否开启白名单 */
    enableWhitelist?: boolean;
    exclusive?: boolean;
    lifetime?: number;
    name?: string;
    params?: Record<string, any>;
    produceFromTransit?: boolean;
    produceStrategy?: 'ProduceOnEmpty' | 'ProduceOnHand' | 'ProduceOnRequest';
    provider?: string;
    releaseStrategy?: 'Discard' | 'Reuse';
    transitIds?: number[];
    /** 切换策略 */
    transitType?: 'Auto' | 'Direct' | 'Transit';
    tunnelTypes?: ('clash' | 'direct' | 'jump' | 'localFrontend' | 'platform' | 'transit')[];
  };

  type DepartmentDto = {
    createTime?: string;
    hidden?: boolean;
    id?: number;
    invitingAuditEnabled?: boolean;
    invitingEnabled?: boolean;
    name?: string;
    parentId?: number;
    sortNumber?: number;
    teamId?: number;
  };

  type FingerprintDto = {
    /** 指纹对应浏览器名称，如chrome */
    browser?: 'Chrome' | 'Edge' | 'Safari';
    /** mongo里的id */
    configId?: string;
    /** 备用字段，指纹类型，抓取or生成 */
    createType?: 'Fetch' | 'Gen' | 'HyGen' | 'MKLong' | 'Temp' | 'Template' | 'Transfer';
    /** 创建者。有可能可能为空! */
    creatorId?: number;
    /**  指纹描述 */
    description?: string;
    /** id */
    id?: number;
    /** 浏览器主版本号，如 109 */
    majorVersion?: number;
    md5sum?: string;
    /** 指纹名称 */
    name?: string;
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    /** 指纹对应平台 */
    platform?: 'Android' | 'IOS' | 'Linux' | 'Mac' | 'Windows';
    stateless?: boolean;
    /** 团队ID */
    teamId?: number;
    /** 指纹所属模板id */
    templateId?: number;
    /** 最后更新时间 */
    updateTime?: string;
  };

  type FingerprintTemplateDto = {
    browser?: 'Chrome' | 'Edge' | 'Safari';
    configId?: string;
    createTime?: string;
    createType?: 'Fetch' | 'Gen' | 'HyGen' | 'MKLong' | 'Temp' | 'Template' | 'Transfer';
    creatorId?: number;
    description?: string;
    id?: number;
    /** 如果不为空则说明该模板生成的实例固定使用该版本号，否则使用随机版本 */
    majorVersion?: number;
    maxIns?: number;
    md5sum?: string;
    name?: string;
    platform?: 'Android' | 'IOS' | 'Linux' | 'Mac' | 'Windows';
    teamId?: number;
    usedIns?: number;
  };

  type IdNameVo = {
    id?: number;
    name?: string;
  };

  type IpLocationDto = {
    city?: string;
    cityEn?: string;
    continent?: string;
    continentCode?: string;
    continentEn?: string;
    country?: string;
    countryCode?: string;
    countryEn?: string;
    createTime?: string;
    geonameId?: number;
    id?: number;
    inEu?: boolean;
    latitude?: number;
    level?: 'City' | 'Continent' | 'Country' | 'District' | 'None' | 'Province' | 'Unknown';
    locale?: string;
    longitude?: number;
    postalCode?: string;
    province?: string;
    provinceCode?: string;
    provinceEn?: string;
    show?: boolean;
    teamId?: number;
    timezone?: string;
  };

  type IppIpDto = {
    activeSessions?: number;
    areaCode?: number;
    createTime?: string;
    directDownTraffic?: number;
    directUpTraffic?: number;
    expireTime?: string;
    host?: string;
    hostDomestic?: boolean;
    id?: number;
    invalidTime?: string;
    ippId?: number;
    lastAllocTime?: string;
    lastProbeTime?: string;
    locationId?: number;
    outboundIp?: string;
    password?: string;
    port?: number;
    probeError?: string;
    proxyType?: string;
    refTeamIp?: number;
    status?: 'Available' | 'Pending' | 'Unavailable';
    teamId?: number;
    transitDownTraffic?: number;
    transitUpTraffic?: number;
    username?: string;
    valid?: boolean;
  };

  type ippIppProvidersGetParams = {
    /** valid */
    valid?: boolean;
  };

  type IpPoolDto = {
    allocateStrategy?: 'ByLessTraffic' | 'ByLoad' | 'ByOrder' | 'ByRandom';
    capacity?: number;
    connectTransits?: string;
    createTime?: string;
    creator?: number;
    description?: string;
    domestic?: boolean;
    enableWhitelist?: boolean;
    exclusive?: boolean;
    id?: number;
    lastApiTime?: string;
    lifetime?: number;
    locationId?: number;
    minApiInterval?: number;
    minApiNum?: number;
    name?: string;
    produceFromTransit?: boolean;
    produceStrategy?: 'ProduceOnEmpty' | 'ProduceOnHand' | 'ProduceOnRequest';
    produced?: number;
    provider?: string;
    releaseStrategy?: 'Discard' | 'Reuse';
    teamId?: number;
    transitType?: 'Auto' | 'Direct' | 'Transit';
    transits?: string;
    tunnelTypes?: string;
  };

  type IppParamDefDto = {
    /** 描述 */
    description?: string;
    id?: number;
    /** 参数名 */
    paramKey: string;
    /** 参数标签 */
    paramLabel: string;
    /** 参数类型 */
    paramType:
      | 'ApiEndpoint'
      | 'ApiEndpointRaw'
      | 'ApiHeader'
      | 'ApiHttpMethod'
      | 'ApiPassword'
      | 'ApiQueryParam'
      | 'ApiSignKey'
      | 'ApiUsername'
      | 'ApiWhitelist'
      | 'GlobalWhitelist'
      | 'MinApiInterval'
      | 'MinApiNum'
      | 'ProxyPassword'
      | 'ProxyType'
      | 'ProxyUsername'
      | 'ProxyWhitelist'
      | 'Unknown';
    /** 供应商 */
    provider: string;
    /** 是否必填参数 */
    required: boolean;
    /** 排序号 */
    sortNo: number;
    /** 是否向用户展示 */
    uiShow: boolean;
    /** 是否由用户输入 */
    userInput: boolean;
    /** 参数值 */
    value?: string;
  };

  type ippParamDefsGetParams = {
    /** provider */
    provider: string;
  };

  type IppParamDetailVo = {
    def?: IppParamDefDto;
    id?: number;
    paramKey?: string;
    paramType?:
      | 'ApiEndpoint'
      | 'ApiEndpointRaw'
      | 'ApiHeader'
      | 'ApiHttpMethod'
      | 'ApiPassword'
      | 'ApiQueryParam'
      | 'ApiSignKey'
      | 'ApiUsername'
      | 'ApiWhitelist'
      | 'GlobalWhitelist'
      | 'MinApiInterval'
      | 'MinApiNum'
      | 'ProxyPassword'
      | 'ProxyType'
      | 'ProxyUsername'
      | 'ProxyWhitelist'
      | 'Unknown';
    poolId?: number;
    provider?: string;
    teamId?: number;
    value?: string;
  };

  type ippPoolByIppIdAllocIppIpGetParams = {
    /** ippId */
    ippId: number;
  };

  type ippPoolByIppIdBasicInfoPutParams = {
    /** ippId */
    ippId: number;
  };

  type ippPoolByIppIdDeleteParams = {
    /** ippId */
    ippId: number;
  };

  type ippPoolByIppIdGetParams = {
    /** ippId */
    ippId: number;
  };

  type ippPoolByIppIdIpsDeleteParams = {
    /** ippId */
    ippId: number;
    /** ippIpIds */
    ippIpIds: number;
  };

  type ippPoolByIppIdIpsGetParams = {
    /** ippId */
    ippId: number;
    /** minActiveSessions */
    minActiveSessions?: number;
    /** valid */
    valid?: boolean;
    /** ip */
    ip?: string;
    /** host */
    host?: string;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** 排序字段 */
    sortFiled?:
      | 'areaCode'
      | 'createTime'
      | 'directDownTraffic'
      | 'directUpTraffic'
      | 'expireTime'
      | 'host'
      | 'invalidTime'
      | 'lastAllocTime'
      | 'outboundIp'
      | 'proxyType'
      | 'status'
      | 'transitDownTraffic'
      | 'transitUpTraffic'
      | 'username'
      | 'valid';
    /** 顺序 */
    sortOrder?: 'asc' | 'desc';
  };

  type ippPoolByIppIdParseIppIpPostParams = {
    /** ippId */
    ippId: number;
  };

  type ippPoolByIppIdProviderParamPutParams = {
    /** ippId */
    ippId: number;
  };

  type ippPoolByIppIdStrategyPutParams = {
    /** ippId */
    ippId: number;
  };

  type ippPoolPageGetParams = {
    /** 供应商 */
    provider?: string;
    /** 国家 */
    country?: string;
    /** 城市 */
    city?: string;
    /** 按标签id过滤 */
    tagIds?: string;
    /** name */
    name?: string;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** 排序字段 */
    sortFiled?: 'capacity' | 'createTime' | 'lastApiTime' | 'lifetime' | 'provider';
    /** 顺序 */
    sortOrder?: 'asc' | 'desc';
  };

  type ippPoolsDeleteParams = {
    /** ippIds */
    ippIds: number;
  };

  type IppPoolVo = {
    allocateStrategy?: 'ByLessTraffic' | 'ByLoad' | 'ByOrder' | 'ByRandom';
    capacity?: number;
    connectTransits?: string;
    createTime?: string;
    creator?: number;
    description?: string;
    domestic?: boolean;
    enableWhitelist?: boolean;
    exclusive?: boolean;
    id?: number;
    lastApiTime?: string;
    lifetime?: number;
    location?: IpLocationDto;
    locationId?: number;
    minApiInterval?: number;
    minApiNum?: number;
    name?: string;
    produceFromTransit?: boolean;
    produceStrategy?: 'ProduceOnEmpty' | 'ProduceOnHand' | 'ProduceOnRequest';
    produced?: number;
    provider?: string;
    providerDto?: IppProviderDto;
    releaseStrategy?: 'Discard' | 'Reuse';
    shops?: ShopDetailVo[];
    size?: number;
    tags?: TagDto[];
    teamId?: number;
    transitType?: 'Auto' | 'Direct' | 'Transit';
    transits?: string;
    tunnelTypes?: string;
  };

  type IppPoolWithParamsVo = {
    allocateStrategy?: 'ByLessTraffic' | 'ByLoad' | 'ByOrder' | 'ByRandom';
    capacity?: number;
    connectTransits?: string;
    createTime?: string;
    creator?: number;
    description?: string;
    domestic?: boolean;
    enableWhitelist?: boolean;
    exclusive?: boolean;
    id?: number;
    lastApiTime?: string;
    lifetime?: number;
    location?: IpLocationDto;
    locationId?: number;
    minApiInterval?: number;
    minApiNum?: number;
    name?: string;
    params?: IppParamDetailVo[];
    produceFromTransit?: boolean;
    produceStrategy?: 'ProduceOnEmpty' | 'ProduceOnHand' | 'ProduceOnRequest';
    produced?: number;
    provider?: string;
    providerDto?: IppProviderDto;
    releaseStrategy?: 'Discard' | 'Reuse';
    shops?: ShopDetailVo[];
    size?: number;
    tags?: TagDto[];
    teamId?: number;
    transitType?: 'Auto' | 'Direct' | 'Transit';
    transits?: string;
    tunnelTypes?: string;
  };

  type IppProviderDto = {
    api?: boolean;
    description?: string;
    hasExpire?: boolean;
    homeUrl?: string;
    id?: number;
    name?: string;
    produceFromTransit?: boolean;
    promoUrl?: string;
    provider?: string;
    rangeType?: 'Domestic' | 'Global' | 'Oversea';
    valid?: boolean;
  };

  type IppWhitelistConfig = {
    ippDomesticWhitelist?: string[];
    ippOverseasWhitelist?: string[];
  };

  type MemberVo = {
    /** 账号 */
    account?: string;
    avatar?: string;
    createTime?: string;
    /** 邮箱 */
    email?: string;
    gender?: 'FEMALE' | 'MALE' | 'UNSPECIFIC';
    id?: number;
    nickname?: string;
    partnerId?: number;
    /** 手机 */
    phone?: string;
    residentCity?: string;
    roleCode?: 'boss' | 'manager' | 'staff' | 'superadmin';
    signature?: string;
    status?: 'ACTIVE' | 'BLOCK' | 'DELETED' | 'INACTIVE';
    teamNickname?: string;
    tenant?: number;
    userType?: 'NORMAL' | 'PARTNER' | 'SHADOW';
    weixin?: string;
  };

  type PageResultIppIpDto = {
    current?: number;
    list?: IppIpDto[];
    pageSize?: number;
    total?: number;
  };

  type PageResultIppPoolVo = {
    current?: number;
    list?: IppPoolVo[];
    pageSize?: number;
    total?: number;
  };

  type ProduceIpSpecVo = {
    /** 需要追加的请求头 */
    headers?: Record<string, any>;
    /** 请求方法，比如GET/POST等 */
    method?: string;
    /** 请求URL，携带参数 */
    url?: string;
  };

  type ShopChannelVo = {
    createTime?: string;
    dynamicStrategy?: 'Off' | 'Remain' | 'SwitchOnSession';
    id?: number;
    /** 通道的IP */
    ip?: TeamIpVo;
    ipId?: number;
    /** 通道的IP池 */
    ipPool?: IpPoolDto;
    ippId?: number;
    locationId?: number;
    locationLevel?: 'City' | 'Continent' | 'Country' | 'District' | 'None' | 'Province' | 'Unknown';
    officialChannelId?: number;
    primary?: boolean;
    shopId?: number;
    teamId?: number;
  };

  type ShopChatDto = {
    chatAccount?: string;
    chatType?:
      | 'None'
      | 'email'
      | 'facebookMessager'
      | 'line'
      | 'qq'
      | 'skype'
      | 'wechat'
      | 'whatsapp'
      | 'zalo';
    id?: number;
    shopId?: number;
    teamId?: number;
  };

  type ShopDetailVo = {
    /** 访问账户次数 */
    accessCount?: number;
    /** 账户账号 */
    account?: string;
    /** 是否允许用户自行安装插件 */
    allowExtension?: boolean;
    /** 会话是否允许监视 */
    allowMonitor?: boolean;
    /** 是否允许跳过敏感操作 */
    allowSkip?: boolean;
    /** 是否自动代填 */
    autoFill?: boolean;
    bookmarkBar?: string;
    /** 通道列表 */
    channels?: ShopChannelVo[];
    /** 联系方式 */
    chats?: ShopChatDto[];
    coordinateId?: string;
    /** 创建时间 */
    createTime?: string;
    /** 创建人信息 */
    creator?: UserVo;
    /** 创建者 */
    creatorId?: number;
    deleteTime?: string;
    deleting?: boolean;
    /** 描述 */
    description?: string;
    domainPolicy?: 'Blacklist' | 'None' | 'Whitelist';
    /** 黑白名单 */
    domains?: ShopDomainItem[];
    /** 是否独占访问 */
    exclusive?: boolean;
    extension?: 'both' | 'extension' | 'huayang';
    extraProp?: string;
    /** 绑定的指纹Id */
    fingerprintId?: number;
    /** 绑定的指纹模板Id（只有无状态账号才允许绑定指纹模板） */
    fingerprintTemplateId?: number;
    /** 指纹模版 */
    fingerprintTemplateVo?: FingerprintTemplateDto;
    /** 指纹 */
    fingerprintVo?: FingerprintDto;
    frontUrl?: string;
    googleTranslateSpeed?: boolean;
    /** 授权的部门 */
    grantDepartmentList?: DepartmentDto[];
    /** 授权来源 */
    grantSource?: 'FromDepartment' | 'FromUser';
    /** 授权给的用户 */
    grantUserVoList?: MemberVo[];
    homePage?: string;
    homePageSites?: string;
    /** id */
    id?: number;
    imageForbiddenSize?: number;
    intranetEnabled?: boolean;
    /** IP绑定次数 */
    ipBindCount?: number;
    /** IP绑定时间 */
    ipBindTime?: string;
    ipSwitchCheckInterval?: number;
    ipSwitchStrategy?: 'Abort' | 'Alert' | 'Off';
    /** IP解绑时间 */
    ipUnbindTime?: string;
    /** 本地代理配置 */
    lanProxy?: ShopLanProxyDto;
    lastAccessTime?: string;
    lastAccessUser?: number;
    lastSyncTime?: string;
    loginStatus?: 'Offline' | 'Online' | 'Unknown';
    /** 任务栏分身标记文字 */
    markCode?: string;
    /** 任务栏分身标记背景颜色 */
    markCodeBg?: number;
    monitorPerception?: boolean;
    /** 账户名称 */
    name?: string;
    nameBookmarkEnabled?: boolean;
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    /** 经营品类 */
    operatingCategory?:
      | '医药保健'
      | '图书文具'
      | '宠物用品'
      | '家具建材'
      | '家电电器'
      | '工业用品'
      | '户外运动'
      | '手机数码'
      | '手表眼镜'
      | '护肤美妆'
      | '母婴玩具'
      | '汽车配件'
      | '生活家居'
      | '电商其他'
      | '电脑平板'
      | '艺术珠宝'
      | '花园聚会'
      | '计生情趣'
      | '软件程序'
      | '鞋服箱包'
      | '音乐影视'
      | '食品生鲜'
      | '鲜花绿植';
    /** 主账号 */
    parentShop?: ShopDto;
    parentShopId?: number;
    /** 主团队 */
    parentTeam?: IdNameVo;
    /** 密码 */
    password?: string;
    /** 平台信息 */
    platform?: ShopPlatformVo;
    /** 平台ID */
    platformId?: number;
    privateAddress?: string;
    /** 私密网站 */
    privateDomains?: string[];
    privateTitle?: string;
    recordPerception?: boolean;
    recordPolicy?: 'Chosen' | 'Disabled' | 'Forced';
    requireIp?: boolean;
    resourcePolicy?: number;
    /** 资源策略 */
    resourcePolicyVo?: ShopResourcePolicyVo;
    /** 通道路由列表 */
    routers?: ShopRouterDto[];
    /** 内置RPA流程数 */
    rpaFlowCount?: number;
    securityPolicyEnabled?: boolean;
    securityPolicyUpdateTime?: string;
    sharePolicyId?: string;
    shopDataSize?: number;
    shopNo?: string;
    /** 快捷方式个数 */
    shortcutCount?: number;
    stateless?: boolean;
    statelessChangeFp?: boolean;
    statelessSyncPolicy?: number;
    /** 无痕云端同步策略 */
    statelessSyncPolicyVo?: ShopSyncPolicyVo;
    /** 是否使用默认访问策略 */
    strategyDefault?: boolean;
    syncPolicy?: number;
    /** 云端同步策略 */
    syncPolicyVo?: ShopSyncPolicyVo;
    /** 标签 */
    tags?: TagDto[];
    /** 团队ID */
    teamId?: number;
    trafficAlertStrategy?: 'Abort' | 'Off';
    trafficAlertThreshold?: number;
    trafficSaving?: boolean;
    type?: 'Global' | 'Local' | 'None';
    webSecurity?: boolean;
  };

  type ShopDomainItem = {
    description?: string;
    domain?: string;
  };

  type ShopDto = {
    /** 账户账号 */
    account?: string;
    /** 是否允许用户自行安装插件 */
    allowExtension?: boolean;
    /** 会话是否允许监视 */
    allowMonitor?: boolean;
    /** 是否允许跳过敏感操作 */
    allowSkip?: boolean;
    /** 是否自动代填 */
    autoFill?: boolean;
    bookmarkBar?: string;
    coordinateId?: string;
    /** 创建时间 */
    createTime?: string;
    /** 创建者 */
    creatorId?: number;
    deleteTime?: string;
    deleting?: boolean;
    /** 描述 */
    description?: string;
    domainPolicy?: 'Blacklist' | 'None' | 'Whitelist';
    /** 是否独占访问 */
    exclusive?: boolean;
    extension?: 'both' | 'extension' | 'huayang';
    extraProp?: string;
    /** 绑定的指纹Id */
    fingerprintId?: number;
    /** 绑定的指纹模板Id（只有无状态账号才允许绑定指纹模板） */
    fingerprintTemplateId?: number;
    frontUrl?: string;
    googleTranslateSpeed?: boolean;
    homePage?: string;
    homePageSites?: string;
    /** id */
    id?: number;
    imageForbiddenSize?: number;
    intranetEnabled?: boolean;
    ipSwitchCheckInterval?: number;
    ipSwitchStrategy?: 'Abort' | 'Alert' | 'Off';
    lastAccessTime?: string;
    lastAccessUser?: number;
    lastSyncTime?: string;
    loginStatus?: 'Offline' | 'Online' | 'Unknown';
    /** 任务栏分身标记文字 */
    markCode?: string;
    /** 任务栏分身标记背景颜色 */
    markCodeBg?: number;
    monitorPerception?: boolean;
    /** 账户名称 */
    name?: string;
    nameBookmarkEnabled?: boolean;
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    /** 经营品类 */
    operatingCategory?:
      | '医药保健'
      | '图书文具'
      | '宠物用品'
      | '家具建材'
      | '家电电器'
      | '工业用品'
      | '户外运动'
      | '手机数码'
      | '手表眼镜'
      | '护肤美妆'
      | '母婴玩具'
      | '汽车配件'
      | '生活家居'
      | '电商其他'
      | '电脑平板'
      | '艺术珠宝'
      | '花园聚会'
      | '计生情趣'
      | '软件程序'
      | '鞋服箱包'
      | '音乐影视'
      | '食品生鲜'
      | '鲜花绿植';
    parentShopId?: number;
    /** 密码 */
    password?: string;
    /** 平台ID */
    platformId?: number;
    privateAddress?: string;
    privateTitle?: string;
    recordPerception?: boolean;
    recordPolicy?: 'Chosen' | 'Disabled' | 'Forced';
    requireIp?: boolean;
    resourcePolicy?: number;
    securityPolicyEnabled?: boolean;
    securityPolicyUpdateTime?: string;
    sharePolicyId?: string;
    shopDataSize?: number;
    shopNo?: string;
    stateless?: boolean;
    statelessChangeFp?: boolean;
    statelessSyncPolicy?: number;
    syncPolicy?: number;
    /** 团队ID */
    teamId?: number;
    trafficAlertStrategy?: 'Abort' | 'Off';
    trafficAlertThreshold?: number;
    trafficSaving?: boolean;
    type?: 'Global' | 'Local' | 'None';
    webSecurity?: boolean;
  };

  type ShopLanProxyDto = {
    enabled?: boolean;
    host?: string;
    hostDomestic?: boolean;
    hostLocationId?: number;
    id?: number;
    latitude?: number;
    locale?: string;
    locationId?: number;
    longitude?: number;
    networkType?: 'UseDirect' | 'UseProxy' | 'UseSystem';
    password?: string;
    port?: number;
    probeOnSession?: boolean;
    proxyType?: string;
    remoteIp?: string;
    sshKey?: string;
    teamId?: number;
    timezone?: string;
    updateTime?: string;
    username?: string;
  };

  type ShopPlatformVo = {
    area?:
      | 'Argentina'
      | 'Australia'
      | 'Austria'
      | 'Belarus'
      | 'Belgium'
      | 'Bolivia'
      | 'Brazil'
      | 'Canada'
      | 'Chile'
      | 'China'
      | 'Colombia'
      | 'Costa_Rica'
      | 'Dominican'
      | 'Ecuador'
      | 'Egypt'
      | 'France'
      | 'Germany'
      | 'Global'
      | 'Guatemala'
      | 'Honduras'
      | 'HongKong'
      | 'India'
      | 'Indonesia'
      | 'Ireland'
      | 'Israel'
      | 'Italy'
      | 'Japan'
      | 'Kazakhstan'
      | 'Korea'
      | 'Malaysia'
      | 'Mexico'
      | 'Netherlands'
      | 'Nicaragua'
      | 'Panama'
      | 'Paraguay'
      | 'Peru'
      | 'Philippines'
      | 'Poland'
      | 'Portuguese'
      | 'Puerto_Rico'
      | 'Russia'
      | 'Salvador'
      | 'Saudi_Arabia'
      | 'Singapore'
      | 'Spain'
      | 'Sweden'
      | 'Switzerland'
      | 'Taiwan'
      | 'Thailand'
      | 'Turkey'
      | 'United_Arab_Emirates'
      | 'United_Kingdom'
      | 'United_States'
      | 'Uruguay'
      | 'Venezuela'
      | 'Vietnam';
    category?: 'IM' | 'Mail' | 'Other' | 'Payment' | 'Shop' | 'SocialMedia';
    frontUrl?: string;
    id?: number;
    loginUrl?: string;
    name?: string;
    typeName?: string;
  };

  type ShopResourcePolicyVo = {
    image?: boolean;
    video?: boolean;
  };

  type ShopRouterDto = {
    channelId?: number;
    channelType?: 'Direct' | 'None' | 'Official' | 'Primary' | 'Secondary';
    createTime?: string;
    description?: string;
    id?: number;
    orderNo?: number;
    rule?: string;
    ruleType?: 'Default' | 'DomainWildcard' | 'UrlPattern' | 'UrlWildcard';
    shopId?: number;
    teamId?: number;
    valid?: boolean;
  };

  type ShopSyncPolicyVo = {
    cookies?: boolean;
    extensions?: boolean;
    history?: boolean;
    indexDb?: boolean;
    localStorage?: boolean;
    passwords?: boolean;
    shopBookmarks?: boolean;
    supportingCache?: boolean;
    teamBookmarks?: boolean;
    userBookmarks?: boolean;
  };

  type TagDto = {
    bizCode?: string;
    color?: number;
    createTime?: string;
    expireTime?: string;
    id?: number;
    resourceType?:
      | 'AK'
      | 'Activity'
      | 'Audit'
      | 'BlockElements'
      | 'Cloud'
      | 'CrsOrder'
      | 'CrsProduct'
      | 'DiskFile'
      | 'FingerPrint'
      | 'FingerPrintTemplate'
      | 'Gateway'
      | 'GhCreator'
      | 'GhGifter'
      | 'GhJobPlan'
      | 'GhUser'
      | 'GhVideoCreator'
      | 'GiftCardPack'
      | 'InsTeamUser'
      | 'InsUser'
      | 'Invoice'
      | 'Ip'
      | 'IpPool'
      | 'IppIp'
      | 'KakaoAccount'
      | 'KakaoFriend'
      | 'KolCreator'
      | 'MobileAccount'
      | 'None'
      | 'Orders'
      | 'PluginTeamPack'
      | 'Record'
      | 'RpaFlow'
      | 'RpaTask'
      | 'RpaTaskItem'
      | 'RpaVoucher'
      | 'Shop'
      | 'ShopSession'
      | 'Tag'
      | 'TeamDiskRoot'
      | 'TeamMobile'
      | 'TkBuyer'
      | 'TkCreator'
      | 'TkTeamPack'
      | 'Tkshop'
      | 'TkshopBuyer'
      | 'TkshopCreator'
      | 'TrafficPack'
      | 'TunnelVps'
      | 'Users'
      | 'View'
      | 'Voucher'
      | 'XhsAccount';
    system?: boolean;
    tag?: string;
    teamId?: number;
  };

  type TeamIpVo = {
    autoRenew?: boolean;
    cloudProvider?: string;
    cloudRegion?: string;
    createTime?: string;
    creatorId?: number;
    description?: string;
    directDownTraffic?: number;
    directUpTraffic?: number;
    domestic?: boolean;
    downTraffic?: number;
    dynamic?: boolean;
    eipId?: number;
    enableWhitelist?: boolean;
    /** 过期状态 */
    expireStatus?: 'Expired' | 'Expiring' | 'Normal';
    forbiddenLongLatitude?: boolean;
    gatewayId?: number;
    goodsId?: number;
    goodsType?:
      | 'Credit'
      | 'CreditPack'
      | 'ExclusiveIp'
      | 'FingerprintQuota'
      | 'IosDeveloperApprove'
      | 'Ip'
      | 'IpGo'
      | 'IpProxy'
      | 'MarketFlow'
      | 'None'
      | 'PluginPack'
      | 'PriceDifference'
      | 'ProxyTraffic'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMobile'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'Rpa_Voucher_Base'
      | 'Rpa_Voucher_Performance'
      | 'SharingIp'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TkPack'
      | 'TkPackTrail'
      | 'Tkshop'
      | 'TkshopEnterprise'
      | 'TkshopStandard'
      | 'Traffic'
      | 'TransitTraffic'
      | 'TransitTrafficV2'
      | 'UserExclusiveIp'
      | 'Voucher';
    id?: number;
    importType?: 'Platform' | 'User';
    invalidTime?: string;
    ip?: string;
    lastProbeTime?: string;
    latitude?: number;
    locale?: string;
    locationId?: number;
    longitude?: number;
    name?: string;
    networkType?: 'cloudIdc' | 'mobile' | 'proxyIdc' | 'residential' | 'unknown' | 'unknownIdc';
    operateStatus?: 'shared' | 'sharing' | 'sole' | 'transferring';
    originalTeam?: number;
    periodUnit?:
      | 'Buyout'
      | 'Byte'
      | 'GB'
      | 'GB天'
      | '个'
      | '个天'
      | '分钟'
      | '周'
      | '天'
      | '年'
      | '张'
      | '无'
      | '月'
      | '次';
    pipeType?: 'None' | 'Proxy' | 'Tunnel' | 'TunnelFailToProxy';
    preferTransit?: number;
    probeError?: string;
    /** 供应商名称 */
    providerName?: string;
    realIp?: string;
    refreshUrl?: string;
    remoteLogin?: boolean;
    renewPrice?: number;
    source?: string;
    speedLimit?: number;
    status?: 'Available' | 'Pending' | 'Unavailable';
    sticky?: boolean;
    teamId?: number;
    testingTime?: number;
    timezone?: string;
    traffic?: number;
    trafficCurrency?: 'CREDIT' | 'RMB' | 'USD';
    trafficPrice?: number;
    trafficUnlimited?: boolean;
    transitType?: 'Auto' | 'Direct' | 'Transit';
    tunnelTypes?: string;
    upTraffic?: number;
    valid?: boolean;
    validEndDate?: string;
    vpsId?: number;
  };

  type UpdateIpPoolBasicInfoRequest = {
    /** 白名单连接的接入点 */
    connectTransitIds?: number[];
    countryCode?: string;
    description?: string;
    domestic?: boolean;
    /** 是否开启白名单 */
    enableWhitelist?: boolean;
    name?: string;
    transitType?: 'Auto' | 'Direct' | 'Transit';
    tunnelTypes?: ('clash' | 'direct' | 'jump' | 'localFrontend' | 'platform' | 'transit')[];
  };

  type UpdateIpPoolProviderParamRequest = {
    params?: Record<string, any>;
  };

  type UpdateIpPoolStrategyRequest = {
    allocateStrategy?: 'ByLessTraffic' | 'ByLoad' | 'ByOrder' | 'ByRandom';
    exclusive?: boolean;
    lifetime?: number;
    produceFromTransit?: boolean;
    produceStrategy?: 'ProduceOnEmpty' | 'ProduceOnHand' | 'ProduceOnRequest';
    releaseStrategy?: 'Discard' | 'Reuse';
    /** 提取接入点 */
    transitIds?: number[];
  };

  type UserVo = {
    /** 账号 */
    account?: string;
    avatar?: string;
    createTime?: string;
    /** 邮箱 */
    email?: string;
    gender?: 'FEMALE' | 'MALE' | 'UNSPECIFIC';
    id?: number;
    nickname?: string;
    partnerId?: number;
    /** 手机 */
    phone?: string;
    residentCity?: string;
    signature?: string;
    status?: 'ACTIVE' | 'BLOCK' | 'DELETED' | 'INACTIVE';
    tenant?: number;
    userType?: 'NORMAL' | 'PARTNER' | 'SHADOW';
    weixin?: string;
  };

  type WebResult = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultAllocIppIpResult = {
    code?: number;
    data?: AllocIppIpResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultIpPoolDto = {
    code?: number;
    data?: IpPoolDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultIppPoolWithParamsVo = {
    code?: number;
    data?: IppPoolWithParamsVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultIppWhitelistConfig = {
    code?: number;
    data?: IppWhitelistConfig;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListCountryDto = {
    code?: number;
    data?: CountryDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListIppParamDefDto = {
    code?: number;
    data?: IppParamDefDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListIppProviderDto = {
    code?: number;
    data?: IppProviderDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultlong = {
    code?: number;
    data?: number;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultIppIpDto = {
    code?: number;
    data?: PageResultIppIpDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultIppPoolVo = {
    code?: number;
    data?: PageResultIppPoolVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };
}
