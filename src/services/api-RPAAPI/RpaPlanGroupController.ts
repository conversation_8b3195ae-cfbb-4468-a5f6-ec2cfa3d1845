// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 修改计划的分组或从分组移除 PUT /api/rpa/plan/${param0}/group */
export async function rpaPlanByPlanIdGroupPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanByPlanIdGroupPutParams,
  options?: { [key: string]: any },
) {
  const { planId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/rpa/plan/${param0}/group`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 创建流程计划分组 POST /api/rpa/plan/group */
export async function rpaPlanGroupPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanGroupPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultRpaPlanGroup>('/api/rpa/plan/group', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取流程计划分组详情 GET /api/rpa/plan/group/${param0} */
export async function rpaPlanGroupByGroupIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanGroupByGroupIdGetParams,
  options?: { [key: string]: any },
) {
  const { groupId: param0, ...queryParams } = params;
  return request<API.WebResultRpaPlanGroupVo>(`/api/rpa/plan/group/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除分组 DELETE /api/rpa/plan/group/${param0} */
export async function rpaPlanGroupByGroupIdDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanGroupByGroupIdDeleteParams,
  options?: { [key: string]: any },
) {
  const { groupId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/rpa/plan/group/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改分组名称 PUT /api/rpa/plan/group/${param0}/name */
export async function rpaPlanGroupByGroupIdNamePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanGroupByGroupIdNamePutParams,
  options?: { [key: string]: any },
) {
  const { groupId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/rpa/plan/group/${param0}/name`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 暂停分组 PUT /api/rpa/plan/group/${param0}/pause */
export async function rpaPlanGroupByGroupIdPausePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanGroupByGroupIdPausePutParams,
  options?: { [key: string]: any },
) {
  const { groupId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/rpa/plan/group/${param0}/pause`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 修改分组时区 PUT /api/rpa/plan/group/${param0}/timezone */
export async function rpaPlanGroupByGroupIdTimezonePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaPlanGroupByGroupIdTimezonePutParams,
  options?: { [key: string]: any },
) {
  const { groupId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/rpa/plan/group/${param0}/timezone`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取流程计划分组列表 GET /api/rpa/plan/groups */
export async function rpaPlanGroupsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListRpaPlanGroupVo>('/api/rpa/plan/groups', {
    method: 'GET',
    ...(options || {}),
  });
}
