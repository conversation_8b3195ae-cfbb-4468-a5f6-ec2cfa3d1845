// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 为客户端上传错误日志生成 sts token，因为该方法不要求登录，每个ip每天最多调用500次。文件路径为： client-logs/{yyyyMM}/{client_uuid}/{yyyyMMddHHmmss}-{log_uuid}.log GET /api/logs/getClientLogSignature */
export async function logsGetClientLogSignatureGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.logsGetClientLogSignatureGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultStsPostSignature>('/api/logs/getClientLogSignature', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 通知门户日志采集完成 GET /api/logs/markFinished */
export async function logsMarkFinishedGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.logsMarkFinishedGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/logs/markFinished', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
