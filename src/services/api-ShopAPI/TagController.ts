// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 查询某个资源标签列表 GET /api/resourceTags */
export async function resourceTagsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.resourceTagsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTagDto>('/api/resourceTags', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建一个标签 POST /api/tag */
export async function tagPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tagPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTagDto>('/api/tag', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 删除一个标签 DELETE /api/tag */
export async function tagDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tagDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tag', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 更新标签 可以将一个系统标签更新为非系统标签，从而删除 PUT /api/tag/${param0} */
export async function tagByTagIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tagByTagIdPutParams,
  options?: { [key: string]: any },
) {
  const { tagId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/tag/${param0}`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 删除一个标签（按ID） DELETE /api/tag/${param0} */
export async function tagByTagIdDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tagByTagIdDeleteParams,
  options?: { [key: string]: any },
) {
  const { tagId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/tag/${param0}`, {
    method: 'DELETE',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 撤销某个资源的标签 DELETE /api/tag/${param0}/resource */
export async function tagByTagIdResourceDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tagByTagIdResourceDeleteParams,
  options?: { [key: string]: any },
) {
  const { tagId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/tag/${param0}/resource`, {
    method: 'DELETE',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 查询一批资源的标签列表 POST /api/tag/listByResourceIds */
export async function tagListByResourceIdsPost(
  body: API.TagBatchRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTagDto>('/api/tag/listByResourceIds', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量打标签 POST /api/tag/resources */
export async function tagResourcesPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tagResourcesPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tag/resources', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 批量撤销标签 DELETE /api/tag/resources */
export async function tagResourcesDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tagResourcesDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tag/resources', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 清空资源的所有标签 DELETE /api/tag/resources/all */
export async function tagResourcesAllDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tagResourcesAllDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tag/resources/all', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 批量打标签（用标签名） POST /api/tag/resources/byTag */
export async function tagResourcesByTagPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tagResourcesByTagPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tag/resources/byTag', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 批量撤销标签 DELETE /api/tag/resources/byTag */
export async function tagResourcesByTagDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tagResourcesByTagDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tag/resources/byTag', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 批量打标签V2 POST /api/tag/resourcesV2 */
export async function tagResourcesV2Post(
  body: API.TagResourceRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tag/resourcesV2', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 清空资源带特定后缀的标签 PUT /api/tag/untagBySuffix */
export async function tagUntagBySuffixPut(
  body: API.UntagBySuffixRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/tag/untagBySuffix', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询标签列表 GET /api/tags */
export async function tagsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tagsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTagVo>('/api/tags', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 批量删除标签 DELETE /api/tags */
export async function tagsDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tagsDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tags', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询标签的资源数 GET /api/tags/countMap */
export async function tagsCountMapGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tagsCountMapGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMaplong>('/api/tags/countMap', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
