// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取指定用户详情 GET /api/user/${param0} */
export async function userByUserIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.userByUserIdGetParams,
  options?: { [key: string]: any },
) {
  const { userId: param0, ...queryParams } = params;
  return request<API.WebResultUserTeamVo>(`/api/user/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取用户对所有权限的元信息 GET /api/user/${param0}/functions */
export async function userByUserIdFunctionsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.userByUserIdFunctionsGetParams,
  options?: { [key: string]: any },
) {
  const { userId: param0, ...queryParams } = params;
  return request<API.WebResultListUserFunctionMetaVo>(`/api/user/${param0}/functions`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 更新用户权限 PUT /api/user/${param0}/functions */
export async function userByUserIdFunctionsPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.userByUserIdFunctionsPutParams,
  options?: { [key: string]: any },
) {
  const { userId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/user/${param0}/functions`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 更新用户权限V2 PUT /api/user/${param0}/functionsV2 */
export async function userByUserIdFunctionsV2Put(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.userByUserIdFunctionsV2PutParams,
  options?: { [key: string]: any },
) {
  const { userId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/user/${param0}/functionsV2`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 强制授权或剥夺权限或取消 PUT /api/user/${param0}/grant */
export async function userByUserIdGrantPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.userByUserIdGrantPutParams,
  options?: { [key: string]: any },
) {
  const { userId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/user/${param0}/grant`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取用户已经授权的权限 GET /api/user/${param0}/grantedFunctions */
export async function userByUserIdGrantedFunctionsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.userByUserIdGrantedFunctionsGetParams,
  options?: { [key: string]: any },
) {
  const { userId: param0, ...queryParams } = params;
  return request<API.WebResultListFunctionVo>(`/api/user/${param0}/grantedFunctions`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取用户强制授权或剥夺列表 GET /api/user/${param0}/grantList */
export async function userByUserIdGrantListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.userByUserIdGrantListGetParams,
  options?: { [key: string]: any },
) {
  const { userId: param0, ...queryParams } = params;
  return request<API.WebResultListUserFunctionDto>(`/api/user/${param0}/grantList`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 踢出团队成员 PUT /api/user/${param0}/kickOut */
export async function userByUserIdKickOutPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.userByUserIdKickOutPutParams,
  options?: { [key: string]: any },
) {
  const { userId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/user/${param0}/kickOut`, {
    method: 'PUT',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取用户角色 GET /api/user/${param0}/role */
export async function userByUserIdRoleGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.userByUserIdRoleGetParams,
  options?: { [key: string]: any },
) {
  const { userId: param0, ...queryParams } = params;
  return request<API.WebResultRoleVo>(`/api/user/${param0}/role`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 设置用户角色 PUT /api/user/${param0}/role */
export async function userByUserIdRolePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.userByUserIdRolePutParams,
  options?: { [key: string]: any },
) {
  const { userId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/user/${param0}/role`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取当前用户的团队昵称 GET /api/user/${param0}/teamNickname */
export async function userByUserIdTeamNicknameGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.userByUserIdTeamNicknameGetParams,
  options?: { [key: string]: any },
) {
  const { userId: param0, ...queryParams } = params;
  return request<API.WebResultstring>(`/api/user/${param0}/teamNickname`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改指定用户的团队昵称 PUT /api/user/${param0}/teamNickname */
export async function userByUserIdTeamNicknamePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.userByUserIdTeamNicknamePutParams,
  options?: { [key: string]: any },
) {
  const { userId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/user/${param0}/teamNickname`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取指定团队的成员列表 GET /api/user/byTeam/${param0}/users */
export async function userByTeamByTeamIdUsersGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.userByTeamByTeamIdUsersGetParams,
  options?: { [key: string]: any },
) {
  const { teamId: param0, ...queryParams } = params;
  return request<API.WebResultPageResultUserDetailVo>(`/api/user/byTeam/${param0}/users`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 修复角色权限数据 GET /api/user/data/roleFunction/repair */
export async function userDataRoleFunctionRepairGet(options?: { [key: string]: any }) {
  return request<API.WebResult>('/api/user/data/roleFunction/repair', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 用户主动退出团队 PUT /api/user/exitTeam */
export async function userExitTeamPut(options?: { [key: string]: any }) {
  return request<API.WebResult>('/api/user/exitTeam', {
    method: 'PUT',
    ...(options || {}),
  });
}

/** 给用户授权账户 POST /api/user/grantShops */
export async function userGrantShopsPost(
  body: API.GrantShopsParamVo,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/user/grantShops', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取团队成员列表（含授权信息） GET /api/user/memberGrantList */
export async function userMemberGrantListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.userMemberGrantListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultUserDetailVo>('/api/user/memberGrantList', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修改当前用户的团队昵称 PUT /api/user/teamNickname */
export async function userTeamNicknamePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.userTeamNicknamePutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/user/teamNickname', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
