import type { HTMLAttributes } from 'react';
import { useEffect } from 'react';
import React, { useState, useMemo } from 'react';
import { Avatar, Typography } from 'antd';
import classNames from 'classnames';
import type { AvatarProps } from 'antd/lib/avatar/avatar';
import styles from './UserAvatarAndName.less';
import { SkipErrorNotifyOption } from '@/utils/utils';
import Placeholder from '@/components/Common/Placeholder';
import { CreatorAvatar } from '@/components/Common/TkShopCreator';
import { useRequest } from '@@/plugin-request/request';
import { tkshopBuyerByIdGet } from '@/services/api-TKShopAPI/TkshopBuyerController';
import { GhostModalCaller } from '@/mixins/modal';
import BuyerDetailModal from '@/pages/Buyer/components/BuyerDetailModal';
import CopyableText from '@/components/Common/CopyableText';

interface Props {
  buyer: { id: number; handle: string; avatar?: string };
  avatarSize?: number;
  avatarProps?: AvatarProps;
}

const TkShopBuyer: React.FC<Props & HTMLAttributes<any>> = (props) => {
  const { buyer, avatarSize = 24, avatarProps, className, ...otherProps } = props;
  const { handle, avatar, id } = buyer;

  const img = useMemo(() => {
    if (avatar) {
      return (
        <Avatar
          src={avatar}
          style={{ minWidth: avatarSize }}
          size={avatarSize}
          shape={'circle'}
          {...avatarProps}
        />
      );
    }
    return <CreatorAvatar size={avatarSize} id={id} style={{ flexShrink: 0 }} {...avatarProps} />;
  }, [avatar, avatarProps, avatarSize, id]);
  return (
    <span
      title={handle}
      className={classNames(styles.userAvatarAndName, className)}
      {...otherProps}
    >
      {img}
      <span title={handle} className="nickname">
        {handle}
      </span>
    </span>
  );
};
const TkShopBuyerByIdCache: Record<number, Promise<API.TkBuyerVo>> = {};

export function useTkShopBuyerById(buyerId: number) {
  const [data, setData] = useState<API.TkBuyerVo>();
  useEffect(() => {
    if (!Number.isNaN(Number(buyerId)) && buyerId) {
      if (!TkShopBuyerByIdCache[buyerId]) {
        TkShopBuyerByIdCache[buyerId] = tkshopBuyerByIdGet(
          {
            id: buyerId,
          },
          SkipErrorNotifyOption,
        )
          .then((res) => {
            return res.data!;
          })
          .catch((e) => {
            return {
              code: e?.data?.code,
              id: buyerId,
              error: true,
              message: e.message,
            };
          });
      }
      TkShopBuyerByIdCache[buyerId].then((res) => {
        setData(res);
      });
    }
  }, [buyerId]);
  return data;
}

export const TkShopBuyerById = (props: { id: number; size?: number }) => {
  const { id, size = 24, ...otherProps } = props;
  const tkShopBuyer = useTkShopBuyerById(id);

  const img = useMemo(() => {
    return (
      <CreatorAvatar
        size={size}
        style={{ flexShrink: 0, minWidth: size }}
        src={tkShopBuyer?.avatar}
      />
    );
  }, [tkShopBuyer]);
  const name = useMemo(() => {
    if (tkShopBuyer) {
      return tkShopBuyer.handle;
    }
    return <Placeholder />;
  }, [tkShopBuyer]);
  const { run: onClick, loading } = useRequest(
    async () => {
      if (loading || !tkShopBuyer) {
        return;
      }

      GhostModalCaller(<BuyerDetailModal data={tkShopBuyer} tab={'order'} />, 'BuyerDetailModal');
    },
    {
      manual: true,
    },
  );
  return (
    <span className={classNames(styles.userAvatarAndName)} {...otherProps}>
      {img}
      <Typography.Link onClick={onClick} ellipsis className="nickname">
        <CopyableText text={name} type={'买家ID'}>
          {name}
        </CopyableText>
      </Typography.Link>
    </span>
  );
};
export default TkShopBuyer;
