// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** proxy GET /api/gpt/proxy/v1/chat/completions */
export async function gptProxyV1ChatCompletionsGet(
  body: Record<string, any>,
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>('/api/gpt/proxy/v1/chat/completions', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** proxy PUT /api/gpt/proxy/v1/chat/completions */
export async function gptProxyV1ChatCompletionsPut(
  body: Record<string, any>,
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>('/api/gpt/proxy/v1/chat/completions', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** proxy POST /api/gpt/proxy/v1/chat/completions */
export async function gptProxyV1ChatCompletionsPost(
  body: Record<string, any>,
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>('/api/gpt/proxy/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** proxy DELETE /api/gpt/proxy/v1/chat/completions */
export async function gptProxyV1ChatCompletionsDelete(
  body: Record<string, any>,
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>('/api/gpt/proxy/v1/chat/completions', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** proxy PATCH /api/gpt/proxy/v1/chat/completions */
export async function gptProxyV1ChatCompletionsPatch(
  body: Record<string, any>,
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>('/api/gpt/proxy/v1/chat/completions', {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 调用OpenAi接口生成对话 POST /api/rpa/task/ai/completion */
export async function rpaTaskAiCompletionPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskAiCompletionPostParams,
  body: API.OpenAiRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultOpenAiResponse>('/api/rpa/task/ai/completion', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 调用OpenAi接口生成对话 GET /api/rpa/task/ai/getAiConfig */
export async function rpaTaskAiGetAiConfigGet(options?: { [key: string]: any }) {
  return request<API.WebResultTeamAiConfig>('/api/rpa/task/ai/getAiConfig', {
    method: 'GET',
    ...(options || {}),
  });
}

/** AI图像理解 POST /api/rpa/task/ai/imageUnderstanding */
export async function rpaTaskAiImageUnderstandingPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskAiImageUnderstandingPostParams,
  body: API.ImageUnderstandingRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultOpenAiResponse>('/api/rpa/task/ai/imageUnderstanding', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}
