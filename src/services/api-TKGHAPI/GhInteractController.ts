// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 是否与达人产生过交互 GET /api/gh/hasInteraction */
export async function ghHasInteractionGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghHasInteractionGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultboolean>('/api/gh/hasInteraction', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 是否与达人产生过交互（批量） POST /api/gh/hasInteractions */
export async function ghHasInteractionsPost(
  body: API.HasGhInteractionRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListHasInteractionVo>('/api/gh/hasInteractions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 添加交互记录 POST /api/gh/interaction */
export async function ghInteractionPost(
  body: API.GhInteractionVo,
  options?: { [key: string]: any },
) {
  return request<API.WebResultGhInteractionDto>('/api/gh/interaction', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询交互记录详情 GET /api/gh/interaction/${param0} */
export async function ghInteractionByIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghInteractionByIdGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultGhInteractionDetailVo>(`/api/gh/interaction/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 查询交互记录 GET /api/gh/interaction/page */
export async function ghInteractionPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghInteractionPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultGhInteractionDetailVo>('/api/gh/interaction/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 添加交互记录（批量） POST /api/gh/interactions */
export async function ghInteractionsPost(
  body: API.AddGhInteractionRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/interactions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询与达人最近产生过交互 GET /api/gh/latestInteraction */
export async function ghLatestInteractionGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghLatestInteractionGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkInteractionDetailVo>('/api/gh/latestInteraction', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
