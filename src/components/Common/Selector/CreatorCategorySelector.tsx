import { Typography } from 'antd';
import type { SelectorProps } from '@/components/Common/Selector/index';
import Selector from '@/components/Common/Selector/index';
import { useRequest } from '@@/plugin-request/request';
import { tkshopCreatorCategoriesGet } from '@/services/api-TKShopAPI/TkshopCreatorController';

const CreatorCategorySelector = (props: SelectorProps) => {
  const { data } = useRequest(() => {
    return tkshopCreatorCategoriesGet();
  });
  return (
    <Selector
      mode={'multiple'}
      placeholder={
        <Typography.Text>
          不限
          <Typography.Text type={'secondary'} style={{ fontSize: 13 }}>
            （支持多选，多个为或者关系）
          </Typography.Text>
        </Typography.Text>
      }
      showPlaceholderOption
      options={data?.map((item) => {
        return {
          label: item.category,
          value: item.id,
        };
      })}
      {...props}
    />
  );
};
export default CreatorCategorySelector;
