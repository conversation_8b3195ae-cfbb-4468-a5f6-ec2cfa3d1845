// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 强制IPGO汇报IP GET /api/ipgo/${param0}/forceReportIp */
export async function ipgoByIdForceReportIpGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipgoByIdForceReportIpGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/ipgo/${param0}/forceReportIp`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 设置IPGO的导入IP列表 PUT /api/ipgo/${param0}/import */
export async function ipgoByIdImportPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipgoByIdImportPutParams,
  body: API.ImportIpgoIpRequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/ipgo/${param0}/import`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 重启IPGO GET /api/ipgo/${param0}/restart */
export async function ipgoByIdRestartGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipgoByIdRestartGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/ipgo/${param0}/restart`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 卸载IPGO GET /api/ipgo/${param0}/uninstall */
export async function ipgoByIdUninstallGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipgoByIdUninstallGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/ipgo/${param0}/uninstall`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 分页查询IPGO GET /api/ipgo/page */
export async function ipgoPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ipgoPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultGatewayDetailVo>('/api/ipgo/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
