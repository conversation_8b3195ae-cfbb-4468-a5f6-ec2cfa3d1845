// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 购买流量包 POST /api/traffic/pack/buyPacks */
export async function trafficPackBuyPacksPost(
  body: API.TrafficPackCreateOrderRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCreateOrderResponse>('/api/traffic/pack/buyPacks', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
