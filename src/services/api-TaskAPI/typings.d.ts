declare namespace API {
  type CreateTaskRequest = {
    /** 任务详情，用于展示 */
    detailVo?: Record<string, any>;
    /** 任务名称 */
    name?: string;
    status?: 'Abort' | 'Fail' | 'Pending' | 'Running' | 'Success' | 'Timeout';
    /** 任务关联的目标ID */
    targetId?: number;
    /** 任务类型 */
    taskType?:
      | 'BatchUpdateBandwidth'
      | 'CleanColdTable'
      | 'CleanMongo'
      | 'CleanTable'
      | 'CopyTkCreatorToClient'
      | 'CrsTransferOrder'
      | 'DbTransfer'
      | 'DeleteIps'
      | 'FireOpsMessage'
      | 'ImportAccounts'
      | 'ImportIp'
      | 'ImportIppIps'
      | 'ImportIps'
      | 'ImportShop'
      | 'OpenaiChatGenerate'
      | 'OpenaiChatTranslate'
      | 'ProbeBatchLaunchInstance'
      | 'ProbeIps'
      | 'RebootIp'
      | 'RefreshClash'
      | 'RefreshExtensions'
      | 'RepairGhLiveTime'
      | 'RepairKolLiveRate'
      | 'RepairKolLiveTime'
      | 'RepairOps'
      | 'RepairTkCreatorFollower'
      | 'ResetJdEip'
      | 'ReviseIpHostLocation'
      | 'ShardTableOps'
      | 'ShardTeamTableSql'
      | 'SshChangePort'
      | 'SshCommands'
      | 'SshCommandsBatchLaunchInstance'
      | 'SyncKolCreator'
      | 'SyncKolRegionMap'
      | 'TkSendEmail'
      | 'TransferShardTable'
      | 'TransferTable'
      | 'TransferTagResource'
      | 'TransferTkCreator'
      | 'UpgradeGhMessage'
      | 'UploadAiKnowledge'
      | 'UploadDiskFile'
      | 'UserRefreshIp';
    /** 任务超时,0表示永不超时。单位：秒 */
    timeout?: number;
  };

  type PageResultTaskDto = {
    current?: number;
    list?: TaskDto[];
    pageSize?: number;
    total?: number;
  };

  type queueTaskRankGetParams = {
    /** queueType */
    queueType?: 'CloudRpaTask';
    /** 目标任务ID，比如rpa_task.id */
    targetId: number;
  };

  type RankVo = {
    rank?: number;
    total?: number;
    waitSeconds?: number;
  };

  type taskByTaskIdAbortPutParams = {
    /** taskId */
    taskId: number;
  };

  type taskByTaskIdDeleteParams = {
    /** taskId */
    taskId: number;
  };

  type taskByTaskIdFinishedPutParams = {
    /** taskId */
    taskId: number;
    /** progress */
    progress?: number;
    /** status */
    status: 'Abort' | 'Fail' | 'Pending' | 'Running' | 'Success' | 'Timeout';
    /** remarks */
    remarks?: string;
  };

  type taskByTaskIdGetParams = {
    /** taskId */
    taskId: number;
  };

  type taskByTaskIdProgressPutParams = {
    /** taskId */
    taskId: number;
    /** progress */
    progress: number;
  };

  type taskByTaskIdResultGetParams = {
    /** taskId */
    taskId: number;
  };

  type TaskDetailVo = {
    createTime?: string;
    creatorId?: number;
    detail?: string;
    finishTime?: string;
    id?: number;
    name?: string;
    progress?: number;
    remarks?: string;
    result?: TaskResult;
    status?: 'Abort' | 'Fail' | 'Pending' | 'Running' | 'Success' | 'Timeout';
    targetId?: number;
    taskType?:
      | 'BatchUpdateBandwidth'
      | 'CleanColdTable'
      | 'CleanMongo'
      | 'CleanTable'
      | 'CopyTkCreatorToClient'
      | 'CrsTransferOrder'
      | 'DbTransfer'
      | 'DeleteIps'
      | 'FireOpsMessage'
      | 'ImportAccounts'
      | 'ImportIp'
      | 'ImportIppIps'
      | 'ImportIps'
      | 'ImportShop'
      | 'OpenaiChatGenerate'
      | 'OpenaiChatTranslate'
      | 'ProbeBatchLaunchInstance'
      | 'ProbeIps'
      | 'RebootIp'
      | 'RefreshClash'
      | 'RefreshExtensions'
      | 'RepairGhLiveTime'
      | 'RepairKolLiveRate'
      | 'RepairKolLiveTime'
      | 'RepairOps'
      | 'RepairTkCreatorFollower'
      | 'ResetJdEip'
      | 'ReviseIpHostLocation'
      | 'ShardTableOps'
      | 'ShardTeamTableSql'
      | 'SshChangePort'
      | 'SshCommands'
      | 'SshCommandsBatchLaunchInstance'
      | 'SyncKolCreator'
      | 'SyncKolRegionMap'
      | 'TkSendEmail'
      | 'TransferShardTable'
      | 'TransferTable'
      | 'TransferTagResource'
      | 'TransferTkCreator'
      | 'UpgradeGhMessage'
      | 'UploadAiKnowledge'
      | 'UploadDiskFile'
      | 'UserRefreshIp';
    teamId?: number;
  };

  type TaskDto = {
    createTime?: string;
    creatorId?: number;
    detail?: string;
    finishTime?: string;
    id?: number;
    name?: string;
    progress?: number;
    remarks?: string;
    status?: 'Abort' | 'Fail' | 'Pending' | 'Running' | 'Success' | 'Timeout';
    targetId?: number;
    taskType?:
      | 'BatchUpdateBandwidth'
      | 'CleanColdTable'
      | 'CleanMongo'
      | 'CleanTable'
      | 'CopyTkCreatorToClient'
      | 'CrsTransferOrder'
      | 'DbTransfer'
      | 'DeleteIps'
      | 'FireOpsMessage'
      | 'ImportAccounts'
      | 'ImportIp'
      | 'ImportIppIps'
      | 'ImportIps'
      | 'ImportShop'
      | 'OpenaiChatGenerate'
      | 'OpenaiChatTranslate'
      | 'ProbeBatchLaunchInstance'
      | 'ProbeIps'
      | 'RebootIp'
      | 'RefreshClash'
      | 'RefreshExtensions'
      | 'RepairGhLiveTime'
      | 'RepairKolLiveRate'
      | 'RepairKolLiveTime'
      | 'RepairOps'
      | 'RepairTkCreatorFollower'
      | 'ResetJdEip'
      | 'ReviseIpHostLocation'
      | 'ShardTableOps'
      | 'ShardTeamTableSql'
      | 'SshChangePort'
      | 'SshCommands'
      | 'SshCommandsBatchLaunchInstance'
      | 'SyncKolCreator'
      | 'SyncKolRegionMap'
      | 'TkSendEmail'
      | 'TransferShardTable'
      | 'TransferTable'
      | 'TransferTagResource'
      | 'TransferTkCreator'
      | 'UpgradeGhMessage'
      | 'UploadAiKnowledge'
      | 'UploadDiskFile'
      | 'UserRefreshIp';
    teamId?: number;
  };

  type taskPageGetParams = {
    /** taskType */
    taskType?: string;
    /** createTimeFrom */
    createTimeFrom?: string;
    /** createTimeTo */
    createTimeTo?: string;
    /** finishTimeFrom */
    finishTimeFrom?: string;
    /** finishTimeTo */
    finishTimeTo?: string;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type TaskResult = {
    id?: string;
    result?: Record<string, any>;
    taskId?: number;
    teamId?: number;
  };

  type WebResult = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultint = {
    code?: number;
    data?: number;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultlong = {
    code?: number;
    data?: number;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultTaskDto = {
    code?: number;
    data?: PageResultTaskDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultRankVo = {
    code?: number;
    data?: RankVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTaskDetailVo = {
    code?: number;
    data?: TaskDetailVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultTaskResult = {
    code?: number;
    data?: TaskResult;
    message?: string;
    requestId?: string;
    success?: boolean;
  };
}
