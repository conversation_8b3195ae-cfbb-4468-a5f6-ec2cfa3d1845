// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取某个店铺的书签 GET /api/shop/bookmarks/${param0}/bookmarks */
export async function shopBookmarksByShopIdBookmarksGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopBookmarksByShopIdBookmarksGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultstring>(`/api/shop/bookmarks/${param0}/bookmarks`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 覆盖店铺书签 POST /api/shop/bookmarks/${param0}/overrideShopBookmarks */
export async function shopBookmarksByShopIdOverrideShopBookmarksPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopBookmarksByShopIdOverrideShopBookmarksPostParams,
  body: string,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/shop/bookmarks/${param0}/overrideShopBookmarks`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 获取团队书签 GET /api/shop/bookmarks/getTeamBookmarks */
export async function shopBookmarksGetTeamBookmarksGet(options?: { [key: string]: any }) {
  return request<API.WebResultstring>('/api/shop/bookmarks/getTeamBookmarks', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取用户书签 GET /api/shop/bookmarks/getUserBookmarks */
export async function shopBookmarksGetUserBookmarksGet(options?: { [key: string]: any }) {
  return request<API.WebResultstring>('/api/shop/bookmarks/getUserBookmarks', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 覆盖团队书签 POST /api/shop/bookmarks/overrideTeamBookmarks */
export async function shopBookmarksOverrideTeamBookmarksPost(
  body: string,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/bookmarks/overrideTeamBookmarks', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 覆盖用户书签 POST /api/shop/bookmarks/overrideUserBookmarks */
export async function shopBookmarksOverrideUserBookmarksPost(
  body: string,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/bookmarks/overrideUserBookmarks', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 同步全部分身标签 PUT /api/shop/bookmarks/syncAllShop */
export async function shopBookmarksSyncAllShopPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopBookmarksSyncAllShopPutParams,
  body: string,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/bookmarks/syncAllShop', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量同步分身标签 PUT /api/shop/bookmarks/syncBatchShop */
export async function shopBookmarksSyncBatchShopPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopBookmarksSyncBatchShopPutParams,
  body: API.SyncShopBookmarksRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/bookmarks/syncBatchShop', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 同步默认标签 PUT /api/shop/bookmarks/syncDefault */
export async function shopBookmarksSyncDefaultPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopBookmarksSyncDefaultPutParams,
  body: string,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/bookmarks/syncDefault', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 同步团队标签 PUT /api/shop/bookmarks/syncTeam */
export async function shopBookmarksSyncTeamPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopBookmarksSyncTeamPutParams,
  body: string,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/bookmarks/syncTeam', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 同步个人标签 PUT /api/shop/bookmarks/syncUser */
export async function shopBookmarksSyncUserPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopBookmarksSyncUserPutParams,
  body: string,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/bookmarks/syncUser', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}
