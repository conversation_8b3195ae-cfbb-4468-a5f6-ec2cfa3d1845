// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 查询团队达人数量 POST /api/tk/creator-view/count */
export async function tkCreatorViewCountPost(
  body: API.PageTkCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultlong>('/api/tk/creator-view/count', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 下载达人信息 GET /api/tk/creator-view/download/${param0} */
export async function tkCreatorViewDownloadByTokenGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkCreatorViewDownloadByTokenGetParams,
  options?: { [key: string]: any },
) {
  const { token: param0, ...queryParams } = params;
  return request<API.Resource>(`/api/tk/creator-view/download/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 下载达人的token POST /api/tk/creator-view/exportToken */
export async function tkCreatorViewExportTokenPost(
  body: API.ExportTkCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/tk/creator-view/exportToken', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 达人可显示字段元信息 GET /api/tk/creator-view/fields */
export async function tkCreatorViewFieldsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTkCreatorFiledVo>('/api/tk/creator-view/fields', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 分页查询团队达人（不返回总数） POST /api/tk/creator-view/page */
export async function tkCreatorViewPagePost(
  body: API.PageTkCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkCreatorDetailVo>('/api/tk/creator-view/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询团队达人（高级查询） POST /api/tk/creator-view/page-complex */
export async function tkCreatorViewPageComplexPost(
  body: API.PageCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkCreatorDetailVo>('/api/tk/creator-view/page-complex', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询团队达人（简洁版） GET /api/tk/creator-view/page-simple */
export async function tkCreatorViewPageSimpleGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkCreatorViewPageSimpleGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkCreatorDetailVo>('/api/tk/creator-view/page-simple', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询团队达人（简洁版）V2 POST /api/tk/creator-view/page-simple-v2 */
export async function tkCreatorViewPageSimpleV2Post(
  body: API.PageCreatorSimpleRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkCreatorDetailVo>('/api/tk/creator-view/page-simple-v2', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
