// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 批量同步买家信息 POST /api/tkshop/jobs/batchSyncBuyerInfo */
export async function tkshopJobsBatchSyncBuyerInfoPost(
  body: API.BatchSyncBuyerInfoRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/jobs/batchSyncBuyerInfo', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量对达人执行基础信息更新 POST /api/tkshop/jobs/batchSyncCreators */
export async function tkshopJobsBatchSyncCreatorsPost(
  body: API.BatchSyncCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/jobs/batchSyncCreators', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量创建待审批的索样达人抓取任务 POST /api/tkshop/jobs/batchSyncSampleCreatorRequest */
export async function tkshopJobsBatchSyncSampleCreatorRequestPost(
  body: API.BatchSyncSampleCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/jobs/batchSyncSampleCreatorRequest', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量同步视频信息 POST /api/tkshop/jobs/batchSyncVideos */
export async function tkshopJobsBatchSyncVideosPost(
  body: API.BatchSyncVideoRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/jobs/batchSyncVideos', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 添加达人手机到手机通讯录 PUT /api/tkshop/jobs/sendAddContacts */
export async function tkshopJobsSendAddContactsPut(
  body: API.SendAddContactsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/jobs/sendAddContacts', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 添加Line,WhatsApp等好友 PUT /api/tkshop/jobs/sendAddFriends */
export async function tkshopJobsSendAddFriendsPut(
  body: API.SendAddFriendsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/jobs/sendAddFriends', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 给买家发送站内信 POST /api/tkshop/jobs/sendBuyerIMChat */
export async function tkshopJobsSendBuyerImChatPost(
  body: API.SendBuyerIMChatRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/jobs/sendBuyerIMChat', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 未付款订单催付 POST /api/tkshop/jobs/sendDemandPayment */
export async function tkshopJobsSendDemandPaymentPost(
  body: API.DemandPaymentRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/jobs/sendDemandPayment', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量执行选中的抽屉中的任务 POST /api/tkshop/jobs/sendDrawerToJobs */
export async function tkshopJobsSendDrawerToJobsPost(
  body: API.piliangzhixingchoutilixuanzhongderenwu,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/jobs/sendDrawerToJobs', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建发送邮件的任务 POST /api/tkshop/jobs/sendEmail */
export async function tkshopJobsSendEmailPost(
  body: API.SendEmailRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/jobs/sendEmail', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建发送站内消息（指定筛选条件） POST /api/tkshop/jobs/sendIMChatByFilter */
export async function tkshopJobsSendImChatByFilterPost(
  body: API.SendIMChatByFilterRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/jobs/sendIMChatByFilter', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建发送站内消息（指定达人列表） POST /api/tkshop/jobs/sendIMChatByHandle */
export async function tkshopJobsSendImChatByHandlePost(
  body: API.SendIMChatByHandleRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/jobs/sendIMChatByHandle', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 发送即时信息 PUT /api/tkshop/jobs/sendInstantMessage */
export async function tkshopJobsSendInstantMessagePut(
  body: API.SendInstantMessageRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/jobs/sendInstantMessage', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 对某个店铺执行登录状态检查流程 POST /api/tkshop/jobs/sendLoginCheck */
export async function tkshopJobsSendLoginCheckPost(
  body: API.SendLoginCheckRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/jobs/sendLoginCheck', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 同步买家信息 POST /api/tkshop/jobs/sendSyncBuyerInfo */
export async function tkshopJobsSendSyncBuyerInfoPost(
  body: API.SyncBuyerInfoRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/jobs/sendSyncBuyerInfo', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 对选定店铺执行同步订单流程 POST /api/tkshop/jobs/sendSyncOrders */
export async function tkshopJobsSendSyncOrdersPost(
  body: API.SyncOrdersRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/jobs/sendSyncOrders', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 对选定店铺执行同步商品流程 POST /api/tkshop/jobs/sendSyncProducts */
export async function tkshopJobsSendSyncProductsPost(
  body: API.SyncProductsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/jobs/sendSyncProducts', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建待审批的索样达人抓取任务 POST /api/tkshop/jobs/sendSyncSampleCreatorRequest */
export async function tkshopJobsSendSyncSampleCreatorRequestPost(
  body: API.SyncSampleCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/jobs/sendSyncSampleCreatorRequest', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 对选定店铺执行信息更新的流程 POST /api/tkshop/jobs/sendSyncShopInfo */
export async function tkshopJobsSendSyncShopInfoPost(
  body: API.SyncShopInfoRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/jobs/sendSyncShopInfo', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建定向邀约(指定筛选条件) POST /api/tkshop/jobs/sendTargetPlanByFilter */
export async function tkshopJobsSendTargetPlanByFilterPost(
  body: API.SendTargetPlanByFilterRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/jobs/sendTargetPlanByFilter', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 创建定向邀约（指定达人列表) POST /api/tkshop/jobs/sendTargetPlanByHandle */
export async function tkshopJobsSendTargetPlanByHandlePost(
  body: API.SendTargetPlanByHandleRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/jobs/sendTargetPlanByHandle', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 清理定向邀约计划 POST /api/tkshop/jobs/sendTargetPlanClear */
export async function tkshopJobsSendTargetPlanClearPost(
  body: API.TargetPlanClearRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/jobs/sendTargetPlanClear', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 选中达人列表获取视频投流码 POST /api/tkshop/jobs/sendVideoAdCode */
export async function tkshopJobsSendVideoAdCodePost(
  body: API.SendVideoADCodeRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/jobs/sendVideoAdCode', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 流程执行时获取任务 GET /api/tkshop/jobs/task/fetchJob */
export async function tkshopJobsTaskFetchJobGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopJobsTaskFetchJobGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultGhJobDto>('/api/tkshop/jobs/task/fetchJob', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 汇报ghJob执行结果 PUT /api/tkshop/jobs/task/reportJob */
export async function tkshopJobsTaskReportJobPut(
  body: API.ReportGhJobRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/jobs/task/reportJob', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
