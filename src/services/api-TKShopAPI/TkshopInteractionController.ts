// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 查询店铺特定类型交互的次数 GET /api/tkshop/countTodayInteractions */
export async function tkshopCountTodayInteractionsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopCountTodayInteractionsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/tkshop/countTodayInteractions', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 新增交互记录 POST /api/tkshop/interaction */
export async function tkshopInteractionPost(
  body: API.AddTkshopInteractionRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkshopInteractionDto>('/api/tkshop/interaction', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量添加手动记录 POST /api/tkshop/interaction/addManualRemark */
export async function tkshopInteractionAddManualRemarkPost(
  body: API.AddTkshopManualRemarkRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/interaction/addManualRemark', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除手动记录 PUT /api/tkshop/interaction/deleteManualRemark */
export async function tkshopInteractionDeleteManualRemarkPut(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/tkshop/interaction/deleteManualRemark', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量修改手动记录 PUT /api/tkshop/interaction/updateManualRemark */
export async function tkshopInteractionUpdateManualRemarkPut(
  body: API.AddTkshopManualRemarkRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/interaction/updateManualRemark', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询交互记录 GET /api/tkshop/interactions */
export async function tkshopInteractionsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopInteractionsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkshopInteractionDto>('/api/tkshop/interactions', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 新增交互记录（批量） POST /api/tkshop/interactions */
export async function tkshopInteractionsPost(
  body: API.AddTkshopInteractionsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/tkshop/interactions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询特定类型的最后一次交互 GET /api/tkshop/lastInteraction */
export async function tkshopLastInteractionGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopLastInteractionGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkshopInteractionDto>('/api/tkshop/lastInteraction', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
