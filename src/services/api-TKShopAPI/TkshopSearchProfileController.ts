// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 保存查询方案（新增或修改） POST /api/tkshop/searchProfile */
export async function tkshopSearchProfilePost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopSearchProfilePostParams,
  body: Record<string, any>,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkshopSearchProfileDto>('/api/tkshop/searchProfile', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除查询方案 DELETE /api/tkshop/searchProfile/${param0} */
export async function tkshopSearchProfileByIdDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopSearchProfileByIdDeleteParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/tkshop/searchProfile/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 列出所有查询方案 GET /api/tkshop/searchProfile/list */
export async function tkshopSearchProfileListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopSearchProfileListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTkshopSearchProfileDto>('/api/tkshop/searchProfile/list', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
