// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 检查资源是否被当前用户授权 GET /api/resource/checkAuthorized */
export async function resourceCheckAuthorizedGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.resourceCheckAuthorizedGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/resource/checkAuthorized', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 授权给用户和部门 POST /api/resource/grant */
export async function resourceGrantPost(
  body: API.ResourceGrantRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/resource/grant', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取资源的授权信息 GET /api/resource/grant/${param0}/${param1} */
export async function resourceGrantByResourceTypeByResourceIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.resourceGrantByResourceTypeByResourceIdGetParams,
  options?: { [key: string]: any },
) {
  const { resourceType: param0, resourceId: param1, ...queryParams } = params;
  return request<API.WebResultGrantInfo>(`/api/resource/grant/${param0}/${param1}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}
