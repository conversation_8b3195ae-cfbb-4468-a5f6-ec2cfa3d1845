// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 加入团队 POST /api/wx/invite/join */
export async function wxInviteJoinPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.wxInviteJoinPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/wx/invite/join', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取邀请二维码 GET /api/wx/invite/qrcode */
export async function wxInviteQrcodeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.wxInviteQrcodeGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultWxQrCodeVo>('/api/wx/invite/qrcode', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取业务二维码扫描结果 GET /api/wx/invite/qrcodeResult */
export async function wxInviteQrcodeResultGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.wxInviteQrcodeResultGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultWxQrCodeInviteResult>('/api/wx/invite/qrcodeResult', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
