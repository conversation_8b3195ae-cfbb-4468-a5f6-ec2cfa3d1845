// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 用来检查某个内核版本是否有更新 GET /api/meta/chromium/checkUpdate */
export async function metaChromiumCheckUpdateGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.metaChromiumCheckUpdateGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultChromiumKernelVo>('/api/meta/chromium/checkUpdate', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 用来获取一个打开指定ua version的内核 GET /api/meta/chromium/findSuitableKernel */
export async function metaChromiumFindSuitableKernelGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.metaChromiumFindSuitableKernelGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultChromiumKernelVo>('/api/meta/chromium/findSuitableKernel', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
