// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 判断达人是否存在 GET /api/tk/creator/exists */
export async function tkCreatorExistsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkCreatorExistsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultboolean>('/api/tk/creator/exists', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 更新达人初始化信息 PUT /api/tk/creator/initInfo */
export async function tkCreatorInitInfoPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkCreatorInitInfoPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tk/creator/initInfo', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询团队达人V2 GET /api/tk/creator/list */
export async function tkCreatorListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkCreatorListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkCreatorDto>('/api/tk/creator/list', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
