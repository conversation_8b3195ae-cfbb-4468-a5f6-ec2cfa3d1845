// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** Rpa执行器与门户联络一次，创建或更新登录设备 创建登录设备 POST /api/rpa/cloud/executor/contact */
export async function rpaCloudExecutorContactPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaCloudExecutorContactPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/rpa/cloud/executor/contact', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 可用区域 GET /api/rpa/cloud/regions */
export async function rpaCloudRegionsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaCloudRegionsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListRpaAvailableRegionVo>('/api/rpa/cloud/regions', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
