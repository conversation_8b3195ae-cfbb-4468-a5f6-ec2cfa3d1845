// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：
import * as AlipayController from './AlipayController';
import * as GiftCardController from './GiftCardController';
import * as InvoiceController from './InvoiceController';
import * as PaymentController from './PaymentController';
import * as PaymentRemoteServiceImpl from './PaymentRemoteServiceImpl';
import * as WebhookPaymentController from './WebhookPaymentController';
import * as WechatPayController from './WechatPayController';
export default {
  AlipayController,
  GiftCardController,
  InvoiceController,
  PaymentController,
  PaymentRemoteServiceImpl,
  WebhookPaymentController,
  WechatPayController,
};
