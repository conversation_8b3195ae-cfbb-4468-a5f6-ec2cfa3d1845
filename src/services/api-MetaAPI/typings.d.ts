declare namespace API {
  type AppPlatformVersionConfig = {
    appVersion?: string;
    betaAppVersion?: string;
    betaBuildNo?: number;
    browserVersion?: number;
    currentBuildNo?: number;
    earlyTryVersion?: number;
    minBuildNo?: number;
    platfrom?: 'android' | 'linux' | 'macos' | 'unknown' | 'web' | 'windows' | 'windows7';
  };

  type AppVersionConfig = {
    betaUserIds?: number[];
    versionConfigs?: AppPlatformVersionConfig[];
  };

  type AreaVo = {
    area?:
      | 'Argentina'
      | 'Australia'
      | 'Austria'
      | 'Belarus'
      | 'Belgium'
      | 'Bolivia'
      | 'Brazil'
      | 'Canada'
      | 'Chile'
      | 'China'
      | 'Colombia'
      | 'Costa_Rica'
      | 'Dominican'
      | 'Ecuador'
      | 'Egypt'
      | 'France'
      | 'Germany'
      | 'Global'
      | 'Guatemala'
      | 'Honduras'
      | 'HongKong'
      | 'India'
      | 'Indonesia'
      | 'Ireland'
      | 'Israel'
      | 'Italy'
      | 'Japan'
      | 'Kazakhstan'
      | 'Korea'
      | 'Malaysia'
      | 'Mexico'
      | 'Netherlands'
      | 'Nicaragua'
      | 'Panama'
      | 'Paraguay'
      | 'Peru'
      | 'Philippines'
      | 'Poland'
      | 'Portuguese'
      | 'Puerto_Rico'
      | 'Russia'
      | 'Salvador'
      | 'Saudi_Arabia'
      | 'Singapore'
      | 'Spain'
      | 'Sweden'
      | 'Switzerland'
      | 'Taiwan'
      | 'Thailand'
      | 'Turkey'
      | 'United_Arab_Emirates'
      | 'United_Kingdom'
      | 'United_States'
      | 'Uruguay'
      | 'Venezuela'
      | 'Vietnam';
    desc?: string;
  };

  type ChromiumKernelVo = {
    /** 构建号，最好填 yyyyMMdd{index} */
    buildNumber?: number;
    description?: string;
    /** 下载地址 */
    downloadLink?: string;
    id?: number;
    /** 允许模拟的最大的UA版本号，如125，客户端要根据min_ua和max_ua在已经下载的内核列表里找到一个合适的内核来打开会话 */
    maxUa?: number;
    /** 允许模拟的最小的UA版本号，如119 */
    minUa?: number;
    name?: string;
    oem?: string;
    /** 'windows10' | 'windows7' | 'macos_arm' | 'macos_x64' | 'linux' //该版本内核能安装在哪个操作系统上 */
    platform?: string;
    /** 这个内核的更新时间 */
    updateTime?: string;
    /** 主版本号，如 125 */
    versionNumber?: number;
  };

  type CloudProviderVo = {
    desc?: string;
    provider?:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
    regions?: CloudRegionVo[];
  };

  type CloudRegionVo = {
    area?: '中东' | '中国' | '亚太' | '北美' | '南极洲' | '南美' | '欧洲' | '非洲';
    city?: string;
    country?: string;
    ipLocation?: IpLocationDto;
    location?: string;
    region?: string;
  };

  type CountryVo = {
    code?: string;
    continentCode?: string;
    continentName?: string;
    continentNameEn?: string;
    geonameId?: number;
    id?: number;
    inEu?: boolean;
    location?: IpLocationDto;
    name?: string;
    nameEn?: string;
    show?: boolean;
  };

  type CreditTypeVo = {
    creditType?:
      | 'BuyCredit'
      | 'ConsumeCredit'
      | 'FingerprintQuota'
      | 'GiftCard'
      | 'InitPresent'
      | 'IosDeveloperApprove'
      | 'IpOverQuotaTraffic'
      | 'OrderRefund'
      | 'Present'
      | 'RpaCaptcha'
      | 'RpaExecuteQuota'
      | 'RpaMarketFlow'
      | 'RpaOpenAi'
      | 'RpaSendEmail'
      | 'RpaSendSms'
      | 'RpaSendWeChat'
      | 'ShopQuota'
      | 'ShopSecurityPolicy'
      | 'StorageQuota'
      | 'TeamMemberQuota'
      | 'TeamMobileQuota'
      | 'TransferIn'
      | 'TransferOut'
      | 'TransitTraffic';
    /** 是否是支出 */
    decrease?: boolean;
    desc?: string;
  };

  type ExchangeDto = {
    countryCode?: string;
    currency?: string;
    id?: number;
    name?: string;
    rate?: number;
    region?: string;
    show?: boolean;
    symbol?: string;
    updateTime?: string;
  };

  type IpDataVo = {
    city?: string;
    country?: string;
    countryCode?: string;
    district?: string;
    id?: number;
    ip?: string;
    isp?: string;
    location?: IpLocationDto;
    locationId?: number;
    province?: string;
    revisedLocationId?: number;
    revisedProvider?: string;
    tag?: string;
    updateTime?: string;
    zip?: string;
    zone?: string;
  };

  type IpLocationDto = {
    city?: string;
    cityEn?: string;
    continent?: string;
    continentCode?: string;
    continentEn?: string;
    country?: string;
    countryCode?: string;
    countryEn?: string;
    createTime?: string;
    geonameId?: number;
    id?: number;
    inEu?: boolean;
    latitude?: number;
    level?: 'City' | 'Continent' | 'Country' | 'District' | 'None' | 'Province' | 'Unknown';
    locale?: string;
    longitude?: number;
    postalCode?: string;
    province?: string;
    provinceCode?: string;
    provinceEn?: string;
    show?: boolean;
    teamId?: number;
    timezone?: string;
  };

  type IpLocationResultVo = {
    currentProduct?: ProductInfo;
    result?: Record<string, any>;
  };

  type IpLocationVo = {
    city?: string;
    cityEn?: string;
    continent?: string;
    continentCode?: string;
    continentEn?: string;
    country?: string;
    countryCode?: string;
    countryEn?: string;
    createTime?: string;
    domestic?: boolean;
    geonameId?: number;
    id?: number;
    inEu?: boolean;
    ip?: string;
    latitude?: number;
    level?: 'City' | 'Continent' | 'Country' | 'District' | 'None' | 'Province' | 'Unknown';
    locale?: string;
    longitude?: number;
    postalCode?: string;
    province?: string;
    provinceCode?: string;
    provinceEn?: string;
    show?: boolean;
    teamId?: number;
    timezone?: string;
  };

  type KfFaqDto = {
    hide?: boolean;
    id?: number;
    sortNo?: number;
    title?: string;
    updateTime?: string;
    url?: string;
  };

  type LocaleVo = {
    displayName?: string;
    locale?: string;
  };

  type metaChromiumCheckUpdateGetParams = {
    /** platform */
    platform: string;
    /** oemName */
    oemName: string;
    /** version */
    version: number;
  };

  type metaChromiumFindSuitableKernelGetParams = {
    /** platform */
    platform: string;
    /** oemName */
    oemName?: string;
    /** uaVersion */
    uaVersion: number;
  };

  type metaCloudRegionsGetParams = {
    /** provider */
    provider?:
      | 'aliyun'
      | 'aws'
      | 'aws_cn'
      | 'aws_ls'
      | 'azure'
      | 'azure_cn'
      | 'baidu'
      | 'baoliannet'
      | 'bluevps'
      | 'dmit'
      | 'ecloud10086'
      | 'googlecloud'
      | 'huawei'
      | 'huayang'
      | 'huoshan'
      | 'jdbox'
      | 'jdcloud'
      | 'jdeip'
      | 'lan'
      | 'oracle'
      | 'other'
      | 'qcloud'
      | 'raincloud'
      | 'ucloud'
      | 'vlcloud'
      | 'vps'
      | 'ygeip';
  };

  type metaCountriesGetParams = {
    /** 是否按照show字段过滤 */
    show?: boolean;
    /** 是否按名称模糊匹配过滤 */
    name?: string;
    /** includeLocation */
    includeLocation?: boolean;
  };

  type metaExchangeRateGetParams = {
    /** from */
    from: string;
    /** to */
    to: string;
  };

  type metaFaqPageGetParams = {
    /** title */
    title?: string;
    /** hide */
    hide?: boolean;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type metaIp138GetParams = {
    /** ip */
    ip?: string;
    /** force */
    force?: boolean;
  };

  type metaIpLocationByLocationIdGetParams = {
    /** locationId */
    locationId: number;
  };

  type metaIpLocationByLocationIdHierarchyGetParams = {
    /** locationId */
    locationId: number;
  };

  type metaIpLocationCountryGetParams = {
    /** countryCode */
    countryCode: string;
  };

  type metaIpLocationPostParams = {
    /** countryCode */
    countryCode: string;
    /** province */
    province: string;
    /** city */
    city?: string;
  };

  type metaIpLocationTreeGetParams = {
    /** countryCode */
    countryCode: string;
  };

  type metaIpMyLocationGetParams = {
    /** ip */
    ip?: string;
    /** force */
    force?: boolean;
  };

  type metaIpSearchCityGetParams = {
    /** countryCode */
    countryCode?: string;
    /** provinceCode */
    provinceCode?: string;
    /** query */
    query: string;
  };

  type metaIpSearchGetParams = {
    /** query */
    query: string;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type metaIpSearchProvinceGetParams = {
    /** countryCode */
    countryCode: string;
    /** query */
    query: string;
  };

  type metaLocalesGetParams = {
    /** countryCode */
    countryCode?: string;
  };

  type metaPlatformByIdGetParams = {
    /** id */
    id: number;
  };

  type PageResultIpLocationDto = {
    current?: number;
    list?: IpLocationDto[];
    pageSize?: number;
    total?: number;
  };

  type PageResultKfFaqDto = {
    current?: number;
    list?: KfFaqDto[];
    pageSize?: number;
    total?: number;
  };

  type PlatformTypeDto = {
    category?: 'IM' | 'Mail' | 'Other' | 'Payment' | 'Shop' | 'SocialMedia';
    defaultArea?:
      | 'Argentina'
      | 'Australia'
      | 'Austria'
      | 'Belarus'
      | 'Belgium'
      | 'Bolivia'
      | 'Brazil'
      | 'Canada'
      | 'Chile'
      | 'China'
      | 'Colombia'
      | 'Costa_Rica'
      | 'Dominican'
      | 'Ecuador'
      | 'Egypt'
      | 'France'
      | 'Germany'
      | 'Global'
      | 'Guatemala'
      | 'Honduras'
      | 'HongKong'
      | 'India'
      | 'Indonesia'
      | 'Ireland'
      | 'Israel'
      | 'Italy'
      | 'Japan'
      | 'Kazakhstan'
      | 'Korea'
      | 'Malaysia'
      | 'Mexico'
      | 'Netherlands'
      | 'Nicaragua'
      | 'Panama'
      | 'Paraguay'
      | 'Peru'
      | 'Philippines'
      | 'Poland'
      | 'Portuguese'
      | 'Puerto_Rico'
      | 'Russia'
      | 'Salvador'
      | 'Saudi_Arabia'
      | 'Singapore'
      | 'Spain'
      | 'Sweden'
      | 'Switzerland'
      | 'Taiwan'
      | 'Thailand'
      | 'Turkey'
      | 'United_Arab_Emirates'
      | 'United_Kingdom'
      | 'United_States'
      | 'Uruguay'
      | 'Venezuela'
      | 'Vietnam';
    description?: string;
    id?: number;
    name?: string;
    show?: boolean;
    type?: string;
  };

  type PlatformVo = {
    category?: 'IM' | 'Mail' | 'Other' | 'Payment' | 'Shop' | 'SocialMedia';
    defaultArea?:
      | 'Argentina'
      | 'Australia'
      | 'Austria'
      | 'Belarus'
      | 'Belgium'
      | 'Bolivia'
      | 'Brazil'
      | 'Canada'
      | 'Chile'
      | 'China'
      | 'Colombia'
      | 'Costa_Rica'
      | 'Dominican'
      | 'Ecuador'
      | 'Egypt'
      | 'France'
      | 'Germany'
      | 'Global'
      | 'Guatemala'
      | 'Honduras'
      | 'HongKong'
      | 'India'
      | 'Indonesia'
      | 'Ireland'
      | 'Israel'
      | 'Italy'
      | 'Japan'
      | 'Kazakhstan'
      | 'Korea'
      | 'Malaysia'
      | 'Mexico'
      | 'Netherlands'
      | 'Nicaragua'
      | 'Panama'
      | 'Paraguay'
      | 'Peru'
      | 'Philippines'
      | 'Poland'
      | 'Portuguese'
      | 'Puerto_Rico'
      | 'Russia'
      | 'Salvador'
      | 'Saudi_Arabia'
      | 'Singapore'
      | 'Spain'
      | 'Sweden'
      | 'Switzerland'
      | 'Taiwan'
      | 'Thailand'
      | 'Turkey'
      | 'United_Arab_Emirates'
      | 'United_Kingdom'
      | 'United_States'
      | 'Uruguay'
      | 'Venezuela'
      | 'Vietnam';
    endpoints?: ShopPlatformDetail[];
    show?: boolean;
    type?: string;
  };

  type ProductInfo = {
    name?: string;
    principal?: string;
    version?: string;
  };

  type ProvinceCityListVo = {
    city?: string[];
    province?: string;
  };

  type ProvinceDetailVo = {
    cities?: IpLocationDto[];
    city?: string;
    cityEn?: string;
    continent?: string;
    continentCode?: string;
    continentEn?: string;
    country?: string;
    countryCode?: string;
    countryEn?: string;
    createTime?: string;
    geonameId?: number;
    id?: number;
    inEu?: boolean;
    latitude?: number;
    level?: 'City' | 'Continent' | 'Country' | 'District' | 'None' | 'Province' | 'Unknown';
    locale?: string;
    longitude?: number;
    postalCode?: string;
    province?: string;
    provinceCode?: string;
    provinceEn?: string;
    show?: boolean;
    teamId?: number;
    timezone?: string;
  };

  type ProvinceVo = {
    countryCode?: string;
    province?: string;
    provinceCode?: string;
    provinceEn?: string;
  };

  type remoteMetaIpLocationGetParams = {
    /** ip */
    ip: string;
  };

  type remoteMetaIpReloadLocationProviderGetParams = {
    /** geoFile */
    geoFile?: string;
    /** qqwryFile */
    qqwryFile?: string;
  };

  type ShopPlatformDetail = {
    area?:
      | 'Argentina'
      | 'Australia'
      | 'Austria'
      | 'Belarus'
      | 'Belgium'
      | 'Bolivia'
      | 'Brazil'
      | 'Canada'
      | 'Chile'
      | 'China'
      | 'Colombia'
      | 'Costa_Rica'
      | 'Dominican'
      | 'Ecuador'
      | 'Egypt'
      | 'France'
      | 'Germany'
      | 'Global'
      | 'Guatemala'
      | 'Honduras'
      | 'HongKong'
      | 'India'
      | 'Indonesia'
      | 'Ireland'
      | 'Israel'
      | 'Italy'
      | 'Japan'
      | 'Kazakhstan'
      | 'Korea'
      | 'Malaysia'
      | 'Mexico'
      | 'Netherlands'
      | 'Nicaragua'
      | 'Panama'
      | 'Paraguay'
      | 'Peru'
      | 'Philippines'
      | 'Poland'
      | 'Portuguese'
      | 'Puerto_Rico'
      | 'Russia'
      | 'Salvador'
      | 'Saudi_Arabia'
      | 'Singapore'
      | 'Spain'
      | 'Sweden'
      | 'Switzerland'
      | 'Taiwan'
      | 'Thailand'
      | 'Turkey'
      | 'United_Arab_Emirates'
      | 'United_Kingdom'
      | 'United_States'
      | 'Uruguay'
      | 'Venezuela'
      | 'Vietnam';
    category?: 'IM' | 'Mail' | 'Other' | 'Payment' | 'Shop' | 'SocialMedia';
    country?: string;
    countryEn?: string;
    frontUrl?: string;
    id?: number;
    loginUrl?: string;
    name?: string;
    typeName?: string;
  };

  type ShopPlatformVo = {
    area?:
      | 'Argentina'
      | 'Australia'
      | 'Austria'
      | 'Belarus'
      | 'Belgium'
      | 'Bolivia'
      | 'Brazil'
      | 'Canada'
      | 'Chile'
      | 'China'
      | 'Colombia'
      | 'Costa_Rica'
      | 'Dominican'
      | 'Ecuador'
      | 'Egypt'
      | 'France'
      | 'Germany'
      | 'Global'
      | 'Guatemala'
      | 'Honduras'
      | 'HongKong'
      | 'India'
      | 'Indonesia'
      | 'Ireland'
      | 'Israel'
      | 'Italy'
      | 'Japan'
      | 'Kazakhstan'
      | 'Korea'
      | 'Malaysia'
      | 'Mexico'
      | 'Netherlands'
      | 'Nicaragua'
      | 'Panama'
      | 'Paraguay'
      | 'Peru'
      | 'Philippines'
      | 'Poland'
      | 'Portuguese'
      | 'Puerto_Rico'
      | 'Russia'
      | 'Salvador'
      | 'Saudi_Arabia'
      | 'Singapore'
      | 'Spain'
      | 'Sweden'
      | 'Switzerland'
      | 'Taiwan'
      | 'Thailand'
      | 'Turkey'
      | 'United_Arab_Emirates'
      | 'United_Kingdom'
      | 'United_States'
      | 'Uruguay'
      | 'Venezuela'
      | 'Vietnam';
    category?: 'IM' | 'Mail' | 'Other' | 'Payment' | 'Shop' | 'SocialMedia';
    frontUrl?: string;
    id?: number;
    loginUrl?: string;
    name?: string;
    typeName?: string;
  };

  type SystemOpConfig = {
    downloadAppOnLogin?: boolean;
    recordHttpLog?: boolean;
  };

  type TimezoneVo = {
    offset?: number;
    timezone?: string;
  };

  type TkPackParamVo = {
    label?: string;
    paramKey?: string;
  };

  type WebResult = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultAppVersionConfig = {
    code?: number;
    data?: AppVersionConfig;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultChromiumKernelVo = {
    code?: number;
    data?: ChromiumKernelVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultdouble = {
    code?: number;
    data?: number;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultIpDataVo = {
    code?: number;
    data?: IpDataVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultIpLocationDto = {
    code?: number;
    data?: IpLocationDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultIpLocationResultVo = {
    code?: number;
    data?: IpLocationResultVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultIpLocationVo = {
    code?: number;
    data?: IpLocationVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListAreaVo = {
    code?: number;
    data?: AreaVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListCloudProviderVo = {
    code?: number;
    data?: CloudProviderVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListCountryVo = {
    code?: number;
    data?: CountryVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListCreditTypeVo = {
    code?: number;
    data?: CreditTypeVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListExchangeDto = {
    code?: number;
    data?: ExchangeDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListIpLocationDto = {
    code?: number;
    data?: IpLocationDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListLocaleVo = {
    code?: number;
    data?: LocaleVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListPlatformTypeDto = {
    code?: number;
    data?: PlatformTypeDto[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListPlatformVo = {
    code?: number;
    data?: PlatformVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListProvinceCityListVo = {
    code?: number;
    data?: ProvinceCityListVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListProvinceDetailVo = {
    code?: number;
    data?: ProvinceDetailVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListProvinceVo = {
    code?: number;
    data?: ProvinceVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListstring = {
    code?: number;
    data?: string[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTimezoneVo = {
    code?: number;
    data?: TimezoneVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultListTkPackParamVo = {
    code?: number;
    data?: TkPackParamVo[];
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultIpLocationDto = {
    code?: number;
    data?: PageResultIpLocationDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultPageResultKfFaqDto = {
    code?: number;
    data?: PageResultKfFaqDto;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultShopPlatformVo = {
    code?: number;
    data?: ShopPlatformVo;
    message?: string;
    requestId?: string;
    success?: boolean;
  };

  type WebResultSystemOpConfig = {
    code?: number;
    data?: SystemOpConfig;
    message?: string;
    requestId?: string;
    success?: boolean;
  };
}
