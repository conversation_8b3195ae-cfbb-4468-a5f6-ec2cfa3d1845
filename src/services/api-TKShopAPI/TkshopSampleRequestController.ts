// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取表达式符号 GET /api/tkshop/expressionSymbols */
export async function tkshopExpressionSymbolsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListExpressionSymbolVo>('/api/tkshop/expressionSymbols', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 添加索样策略 POST /api/tkshop/sample-request/batchDelete */
export async function tkshopSampleRequestBatchDeletePost(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/sample-request/batchDelete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取若干天已完成的索样记录ID GET /api/tkshop/sample-request/completedApplyIds */
export async function tkshopSampleRequestCompletedApplyIdsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopSampleRequestCompletedApplyIdsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListstring>('/api/tkshop/sample-request/completedApplyIds', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询索样记录 GET /api/tkshop/sample-request/page */
export async function tkshopSampleRequestPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopSampleRequestPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkshopSampleRequestDetailVo>(
    '/api/tkshop/sample-request/page',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 获取索样审批的店铺 GET /api/tkshop/sample-request/pageShopsToReview */
export async function tkshopSampleRequestPageShopsToReviewGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopSampleRequestPageShopsToReviewGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultSampleRequestShopBriefVo>(
    '/api/tkshop/sample-request/pageShopsToReview',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 分页查询索样记录 POST /api/tkshop/sample-request/pageV2 */
export async function tkshopSampleRequestPageV2Post(
  body: API.PageSampleRequestRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkshopSampleRequestDetailVo>(
    '/api/tkshop/sample-request/pageV2',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 分页查询索样记录 GET /api/tkshop/sample-request/pageWithPolicy */
export async function tkshopSampleRequestPageWithPolicyGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopSampleRequestPageWithPolicyGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkshopSampleRequestAuditVo>(
    '/api/tkshop/sample-request/pageWithPolicy',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 添加索样策略 POST /api/tkshop/sample-request/policy */
export async function tkshopSampleRequestPolicyPost(
  body: API.AddSampleRequestPolicyRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkshopSampleRequestPolicyVo>('/api/tkshop/sample-request/policy', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改索样策略 PUT /api/tkshop/sample-request/policy/${param0} */
export async function tkshopSampleRequestPolicyByIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopSampleRequestPolicyByIdPutParams,
  body: API.AddSampleRequestPolicyRequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultTkshopSampleRequestPolicyVo>(
    `/api/tkshop/sample-request/policy/${param0}`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      params: { ...queryParams },
      data: body,
      ...(options || {}),
    },
  );
}

/** 删除索样策略 DELETE /api/tkshop/sample-request/policy/${param0} */
export async function tkshopSampleRequestPolicyByIdDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopSampleRequestPolicyByIdDeleteParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/tkshop/sample-request/policy/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取所有索样策略 GET /api/tkshop/sample-request/policy/list */
export async function tkshopSampleRequestPolicyListGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTkshopSampleRequestPolicyVo>(
    '/api/tkshop/sample-request/policy/list',
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/** 获取索样审批的店铺 GET /api/tkshop/sample-request/shopsToReview */
export async function tkshopSampleRequestShopsToReviewGet(options?: { [key: string]: any }) {
  return request<API.WebResultListSampleRequestShopVo>('/api/tkshop/sample-request/shopsToReview', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 同步索样记录 POST /api/tkshop/sample-request/sync */
export async function tkshopSampleRequestSyncPost(
  body: API.SyncSampleRequestRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/sample-request/sync', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 同步索样记录的直播和视频 POST /api/tkshop/sample-request/syncMedia */
export async function tkshopSampleRequestSyncMediaPost(
  body: API.SyncSampleRequestMediaRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/sample-request/syncMedia', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 同步索样记录状态 PUT /api/tkshop/sample-request/syncStatus */
export async function tkshopSampleRequestSyncStatusPut(
  body: API.SyncSampleRequestStatusRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/sample-request/syncStatus', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
