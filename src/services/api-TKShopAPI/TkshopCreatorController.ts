// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取达人详情 GET /api/tkshop/creator/${param0} */
export async function tkshopCreatorByIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopCreatorByIdGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultTkshopCreatorDetailVo>(`/api/tkshop/creator/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取达人带货成果 GET /api/tkshop/creator/${param0}/achievements */
export async function tkshopCreatorByIdAchievementsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopCreatorByIdAchievementsGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultTkshopCreatorAchievementResult>(
    `/api/tkshop/creator/${param0}/achievements`,
    {
      method: 'GET',
      params: {
        ...queryParams,
      },
      ...(options || {}),
    },
  );
}

/** 获取达人分身带货数据 GET /api/tkshop/creator/${param0}/achievementShop */
export async function tkshopCreatorByIdAchievementShopGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopCreatorByIdAchievementShopGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultTkshopCreatorShopDto>(
    `/api/tkshop/creator/${param0}/achievementShop`,
    {
      method: 'GET',
      params: {
        ...queryParams,
      },
      ...(options || {}),
    },
  );
}

/** 获取达人带货的店铺列表 GET /api/tkshop/creator/${param0}/achievementShops */
export async function tkshopCreatorByIdAchievementShopsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopCreatorByIdAchievementShopsGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultListShopBriefVo>(`/api/tkshop/creator/${param0}/achievementShops`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取达人带货成果 GET /api/tkshop/creator/${param0}/achievementStat */
export async function tkshopCreatorByIdAchievementStatGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopCreatorByIdAchievementStatGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultTkshopCreatorAchievementStat>(
    `/api/tkshop/creator/${param0}/achievementStat`,
    {
      method: 'GET',
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}

/** 修改达人联系方式（全量更新） PUT /api/tkshop/creator/${param0}/contact */
export async function tkshopCreatorByIdContactPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopCreatorByIdContactPutParams,
  body: API.UpdateContactRequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/tkshop/creator/${param0}/contact`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 获取达人交互的店铺列表 GET /api/tkshop/creator/${param0}/interactShops */
export async function tkshopCreatorByIdInteractShopsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopCreatorByIdInteractShopsGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultListShopBriefVo>(`/api/tkshop/creator/${param0}/interactShops`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取达人带货直播 GET /api/tkshop/creator/${param0}/lives */
export async function tkshopCreatorByIdLivesGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopCreatorByIdLivesGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultPageResultTkshopCreatorLiveVo>(
    `/api/tkshop/creator/${param0}/lives`,
    {
      method: 'GET',
      params: {
        ...queryParams,
      },
      ...(options || {}),
    },
  );
}

/** 修改一种联系方式 PUT /api/tkshop/creator/${param0}/oneContact */
export async function tkshopCreatorByIdOneContactPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopCreatorByIdOneContactPutParams,
  body: API.UpdateOneContactRequest,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/tkshop/creator/${param0}/oneContact`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 获取达人带货商品 GET /api/tkshop/creator/${param0}/products */
export async function tkshopCreatorByIdProductsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopCreatorByIdProductsGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultPageResultTkshopCreatorProductDto>(
    `/api/tkshop/creator/${param0}/products`,
    {
      method: 'GET',
      params: {
        ...queryParams,
      },
      ...(options || {}),
    },
  );
}

/** 修改达人备注 PUT /api/tkshop/creator/${param0}/remark */
export async function tkshopCreatorByIdRemarkPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopCreatorByIdRemarkPutParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/tkshop/creator/${param0}/remark`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 修改达人备注 PUT /api/tkshop/creator/${param0}/statusAndRemark */
export async function tkshopCreatorByIdStatusAndRemarkPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopCreatorByIdStatusAndRemarkPutParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/tkshop/creator/${param0}/statusAndRemark`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取达人视频直播 GET /api/tkshop/creator/${param0}/videos */
export async function tkshopCreatorByIdVideosGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopCreatorByIdVideosGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultPageResultTkshopCreatorVideoVo>(
    `/api/tkshop/creator/${param0}/videos`,
    {
      method: 'GET',
      params: {
        ...queryParams,
      },
      ...(options || {}),
    },
  );
}

/** 认领达人 PUT /api/tkshop/creator/acquire */
export async function tkshopCreatorAcquirePut(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/creator/acquire', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分配达人 PUT /api/tkshop/creator/allocate */
export async function tkshopCreatorAllocatePut(
  body: API.TkshopCreatorAllocateRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/creator/allocate', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 自动化标签 POST /api/tkshop/creator/autoTagExpiredTag */
export async function tkshopCreatorAutoTagExpiredTagPost(
  body: API.TkshopAutoTagRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/creator/autoTagExpiredTag', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 取消分配 PUT /api/tkshop/creator/cancelAllocate */
export async function tkshopCreatorCancelAllocatePut(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/creator/cancelAllocate', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取分类列表 GET /api/tkshop/creator/categories */
export async function tkshopCreatorCategoriesGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTkshopCategoryDto>('/api/tkshop/creator/categories', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取达人联系方式列表 POST /api/tkshop/creator/contactList */
export async function tkshopCreatorContactListPost(
  body: API.GhCreatorHandleRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTkshopContactVo>('/api/tkshop/creator/contactList', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取达人特定联系方式列表 POST /api/tkshop/creator/contacts */
export async function tkshopCreatorContactsPost(
  body: API.GhCreatorContactRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListstring>('/api/tkshop/creator/contacts', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询店铺达人总数 POST /api/tkshop/creator/count */
export async function tkshopCreatorCountPost(
  body: API.FindTkshopCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultlong>('/api/tkshop/creator/count', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除达人 POST /api/tkshop/creator/delete */
export async function tkshopCreatorDeletePost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopCreatorDeletePostParams,
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/creator/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除达人（按查询） POST /api/tkshop/creator/deleteByQuery */
export async function tkshopCreatorDeleteByQueryPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopCreatorDeleteByQueryPostParams,
  body: API.PageTkshopCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/creator/deleteByQuery', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 过滤出在若干天内同步过的达人 POST /api/tkshop/creator/filterBasicSynced */
export async function tkshopCreatorFilterBasicSyncedPost(
  body: API.FilterBasicSyncedRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListstring>('/api/tkshop/creator/filterBasicSynced', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 新增好友关系 POST /api/tkshop/creator/friendship */
export async function tkshopCreatorFriendshipPost(
  body: API.TkshopAddFriendshipRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/creator/friendship', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取当天自动化标签值 GET /api/tkshop/creator/getAutoTagValue */
export async function tkshopCreatorGetAutoTagValueGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopCreatorGetAutoTagValueGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/tkshop/creator/getAutoTagValue', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 按区域分组查询达人 POST /api/tkshop/creator/groupByRegion */
export async function tkshopCreatorGroupByRegionPost(
  body: API.PageTkshopCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListRegionBatchVo>('/api/tkshop/creator/groupByRegion', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取达人的cid POST /api/tkshop/creator/loadCid */
export async function tkshopCreatorLoadCidPost(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMaplong>('/api/tkshop/creator/loadCid', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据handles获取达人的cid POST /api/tkshop/creator/loadCidByHandles */
export async function tkshopCreatorLoadCidByHandlesPost(
  body: API.HandlesRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMapstring>('/api/tkshop/creator/loadCidByHandles', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量获取达人联系方式详情 POST /api/tkshop/creator/loadContactDetails */
export async function tkshopCreatorLoadContactDetailsPost(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTkshopCreatorContactDetailVo>(
    '/api/tkshop/creator/loadContactDetails',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 分页查询达人（不返回总数） POST /api/tkshop/creator/page */
export async function tkshopCreatorPagePost(
  body: API.PageTkshopCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkshopCreatorDetailVo>('/api/tkshop/creator/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 恢复达人 POST /api/tkshop/creator/recover */
export async function tkshopCreatorRecoverPost(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/creator/recover', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改区域 PUT /api/tkshop/creator/region */
export async function tkshopCreatorRegionPut(
  body: API.UpdateRegionRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/creator/region', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取达人的区域（根据用户权限过滤） GET /api/tkshop/creator/regions */
export async function tkshopCreatorRegionsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListstring>('/api/tkshop/creator/regions', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 取消认领达人 PUT /api/tkshop/creator/release */
export async function tkshopCreatorReleasePut(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/creator/release', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** getResponsibleUserList GET /api/tkshop/creator/responsibleUserList */
export async function tkshopCreatorResponsibleUserListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopCreatorResponsibleUserListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListlong>('/api/tkshop/creator/responsibleUserList', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取facebook的profile，会返回fbid数字id，用于跳转到facebook的消息页面 GET /api/tkshop/creator/scrapFBProfile */
export async function tkshopCreatorScrapFbProfileGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopCreatorScrapFBProfileGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultFBUserProfile>('/api/tkshop/creator/scrapFBProfile', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 根据handles更新达人状态 PUT /api/tkshop/creator/status */
export async function tkshopCreatorStatusPut(
  body: API.UpdateStatusRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/creator/status', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量同步达人带货能力数据 POST /api/tkshop/creator/syncBatchDetail */
export async function tkshopCreatorSyncBatchDetailPost(
  body: API.SyncCreatorDetailRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/creator/syncBatchDetail', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量同步达人在店铺中带货信息 POST /api/tkshop/creator/syncBatchOnShop */
export async function tkshopCreatorSyncBatchOnShopPost(
  body: API.SyncCreatorShopRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/creator/syncBatchOnShop', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 达人可导出的字段元信息 GET /api/tkshop/fields */
export async function tkshopFieldsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTkshopCreatorFiledVo>('/api/tkshop/fields', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 是否与达人产生过交互（批量） POST /api/tkshop/hasInteractions */
export async function tkshopHasInteractionsPost(
  body: API.TkshopHasInteractionRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListHasInteractionVo>('/api/tkshop/hasInteractions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新分身的登录态 PUT /api/tkshop/shop/${param0}/loginStatus */
export async function tkshopShopByShopIdLoginStatusPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkshopShopByShopIdLoginStatusPutParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/tkshop/shop/${param0}/loginStatus`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}
