// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 取消订阅 GET /api/wx/subscribe/cancel/${param0} */
export async function wxSubscribeCancelByIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.wxSubscribeCancelByIdGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/wx/subscribe/cancel/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取订阅记录 GET /api/wx/subscribe/get/${param0} */
export async function wxSubscribeGetByIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.wxSubscribeGetByIdGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultWxSubscribeDto>(`/api/wx/subscribe/get/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取订阅二维码 POST /api/wx/subscribe/qrcode */
export async function wxSubscribeQrcodePost(
  body: API.SubscribeQrCodeRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultWxQrCodeVo>('/api/wx/subscribe/qrcode', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取业务二维码扫描结果 GET /api/wx/subscribe/qrcodeResult */
export async function wxSubscribeQrcodeResultGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.wxSubscribeQrcodeResultGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultWxQrCodeBizResult>('/api/wx/subscribe/qrcodeResult', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
