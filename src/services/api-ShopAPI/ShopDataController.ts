// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 清除店铺的cookies DELETE /api/shop/${param0}/cleanCookies */
export async function shopByShopIdCleanCookiesDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopByShopIdCleanCookiesDeleteParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/shop/${param0}/cleanCookies`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除店铺位于oss上的数据. localstorage等 DELETE /api/shop/${param0}/clearShopDataInOss */
export async function shopByShopIdClearShopDataInOssDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopByShopIdClearShopDataInOssDeleteParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/shop/${param0}/clearShopDataInOss`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取某个店铺的常用网址 GET /api/shop/${param0}/favoriteSites */
export async function shopByShopIdFavoriteSitesGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopByShopIdFavoriteSitesGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultListFavoriteSiteVo>(`/api/shop/${param0}/favoriteSites`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除店铺的常用网址 DELETE /api/shop/${param0}/favoriteSites */
export async function shopByShopIdFavoriteSitesDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopByShopIdFavoriteSitesDeleteParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/shop/${param0}/favoriteSites`, {
    method: 'DELETE',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 获取某个店铺的cookies GET /api/shop/${param0}/getCookies */
export async function shopByShopIdGetCookiesGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopByShopIdGetCookiesGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultShopCookiesVo>(`/api/shop/${param0}/getCookies`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取某个店铺的浏览历史记录 GET /api/shop/${param0}/getHistories */
export async function shopByShopIdGetHistoriesGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopByShopIdGetHistoriesGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultListShopSiteHistoryVo>(`/api/shop/${param0}/getHistories`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 更改店铺的cookies PUT /api/shop/${param0}/setCookies */
export async function shopByShopIdSetCookiesPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopByShopIdSetCookiesPutParams,
  body: API.SetShopCookiesParamVo,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/shop/${param0}/setCookies`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 为店铺的localstorage等文件存取生成签名链接 GET /api/shop/${param0}/signatureShopData */
export async function shopByShopIdSignatureShopDataGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopByShopIdSignatureShopDataGetParams,
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResultStsPostSignature>(`/api/shop/${param0}/signatureShopData`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 覆盖某个店铺的浏览历史记录 POST /api/shop/${param0}/uploadHistories */
export async function shopByShopIdUploadHistoriesPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopByShopIdUploadHistoriesPostParams,
  body: Record<string, any>[],
  options?: { [key: string]: any },
) {
  const { shopId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/shop/${param0}/uploadHistories`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 创建一个常用网址，如果scope==User，ownerId一定是当前用户 POST /api/shop/createFavoriteSite */
export async function shopCreateFavoriteSitePost(
  body: API.UpdateFavoriteSiteRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultFavoriteSiteVo>('/api/shop/createFavoriteSite', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量添加快捷方式 POST /api/shop/createFavoriteSiteBatch */
export async function shopCreateFavoriteSiteBatchPost(
  body: API.UpdateFavoriteSiteBatchRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/createFavoriteSiteBatch', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新一个常用网址 POST /api/shop/updateFavoriteSite */
export async function shopUpdateFavoriteSitePost(
  body: API.UpdateFavoriteSiteRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultFavoriteSiteVo>('/api/shop/updateFavoriteSite', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
