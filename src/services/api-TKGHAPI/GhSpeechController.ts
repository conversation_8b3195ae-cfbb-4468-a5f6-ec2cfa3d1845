// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取话术详情 GET /api/gh/speech/${param0} */
export async function ghSpeechByIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSpeechByIdGetParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResultGhSpeechDto>(`/api/gh/speech/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改话术 PUT /api/gh/speech/${param0} */
export async function ghSpeechByIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSpeechByIdPutParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/gh/speech/${param0}`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 修改手机账号（或分身的）默认话术分组 不传入groupId时，清除配置 GET /api/gh/speech/accountConfig */
export async function ghSpeechAccountConfigGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSpeechAccountConfigGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultGhSpeechGroupDto>('/api/gh/speech/accountConfig', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修改手机账号（或分身的）默认话术分组 不传入groupId时，清除配置 PUT /api/gh/speech/accountConfig */
export async function ghSpeechAccountConfigPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSpeechAccountConfigPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/speech/accountConfig', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 校验话术分组 POST /api/gh/speech/check */
export async function ghSpeechCheckPost(
  body: API.CheckSpeechAndGroupRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListGhSpeechGroupVo>('/api/gh/speech/check', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量创建话术 POST /api/gh/speech/create */
export async function ghSpeechCreatePost(
  body: API.CreateSpeechRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/speech/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除话术 POST /api/gh/speech/delete */
export async function ghSpeechDeletePost(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/speech/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除分组 DELETE /api/gh/speech/group/${param0} */
export async function ghSpeechGroupByGroupIdDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSpeechGroupByGroupIdDeleteParams,
  options?: { [key: string]: any },
) {
  const { groupId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/gh/speech/group/${param0}`, {
    method: 'DELETE',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 修改分组名称 PUT /api/gh/speech/group/${param0}/name */
export async function ghSpeechGroupByIdNamePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSpeechGroupByIdNamePutParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/gh/speech/group/${param0}/name`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 创建话术分组 POST /api/gh/speech/group/create */
export async function ghSpeechGroupCreatePost(
  body: API.CreateSpeechGroupRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultGhSpeechGroupDto>('/api/gh/speech/group/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询话术分组 GET /api/gh/speech/group/list */
export async function ghSpeechGroupListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSpeechGroupListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListGhSpeechGroupVo>('/api/gh/speech/group/list', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询话术 GET /api/gh/speech/page */
export async function ghSpeechPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSpeechPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultGhSpeechDto>('/api/gh/speech/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 切换话术顺序 PUT /api/gh/speech/switchSortNo */
export async function ghSpeechSwitchSortNoPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSpeechSwitchSortNoPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/speech/switchSortNo', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 更新话术顺序 PUT /api/gh/speech/updateSortNo */
export async function ghSpeechUpdateSortNoPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ghSpeechUpdateSortNoPutParams,
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/gh/speech/updateSortNo', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}
