import { useEffect, useMemo, useRef, useState } from 'react';
import DMModal from '@/components/Common/Modal/DMModal';
import I18N from '@/i18n';
import {
  Alert,
  Button,
  Col,
  Form,
  Input,
  InputNumber,
  message,
  Radio,
  Row,
  Select,
  Space,
  Tooltip,
  Typography,
} from 'antd';
import type { ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { scrollProTableOptionFn } from '@/mixins/table';
import {
  tkshopSampleRequestPolicyByIdDelete,
  tkshopSampleRequestPolicyByIdPut,
  tkshopSampleRequestPolicyListGet,
  tkshopSampleRequestPolicyPost,
} from '@/services/api-TKShopAPI/TkshopSampleRequestController';
import DMFormItem, { DMFormItemContext } from '@/components/Common/DMFormItem';
import { GhostModalCaller } from '@/mixins/modal';
import { useRequest } from '@@/plugin-request/request';
import { trimValues } from '@/utils/utils';
import _ from 'lodash';
import DMConfirm from '@/components/Common/DMConfirm';
import {
  getTargetConditionColumn,
  TriggerPolicyConfirmModal,
  usePolicyItems,
} from '@/pages/Sample/components/SampleRequestByPolicyModal';
import Placeholder from '@/components/Common/Placeholder';

const PolicyItemModal = (props: {
  record?: API.SampleRequestConditionVo;
  onUpdate: (values: API.SampleRequestConditionVo) => void;
}) => {
  const { record, onUpdate } = props;
  const [open, setOpen] = useState(true);
  const [creatorColumn, setCreatorColumn] = useState<API.TkshopCreatorFiledVo>();
  const [productColumn, setProductColumn] = useState<API.TkshopCreatorFiledVo>();
  const [creatorOptions, setCreatorOptions] = useState<any[]>([]);
  const [productOptions, setProductOptions] = useState<any[]>([]);
  const [symbol, setSymbol] = useState<string>(() => {
    if (record) {
      return record.symbol!;
    }
    return 'gt';
  });
  const [radioValue, setRadioValue] = useState<'Creator' | 'Product'>(() => {
    if (record?.filedType === 'ProductColumn') {
      return 'Product';
    }
    return 'Creator';
  });
  const [otherValue, setOtherValue] = useState<any>(() => {
    if (record) {
      return record.value;
    }
    return '100';
  });
  const [form] = Form.useForm();
  const { loading } = usePolicyItems({
    onSuccess(data) {
      const { creator, product } = data;
      setCreatorOptions(creator);
      setProductOptions(product);
      if (record && record.filedType !== 'ProductColumn') {
        setCreatorColumn(getTargetConditionColumn(record, data) || record);
      } else {
        setCreatorColumn(creator?.[0]);
      }
      if (record && record.filedType === 'ProductColumn') {
        setProductColumn(getTargetConditionColumn(record, data) || record);
      } else {
        setProductColumn(product?.[0]);
      }
    },
  });
  const symbolOptions = useMemo(() => {
    const all_options = [
      {
        label: I18N.t('大于'),
        value: 'gt',
      },
      {
        label: I18N.t('大于等于'),
        value: 'ge',
      },
      {
        label: I18N.t('小于'),
        value: 'lt',
      },
      {
        label: I18N.t('小于等于'),
        value: 'le',
      },
      {
        label: I18N.t('等于'),
        value: 'eq',
      },
      {
        label: I18N.t('不等于'),
        value: 'ne',
      },
      {
        label: I18N.t('包含'),
        value: 'contains',
      },
      {
        label: I18N.t('不包含'),
        value: 'notContains',
      },
      {
        label: I18N.t('为空'),
        value: 'isEmpty',
      },
      {
        label: I18N.t('非空'),
        value: 'notEmpty',
      },
    ];
    return all_options.filter((item) => {
      if (!creatorColumn || !productColumn) {
        return true;
      }
      if (radioValue === 'Creator') {
        // enum类型
      }
      if (
        (radioValue === 'Creator' && creatorColumn?.valueType === 'number') ||
        (radioValue === 'Product' && productColumn?.valueType === 'number')
      ) {
        // number 类型
        return (
          item.value === 'eq' ||
          item.value === 'ne' ||
          item.value === 'gt' ||
          item.value === 'ge' ||
          item.value === 'lt' ||
          item.value === 'le' ||
          item.value === 'isEmpty' ||
          item.value === 'notEmpty'
        );
      }
      // 字符串类型
      return (
        item.value === 'eq' ||
        item.value === 'ne' ||
        item.value === 'contains' ||
        item.value === 'notContains' ||
        item.value === 'isEmpty' ||
        item.value === 'notEmpty'
      );
    }, []);
  }, [creatorColumn, productColumn, radioValue]);

  useEffect(() => {
    if (symbolOptions.length) {
      setSymbol((val) => {
        const index = _.findIndex(symbolOptions, (i) => {
          return i.value === val;
        });
        if (index === -1) {
          return symbolOptions[0].value;
        }
        return val;
      });
    }
  }, [symbolOptions]);
  useEffect(() => {
    setOtherValue((prev: any) => {
      if (radioValue === 'Creator') {
        if (!creatorColumn) {
          return prev;
        }
        if (creatorColumn.valueType === typeof prev) {
          return prev;
        }
        if (creatorColumn.valueType === 'number') {
          return 100;
        }
        return '';
      }
      if (radioValue === 'Product') {
        if (!productColumn) {
          return prev;
        }
        if (productColumn.valueType === typeof prev) {
          return prev;
        }
        if (productColumn.valueType === 'number') {
          return 100;
        }
        return '';
      }
      return prev;
    });
  }, [radioValue, productColumn, creatorColumn]);
  useEffect(() => {
    if (radioValue && (creatorColumn || productColumn)) {
      form.validateFields(['_value']);
    }
  }, [form, otherValue, radioValue, creatorColumn, productColumn]);

  return (
    <DMModal
      headless
      open={open}
      bodyStyle={{ paddingBottom: 0 }}
      width={520}
      onCancel={() => {
        setOpen(false);
      }}
      onOk={form.submit}
    >
      <DMFormItemContext.Provider value={{ disableLabelMuted: true }}>
        <Form<API.SampleRequestConditionVo>
          form={form}
          requiredMark={false}
          onFinish={() => {
            let filedType;
            let key;
            if (radioValue === 'Product') {
              filedType = 'ProductColumn';
              key = productColumn?.key;
            } else {
              filedType = creatorColumn?.filedType;
              key = creatorColumn?.key;
            }
            onUpdate({
              value: symbol !== 'isEmpty' && symbol !== 'notEmpty' ? _.trim(otherValue) : undefined,
              key,
              filedType,
              symbol,
            });
            setOpen(false);
          }}
          initialValues={record}
        >
          <DMFormItem
            style={{ flex: 1, overflow: 'hidden' }}
            label={
              <Radio
                style={{ lineHeight: '32px' }}
                checked={radioValue === 'Creator'}
                onClick={() => {
                  setRadioValue('Creator');
                }}
              >
                {I18N.t('达人属性')}
              </Radio>
            }
            name={'creator_key'}
            rules={[
              {
                validator() {
                  if (!creatorColumn) {
                    return Promise.reject(I18N.t('请选择达人属性'));
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Form.Item noStyle shouldUpdate>
              <Select
                dropdownMatchSelectWidth={460}
                disabled={radioValue !== 'Creator'}
                loading={loading}
                value={creatorColumn?.key}
                options={creatorOptions?.map((item) => {
                  return {
                    ...item,
                    label: (
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          gap: 8,
                        }}
                      >
                        <span
                          style={{
                            whiteSpace: 'nowrap',
                            textTransform: 'capitalize',
                            overflow: 'visible',
                          }}
                        >
                          {item.label}
                        </span>
                        <div style={{ overflow: 'hidden', flex: 1, textAlign: 'right' }}>
                          <Typography.Text
                            className="hide-in-selector"
                            ellipsis={{
                              tooltip: {
                                title: item.desc,
                                placement: 'right',
                              },
                            }}
                          >
                            {item.desc}
                          </Typography.Text>
                        </div>
                      </div>
                    ),
                    value: item.key,
                  };
                })}
                onChange={(value, option) => {
                  setCreatorColumn(option);
                }}
              />
            </Form.Item>
          </DMFormItem>
          <DMFormItem
            label={
              <Radio
                style={{ lineHeight: '32px' }}
                checked={radioValue === 'Product'}
                onClick={() => {
                  setRadioValue('Product');
                }}
              >
                {I18N.t('商品属性')}
              </Radio>
            }
            name={'product_key'}
            rules={[
              {
                validator() {
                  if (!productColumn) {
                    return Promise.reject(I18N.t('请选择达人属性'));
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Form.Item noStyle shouldUpdate>
              <Select
                loading={loading}
                disabled={radioValue !== 'Product'}
                options={productOptions?.map((item) => {
                  return {
                    ...item,
                    label: item.desc,
                    value: item.key,
                  };
                })}
                value={productColumn?.key}
                onChange={(value, option) => {
                  setProductColumn(option);
                }}
              />
            </Form.Item>
          </DMFormItem>
          <DMFormItem label={I18N.t('操作符')}>
            <Select value={symbol} onChange={setSymbol} options={symbolOptions} />
          </DMFormItem>

          <Form.Item noStyle shouldUpdate>
            {() => {
              const compare_disabled = symbol === 'isEmpty' || symbol === 'notEmpty';
              const _common_props = {
                rules: [
                  {
                    validator() {
                      if (compare_disabled) {
                        return Promise.resolve();
                      }
                      if (_.isNil(otherValue)) {
                        return Promise.reject(new Error(I18N.t('请输入比较值')));
                      }
                      if (!_.trim(otherValue)) {
                        return Promise.reject(new Error(I18N.t('请输入比较值')));
                      }
                      return Promise.resolve();
                    },
                  },
                ],
                label: I18N.t('比较值'),
                name: '_value',
              };
              if (
                (radioValue === 'Product' && productColumn?.valueType === 'number') ||
                (radioValue === 'Creator' && creatorColumn?.valueType === 'number')
              ) {
                return (
                  <DMFormItem {..._common_props}>
                    <Form.Item noStyle shouldUpdate>
                      <InputNumber
                        style={{ width: '100%' }}
                        disabled={compare_disabled}
                        value={otherValue}
                        onChange={setOtherValue}
                      />
                    </Form.Item>
                  </DMFormItem>
                );
              }
              return (
                <DMFormItem {..._common_props}>
                  <Form.Item noStyle shouldUpdate>
                    <Input
                      disabled={compare_disabled}
                      value={otherValue}
                      onChange={(e) => {
                        setOtherValue(e.target.value);
                      }}
                    />
                  </Form.Item>
                </DMFormItem>
              );
            }}
          </Form.Item>
        </Form>
      </DMFormItemContext.Provider>
    </DMModal>
  );
};
const ConditionListField = (props: { value?: any; onChange?: (data: any) => void }) => {
  const { value, onChange } = props;
  const { data: policyMeta } = usePolicyItems();
  return (
    <div style={{ overflow: 'hidden', height: 250 }}>
      <ProTable<API.SampleRequestConditionVo>
        dataSource={[...(value || []), 'add']}
        columns={[
          {
            dataIndex: 'key',
            title: I18N.t('属性'),
            onCell(data) {
              return {
                colSpan: data === 'add' ? 4 : 1,
              };
            },
            ellipsis: true,
            renderText(_dom, record) {
              if (record === 'add') {
                return false;
              }
              const targetColumn = getTargetConditionColumn(record, policyMeta);
              const label = targetColumn?.label || record.key;
              const desc = targetColumn?.desc;
              return (
                <Tooltip title={desc} placement={'right'}>
                  <Typography.Text ellipsis style={{ textTransform: 'capitalize' }}>
                    {label}
                  </Typography.Text>
                </Tooltip>
              );
            },
            render(_dom, record) {
              if (record === 'add') {
                return (
                  <Typography.Link
                    onClick={() => {
                      GhostModalCaller(
                        <PolicyItemModal
                          onUpdate={(values) => {
                            onChange?.([...(value || []), values]);
                          }}
                        />,
                      );
                    }}
                  >
                    <Space size={4}>
                      <IconFontIcon iconName={'tianjia_24'} />
                      <span>{I18N.t('增加新的条件')}</span>
                    </Space>
                  </Typography.Link>
                );
              }
              return _dom;
            },
          },
          {
            dataIndex: 'symbol',
            width: 80,
            title: I18N.t('操作符'),
            onCell(data) {
              return {
                colSpan: data === 'add' ? 0 : 1,
              };
            },
            valueEnum: {
              gt: I18N.t('大于'),
              ge: I18N.t('大于等于'),
              lt: I18N.t('小于'),
              le: I18N.t('小于等于'),
              eq: I18N.t('等于'),
              ne: I18N.t('不等于'),
              contains: I18N.t('包含'),
              notContains: I18N.t('不包含'),
              isEmpty: I18N.t('为空'),
              notEmpty: I18N.t('非空'),
            },
          },
          {
            dataIndex: 'value',
            title: I18N.t('比较值'),
            onCell(data) {
              return {
                colSpan: data === 'add' ? 0 : 1,
              };
            },
            width: 85,
            ellipsis: true,
            render(_dom, record) {
              const { value, filedType, key } = record;
              return (
                <Typography.Text ellipsis={{ tooltip: value }}>
                  {value || <Placeholder />}{' '}
                </Typography.Text>
              );
            },
          },
          {
            valueType: 'option',
            width: 60,
            title: I18N.t('操作'),
            onCell(data) {
              return {
                colSpan: data === 'add' ? 0 : 1,
              };
            },
            render(_dom, record, _index) {
              return (
                <Space>
                  <Tooltip title={I18N.t('编辑')}>
                    <Typography.Link
                      onClick={() => {
                        GhostModalCaller(
                          <PolicyItemModal
                            record={record}
                            onUpdate={(data: any) => {
                              onChange?.(
                                value.map((item, index) => {
                                  if (index === _index) {
                                    return data;
                                  }
                                  return item;
                                }),
                              );
                            }}
                          />,
                        );
                      }}
                    >
                      <IconFontIcon iconName={'edit_24'} />
                    </Typography.Link>
                  </Tooltip>
                  <Tooltip title={I18N.t('删除')}>
                    <Typography.Link
                      onClick={() => {
                        onChange?.(
                          value.filter((item, index) => {
                            return index !== _index;
                          }),
                        );
                      }}
                    >
                      <IconFontIcon iconName={'Trash_24'} />
                    </Typography.Link>
                  </Tooltip>
                </Space>
              );
            },
          },
        ]}
        {...scrollProTableOptionFn({
          pagination: false,
          size: 'small',
        })}
      />
    </div>
  );
};

const PolicyFormModal = (props: {
  data?: API.TkshopSampleRequestAuditVo;
  onUpdate: () => void;
}) => {
  const { data, onUpdate } = props;
  const [open, setOpen] = useState(true);
  const [form] = Form.useForm();
  const { run: submit, loading } = useRequest(
    async () => {
      const values = trimValues(await form.validateFields()) as any;
      if (data) {
        await tkshopSampleRequestPolicyByIdPut(
          {
            id: data.id!,
          },
          values,
        );
        message.success(I18N.t('修改成功'));
      } else {
        await tkshopSampleRequestPolicyPost(values);
        message.success(I18N.t('添加成功'));
      }
      setOpen(false);
      onUpdate();
    },
    {
      manual: true,
    },
  );
  return (
    <DMModal
      open={open}
      width={720}
      bodyStyle={{ paddingBottom: 0 }}
      title={data ? I18N.t('修改策略') : I18N.t('增加新的策略')}
      onCancel={() => {
        setOpen(false);
      }}
      footer={
        <Space align={'center'}>
          <Button type={'primary'} onClick={submit} loading={loading}>
            {I18N.t('确定')}
          </Button>
          <Button
            onClick={() => {
              setOpen(false);
            }}
          >
            {I18N.t('关闭')}
          </Button>
        </Space>
      }
    >
      <DMFormItemContext.Provider
        value={{
          disableLabelMuted: true,
        }}
      >
        <Form<API.AddSampleRequestPolicyRequest>
          initialValues={data}
          requiredMark={false}
          form={form}
        >
          <DMFormItem label={I18N.t('策略名称')} name={'policyName'}>
            <Input autoFocus />
          </DMFormItem>
          <DMFormItem label={I18N.t('策略类型')} name={'approved'} initialValue={true}>
            <Radio.Group style={{ width: '100%' }}>
              <Row>
                <Col span={8}>
                  <Radio value={true}>{I18N.t('同意策略')}</Radio>
                </Col>
                <Col>
                  <Radio value={false}>{I18N.t('拒绝策略')}</Radio>
                </Col>
              </Row>
            </Radio.Group>
          </DMFormItem>
          <DMFormItem label={I18N.t('条件关系')} name={'logicalCondition'} initialValue={'AND'}>
            <Radio.Group style={{ width: '100%' }}>
              <Row>
                <Col span={8}>
                  <Radio value={'AND'}>{I18N.t('并且')}</Radio>
                </Col>
                <Col>
                  <Radio value={'OR'}>{I18N.t('或者')}</Radio>
                </Col>
              </Row>
            </Radio.Group>
          </DMFormItem>
          <DMFormItem label={I18N.t('描述')} name={'policyDesc'}>
            <Input.TextArea style={{ resize: 'none', height: 50, overflow: 'auto' }} />
          </DMFormItem>
          <DMFormItem
            label={I18N.t('子条件')}
            name={'conditionList'}
            initialValue={[]}
            rules={[
              {
                required: true,
                type: 'array',
                message: I18N.t('请设置子条件'),
              },
            ]}
          >
            <ConditionListField />
          </DMFormItem>
        </Form>
      </DMFormItemContext.Provider>
    </DMModal>
  );
};

const SampleRequestPolicy = (props: { shopId?: number }) => {
  const { shopId } = props;
  const [visible, setVisible] = useState(true);
  const actionRef = useRef<ActionType>();

  return (
    <DMModal
      open={visible}
      width={720}
      style={{
        paddingTop: 12,
      }}
      footer={
        <div style={{ display: 'flex', justifyContent: 'space-between', flex: 1 }}>
          <Button
            type={'primary'}
            ghost
            onClick={() => {
              GhostModalCaller(
                <PolicyFormModal
                  onUpdate={() => {
                    actionRef.current?.reload();
                  }}
                />,
              );
            }}
            icon={<IconFontIcon iconName={'tianjia_24'} />}
          >
            <span>{I18N.t('增加新的策略')}</span>
          </Button>
          <Button
            type={'primary'}
            onClick={() => {
              setVisible(false);
            }}
          >
            {I18N.t('关闭')}
          </Button>
        </div>
      }
      title={I18N.t('达人索样自动审批策略')}
      onCancel={() => {
        setVisible(false);
      }}
    >
      <Alert
        showIcon
        message={I18N.t(
          '您可以创建若干针对达人索样请求的自动审批策略，只要满足条件即可自动同意或拒绝，这将大幅降低您对索样请求的审批效率',
        )}
      />
      <div style={{ height: 370, marginTop: 8 }}>
        <ProTable<API.TkshopSampleRequestPolicyVo>
          actionRef={actionRef}
          {...scrollProTableOptionFn({
            search: false,
            pagination: false,
          })}
          request={async () => {
            return tkshopSampleRequestPolicyListGet({}).then((res) => {
              return {
                data: res.data || [],
              };
            });
          }}
          columns={[
            {
              title: '名称',
              dataIndex: 'policyName', // the field name in the data source
              ellipsis: true, // optional, for truncating text if too long
            },
            {
              title: '策略类型',
              dataIndex: 'approved',
              width: 85,
              ellipsis: true,
              render(_text, record) {
                return record.approved ? (
                  <Typography.Text type={'success'}>{I18N.t('同意策略')}</Typography.Text>
                ) : (
                  <Typography.Text type={'danger'}>{I18N.t('拒绝策略')}</Typography.Text>
                );
              },
            },
            {
              title: '子条件',
              width: 70,
              dataIndex: 'conditions',
              ellipsis: true,
              valueType: 'digit',
              renderText(_dom, record) {
                return record.conditionList?.length || 0;
              },
            },
            {
              title: '条件关系',
              width: 85,
              dataIndex: 'logicalCondition',
              ellipsis: true,
              valueEnum: {
                AND: '并且',
                OR: '或者',
              },
            },
            {
              title: '创建时间',
              width: 120,
              dataIndex: 'createTime',
              valueType: 'date', // Use for date formatting
              ellipsis: true,
            },
            {
              title: '操作',
              key: 'action',
              width: 90,
              render: (_dom, record) => (
                <Space size={12}>
                  <Typography.Link
                    type={'danger'}
                    onClick={() => {
                      GhostModalCaller(
                        <TriggerPolicyConfirmModal policy={record} shopId={shopId} />,
                      );
                    }}
                  >
                    <Tooltip title={I18N.t('执行')}>
                      <IconFontIcon iconName={'kaishi_24'} />
                    </Tooltip>
                  </Typography.Link>
                  <Typography.Link
                    onClick={() => {
                      GhostModalCaller(
                        <PolicyFormModal
                          data={record}
                          onUpdate={() => {
                            actionRef.current?.reload();
                          }}
                        />,
                      );
                    }}
                  >
                    <Tooltip title={I18N.t('编辑')}>
                      <IconFontIcon iconName={'edit_24'} />
                    </Tooltip>
                  </Typography.Link>
                  <Typography.Link
                    onClick={() => {
                      DMConfirm({
                        width: 460,
                        title: I18N.t('确定要删除此自动审批策略吗？'),
                        content: I18N.t('该操作不可恢复，请确认是否继续'),
                        onOk() {
                          tkshopSampleRequestPolicyByIdDelete({
                            id: record.id!,
                          }).then(() => {
                            message.success(I18N.t('删除成功'));
                            actionRef.current?.reload();
                          });
                        },
                      });
                    }}
                  >
                    <Tooltip title={I18N.t('删除')}>
                      <IconFontIcon iconName={'Trash_24'} />
                    </Tooltip>
                  </Typography.Link>
                </Space>
              ),
            },
          ]}
        />
      </div>
    </DMModal>
  );
};
export default SampleRequestPolicy;
