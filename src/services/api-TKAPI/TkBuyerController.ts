// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 分页查询团队买家 GET /api/tk/buyer/creator/page */
export async function tkBuyerCreatorPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkBuyerCreatorPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultTkBuyerVo>('/api/tk/buyer/creator/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 根据handle列表查询出对应的买家列表 GET /api/tk/buyer/findBuyerIdsByHandles */
export async function tkBuyerFindBuyerIdsByHandlesGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkBuyerFindBuyerIdsByHandlesGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMapstring>('/api/tk/buyer/findBuyerIdsByHandles', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询某个tk买家的订单 GET /api/tk/buyer/findBuyerOrders */
export async function tkBuyerFindBuyerOrdersGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkBuyerFindBuyerOrdersGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListTkOrderVo>('/api/tk/buyer/findBuyerOrders', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取某个分身数据库里最后一个订单的orderNo，为空表示数据库里没有订单 GET /api/tk/buyer/findLatestOrderNo */
export async function tkBuyerFindLatestOrderNoGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkBuyerFindLatestOrderNoGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultstring>('/api/tk/buyer/findLatestOrderNo', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 同步一个订单，如果 买家/订单/产品 信息不存在则创建，存在则更新 POST /api/tk/buyer/syncOrder */
export async function tkBuyerSyncOrderPost(
  body: API.SyncOrderRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tk/buyer/syncOrder', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
