import {
  Al<PERSON>,
  Card,
  Col,
  Form,
  InputNumber,
  Layout,
  message,
  Radio,
  Row,
  Space,
  Tabs,
  Typography,
} from 'antd';
import styles from './style.less';
import LabelRow, { LabelRowContext } from '@/components/Common/LabelRow';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { useMemo, useState } from 'react';
import DMModal from '@/components/Common/Modal/DMModal';
import DMFormItem, { DMFormItemContext } from '@/components/Common/DMFormItem';
import { useMount, useThrottleFn } from 'ahooks';
import type { GhostModalWrapperComponentProps } from '@/mixins/modal';
import { GhostModalCaller } from '@/mixins/modal';
import type { SystemConfig } from '@/hooks/useSystemConfig';
import { useSystemConfig } from '@/hooks/useSystemConfig';
import { SkipErrorNotifyOption, trimValues } from '@/utils/utils';
import TeamStatusCard from '@/pages/Setting/components/TeamStatusCard';
import { useLocation, history } from 'umi';
import EmailTemplateCard from '@/pages/Setting/components/EmailTemplateCard';
import { useRequest } from '@@/plugin-request/request';
import { tkEmailListGet } from '@/services/api-TKAPI/TkEmailController';
import EmailServiceCard from '@/pages/Setting/components/EmailServiceCard';
import EmailServiceModal from '@/pages/Setting/components/EmailServiceModal';
import I18N from '@/i18n/I18N';
import SpeechConfig from '@/pages/Setting/components/SpeechConfig';
import { StyledSettingCard } from '@/pages/Setting/utils';
import AutoTagCard from '@/pages/Setting/components/AutoTagCard';
import { AccessDeniedWrapper } from '@/components/Common/AccessDenied';
import Functions from '@/constants/Functions';
import BrowserPolicyCard from '@/pages/Setting/components/BrowserPolicyCard';
import DeviceConfigCard from '@/pages/Setting/components/DeviceConfigCard';
import FlowConfigCard from '@/pages/Setting/components/FlowConfigCard';
import ExchangeCard from '@/pages/Setting/components/ExchangeCard';
import { ClosableAlert } from '@/components/Common/ClosableAlert';
import { paymentOrdersGet } from '@/services/api-PaymentAPI/PaymentController';
import { openClient } from '@/utils/pageUtils';
import { getCurrentTeamId } from '@/hooks/useCurrentTeam';

const PersonalConfigPanel = () => {
  return (
    <Row gutter={[16, 16]} wrap style={{ overflow: 'auto', paddingBottom: 24 }}>
      <Col span={12}>
        <BrowserPolicyCard />
      </Col>
      <Col span={12}>
        <DeviceConfigCard />
      </Col>
    </Row>
  );
};

const SettingModal = (props: GhostModalWrapperComponentProps) => {
  const { modalProps } = props;
  const [visible, changeVisible] = useState(true);
  const [form] = Form.useForm();
  const { data, update } = useSystemConfig();
  const { run: submit, loading } = useRequest(
    async () => {
      const values = await form.validateFields();
      const vals = trimValues(values) as API.TeamTkshopConfig;
      await update(vals);
      message.success(I18N.t('保存成功'));
      changeVisible(false);
    },
    {
      manual: true,
    },
  );

  return (
    <DMModal
      bodyStyle={{ paddingBottom: 0, paddingTop: 12 }}
      width={640}
      title={'通用配置'}
      onOk={submit}
      confirmLoading={loading}
      open={visible}
      {...modalProps}
      onCancel={() => {
        changeVisible(false);
      }}
    >
      <Alert showIcon type={'info'} message={'针对当前团队的一些通用性的系统设置'} />
      <Form<Omit<SystemConfig, 'remind'>>
        className={styles.settingForm}
        style={{ paddingTop: 8 }}
        initialValues={data}
        form={form}
      >
        <DMFormItemContext.Provider
          value={{
            labelWidth: 195,
            disableLabelMuted: true,
          }}
        >
          <DMFormItem
            label={'团队套餐过期前提醒'}
            tooltip="当前团队套餐在过期前会以弹窗形式予以提醒"
            name={'expireRemind'}
          >
            <Radio.Group>
              <Space direction={'vertical'} style={{ overflow: 'hidden' }}>
                <Radio value={true}>
                  <Space>
                    <span>过期前</span>
                    <Form.Item shouldUpdate noStyle>
                      {() => {
                        const disabled = !form.getFieldValue('expireRemind');
                        return (
                          <Form.Item name={'remainBeforeDays'} noStyle>
                            <InputNumber min={1} max={25} disabled={disabled} />
                          </Form.Item>
                        );
                      }}
                    </Form.Item>
                    <span>天提醒</span>
                  </Space>
                </Radio>
                <Radio value={false}>不提醒</Radio>
              </Space>
            </Radio.Group>
          </DMFormItem>
          <DMFormItem
            tooltip={I18N.t(
              '千次播放转化率划分为优秀、正常、较差三个区间，它们分别会用不同的颜色以示区分，请注意，该数值取决于您所售商品的性质，系统默认给出区间设置仅供参考',
            )}
            label={'千次播放转化率优秀区间'}
          >
            <Space align="start">
              <span style={{ lineHeight: '32px' }}>&gt;</span>
              <Form.Item
                name={['ordersPmQueryVo', 'goodRate']}
                validateFirst
                dependencies={[['ordersPmQueryVo', 'badRate']]}
                rules={[
                  {
                    validator: (rule, value) => {
                      // 必填
                      if (!value) {
                        return Promise.reject(new Error('请输入'));
                      }
                      // 优秀区间需要大于较差区间
                      if (value <= form.getFieldValue(['ordersPmQueryVo', 'badRate'])) {
                        return Promise.reject(new Error('优秀区间需要大于较差区间'));
                      }
                      // 取值范围
                      if (value < 0.001 || value > 1000) {
                        return Promise.reject(new Error('取值范围为0.001 - 1000'));
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <InputNumber style={{ width: 120 }} step={0.001} />
              </Form.Item>
            </Space>
          </DMFormItem>
          <DMFormItem
            tooltip={I18N.t(
              '千次播放转化率划分为优秀、正常、较差三个区间，它们分别会用不同的颜色以示区分，请注意，该数值取决于您所售商品的性质，系统默认给出区间设置仅供参考',
            )}
            label={'千次播放转化率较差区间'}
          >
            <Space align="start">
              <span style={{ lineHeight: '32px' }}>&lt;</span>
              <Form.Item
                name={['ordersPmQueryVo', 'badRate']}
                dependencies={[['ordersPmQueryVo', 'goodRate']]}
                rules={[
                  {
                    validator: (rule, value) => {
                      // 必填
                      if (!value) {
                        return Promise.reject(new Error('请输入'));
                      }
                      // 较差区间需要小于优秀区间
                      if (value >= form.getFieldValue(['ordersPmQueryVo', 'goodRate'])) {
                        return Promise.reject(new Error('较差区间需要小于优秀区间'));
                      }
                      // 取值范围
                      if (value < 0.001 || value > 1000) {
                        return Promise.reject(new Error('取值范围为0.001 - 1000'));
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <InputNumber style={{ width: 120 }} step={0.001} />
              </Form.Item>
            </Space>
          </DMFormItem>
          <DMFormItem label={I18N.t('千次播放转化率异常条件')}>
            <div>
              <div style={{ display: 'flex', alignItems: 'flex-start', gap: 8 }}>
                <div style={{ lineHeight: '32px' }}>订单同步时间和播放量更新时间相差大于</div>
                <Form.Item
                  name={['ordersPmQueryVo', 'abnormalSyncTimeDiff']}
                  rules={[
                    {
                      validator: (rule, value) => {
                        // 必填
                        if (!value) {
                          return Promise.reject(new Error('请输入'));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <InputNumber
                    min={1}
                    style={{ width: 130 }}
                    precision={0}
                    step={1}
                    addonAfter={'小时'}
                  />
                </Form.Item>
              </div>
              <div style={{ display: 'flex', alignItems: 'flex-start', gap: 8 }}>
                <div style={{ lineHeight: '32px' }}>播放量小于</div>
                <Form.Item
                  name={['ordersPmQueryVo', 'abnormalViewCnt']}
                  rules={[
                    {
                      validator: (rule, value) => {
                        // 必填
                        if (!value) {
                          return Promise.reject(new Error('请输入'));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <InputNumber style={{ width: 100 }} min={1} precision={0} step={1} />
                </Form.Item>
              </div>
            </div>
          </DMFormItem>
        </DMFormItemContext.Provider>
      </Form>
    </DMModal>
  );
};

const SystemConfigPanel = () => {
  const { data } = useSystemConfig();
  const { data: orderCount } = useRequest(
    () => {
      return paymentOrdersGet({
        orderTypes: 'BuyTkshop,RenewTkshop,UpgradeTkshop',
        payStatusList: 'WAIT_CONFIRM,Locked,Created',
        pageNum: 1,
        pageSize: 1,
      },SkipErrorNotifyOption).then((res) => {
        return {
          data: res.data?.total || 0,
        };
      });
    },
    {
      pollingInterval: 15 * 1000,
    },
  );

  const remind = useMemo(() => {
    if (data?.expireRemind) {
      return `过期前 ${data?.remainBeforeDays} 天提醒`;
    }
    return '不提醒';
  }, [data]);

  const { run: open } = useThrottleFn(
    () => {
      GhostModalCaller(<SettingModal />);
    },
    { wait: 100 },
  );
  return (
    <AccessDeniedWrapper code={Functions.TKSHOP_MANAGE}>
      {orderCount > 0 ? (
        <Alert
          style={{ marginBottom: 8 }}
          showIcon
          message={
            <div>
              <span>您有 {orderCount?.toLocaleString()} 笔待处理的 TKShop 套餐订单</span>
              <Typography.Link
                style={{ marginLeft: 16 }}
                onClick={() => {
                  openClient({
                    action: 'openUrl',
                    method: 'post',
                    url: `/team/${getCurrentTeamId()}/costManage/order`,
                  });
                }}
              >
                查看详情
              </Typography.Link>
            </div>
          }
        />
      ) : null}
      <Row gutter={[16, 16]} wrap style={{ overflow: 'auto', paddingBottom: 24 }}>
        <Col span={12}>
          <TeamStatusCard />
        </Col>
        <Col span={12}>
          <AutoTagCard />
        </Col>
        <Col span={12} style={{ height: 260 }}>
          <Card
            id={'hash_system_config'}
            hoverable
            className={styles.settingCard}
            loading={!data}
            onClick={() => {
              open();
            }}
          >
            <div className="title">通用配置</div>
            <div className="extra">
              <Typography.Link
                onClick={(e) => {
                  e.stopPropagation();
                  open();
                }}
              >
                <IconFontIcon iconName={'shezhi_24'} />
              </Typography.Link>
            </div>
            <div className={'main'} style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
              <LabelRowContext.Provider value={{ labelWidth: 180, labelMuted: true }}>
                <LabelRow label={'团队套餐过期前提醒'}>{remind}</LabelRow>
                <LabelRow label={'千次播放转化率优秀区间'}>
                  &gt; {data?.ordersPmQueryVo?.goodRate}
                </LabelRow>
                <LabelRow label={'千次播放转化率较差区间'}>
                  &lt; {data?.ordersPmQueryVo?.badRate}
                </LabelRow>
                <LabelRow label={'千次播放转化率异常条件'}>
                  <div style={{ display: 'flex', flexDirection: 'column' }}>
                    <div style={{ height: '30px', display: 'flex', alignItems: 'center' }}>
                      订单同步时间和播放量更新时间相差大于{' '}
                      {data?.ordersPmQueryVo?.abnormalSyncTimeDiff} 小时
                    </div>
                    <div style={{ height: '30px', display: 'flex', alignItems: 'center' }}>
                      播放量小于 {data?.ordersPmQueryVo?.abnormalViewCnt}
                    </div>
                  </div>
                </LabelRow>
              </LabelRowContext.Provider>
            </div>
          </Card>
        </Col>
        <Col span={12} style={{ height: 260 }}>
          <FlowConfigCard />
        </Col>
        <Col span={12}>
          <ExchangeCard />
        </Col>
      </Row>
    </AccessDeniedWrapper>
  );
};

const SpeechConfigPanel = () => {
  return (
    <AccessDeniedWrapper code={Functions.TKSHOP_MANAGE}>
      <ClosableAlert
        style={{ marginBottom: 8 }}
        message={I18N.t(
          '请注意，相同场景下的话术可以创建不同的分组，以定向邀约为例，可以创建分组“马来话术”给马来店铺使用，创建“泰国话术”给泰国店铺使用',
        )}
        preserveKey={'speech-config-alert'}
      />
      <Row gutter={[16, 16]} wrap>
        <Col span={24}>
          <SpeechConfig scene={'ShopCreator'} speechType={'TargetPlan'} />
        </Col>
        <Col span={24}>
          <SpeechConfig scene={'ShopCreator'} speechType={'SendMsg'} />
        </Col>
        <Col span={24}>
          <SpeechConfig scene={'ShopCreator'} speechType={'RequestAdCode'} />
        </Col>
        <Col span={24}>
          <SpeechConfig scene={'ShopCreator'} speechType={'ImChat'} />
        </Col>
        <Col span={24}>
          <SpeechConfig scene={'ShopBuyer'} speechType={'SendMsg'} />
        </Col>
        <Col span={24}>
          <SpeechConfig scene={'ShopBuyer'} speechType={'ImChat'} />
        </Col>
      </Row>
    </AccessDeniedWrapper>
  );
};
const EmailConfigPanel = () => {
  const { data: emailServices, run } = useRequest(() => {
    return tkEmailListGet();
  });
  return (
    <AccessDeniedWrapper code={Functions.TKSHOP_MANAGE}>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <EmailTemplateCard />
        </Col>
        {emailServices?.map((item) => {
          return (
            <Col key={item.id!} span={12}>
              <EmailServiceCard
                data={item}
                onUpdate={() => {
                  run();
                }}
              />
            </Col>
          );
        })}
        <Col span={12}>
          <StyledSettingCard
            hoverable
            bodyStyle={{ height: 360 }}
            onClick={() => {
              GhostModalCaller(<EmailServiceModal onUpdate={run} />);
            }}
          >
            <div
              style={{
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: 24,
              }}
            >
              <Typography.Link>
                <IconFontIcon size={64} iconName={'tianjia_24'} />
              </Typography.Link>
              <div>增加新的电子邮件发送服务</div>
            </div>
          </StyledSettingCard>
        </Col>
      </Row>
    </AccessDeniedWrapper>
  );
};

const Setting = () => {
  const { pathname, query, hash } = useLocation();
  const [activeKey, setActiveKey] = useState('personal');
  useMount(() => {
    if (['base', 'speech', 'email', 'personal'].includes(query?.tab)) {
      setActiveKey(query?.tab);
      history.replace(pathname);
      // 保留锚点，滚动到锚点位置
      setTimeout(() => {
        if (hash) {
          window.location.hash = hash;
        }
      }, 300);
    }
  });
  return (
    <Layout.Content className={styles.settingContainer}>
      <Tabs destroyInactiveTabPane centered activeKey={activeKey} onChange={setActiveKey}>
        <Tabs.TabPane key={'personal'} tab={I18N.t('我的个人设置')}>
          <PersonalConfigPanel />
        </Tabs.TabPane>
        <Tabs.TabPane key={'base'} tab={I18N.t('系统通用设置')}>
          <SystemConfigPanel />
        </Tabs.TabPane>
        <Tabs.TabPane key={'speech'} tab={I18N.t('邀约话术设置')}>
          <SpeechConfigPanel />
        </Tabs.TabPane>
        <Tabs.TabPane key={'email'} tab={I18N.t('电子邮件设置')}>
          <EmailConfigPanel />
        </Tabs.TabPane>
      </Tabs>
    </Layout.Content>
  );
};
export default Setting;
