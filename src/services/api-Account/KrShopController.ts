// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取GBS配置 GET /api/krshop/gbsConfig */
export async function krshopGbsConfigGet(options?: { [key: string]: any }) {
  return request<API.WebResultGbsConfig>('/api/krshop/gbsConfig', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 设置GBS PUT /api/krshop/gbsConfig */
export async function krshopGbsConfigPut(body: API.GbsConfig, options?: { [key: string]: any }) {
  return request<API.WebResult>('/api/krshop/gbsConfig', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
