import { StyledLeftTabs, StyledTableWrapper } from '@/style/styled';
import { Button, Tabs, Typography } from 'antd';
import DMModal from '@/components/Common/Modal/DMModal';
import I18N from '@/i18n';
import { ProTable } from '@ant-design/pro-table';
import colors from '@/style/color.less';
import { getNumberDom } from '@/pages/TikTok/utils/utils';
import { scrollProTableOptionFn } from '@/mixins/table';
import FallbackImage from '@/components/FallbackImage';
import type { GhostModalWrapperComponentProps } from '@/mixins/modal';
import { useState } from 'react';
import { useVideoDetailModal } from '@/pages/VideoManage/components/VideoDetailModal';
import { useLiveDetailModal } from '@/pages/LiveManage/components/LiveDetailModal';

const SampleRequestOutputModal = (
  props: GhostModalWrapperComponentProps & {
    data: API.TkshopSampleRequestAuditVo;
    defaultActiveKey?: 'video' | 'live';
  },
) => {
  const { data, defaultActiveKey = 'video', modalProps } = props;
  const { lives, videos, creator } = data;
  const { run: openVideoModal } = useVideoDetailModal();
  const { run: openLiveModal } = useLiveDetailModal();
  const [open, setOpen] = useState(true);
  return (
    <DMModal
      width={920}
      footer={
        <Button
          type={'primary'}
          onClick={() => {
            setOpen(false);
          }}
        >
          {I18N.t('关闭')}
        </Button>
      }
      {...modalProps}
      bodyStyle={{
        padding: 0,
        height: 500,
      }}
      title={I18N.t('索样媒体成果')}
      open={open}
      onCancel={() => {
        setOpen(false);
      }}
    >
      <StyledLeftTabs defaultActiveKey={defaultActiveKey}>
        <Tabs.TabPane key={'video'} tab={I18N.t('视频')}>
          <StyledTableWrapper>
            <ProTable<API.TkshopVideoDto>
              dataSource={videos}
              columns={[
                {
                  title: I18N.t('带货视频'),
                  dataIndex: 'src',
                  ellipsis: true,
                  render(_text, record) {
                    const { mediaAvatar, mediaName } = record;
                    return (
                      <Typography.Text
                        style={{ color: colors.primaryColor, cursor: 'pointer' }}
                        ellipsis={{ tooltip: mediaName }}
                      >
                        <FallbackImage
                          onPreview={() => {
                            openVideoModal({ ...record, creator });
                          }}
                          src={mediaAvatar}
                        />
                        <span
                          onClick={() => {
                            openVideoModal({ ...record, creator });
                          }}
                          style={{ marginLeft: 8, lineHeight: '32px' }}
                        >
                          {mediaName}
                        </span>
                      </Typography.Text>
                    );
                  },
                },
                {
                  title: I18N.t('发布时间'),
                  dataIndex: 'postTime',
                  ellipsis: true,
                  valueType: 'dateTime',
                  width: 165,
                },
                {
                  title: I18N.t('商品售出总量'),
                  dataIndex: 'itemsSold',
                  width: 120,
                  render(_text, record) {
                    const { itemsSold } = record;
                    return getNumberDom(itemsSold);
                  },
                },
                {
                  title: I18N.t('商品售出金额'),
                  dataIndex: 'gmv',
                  width: 120,
                  render(_text, record) {
                    const { gmv, unit } = record;
                    return getNumberDom(gmv, unit);
                  },
                },
                {
                  title: I18N.t('佣金'),
                  dataIndex: 'estCommission',
                  width: 80,
                  render(_text, record) {
                    const { estCommission, unit } = record;
                    return getNumberDom(estCommission, unit);
                  },
                },
              ]}
              {...scrollProTableOptionFn({
                pageId: 'SampleRequestOutputVideosModal',
                pagination: false,
              })}
            />
          </StyledTableWrapper>
        </Tabs.TabPane>
        <Tabs.TabPane key={'live'} tab={I18N.t('直播')}>
          <StyledTableWrapper>
            <ProTable<API.TkshopLiveDto>
              dataSource={lives}
              columns={[
                {
                  title: I18N.t('带货直播'),
                  dataIndex: 'src',
                  ellipsis: true,
                  render(_text, record) {
                    const { mediaName } = record;
                    return (
                      <Typography.Text
                        style={{ color: colors.primaryColor, cursor: 'pointer' }}
                        ellipsis={{ tooltip: mediaName }}
                        onClick={() => {
                          openLiveModal({ ...record, creator });
                        }}
                      >
                        <span style={{ marginLeft: 8, lineHeight: '32px' }}>
                          {mediaName || '未命名'}
                        </span>
                      </Typography.Text>
                    );
                  },
                },
                {
                  title: I18N.t('开始时间'),
                  dataIndex: 'startTime',
                  valueType: 'dateTime',
                  ellipsis: true,
                  width: 155,
                },
                {
                  title: I18N.t('结束时间'),
                  dataIndex: 'endTime',
                  ellipsis: true,
                  valueType: 'dateTime',
                  width: 165,
                },
                {
                  title: I18N.t('商品售出总量'),
                  dataIndex: 'itemsSold',
                  width: 120,
                  render(_text, record) {
                    const { itemsSold } = record;
                    return getNumberDom(itemsSold);
                  },
                },
                {
                  title: I18N.t('商品售出金额'),
                  dataIndex: 'gmv',
                  width: 120,
                  render(_text, record) {
                    const { gmv, unit } = record;
                    return getNumberDom(gmv, unit);
                  },
                },
                {
                  title: I18N.t('佣金'),
                  dataIndex: 'estCommission',
                  width: 80,
                  render(_text, record) {
                    const { estCommission, unit } = record;
                    return getNumberDom(estCommission, unit);
                  },
                },
              ]}
              {...scrollProTableOptionFn({
                pageId: 'SampleRequestOutputModal',
                pagination: false,
              })}
            />
          </StyledTableWrapper>
        </Tabs.TabPane>
      </StyledLeftTabs>
    </DMModal>
  );
};
export default SampleRequestOutputModal;
