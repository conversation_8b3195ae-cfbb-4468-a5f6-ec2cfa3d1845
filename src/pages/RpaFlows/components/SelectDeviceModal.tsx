import type { ReactNode } from 'react';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { <PERSON>ert, Button, Col, Row, Tooltip, Typography } from 'antd';
import styles from './selectDeviceModal.less';
import type { GhostModalWrapperComponentProps } from '@/mixins/modal';
import { GhostModalCaller } from '@/mixins/modal';
import DMModal from '@/components/Common/Modal/DMModal';
import { useRequest } from '@@/plugin-request/request';
import { rpaTaskDevicesGet } from '@/services/api-RPAAPI/RpaTaskController';
import ColoursIcon from '@/components/Common/ColoursIcon';
import IconFontIcon from '@/components/Common/IconFontIcon';
import OsPlatform from '@/components/Common/OsPlatform';
import uaParser from 'ua-parser-js';
import { osPlatformMap } from '@/utils/fingerprint/user-agent.util';
import { checkClient, openPureClient } from '@/utils/pageUtils';
import { getJwt } from '@/utils/utils';
import I18N from '@/i18n';
import _ from 'lodash';
import { ProTable } from '@ant-design/pro-table';
import { scrollProTableOptionFn } from '@/mixins/table';
import { getCurrentUser } from '@/hooks/useCurrentUser';

type Props = GhostModalWrapperComponentProps & {
  flowId?: number;
  selected?: string[];
  onSubmit: (devices: API.RpaRunDeviceVo[]) => Promise<void>;
  onCancel?: () => void;
  title?: string;
  alert?: ReactNode;
};
const ErrorMsg = {
  404: I18N.t('客户端未打开'),
  401: I18N.t('用户身份不一致'),
};
function useLocalDeviceId() {
  const timer = useRef<any>();
  const [port, setPort] = useState(0);
  const [error, setError] = useState(0);
  const { data: deviceId, run } = useRequest(
    async () => {
      let _port = port;
      if (!port) {
        _port = await checkClient(0).catch(() => {
          setError(404);
        });
        setPort(_port as number);
      }
      const jwtData = await window
        .fetch(`http://127.0.0.1:${_port}/checkJwt?jwt=${getJwt()}`)
        .then((rs) => rs.json());
      if (!jwtData.hasJwt) {
        setError(404);
        throw new Error();
      } else if (!jwtData.isSame) {
        setError(401);
        throw new Error();
      } else {
        const data = await window
          .fetch(`http://127.0.0.1:${_port}/getDeviceId`)
          .then((res) => res.json());
        setError(0);
        return {
          data: data?.deviceId,
        };
      }
    },
    {
      onSuccess(res) {
        if (res) {
          clearTimeout(timer.current);
        } else {
          timer.current = setTimeout(run, 3000);
        }
      },
      onError() {
        timer.current = setTimeout(run, 3000);
      },
    },
  );
  useEffect(() => {
    return () => {
      clearTimeout(timer.current);
    };
  }, []);
  return {
    deviceId,
    error,
    port,
  };
}
/**
 * 选择客户端设备
 * @param props
 * @constructor
 */
const SelectDeviceModal: React.FC<Props> = (props) => {
  const {
    flowId,
    selected,
    onSubmit,
    title = I18N.t('请选择要执行流程的设备'),
    onCancel,
    modalProps,
    alert,
  } = props;
  const [visible, setVisible] = useState(true);
  const { deviceId } = useLocalDeviceId();
  const onClose = useCallback(() => {
    setVisible(false);
    onCancel?.();
  }, [onCancel]);
  const [devices, setDevices] = useState<API.RpaRunDeviceVo[]>([]);

  const { run: submit, loading } = useRequest(
    async () => {
      await onSubmit(devices);
      setVisible(false);
    },
    {
      manual: true,
    },
  );

  const { data = [], loading: deviceLoading } = useRequest(() => rpaTaskDevicesGet({ flowId }), {
    formatResult: (res) =>
      res.data?.filter((d) => d.deviceType === 'App' || d.deviceType === 'Extension'),
  });
  const _sorted_list = useMemo(() => {
    return _.orderBy(
      data || [],
      [
        (item) => {
          return item.deviceId === deviceId || selected?.includes(item.deviceId!);
        },
        'online',
        'lastActiveTime',
      ],
      ['desc', 'desc', 'desc'],
    );
  }, [data, deviceId, selected]);

  useEffect(() => {
    if (!deviceLoading) {
      if (selected?.length) {
        const deviceVos = data.filter((vo) => {
          return (
            selected.findIndex((id) => {
              return id === vo.deviceId;
            }) !== -1
          );
        });
        setDevices(deviceVos);
      } else {
        const first_online = _.find(data, (i) => {
          if (deviceId) {
            return i.deviceId === deviceId && i.online!;
          }
          return i.online!;
        });
        if (first_online) {
          setDevices([first_online]);
        }
      }
    }
  }, [data, deviceId, deviceLoading, selected]);
  const alertNode = useMemo(() => {
    if (_.isNil(alert)) {
      return (
        <Alert
          showIcon
          message={I18N.t(
            `您在此处能够看到的设备是指：该电脑必须安装了花漾客户端且必须以您的身份（${
              getCurrentUser()?.nickname
            }）登录过；请注意，这两者缺一不可`,
          )}
        />
      );
    }
    return <Alert showIcon message={alert} />;
  }, [alert]);

  return (
    <DMModal
      open={visible}
      title={title}
      width={800}
      onOk={submit}
      bodyStyle={{ display: 'flex', flexDirection: 'column', gap: 8, paddingTop: alert ? 12 : 16 }}
      onCancel={onClose}
      okButtonProps={{
        disabled: !devices.length,
      }}
      confirmLoading={loading}
      {...modalProps}
    >
      {alertNode}
      <div style={{ height: 430, overflow: 'hidden' }}>
        <ProTable<API.RpaRunDeviceVo>
          loading={deviceLoading}
          {...scrollProTableOptionFn({})}
          size="middle"
          onRow={(record) => {
            return {
              style: {
                cursor: 'pointer',
              },
              onClick() {
                setDevices([record]);
              },
            };
          }}
          columns={[
            {
              title: '设备名称',
              dataIndex: 'hostName',
              render: (dom, record) => {
                if (record.deviceType === 'Extension') {
                  return (
                    <Typography.Text ellipsis title={record.hostName}>
                      <IconFontIcon
                        iconName="tuozhanchengxu_24"
                        style={{ marginRight: 4, color: '#3b78f4' }}
                      />
                      {record.hostName || I18N.t('浏览器插件')}
                    </Typography.Text>
                  );
                }
                return (
                  <Typography.Text ellipsis title={record.hostName}>
                    <ColoursIcon className="kehuduan_24" style={{ marginRight: 4 }} />
                    {dom}
                  </Typography.Text>
                );
              },
            },
            {
              title: '状态',
              width: 110,
              dataIndex: 'status',
              render: (dom, record) => {
                const { online, deviceId: d_id } = record;
                let suffix = '';
                if (d_id === deviceId) {
                  suffix = '（本机）';
                }
                if (!online) {
                  return <Typography.Text type={'secondary'}>{I18N.t('离线')}</Typography.Text>;
                }
                return (
                  <Typography.Text type={'success'}>
                    {I18N.t('在线')}
                    {suffix}
                  </Typography.Text>
                );
              },
            },
            {
              title: '平台',
              dataIndex: 'userAgent',
              width: 50,
              render: (dom, record) => {
                const ua = uaParser(record.userAgent);
                const code = osPlatformMap[ua.os.name!] || 'Win32';
                return <OsPlatform hideName code={code} />;
              },
            },
            {
              title: '核数',
              dataIndex: 'cpus',
              width: 50,
            },
            {
              title: '设备标识',
              dataIndex: 'deviceId',
              width: 125,
              ellipsis: true,
            },
          ]}
          dataSource={_sorted_list}
          rowKey="deviceId"
          rowSelection={{
            type: 'radio',
            selectedRowKeys: devices.map((item) => {
              return item.deviceId!;
            }),
            onChange: (ids, rows) => {
              setDevices(rows);
            },
          }}
          pagination={false}
        />
      </div>
    </DMModal>
  );
};

type SelectDeviceProps = {
  flowId?: number;
  value?: API.RpaRunDeviceVo;
  ignoreDeviceType?: API.RpaRunDeviceVo['deviceType'][];
  onChange?: (val?: API.RpaRunDeviceVo) => void;
};

export function SelectDeviceField({
  flowId,
  value,
  ignoreDeviceType = [],
  onChange,
}: SelectDeviceProps) {
  const showSelectDeviceModal = useSelectDeviceModal();
  const { deviceId, error, port } = useLocalDeviceId();
  const loadingRef = useRef(false);
  const { run: reloadDevice, loading } = useRequest((target?: string) => {
    return rpaTaskDevicesGet({ flowId }).then((res) => {
      const list = (res?.data || []).filter((item) => {
        if (ignoreDeviceType.includes(item.deviceType)) {
          return false;
        }
        return item.deviceType === 'App' || item.deviceType === 'Extension';
      });
      if (target && !value) {
        const _target = list.find((item) => {
          return item.deviceId === target;
        });
        if (_target) {
          onChange?.(_target);
        }
      }
      return {
        data: list,
      };
    });
  });
  useEffect(() => {
    if (deviceId) {
      setTimeout(() => {
        reloadDevice(deviceId);
      }, 1000);
    }
  }, [deviceId, reloadDevice]);

  const errMsgAction = useMemo(() => {
    const loginByJwt = () => {
      const obj = { jwt: getJwt() };
      const queryStr = new URLSearchParams(obj).toString();
      window.fetch(`http://127.0.0.1:${port}/openUrl`, {
        method: 'post',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url: `?${queryStr}` }),
      });
    };
    if (port && (error === 404 || error === 401)) {
      return (
        <Typography.Link
          underline
          style={{ color: 'white' }}
          onClick={(e) => {
            e.stopPropagation();
            if (loadingRef.current) return;
            loadingRef.current = true;
            loginByJwt();
            setTimeout(() => (loadingRef.current = false), 3000);
          }}
        >
          {error === 404 ? '立即登录' : '重新登录'}
        </Typography.Link>
      );
    }
    return (
      <Typography.Link
        underline
        style={{ color: 'white' }}
        onClick={(e) => {
          e.stopPropagation();
          const obj = { jwt: getJwt() };
          const queryStr = new URLSearchParams(obj).toString();
          openPureClient(`?${queryStr}`);
        }}
      >
        立即启动
      </Typography.Link>
    );
  }, [error, port]);

  const getDeviceContent = useCallback(() => {
    if (value) {
      if (value.deviceType === 'Extension') {
        return (
          <Row
            style={{ width: '100%', overflow: 'hidden', marginRight: 8, fontSize: 14 }}
            wrap={false}
          >
            <Col flex={1}>
              <Typography.Text title={value.hostName} ellipsis>
                <IconFontIcon
                  iconName="tuozhanchengxu_24"
                  style={{ marginRight: 4, color: '#3b78f4' }}
                />
                {value.hostName || I18N.t('浏览器插件')}
              </Typography.Text>
            </Col>
          </Row>
        );
      }
      return (
        <Row
          style={{ width: '100%', overflow: 'hidden', marginRight: 8, fontSize: 14 }}
          wrap={false}
        >
          <Col flex={1}>
            <Typography.Text title={value.hostName} ellipsis>
              <ColoursIcon className="kehuduan_24" style={{ marginRight: 4 }} />
              {value.hostName}
            </Typography.Text>
          </Col>
          {/*<IconFontIcon*/}
          {/*  iconName="Close-Circle_24"*/}
          {/*  className={styles.clearBtn}*/}
          {/*  onClick={(e) => {*/}
          {/*    e.stopPropagation();*/}
          {/*    onChange?.(undefined);*/}
          {/*  }}*/}
          {/*/>*/}
        </Row>
      );
    }
    if (error) {
      return (
        <Tooltip placement={'left'} title={errMsgAction}>
          <Typography.Text type="danger">
            <IconFontIcon iconName="jinggao_24" style={{ marginRight: 4 }} />
            {ErrorMsg[error]}
          </Typography.Text>
        </Tooltip>
      );
    }
    return <span style={{ color: '#999' }}>请选择运行设备</span>;
  }, [error, errMsgAction, value]);
  return (
    <Row
      wrap={false}
      onClick={() => {
        if (loading) {
          return;
        }
        showSelectDeviceModal({
          flowId,
          selected: value ? [value.deviceId!] : [],
          onSubmit: async (devices) => {
            const [rpaRunDeviceVo] = devices;
            onChange?.(rpaRunDeviceVo);
          },
        });
      }}
    >
      <Col flex={1} className={styles.deviceInput}>
        {getDeviceContent()}
      </Col>
      <Col>
        <Button type="primary" disabled={loading} className={styles.deviceBtn}>
          选择
        </Button>
      </Col>
    </Row>
  );
}

export default SelectDeviceModal;

export function useSelectDeviceModal() {
  return useCallback((props: Props) => {
    GhostModalCaller(<SelectDeviceModal {...props} />, 'SelectDeviceModal');
  }, []);
}
