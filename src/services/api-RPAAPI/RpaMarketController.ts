// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取某个流程信息 GET /api/rpa/market/${param0} */
export async function rpaMarketByMarketFlowIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaMarketByMarketFlowIdGetParams,
  options?: { [key: string]: any },
) {
  const { marketFlowId: param0, ...queryParams } = params;
  return request<API.WebResultMarketFlowVo>(`/api/rpa/market/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取市场流程的图片 GET /api/rpa/market/${param0}/images */
export async function rpaMarketByMarketFlowIdImagesGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaMarketByMarketFlowIdImagesGetParams,
  options?: { [key: string]: any },
) {
  const { marketFlowId: param0, ...queryParams } = params;
  return request<API.WebResultListMarketFlowImageVo>(`/api/rpa/market/${param0}/images`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 购买一个市场流程 PUT /api/rpa/market/buyMarketFlow */
export async function rpaMarketBuyMarketFlowPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaMarketBuyMarketFlowPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMarketFlowGainsVo>('/api/rpa/market/buyMarketFlow', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取某个团队是不是已经买过一个市场流程了。只要返回不为空就说明购买还在有效期 GET /api/rpa/market/findBuyHis */
export async function rpaMarketFindBuyHisGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaMarketFindBuyHisGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMarketFlowGainsVo>('/api/rpa/market/findBuyHis', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页获取市场流程列表 GET /api/rpa/market/list */
export async function rpaMarketListGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaMarketListGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultMarketFlowVo>('/api/rpa/market/list', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 续费一个市场流程 PUT /api/rpa/market/renewMarketFlow */
export async function rpaMarketRenewMarketFlowPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaMarketRenewMarketFlowPutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultMarketFlowGainsVo>('/api/rpa/market/renewMarketFlow', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
