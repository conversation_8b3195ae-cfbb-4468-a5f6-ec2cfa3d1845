// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 检查哪些达人是否可以邀约 返回可邀约的达人列表 POST /api/tkshop/checkInvitationCreators */
export async function tkshopCheckInvitationCreatorsPost(
  body: API.TkshopCheckInvitationCreatorRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListstring>('/api/tkshop/checkInvitationCreators', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 检查哪些计划需要同步 POST /api/tkshop/checkInvitations */
export async function tkshopCheckInvitationsPost(
  body: API.TkshopCheckInvitationRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListstring>('/api/tkshop/checkInvitations', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 同步计划 POST /api/tkshop/syncInvitations */
export async function tkshopSyncInvitationsPost(
  body: API.TkshopSyncInvitationRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tkshop/syncInvitations', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
