import { Alert, Card, Form, Input, InputNumber, message, Typography } from 'antd';
import styles from '@/pages/Setting/style.less';
import IconFontIcon from '@/components/Common/IconFontIcon';
import LabelRow, { LabelRowContext } from '@/components/Common/LabelRow';
import { useCallback, useState } from 'react';
import DMModal from '@/components/Common/Modal/DMModal';
import I18N from '@/i18n';
import { GhostModalCaller } from '@/mixins/modal';
import { useRequest } from '@@/plugin-request/request';
import DMFormItem, { DMFormItemContext } from '@/components/Common/DMFormItem';
import { trimValues } from '@/utils/utils';
import Placeholder from '@/components/Common/Placeholder';
import {
  tkshopSettingsUpdateTsSyncCreatorPolicyPut,
  tkshopSettingsGetTsSyncCreatorPolicyGet,
} from '@/services/api-TKShopAPI/TkshopSettingController';

const FlowConfigModal = (props: { onUpdate: () => void; data?: API.TSSyncCreatorPolicy }) => {
  const { onUpdate, data } = props;
  const [open, setOpen] = useState(true);
  const [form] = Form.useForm();
  const { run: submit, loading } = useRequest(
    async () => {
      const vals = trimValues(await form.validateFields());
      await tkshopSettingsUpdateTsSyncCreatorPolicyPut(vals);
      message.success(I18N.t('修改成功'));
      setOpen(false);
      onUpdate();
    },
    {
      manual: true,
    },
  );
  return (
    <DMModal
      width={720}
      open={open}
      bodyStyle={{
        paddingTop: 12,
        paddingBottom: 0,
      }}
      onOk={submit}
      confirmLoading={loading}
      title={I18N.t('流程参数')}
      onCancel={() => {
        setOpen(false);
      }}
    >
      <Alert showIcon message={I18N.t('在此处的设置会影响当前团队内所有成员执行流程的效果')} />
      <Form<API.TSSyncCreatorPolicy>
        initialValues={data}
        requiredMark={false}
        form={form}
        style={{ paddingTop: 8 }}
      >
        <DMFormItemContext.Provider value={{ disableLabelMuted: true, labelWidth: 215 }}>
          <DMFormItem
            rules={[
              {
                required: true,
                message: I18N.t('请输入数字'),
              },
            ]}
            name={'maxShopTaskSyncCreator'}
            label={I18N.t('达人基础信息更新每批更新数量')}
          >
            <InputNumber precision={0} min={1} addonAfter={I18N.t('个')} />
          </DMFormItem>
          <DMFormItem
            rules={[
              {
                required: true,
                message: I18N.t('请输入数字'),
              },
            ]}
            name={'shopTaskSyncCreatorInterval'}
            label={I18N.t('达人基础信息更新每批次间隔')}
          >
            <InputNumber precision={0} min={1} addonAfter={I18N.t('分钟')} />
          </DMFormItem>
        </DMFormItemContext.Provider>
      </Form>
    </DMModal>
  );
};

const FlowConfigCard = () => {
  const { data, run } = useRequest(() => {
    return tkshopSettingsGetTsSyncCreatorPolicyGet();
  });
  const open = useCallback(() => {
    GhostModalCaller(<FlowConfigModal data={data} onUpdate={run} />);
  }, [data, run]);
  return (
    <Card
      hoverable
      className={styles.settingCard}
      loading={!data}
      onClick={() => {
        if (data) {
          open();
        }
      }}
    >
      <div className="title">流程参数</div>
      <div className="extra">
        <Typography.Link
          onClick={(e) => {
            e.stopPropagation();
            open();
          }}
        >
          <IconFontIcon iconName={'shezhi_24'} />
        </Typography.Link>
      </div>
      <div className={'main'} style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
        <LabelRowContext.Provider value={{ labelWidth: 215, labelMuted: true }}>
          <LabelRow label={I18N.t('达人基础信息更新每批更新数量')}>
            {I18N.t('{{count}} 个', {
              count: data?.maxShopTaskSyncCreator?.toLocaleString() || <Placeholder />,
            })}
          </LabelRow>
          <LabelRow label={I18N.t('达人基础信息更新每批次间隔')}>
            {I18N.t('{{min}} 分钟', {
              min: data?.shopTaskSyncCreatorInterval?.toLocaleString() || <Placeholder />,
            })}
          </LabelRow>
        </LabelRowContext.Provider>
      </div>
    </Card>
  );
};
export default FlowConfigCard;
