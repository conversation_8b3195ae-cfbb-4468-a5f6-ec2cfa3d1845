import I18N from '@/i18n';
import { useCallback, useEffect, useState } from 'react';
import { Alert, ConfigProvider, DatePicker, Form, Input, Space, Typography } from 'antd';
import { GhostModalCaller } from '@/mixins/modal';
import _ from 'lodash';
import TableColumnsSettingModalTrigger from '@/components/SortableTransfer/TableColumnsSettingModalTrigger';
import { history, useLocation } from 'umi';
import { useMount } from 'ahooks';
import useVideoColumns from '@/pages/VideoManage/components/useVideoColumns';
import VideoAdvanceSearchModal, {
  getVideoSearchFieldsSize,
} from '@/pages/VideoManage/components/VideoAdvanceSearchModal';
import AdvanceSearchDropdown from '@/components/Common/AdvanceSearch/AdvanceSearchDropdown';
import zhCN from 'antd/lib/locale-provider/zh_CN';
import VideoShopSelector from '@/pages/VideoManage/components/VideoShopSelector';
import { getVideoAdvanceSearchStorageKey } from '@/pages/VideoManage/components/utils';
import Selector from '@/components/Common/Selector';
import ColoursIcon from '@/components/Common/ColoursIcon';
import type { ConversionRateType } from '@/pages/VideoManage/components/ConversionRateSelector';
import ConversionRateSelector from '@/pages/VideoManage/components/ConversionRateSelector';
import { StyledInlineForm } from '@/style/styled';

type SearchBody = {
  enabled: boolean;
  data?: API.PageVideoRequest & {
    _searchByOrdersPm?: boolean;
    _orderPmType?: keyof typeof ConversionRateType | 'Range';
  };
};

const useVideoHeader = (options: { onChange: (reset?: boolean) => void }) => {
  const { onChange } = options;
  const [form] = Form.useForm();
  const { query, pathname } = useLocation();
  const _onChange = useCallback(() => {
    onChange(true);
  }, [onChange]);
  const [searchBody, setSearchBody] = useState<SearchBody>(() => {
    const session_state = localStorage.getItem(getVideoAdvanceSearchStorageKey());
    localStorage.removeItem(getVideoAdvanceSearchStorageKey());
    try {
      if (session_state) {
        const _json = JSON.parse(session_state);
        const size = getVideoSearchFieldsSize(_json);
        return {
          enabled: !!size,
          data: { ..._json },
        };
      }
    } catch (e) {
      console.log(e);
    }
    return {
      enabled: false,
      data: {},
    };
  });
  const onAutoSyncSearch = useCallback(() => {
    setSearchBody({
      enabled: false,
      data: {},
    });
    form.setFieldValue('autoSync', 'autoSync');
  }, [form]);
  const {
    columns,
    update,
    meta,
    sorter,
    getSortParams,
    activeColumns,
    order,
    tableWidth,
    header,
    isInitialized,
  } = useVideoColumns(onChange, onAutoSyncSearch);

  useEffect(() => {
    _onChange();
  }, [_onChange, order, searchBody]);
  useMount(() => {
    if (_.size(query)) {
      history.replace(pathname);
    }
  });

  const advanceSearch = useCallback(
    (action: 'search' | 'open', plan?: API.TkshopSearchProfileDto) => {
      if (action === 'search' && plan) {
        setSearchBody({
          enabled: true,
          data: JSON.parse(plan?.params),
        });
        return;
      }
      const _props = {
        planName: plan?.name,
        initialValues: {},
        onSubmit: async (data: any) => {
          setSearchBody({
            enabled: true,
            data,
          });
        },
      };
      if (plan) {
        try {
          _props.initialValues = JSON.parse(plan.params);
        } catch (e) {
          console.log(e);
        }
      } else {
        _props.initialValues = {
          ...(searchBody?.data || {}),
        };
      }
      GhostModalCaller(<VideoAdvanceSearchModal {..._props} />);
    },
    [searchBody?.data],
  );
  const sorterAndColumnar = (
    <>
      <Form.Item noStyle>
        <TableColumnsSettingModalTrigger columns={activeColumns} meta={meta} onSubmit={update} />
      </Form.Item>
      {sorter ? <Form.Item noStyle>{sorter}</Form.Item> : false}
    </>
  );
  let content = (
    <StyledInlineForm
      style={{ flex: 1 }}
      selectWidth={160}
      initialValues={_.transform(query || {}, (acc, value, key) => {
        if (value) {
          if (key === 'shopId') {
            acc[key] = Number(value);
          } else {
            acc[key] = value;
          }
        }
      })}
      form={form}
      layout={'inline'}
    >
      <ConfigProvider locale={zhCN}>
        <Form.Item name={'_range'} style={{ marginRight: 0 }} label={I18N.t('发布时间')}>
          <DatePicker.RangePicker
            style={{
              width: 230,
            }}
            onChange={() => {
              onChange(true);
            }}
            placeholder={[I18N.t('开始时间'), I18N.t('结束时间')]}
          />
        </Form.Item>
      </ConfigProvider>
      <Form.Item noStyle name={'autoSync'}>
        <Selector
          showPlaceholderOption
          onChange={_onChange}
          allowClear
          options={[
            {
              label: (
                <Space size={4}>
                  <ColoursIcon className={'shoucang_24'} size={16} />
                  <span>{I18N.t('关注的视频')}</span>
                </Space>
              ),
              value: 'autoSync',
            },
          ]}
          placeholder={
            <Space size={4}>
              <ColoursIcon size={16} className={'duanshipin_24'} />
              <Typography.Text>{I18N.t('全部视频')}</Typography.Text>
            </Space>
          }
        />
      </Form.Item>
      <Form.Item noStyle name={'shopId'}>
        <VideoShopSelector dropdownMatchSelectWidth={false} onChange={_onChange} />
      </Form.Item>
      <Form.Item noStyle name={'ordersPmTypes'}>
        <ConversionRateSelector onChange={_onChange} />
      </Form.Item>
      <Form.Item noStyle name={'query'}>
        <Input.Search
          style={{
            flex: '0 0 280px',
          }}
          allowClear
          onSearch={(v) => {
            form.setFieldValue('query', v);
            _onChange();
          }}
          placeholder={I18N.t('依据视频标题/ID/备注/达人检索')}
        />
      </Form.Item>
      {sorterAndColumnar}
    </StyledInlineForm>
  );
  if (searchBody?.enabled) {
    const label = (
      <Space>
        <span>{I18N.t('高级查询：')}</span>
        {I18N.t('共设置了{{total}}个查询条件', {
          total: getVideoSearchFieldsSize(searchBody.data),
        })}
        <Typography.Link
          onClick={() => {
            advanceSearch('open');
          }}
        >
          {I18N.t('查看详情')}
        </Typography.Link>
      </Space>
    );

    content = (
      <div
        style={{
          flex: 1,
          display: 'flex',
          alignItems: 'stretch',
          gap: 8,
          overflow: 'hidden',
          flexWrap: 'nowrap',
        }}
      >
        <Alert
          showIcon={false}
          type={'info'}
          style={{
            flex: 1,
            paddingTop: 0,
            paddingBottom: 0,
            overflow: 'hidden',
            display: 'flex',
            alignItems: 'center',
          }}
          message={label}
          closable
          onClose={() => {
            setSearchBody({
              enabled: false,
              data: {},
            });
          }}
        />
        {sorterAndColumnar}
      </div>
    );
  }

  return {
    columns,
    header: (
      <>
        {content}
        <AdvanceSearchDropdown onSearch={advanceSearch} profileType={'TkshopVideo'} />
      </>
    ),
    getSortParams,
    tableWidth,
    tableHeader: header,
    form,
    isInitialized,
    setSearchBody,
    getSearchParams: () => {
      if (searchBody?.enabled) {
        const { autoSync, _searchByOrdersPm, _orderPmType, ordersPmFrom, ordersPmTo, ...rest } =
          searchBody.data || {};

        // 对千次播放进行转换
        if (_searchByOrdersPm) {
          if (_orderPmType === 'Range') {
            rest.ordersPmFrom = Math.min(ordersPmFrom || 0, ordersPmTo || 0);
            rest.ordersPmTo = Math.max(ordersPmFrom || 0, ordersPmTo || 0);
          }
        }
        return {
          ...rest,
          // @ts-ignore
          autoSync: _.isNil(autoSync) || !autoSync ? undefined : true,
        };
      }
      const { _range, autoSync, ...values } = form.getFieldsValue();
      return {
        ...values,
        // @ts-ignore
        autoSync: _.isNil(autoSync) || !autoSync ? undefined : true,
        postTimeFrom: _range?.[0]?.format('YYYY-MM-DD 00:00:00'),
        postTimeTo: _range?.[1]?.format('YYYY-MM-DD 23:59:59'),
      };
    },
  };
};
export default useVideoHeader;
