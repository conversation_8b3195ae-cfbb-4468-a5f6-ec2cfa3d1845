// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取国家（地区）列表 GET /api/meta/countries */
export async function metaCountriesGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.metaCountriesGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListCountryVo>('/api/meta/countries', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取所有语言 GET /api/meta/locales */
export async function metaLocalesGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.metaLocalesGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListLocaleVo>('/api/meta/locales', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取省份 GET /api/meta/provinces */
export async function metaProvincesGet(options?: { [key: string]: any }) {
  return request<API.WebResultListstring>('/api/meta/provinces', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取所有时区 GET /api/meta/timezones */
export async function metaTimezonesGet(options?: { [key: string]: any }) {
  return request<API.WebResultListTimezoneVo>('/api/meta/timezones', {
    method: 'GET',
    ...(options || {}),
  });
}
