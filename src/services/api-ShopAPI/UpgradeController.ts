// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 迁移ViewShare表 GET /api/upgrade/viewShare */
export async function upgradeViewShareGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.upgradeViewShareGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultint>('/api/upgrade/viewShare', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
