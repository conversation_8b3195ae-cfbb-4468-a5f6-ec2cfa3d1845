// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 查询ip日志 GET /api/shop/ip-monitor/logs */
export async function shopIpMonitorLogsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopIpMonitorLogsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultShopIpLogVo>('/api/shop/ip-monitor/logs', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建IP监管 POST /api/shop/ip-monitor/monitor */
export async function shopIpMonitorMonitorPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopIpMonitorMonitorPostParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPlatformIpMonitorDto>('/api/shop/ip-monitor/monitor', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修改IP监管 PUT /api/shop/ip-monitor/monitor/${param0} */
export async function shopIpMonitorMonitorByIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopIpMonitorMonitorByIdPutParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/shop/ip-monitor/monitor/${param0}`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 删除IP监管 DELETE /api/shop/ip-monitor/monitor/${param0} */
export async function shopIpMonitorMonitorByIdDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopIpMonitorMonitorByIdDeleteParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/shop/ip-monitor/monitor/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取IP监管列表 GET /api/shop/ip-monitor/monitors */
export async function shopIpMonitorMonitorsGet(options?: { [key: string]: any }) {
  return request<API.WebResultListPlatformIpMonitorDto>('/api/shop/ip-monitor/monitors', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 汇报会话的IP GET /api/shop/ip-monitor/report */
export async function shopIpMonitorReportGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopIpMonitorReportGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/ip-monitor/report', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
