// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 备份手机 POST /api/shop/mobile/qcloud/backupMobile */
export async function shopMobileQcloudBackupMobilePost(
  body: API.BackupMobileRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/qcloud/backupMobile', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取手机的备份列表 GET /api/shop/mobile/qcloud/backups */
export async function shopMobileQcloudBackupsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileQcloudBackupsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListQCloudMobileBackup>('/api/shop/mobile/qcloud/backups', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 删除一个手机备份 DELETE /api/shop/mobile/qcloud/deleteBackup */
export async function shopMobileQcloudDeleteBackupDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileQcloudDeleteBackupDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/qcloud/deleteBackup', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 批量往手机上传文件 POST /api/shop/mobile/qcloud/distributeFileToMobiles */
export async function shopMobileQcloudDistributeFileToMobilesPost(
  body: API.DistributeFileToMobilesRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/qcloud/distributeFileToMobiles', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 恢复手机 POST /api/shop/mobile/qcloud/restoreMobile */
export async function shopMobileQcloudRestoreMobilePost(
  body: API.RestoreMobileRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/qcloud/restoreMobile', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取用于临时中转文件的直传url GET /api/shop/mobile/qcloud/tempFile/putOssUrl */
export async function shopMobileQcloudTempFilePutOssUrlGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileQcloudTempFilePutOssUrlGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTempFilePutOssUrl>('/api/shop/mobile/qcloud/tempFile/putOssUrl', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
