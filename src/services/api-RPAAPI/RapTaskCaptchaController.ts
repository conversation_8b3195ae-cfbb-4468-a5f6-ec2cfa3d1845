// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 识别验证码 POST /api/rpa/task/captcha/captchaOcr */
export async function rpaTaskCaptchaCaptchaOcrPost(
  body: API.CaptchaOcrRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCaptchaOcrResponse>('/api/rpa/task/captcha/captchaOcr', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 检查一个验证码房间是否存在 GET /api/rpa/task/captcha/checkRoom/${param0} */
export async function rpaTaskCaptchaCheckRoomByRoomNameGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskCaptchaCheckRoomByRoomNameGetParams,
  options?: { [key: string]: any },
) {
  const { roomName: param0, ...queryParams } = params;
  return request<API.WebResultboolean>(`/api/rpa/task/captcha/checkRoom/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 关闭一个验证码房间 DELETE /api/rpa/task/captcha/closeRoom/${param0} */
export async function rpaTaskCaptchaCloseRoomByRoomNameDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskCaptchaCloseRoomByRoomNameDeleteParams,
  options?: { [key: string]: any },
) {
  const { roomName: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/rpa/task/captcha/closeRoom/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 创建一个验证码房间 PUT /api/rpa/task/captcha/createRoom/${param0} */
export async function rpaTaskCaptchaCreateRoomByRoomNamePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.rpaTaskCaptchaCreateRoomByRoomNamePutParams,
  options?: { [key: string]: any },
) {
  const { roomName: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/rpa/task/captcha/createRoom/${param0}`, {
    method: 'PUT',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 文本ocr POST /api/rpa/task/captcha/textOcr */
export async function rpaTaskCaptchaTextOcrPost(
  body: API.CaptchaOcrRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCaptchaOcrResponse>('/api/rpa/task/captcha/textOcr', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
