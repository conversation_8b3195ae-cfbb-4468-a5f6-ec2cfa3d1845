// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 查询花漾账号绑定的TK店铺信息 GET /api/tk/open/bind-tk-shop */
export async function tkOpenBindTkShopGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkOpenBindTkShopGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultTkOpenShopVo>('/api/tk/open/bind-tk-shop', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
