// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 最近30天直播趋势 GET /api/tk/livestream-daily-trend */
export async function tkLivestreamDailyTrendGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkLivestreamDailyTrendGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCreatorMediaTrendVo>('/api/tk/livestream-daily-trend', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 上传一批视频（或直播）数据（店铺端，按天） POST /api/tk/media */
export async function tkMediaPost(
  body: API.UploadMediaItemRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tk/media', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 触发达人document到stat数据同步 GET /api/tk/media/syncMediaFromDocument */
export async function tkMediaSyncMediaFromDocumentGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkMediaSyncMediaFromDocumentGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tk/media/syncMediaFromDocument', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 触发统计按天数据（店铺端数据） GET /api/tk/media/trigger-calc-daily */
export async function tkMediaTriggerCalcDailyGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkMediaTriggerCalcDailyGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tk/media/trigger-calc-daily', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 触发统计（媒体端数据） GET /api/tk/media/trigger-calc-home */
export async function tkMediaTriggerCalcHomeGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkMediaTriggerCalcHomeGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tk/media/trigger-calc-home', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 媒体端上传一批视频（或直播）数据 POST /api/tk/media2 */
export async function tkMedia2Post(
  body: API.UploadMedia2ItemRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/tk/media2', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 最近30天带货趋势 GET /api/tk/promote-daily-trend */
export async function tkPromoteDailyTrendGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkPromoteDailyTrendGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCreatorPromoteTrendVo>('/api/tk/promote-daily-trend', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 最近30天短视频趋势 GET /api/tk/video-daily-trend */
export async function tkVideoDailyTrendGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.tkVideoDailyTrendGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultCreatorMediaTrendVo>('/api/tk/video-daily-trend', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
