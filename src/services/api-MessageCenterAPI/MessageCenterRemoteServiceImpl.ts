// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 广播一个事件，通常是控制台调用来给门户的用户发送一条消息 POST /api/remote/msg-center/notifyDwlPass */
export async function remoteMsgCenterNotifyDwlPassPost(
  body: API.DwlNotifyRequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/remote/msg-center/notifyDwlPass', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 广播一个事件，通常是控制台调用来给门户的用户发送一条消息 POST /api/remote/msg-center/notifyDwlReject */
export async function remoteMsgCenterNotifyDwlRejectPost(
  body: API.DwlNotifyRequest,
  options?: { [key: string]: any },
) {
  return request<any>('/api/remote/msg-center/notifyDwlReject', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 通知门户系统通知有改变 POST /api/remote/msg-center/notifySysBroadcastChange */
export async function remoteMsgCenterNotifySysBroadcastChangePost(options?: {
  [key: string]: any;
}) {
  return request<any>('/api/remote/msg-center/notifySysBroadcastChange', {
    method: 'POST',
    ...(options || {}),
  });
}
