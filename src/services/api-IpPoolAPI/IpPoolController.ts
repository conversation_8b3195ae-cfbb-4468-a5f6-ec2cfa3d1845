// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取团队所有IP池的国家（地区）列表 GET /api/ipp/countries */
export async function ippCountriesGet(options?: { [key: string]: any }) {
  return request<API.WebResultListCountryDto>('/api/ipp/countries', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 查询IP池供应商 GET /api/ipp/ipp/providers */
export async function ippIppProvidersGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ippIppProvidersGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListIppProviderDto>('/api/ipp/ipp/providers', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询供应商的参数定义列表 GET /api/ipp/paramDefs */
export async function ippParamDefsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ippParamDefsGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListIppParamDefDto>('/api/ipp/paramDefs', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 创建IP池 POST /api/ipp/pool */
export async function ippPoolPost(body: API.CreateIpPoolRequest, options?: { [key: string]: any }) {
  return request<API.WebResultIpPoolDto>('/api/ipp/pool', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询IP池详情 GET /api/ipp/pool/${param0} */
export async function ippPoolByIppIdGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ippPoolByIppIdGetParams,
  options?: { [key: string]: any },
) {
  const { ippId: param0, ...queryParams } = params;
  return request<API.WebResultIppPoolWithParamsVo>(`/api/ipp/pool/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 删除IP池 DELETE /api/ipp/pool/${param0} */
export async function ippPoolByIppIdDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ippPoolByIppIdDeleteParams,
  options?: { [key: string]: any },
) {
  const { ippId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/ipp/pool/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 分配一个ip池中的IP GET /api/ipp/pool/${param0}/allocIppIp */
export async function ippPoolByIppIdAllocIppIpGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ippPoolByIppIdAllocIppIpGetParams,
  options?: { [key: string]: any },
) {
  const { ippId: param0, ...queryParams } = params;
  return request<API.WebResultAllocIppIpResult>(`/api/ipp/pool/${param0}/allocIppIp`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改IP池属性 PUT /api/ipp/pool/${param0}/basicInfo */
export async function ippPoolByIppIdBasicInfoPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ippPoolByIppIdBasicInfoPutParams,
  body: API.UpdateIpPoolBasicInfoRequest,
  options?: { [key: string]: any },
) {
  const { ippId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/ipp/pool/${param0}/basicInfo`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询IP池IP列表 GET /api/ipp/pool/${param0}/ips */
export async function ippPoolByIppIdIpsGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ippPoolByIppIdIpsGetParams,
  options?: { [key: string]: any },
) {
  const { ippId: param0, ...queryParams } = params;
  return request<API.WebResultPageResultIppIpDto>(`/api/ipp/pool/${param0}/ips`, {
    method: 'GET',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 批量删除IP池IP DELETE /api/ipp/pool/${param0}/ips */
export async function ippPoolByIppIdIpsDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ippPoolByIppIdIpsDeleteParams,
  options?: { [key: string]: any },
) {
  const { ippId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/ipp/pool/${param0}/ips`, {
    method: 'DELETE',
    params: {
      ...queryParams,
    },
    ...(options || {}),
  });
}

/** 解析Ip池生产结果 POST /api/ipp/pool/${param0}/parseIppIp */
export async function ippPoolByIppIdParseIppIpPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ippPoolByIppIdParseIppIpPostParams,
  body: API.ClientProduceIpResult,
  options?: { [key: string]: any },
) {
  const { ippId: param0, ...queryParams } = params;
  return request<API.WebResultlong>(`/api/ipp/pool/${param0}/parseIppIp`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 修改IP池属性 PUT /api/ipp/pool/${param0}/providerParam */
export async function ippPoolByIppIdProviderParamPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ippPoolByIppIdProviderParamPutParams,
  body: API.UpdateIpPoolProviderParamRequest,
  options?: { [key: string]: any },
) {
  const { ippId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/ipp/pool/${param0}/providerParam`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 修改IP池属性 PUT /api/ipp/pool/${param0}/strategy */
export async function ippPoolByIppIdStrategyPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ippPoolByIppIdStrategyPutParams,
  body: API.UpdateIpPoolStrategyRequest,
  options?: { [key: string]: any },
) {
  const { ippId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/ipp/pool/${param0}/strategy`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询IP池 GET /api/ipp/pool/page */
export async function ippPoolPageGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ippPoolPageGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultIppPoolVo>('/api/ipp/pool/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 批量删除IP池 DELETE /api/ipp/pools */
export async function ippPoolsDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.ippPoolsDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/ipp/pools', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询白名单配置 GET /api/ipp/whitelistConfig */
export async function ippWhitelistConfigGet(options?: { [key: string]: any }) {
  return request<API.WebResultIppWhitelistConfig>('/api/ipp/whitelistConfig', {
    method: 'GET',
    ...(options || {}),
  });
}
