// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 检查IP池的参数 POST /api/remote/apiWorker/${param0}/ipp/checkParams */
export async function remoteApiWorkerByIdentifierIppCheckParamsPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteApiWorkerByIdentifierIppCheckParamsPostParams,
  body: API.ProduceIpRequest,
  options?: { [key: string]: any },
) {
  const { identifier: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/remote/apiWorker/${param0}/ipp/checkParams`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** 让ip池生产调用规范 POST /api/remote/apiWorker/${param0}/ipp/prepareProduceIpSpec */
export async function remoteApiWorkerByIdentifierIppPrepareProduceIpSpecPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteApiWorkerByIdentifierIppPrepareProduceIpSpecPostParams,
  body: API.ProduceIpRequest,
  options?: { [key: string]: any },
) {
  const { identifier: param0, ...queryParams } = params;
  return request<API.WebResultProduceIpSpecVo>(
    `/api/remote/apiWorker/${param0}/ipp/prepareProduceIpSpec`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      params: { ...queryParams },
      data: body,
      ...(options || {}),
    },
  );
}

/** 让ip池生成一批IP POST /api/remote/apiWorker/${param0}/ipp/produce */
export async function remoteApiWorkerByIdentifierIppProducePost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteApiWorkerByIdentifierIppProducePostParams,
  body: API.ProduceIpRequest,
  options?: { [key: string]: any },
) {
  const { identifier: param0, ...queryParams } = params;
  return request<API.WebResultProduceIpResultVo>(`/api/remote/apiWorker/${param0}/ipp/produce`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}
