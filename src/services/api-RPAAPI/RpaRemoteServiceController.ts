// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** triggerRpaCloudWorkerCalc GET /api/remote/rpa/triggerRpaCloudWorkerCalc */
export async function remoteRpaTriggerRpaCloudWorkerCalcGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.remoteRpaTriggerRpaCloudWorkerCalcGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/remote/rpa/triggerRpaCloudWorkerCalc', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
