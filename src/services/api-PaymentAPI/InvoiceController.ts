// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 取消一个发票申请 PUT /api/payment/cancelInvoice/${param0} */
export async function paymentCancelInvoiceByInvoiceIdPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.paymentCancelInvoiceByInvoiceIdPutParams,
  options?: { [key: string]: any },
) {
  const { invoiceId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/payment/cancelInvoice/${param0}`, {
    method: 'PUT',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改发票邮寄地址 POST /api/payment/configVoiceAddress */
export async function paymentConfigVoiceAddressPost(
  body: API.ConfigVoiceInfoRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/payment/configVoiceAddress', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改开票信息 POST /api/payment/configVoiceInfo */
export async function paymentConfigVoiceInfoPost(
  body: API.ConfigVoiceInfoRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/payment/configVoiceInfo', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 开发票 PUT /api/payment/invoice */
export async function paymentInvoicePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.paymentInvoicePutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/payment/invoice', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取团队发票历史记录的接口 GET /api/payment/invoiceHistory */
export async function paymentInvoiceHistoryGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.paymentInvoiceHistoryGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultPageResultInvoiceHistoryVo>('/api/payment/invoiceHistory', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取团队发票概览信息 GET /api/payment/invoiceSummary */
export async function paymentInvoiceSummaryGet(options?: { [key: string]: any }) {
  return request<API.WebResultInvoiceSummaryVo>('/api/payment/invoiceSummary', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取可以开发票的订单列表，该接口不分页 GET /api/payment/notInvoicedOrders */
export async function paymentNotInvoicedOrdersGet(options?: { [key: string]: any }) {
  return request<API.WebResultListNotInvoicedOrderVo>('/api/payment/notInvoicedOrders', {
    method: 'GET',
    ...(options || {}),
  });
}
