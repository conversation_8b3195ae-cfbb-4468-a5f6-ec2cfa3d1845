import type { CSSProperties } from 'react';
import { useCallback, useMemo } from 'react';
import { useEffect, useState } from 'react';
import { shopByIdPlatformGet, shopByShopIdBriefGet } from '@/services/api-ShopAPI/ShopController';
import { Spin, Tooltip, Typography } from 'antd';
import { SkipErrorNotifyOption } from '@/utils/utils';
import colors from '@/style/color.less';
import IconFontIcon from '@/components/Common/IconFontIcon';
import { RESOURCE_NOT_FOUND } from '@/constants/ErrorCode';
import { AreaIcon } from '@/components/Common/LocationCascader';
import Placeholder from '@/components/Common/Placeholder';

const ShopCache: Record<number, Promise<string>> = {};
const ShopByIdCache: Record<number, Promise<API.ShopBriefVo>> = {};

export function useShopPlatformCacheById(shopId: number, manual = true) {
  const [data, setData] = useState<string>();
  useEffect(() => {
    if (!Number.isNaN(Number(shopId)) && shopId) {
      if (!ShopCache[shopId]) {
        ShopCache[shopId] = shopByIdPlatformGet(
          {
            id: shopId!,
          },
          SkipErrorNotifyOption,
        )
          .then((res) => {
            return res.data!;
          })
          .catch((e) => {
            return 'Other';
          });
      }
      ShopCache[shopId].then((res) => {
        setData(res);
      });
    }
  }, [shopId]);
  return data;
}
export function useGetShopBriefById() {
  return useCallback((id: number) => {
    if (!ShopByIdCache[id]) {
      ShopByIdCache[id] = shopByShopIdBriefGet(
        {
          shopId: id,
        },
        SkipErrorNotifyOption,
      )
        .then((res) => {
          return res.data!;
        })
        .catch((e) => {
          return {
            code: e?.data?.code,
            id,
            error: true,
            message: e.message,
          };
        });
    }
    return ShopByIdCache[id];
  }, []);
}
export function useShopBriefById(shopId?: number) {
  const [data, setData] = useState<API.ShopBriefVo>();
  const getShopBriefById = useGetShopBriefById();
  useEffect(() => {
    if (!Number.isNaN(Number(shopId)) && shopId !== undefined) {
      getShopBriefById(shopId).then((res) => {
        setData(res!);
      });
    }
  }, [getShopBriefById, shopId]);
  return data;
}

export const ShopIdNode = (props: {
  shopId: number;
  shopName: string;
  style?: CSSProperties;
  className?: string;
  iconSize?: number;
}) => {
  const { shopId, style, className, iconSize, shopName } = props;
  const platform = useShopPlatformCacheById(shopId);

  if (!platform) {
    return <Spin size={'small'} />;
  }
  return (
    <div
      className={className}
      style={{
        display: 'flex',
        maxWidth: '100%',
        overflow: 'hidden',
        gap: 4,
        alignItems: 'center',
        ...style,
      }}
    >
      <span style={{ minWidth: iconSize }}>
        <AreaIcon area={platform?.area} size={iconSize} />
      </span>
      <Typography.Text ellipsis={{ tooltip: shopName }} style={{ flex: 1, color: 'inherit' }}>
        {shopName}
      </Typography.Text>
    </div>
  );
};
export const ShopNodeById = (props: {
  id: number;
  style?: CSSProperties;
  className?: string;
  iconSize?: number;
  children?: any;
  onClick?: (data: API.ShopBriefVo) => void;
  showTooltip?: boolean;
}) => {
  const {
    id,
    style = {},
    className,
    iconSize,
    showTooltip = true,
    children = null,
    onClick,
  } = props;
  const data = useShopBriefById(id);
  const clickable = useMemo(() => {
    return data && onClick;
  }, [data, onClick]);
  if (!data) {
    return <IconFontIcon iconName={'loading_24'} spin style={{ fontSize: iconSize }} />;
  }
  if (data?.error) {
    if (data.code === RESOURCE_NOT_FOUND) {
      // 资源不存在就不显示了
      return children;
    }
    return (
      <Tooltip title={data.message}>
        <IconFontIcon iconName={'baoqian_24'} size={iconSize} color={colors.primaryColor} />
      </Tooltip>
    );
  }
  const { platform, name } = data;

  return (
    <div
      className={className}
      style={{
        display: 'flex',
        maxWidth: '100%',
        overflow: 'hidden',
        gap: 4,
        alignItems: 'center',
      }}
    >
      <AreaIcon area={platform?.area!} size={iconSize} />
      <Typography.Text
        onClick={() => {
          if (clickable) {
            onClick?.(data);
          }
        }}
        ellipsis={{ tooltip: showTooltip ? name : false }}
        style={{
          flex: 1,
          color: clickable ? colors.primaryColor : 'inherit',
          cursor: clickable ? 'pointer' : 'default',
          ...style,
        }}
      >
        {name}
      </Typography.Text>
    </div>
  );
};
export function ShopDetailNode(props: {
  data: API.ShopDetailVo;
  iconSize?: number;
  style?: CSSProperties;
  className?: string;
  onClick?: (e: any) => any;
}) {
  const { data, className, style, iconSize, onClick } = props;
  const { platform, name } = data;
  const node = useMemo(() => {
    return (
      <Typography.Text
        onClick={(e) => {
          if (onClick) {
            onClick(e);
          }
        }}
        ellipsis={{ tooltip: name }}
        style={{
          flex: 1,
          color: onClick ? colors.primaryColor : 'inherit',
          cursor: onClick ? 'pointer' : 'inherit',
        }}
      >
        {name}
      </Typography.Text>
    );
  }, [name, onClick]);
  return (
    <div
      className={className}
      style={{
        display: 'flex',
        maxWidth: '100%',
        overflow: 'hidden',
        gap: 4,
        alignItems: 'center',
        ...style,
      }}
    >
      <span style={{ minWidth: iconSize }}>
        <AreaIcon area={platform?.area} size={iconSize} />
      </span>
      {node}
    </div>
  );
}
export const ShopNameLinkById = (props: {
  id: number;
  onClick?: (data: API.ShopBriefVo) => void;
  style?: CSSProperties;
  showTooltip?: boolean;
}) => {
  const { id, onClick, style, showTooltip = true } = props;
  const data = useShopBriefById(id);

  if (!data) {
    return <IconFontIcon iconName={'loading_24'} spin />;
  }
  if (data?.error) {
    if (data.code === RESOURCE_NOT_FOUND) {
      // 资源不存在就不显示了
      return <Placeholder>{id}</Placeholder>;
    }
    return (
      <Typography.Link type={'danger'} ellipsis>
        {data.message}
      </Typography.Link>
    );
  }
  return (
    <Typography.Text
      style={{ color: colors.primaryColor, cursor: 'pointer', ...(style || {}) }}
      onClick={() => {
        if (data && onClick) {
          onClick(data);
        }
      }}
      ellipsis={{
        tooltip: showTooltip ? data.name : false,
      }}
    >
      {data.name!}
    </Typography.Text>
  );
};
export default ShopIdNode;
