// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 批量联营一批手机到目标团队 POST /api/shop/mobile/share/batch */
export async function shopMobileShareBatchPost(
  body: API.AddMobileShareRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResultAuditDto>('/api/shop/mobile/share/batch', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 取消某台手机联营，由发起联营的团队调用 POST /api/shop/mobile/share/delete */
export async function shopMobileShareDeletePost(
  body: API.CommonIdsRequest,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/share/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 取消某个特定的手机分享 DELETE /api/shop/mobile/share/deleteSpecificShare */
export async function shopMobileShareDeleteSpecificShareDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileShareDeleteSpecificShareDeleteParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/shop/mobile/share/deleteSpecificShare', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取一台手机联营到了哪些团队 GET /api/shop/mobile/share/findSharedMobiles */
export async function shopMobileShareFindSharedMobilesGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileShareFindSharedMobilesGetParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResultListMobileShareVo>('/api/shop/mobile/share/findSharedMobiles', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 退还联营的手机，由被联营的团队调用 mobileId是子分身ID DELETE /api/shop/mobile/share/returnShare/${param0} */
export async function shopMobileShareReturnShareByMobileIdDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.shopMobileShareReturnShareByMobileIdDeleteParams,
  options?: { [key: string]: any },
) {
  const { mobileId: param0, ...queryParams } = params;
  return request<API.WebResult>(`/api/shop/mobile/share/returnShare/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}
