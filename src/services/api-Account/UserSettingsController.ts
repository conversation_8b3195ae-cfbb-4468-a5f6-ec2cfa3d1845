// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

/** 获取用户浏览器语言配置: en-US, zh-CN, zh-TW； null或未配置表示由客户端电脑环境决定 GET /api/user/settings/language */
export async function userSettingsLanguageGet(options?: { [key: string]: any }) {
  return request<API.WebResultstring>('/api/user/settings/language', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 设置用户浏览器语言配置: en-US, zh-CN, zh-TW； 未配置表示由客户端电脑环境决定 PUT /api/user/settings/language */
export async function userSettingsLanguagePut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.userSettingsLanguagePutParams,
  options?: { [key: string]: any },
) {
  return request<API.WebResult>('/api/user/settings/language', {
    method: 'PUT',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
